package com.wantwant.sfa.backend;

import com.wantwant.sfa.backend.domain.flow.service.impl.GradeUtils;
import org.junit.Test;
import static org.junit.Assert.assertEquals;

/**
 * GradeUtils工具类单元测试
 * 全面测试Grade字符串中数字提取功能
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class GradeUtilsTest {

    @Test
    public void testExtractGradeNumber_StandardFormats() {
        // 标准格式测试
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumber("S01"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractGradeNumber("S16"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("S00"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumber("BD01"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractGradeNumber("BD16"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("BD00"));
        assertEquals(Integer.valueOf(5), GradeUtils.extractGradeNumber("A05"));
        assertEquals(Integer.valueOf(123), GradeUtils.extractGradeNumber("TEST123"));
    }

    @Test
    public void testExtractGradeNumber_EdgeCases() {
        // 边界情况测试
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber(null));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber(""));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("   "));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("ABC"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("S"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("BD"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("XYZ"));
    }

    @Test
    public void testExtractGradeNumber_SingleDigit() {
        // 单数字测试
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumber("S1"));
        assertEquals(Integer.valueOf(9), GradeUtils.extractGradeNumber("BD9"));
        assertEquals(Integer.valueOf(5), GradeUtils.extractGradeNumber("A5"));
    }

    @Test
    public void testExtractGradeNumber_MultipleDigitGroups() {
        // 多数字组测试（取第一个连续数字组）
        assertEquals(Integer.valueOf(12), GradeUtils.extractGradeNumber("A12B34"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumber("S1D2"));
        assertEquals(Integer.valueOf(100), GradeUtils.extractGradeNumber("TEST100ABC"));
        assertEquals(Integer.valueOf(99), GradeUtils.extractGradeNumber("GRADE99MORE123"));
    }

    @Test
    public void testExtractGradeNumber_LeadingNumbers() {
        // 以数字开头的测试
        assertEquals(Integer.valueOf(123), GradeUtils.extractGradeNumber("123ABC"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumber("1S"));
        assertEquals(Integer.valueOf(99), GradeUtils.extractGradeNumber("99"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("0GRADE"));
    }

    @Test
    public void testExtractGradeNumberAsInteger() {
        // 测试extractGradeNumberAsInteger方法（实际上是extractGradeNumber的别名）
        assertEquals(Integer.valueOf(1), GradeUtils.extractGradeNumberAsInteger("S01"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractGradeNumberAsInteger("BD16"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumberAsInteger(null));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumberAsInteger(""));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumberAsInteger("ABC"));
        assertEquals(Integer.valueOf(123), GradeUtils.extractGradeNumberAsInteger("TEST123XYZ456"));
    }

    @Test
    public void testExtractLastGradeNumber_StandardFormats() {
        // 标准格式测试
        assertEquals(Integer.valueOf(1), GradeUtils.extractLastGradeNumber("S01"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractLastGradeNumber("S16"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("S00"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractLastGradeNumber("BD01"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractLastGradeNumber("BD16"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("BD00"));
        assertEquals(Integer.valueOf(5), GradeUtils.extractLastGradeNumber("A05"));
        assertEquals(Integer.valueOf(99), GradeUtils.extractLastGradeNumber("TEST99"));
    }

    @Test
    public void testExtractLastGradeNumber_EdgeCases() {
        // 边界情况测试
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber(null));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber(""));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("   "));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("ABC"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("S"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("BD"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("XYZ"));
    }

    @Test
    public void testExtractLastGradeNumber_MultipleDigitGroups() {
        // 多数字组测试（取最后一个数字组）
        assertEquals(Integer.valueOf(99), GradeUtils.extractLastGradeNumber("S01BD99"));
        assertEquals(Integer.valueOf(16), GradeUtils.extractLastGradeNumber("A05S16"));
        assertEquals(Integer.valueOf(25), GradeUtils.extractLastGradeNumber("TEST10XYZ25"));
        assertEquals(Integer.valueOf(34), GradeUtils.extractLastGradeNumber("A12B34"));
        assertEquals(Integer.valueOf(2), GradeUtils.extractLastGradeNumber("S1D2"));
        assertEquals(Integer.valueOf(456), GradeUtils.extractLastGradeNumber("TEST100ABC456"));
        assertEquals(Integer.valueOf(999), GradeUtils.extractLastGradeNumber("A1B2C3D999"));
    }

    @Test
    public void testExtractLastGradeNumber_SingleDigitGroup() {
        // 单数字组测试
        assertEquals(Integer.valueOf(123), GradeUtils.extractLastGradeNumber("ABC123"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractLastGradeNumber("S1"));
        assertEquals(Integer.valueOf(99), GradeUtils.extractLastGradeNumber("99"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("PREFIX0"));
    }

    @Test
    public void testExtractLastGradeNumber_ConsecutiveNumbers() {
        // 连续数字测试
        assertEquals(Integer.valueOf(12345), GradeUtils.extractLastGradeNumber("TEST12345"));
        assertEquals(Integer.valueOf(100), GradeUtils.extractLastGradeNumber("A100"));
        assertEquals(Integer.valueOf(789), GradeUtils.extractLastGradeNumber("PREFIX789"));
    }

    @Test
    public void testExtractLastGradeNumber_NumbersAtBeginning() {
        // 数字在开头的测试
        assertEquals(Integer.valueOf(123), GradeUtils.extractLastGradeNumber("123SUFFIX"));
        assertEquals(Integer.valueOf(1), GradeUtils.extractLastGradeNumber("1SUFFIX"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractLastGradeNumber("0SUFFIX"));
    }

    @Test
    public void testExtractLastGradeNumber_ComplexPatterns() {
        // 复杂模式测试
        assertEquals(Integer.valueOf(789), GradeUtils.extractLastGradeNumber("123ABC456DEF789"));
        assertEquals(Integer.valueOf(999), GradeUtils.extractLastGradeNumber("S01BD16A999"));
        assertEquals(Integer.valueOf(88), GradeUtils.extractLastGradeNumber("X1Y2Z3W88"));
    }

    @Test
    public void testBothMethods_ConsistencyCheck() {
        // 测试两个方法在单数字组情况下的一致性
        String[] singleDigitGroupCases = {"S01", "BD16", "A05", "TEST123", "X999"};
        
        for (String testCase : singleDigitGroupCases) {
            assertEquals("Methods should return same result for single digit group: " + testCase,
                GradeUtils.extractGradeNumber(testCase),
                GradeUtils.extractLastGradeNumber(testCase));
        }
    }

    @Test
    public void testBothMethods_DifferenceCheck() {
        // 测试两个方法在多数字组情况下的差异
        assertEquals("First method should return first number", 
            Integer.valueOf(12), GradeUtils.extractGradeNumber("A12B34"));
        assertEquals("Last method should return last number", 
            Integer.valueOf(34), GradeUtils.extractLastGradeNumber("A12B34"));
        
        assertEquals("First method should return first number", 
            Integer.valueOf(1), GradeUtils.extractGradeNumber("S1D2E3"));
        assertEquals("Last method should return last number", 
            Integer.valueOf(3), GradeUtils.extractLastGradeNumber("S1D2E3"));
        
        assertEquals("First method should return first number", 
            Integer.valueOf(100), GradeUtils.extractGradeNumber("TEST100ABC456"));
        assertEquals("Last method should return last number", 
            Integer.valueOf(456), GradeUtils.extractLastGradeNumber("TEST100ABC456"));
    }

    @Test
    public void testLargeNumbers() {
        // 大数字测试
        assertEquals(Integer.valueOf(9999), GradeUtils.extractGradeNumber("GRADE9999"));
        assertEquals(Integer.valueOf(9999), GradeUtils.extractLastGradeNumber("GRADE9999"));
        assertEquals(Integer.valueOf(12345), GradeUtils.extractGradeNumber("S12345"));
        assertEquals(Integer.valueOf(67890), GradeUtils.extractLastGradeNumber("A12345B67890"));
    }

    @Test
    public void testSpecialCharacters() {
        // 特殊字符测试
        assertEquals(Integer.valueOf(123), GradeUtils.extractGradeNumber("@#$123ABC"));
        assertEquals(Integer.valueOf(456), GradeUtils.extractLastGradeNumber("ABC123@#$456"));
        assertEquals(Integer.valueOf(0), GradeUtils.extractGradeNumber("@#$%^&*()"));
        assertEquals(Integer.valueOf(789), GradeUtils.extractGradeNumber("S-789"));
    }
} 