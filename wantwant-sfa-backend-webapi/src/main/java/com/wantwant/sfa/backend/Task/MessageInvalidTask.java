package com.wantwant.sfa.backend.Task;

import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import com.wantwant.sfa.backend.service.impl.MessageServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * 需求: 消息失效事件
 * 1、去除失效消息的置顶
 */
@Configuration
@Slf4j
public class MessageInvalidTask {

    @Autowired
    private MessageServiceImpl messageService;
    
//    @Scheduled(cron = "0 5 0 * * ? ")
    private void messageInvalidTaskexecute() {

    	LocalDate today = LocalDate.now();
        log.info("execute messageInvalidTaskexecute today:{}",today);
        messageService.messageInvalid(today);

    }

}
