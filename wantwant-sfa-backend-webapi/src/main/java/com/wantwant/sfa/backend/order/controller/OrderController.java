package com.wantwant.sfa.backend.order.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.order.api.OrderApi;
import com.wantwant.sfa.backend.order.request.*;
import com.wantwant.sfa.backend.order.vo.*;
import com.wantwant.sfa.backend.service.IOrderService;
import com.wantwant.sfa.backend.service.OfflineExportService;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.base.CommonConstant;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description 订单
 * <AUTHOR>
 * @Date 2020/7/22
 **/
@RestController
@Slf4j
public class OrderController implements OrderApi {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private OfflineExportService offlineExportService;

    @Autowired
    private SettingServiceImpl settingService;

    @Autowired
    private SettingsMapper settingsMapper;

    @Value("${export.order.max.count}")
    private Long maxCount = 10000l;

    //目标变更共用key
    public static final String LOCK_HEAD_CHANGE_ORDER = "changeOrderLock";

    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @Override
    public Response<IPage<OrderListNewVo>> orderList(OrderListNewRequest request) {
        log.info("orderList request:{}", request);
        return Response.success(orderService.orderList(request));
    }

    @ApiOperation(value = "订单列表导出", notes = "订单列表导出")
    @Override
    public void orderListExport(OrderListNewRequest request) {
        log.info("orderListExport request:{}", request);
        request.setRows(Optional.ofNullable(settingsMapper.getSfaSettingsByCode("export_max")).map(Integer::parseInt).orElse(1000));
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd"));
        List<OrderSkuExportNewVo> records = orderService.orderSkuExport(request).getRecords();
        Workbook wb;
        if (CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(RequestUtils.getLoginInfo().getTimezone())) {
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), OrderSkuExportNewVo.class, records);
        } else {
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), OrderSkuExportNewEnVo.class, BeanUtil.copyToList(records, OrderSkuExportNewEnVo.class));
        }
        try {
            String fileName = CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(RequestUtils.getLoginInfo().getTimezone()) ? "订单sku列表" : "Order sku list";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @ApiOperation(value = "订单详情", notes = "订单详情")
    @Override
    public Response<OrderDetailNewVo> orderDetail(OrderDetailNewRequest request) {
        log.info("orderDetail request:{}", request);
        return Response.success(orderService.orderDetail(request));
    }

    @Override
    public Response<List<String>> selectOrderNo(SearchOrderNoRequest searchOrderNoRequest) {

        log.info("【select order no 】request:{}",JSONObject.toJSONString(searchOrderNoRequest));
        return orderService.selectOrderNo(searchOrderNoRequest);
    }

    @ApiOperation(value = "订单商品列表", notes = "订单商品列表")
    @Override
    public Response<IPage<OrderSkuListNewVo>> orderSkuList(OrderSkuListNewRequest request) {
        log.info("orderSkuList request:{}", request);
        return Response.success(orderService.orderSkuList(request));
    }

    @Override
    public void export(OrderListRequest request) {
        log.info("start OrderController export request:{}", request);


        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        Integer count = orderService.CountOrderListExport(request);
        if (maxCount < count) {
            throw new ApplicationException("文件过大，请选择离线导出！");
        }

        Workbook wb = orderService.orderListExportExcel(request);
        try {
            String fileName = "订单列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }


    }

    @Override
    public Response count(OrderListRequest request) {
        Integer count = orderService.CountOrderListExport(request);
        return maxCount < count ? Response.error("文件过大，请选择离线导出！") : Response.success();
    }

    @Override
    public Response<JSONObject> orderListExportAsync(OrderListRequest request) {
        // TODO Auto-generated method stub
        return offlineExportService.export(request.getPerson(), request.getPersonName(), "orderServiceImpl", "orderListExportExcel", "CountOrderListExport", request, OrderListRequest.class);
    }

    @Override
    public Response orderNotice(OrderNoticeRequest request) {
        orderService.notice(request);
        return Response.success();
    }

    @Override
    public Response<IPage<OrderGrantVO>> queryGrantByPage(OrderGrantRequest request) {
        return Response.success(orderService.queryGrantByPage(request));
    }

    @Override
    public void exportGrant(OrderGrantRequest request) {
        orderService.exportGrant(request);
    }

    /**
     * 品相利润监控
     */
    @Override
    public Response<IPage<OrderMonitorVO>> queryMonitorByPage(OrderMonitorRequest request) {
        return Response.success(orderService.queryMonitorByPage(request));
    }

    /**
     * 品相利润监控导出
     */
    @Override
    public void exportMonitor(OrderMonitorRequest request,HttpServletResponse response) {
        orderService.exportMonitor(request,response);
    }

    /**
     * 品相利润监控批量处理
     */
    @Override
    public Response monitorBatch(MonitorBatchRequest batchRequest) {
        orderService.monitorBatch(batchRequest);
        return Response.success();
    }

    /**
     * 利润趋势分析
     */
    @Override
    public Response<List<OrderMonitorVO>> listProfit(Integer employeeInfoId) {
        return Response.success(orderService.listProfit(employeeInfoId));
    }

    /**
     * 品相利润监控订单批量处理
     */
    @Override
    public Response monitorOrderBatch(MonitorOrderBatchRequest batchRequest) {
        orderService.monitorOrderBatch(batchRequest);
        return Response.success();
    }


    @Override
    public Response<OrderBatchUploadVo> orderBatchGrantUpload(MultipartHttpServletRequest request) {
        log.info("批量导入品项利润Excel表：{}", request);

        String person = request.getParameter("person");
        if (StringUtils.isBlank(person)) {
            throw new ApplicationException("员工号person不能为空！");
        }

        if (!redisUtil.setLockIfAbsent(LOCK_HEAD_CHANGE_ORDER, "1", 5, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中~~");
        }

        try {

            ImportParams params = new ImportParams();
            Map<String, MultipartFile> fileMap = request.getFileMap();

            try {
                OrderBatchUploadVo orderGrantUploadVo = new OrderBatchUploadVo();
                List<OrderBatchExcelVo> list = null;

                for (String key : fileMap.keySet()) {
                    MultipartFile multipartFile = fileMap.get(key);
                    log.info("key:{},value:{}", key, multipartFile);

                    list = ExcelImportUtil.importExcel(multipartFile.getInputStream(), OrderBatchExcelVo.class, params);

                }

                List<OrderBatchExcelVo> listFilter = list.stream().filter(e -> StringUtils.isNotBlank(e.getOrderNumber())).collect(Collectors.toList());
                log.info("list:{},size:{}", listFilter, listFilter.size());
                if (listFilter != null && listFilter.size() > 0) {
                    orderGrantUploadVo = orderService.orderBatchGrantUpload(listFilter, person);
                }

                return Response.success(orderGrantUploadVo);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("导入失败！！！");
                return Response.error("导入失败！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.error("导入失败！");
        } finally {
            redisUtil.unLock(LOCK_HEAD_CHANGE_ORDER, "1");
        }
    }

    @Override
    public void orderBatchResultExport(List<OrderBatchUploadListVO> request) {
        log.info("start OrderController orderBatchResultExport:{}", request);


        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        Integer count = request.size();
        if (maxCount < count) {
            throw new ApplicationException("文件过大，请选择离线导出！");
        }

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), OrderBatchUploadListVO.class, request);

        try {
            String fileName = "订单列表";
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }


    }

    @Override
    public Response<List<OrderTypeVO>> queryOrderType() {
        List<OrderTypeVO> list = orderService.queryOrderType();
        return Response.success(list);
    }


}
