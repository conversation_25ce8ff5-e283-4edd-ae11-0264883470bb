package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.EmployeeChangeService;
import com.wantwant.sfa.backend.service.IEmployeeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description: //自动入离职
 * @history: //修改记录 修改人姓名 修改时间 版本号 描述 需求来源
 * @Time 2020-12-28 18:51:05
 */
@Configuration
@Slf4j
public class EmployeeChangeTask {

    @Autowired
    private IEmployeeService employeeService;
    @Autowired
    private EmployeeChangeService employeeChangeService;

    // @Scheduled(cron = "${task.employee.corn}")
    private void employeeChangeTaskExecute() {

        LocalDate today = LocalDate.now();
        log.info("execute employeeChangeTaskExecute today:{}", today);
        employeeService.employeeChangeExecute(today);

    }

    //    @Scheduled(cron = "${task.employee.autoChange.corn}")
    private void autoChangeTask() {
        LocalDateTime today = LocalDateTime.now();
        log.info("execute employeeChangeTaskExecute today:{}", today);
        employeeChangeService.changeEmployee();

    }

    @XxlJob("employeeChangeJobHandler")
    @Transactional
    public ReturnT<String> employeeChangeJobHandler(String param) {

        LocalDate today = LocalDate.now();
        log.info("execute employeeChangeJobHandler today:{}", today);
        employeeService.employeeChangeExecute(today);

        return ReturnT.SUCCESS;
    }

}
