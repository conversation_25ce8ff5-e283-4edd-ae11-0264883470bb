package com.wantwant.sfa.backend.Task;

import java.time.LocalDate;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import com.wantwant.sfa.backend.service.IDeliveryService;
import com.wantwant.sfa.backend.service.IMessageService;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class DeliveryTask {


    @Autowired
    private IMessageService messageService;
    @Autowired
    private IDeliveryService deliveryService;

    /**
     * 物流异常消息定时任务
     */
//    @Scheduled(cron = "${task.delivery.message}")
    private void deliveryAbnormalMessageTasks() {
        LocalDate date = LocalDate.now();
        log.info("execute deliveryAbnormalMessageTasks date:{}", date.toString());
        messageService.pushDeliveryAbnormalMessage(date);
    }
    
    /**
     * 物流异常统计定时任务
     */
//    @Scheduled(cron = "${task.delivery.abnormal}")
    private void deliveryAbnormalTasks() {
        LocalDate date = LocalDate.now();
        log.info("execute deliveryAbnormalTasks date:{}", date.toString());
        deliveryService.deliveryAbnormal(date);
    }


}
