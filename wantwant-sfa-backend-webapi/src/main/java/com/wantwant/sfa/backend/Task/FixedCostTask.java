package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.WarehouseService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;

@Configuration
@Slf4j
public class FixedCostTask {

    @Autowired
    private WarehouseService warehouseService;

    //@Scheduled(cron = "0 30 0 1 * ?")
    //每月1号的00:30:00执行（继承最后一次导入数据）
    //@XxlJob("inheritanceFixedCost")
    //@Scheduled(cron = "${task.inheritanceFixedCost.cron}") 2023-02-08 号每月1号继承数据取消掉
    public void inheritanceFixedCost() {
        LocalDate today = LocalDate.now();
        log.info("execute inheritanceFixedCost today:{}",today);
        warehouseService.inheritanceFixedCost();
    }


//    @Scheduled(cron = "${task.supplierManagementState.cron}")
    @XxlJob("supplierManagementState")
    public ReturnT<String> updateSupplierManagementState(String param){
        LocalDate today = LocalDate.now();
        log.info("execute updateSupplierManagementState today:{}",today);
        warehouseService.updateSupplierManagementState();
        return ReturnT.SUCCESS;
    }
}
