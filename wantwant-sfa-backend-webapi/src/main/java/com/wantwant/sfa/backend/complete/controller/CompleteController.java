package com.wantwant.sfa.backend.complete.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.complete.api.CompleteApi;
import com.wantwant.sfa.backend.complete.request.CompleteAuditRequest;
import com.wantwant.sfa.backend.complete.request.CompleteClockRequest;
import com.wantwant.sfa.backend.complete.request.CompleteDetailRequest;
import com.wantwant.sfa.backend.complete.request.CompleteListRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleCommitRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleListRequest;
import com.wantwant.sfa.backend.complete.request.CompleteRuleRevocationRequest;
import com.wantwant.sfa.backend.complete.request.CompleteTodayLatestRequest;
import com.wantwant.sfa.backend.complete.service.CompleteService;
import com.wantwant.sfa.backend.complete.vo.*;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.CompleteExcelEnum;
import com.wantwant.sfa.backend.mapper.SettingsMapper;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.architecture.SpringContextHelper;
import com.wantwant.sfa.common.architecture.controller.interceptor.ControllerHandlerInterceptor;
import com.wantwant.sfa.common.architecture.global.LocalizedText;
import com.wantwant.sfa.common.architecture.log.AccessLogContext;
import com.wantwant.sfa.common.base.CommonConstant;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Api(tags = "通关API")
@RestController
@Slf4j
public class CompleteController implements CompleteApi {

    private static final String COMPLETE_AUDIT_LOCK = "complete:audit:lock";

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CompleteService completeService;
    @Autowired
    private SettingsMapper settingsMapper;
    @Resource
    private RabbitMQSender rabbitMQSender;

    private String completeListName="sfa.backend.complete.list.export.name";

    @ApiOperation(value = "通关规则提交", notes = "通关规则提交")
    @Override
    public Response completeRuleCommit(CompleteRuleCommitRequest request) {
        log.info("completeRuleCommit request:{}", request);
        completeService.completeRuleCommit(request);
        return Response.success();
    }

    @ApiOperation(value = "通关规则撤回", notes = "通关规则撤回")
    @Override
    public Response completeRuleRevocation(CompleteRuleRevocationRequest request) {
        log.info("completeRuleRevocation request:{}", request);
        completeService.completeRuleRevocation(request);
        return Response.success();
    }

    @ApiOperation(value = "通关规则列表", notes = "通关规则列表")
    @Override
    public Response<IPage<CompleteRuleListVo>> completeRuleList(CompleteRuleListRequest request) {
        log.info("completeRuleList request:{}", request);
        return Response.success(completeService.completeRuleList(request));
    }

    @ApiOperation(value = "通关列表", notes = "通关列表")
    @Override
    public Response<IPage<CompleteListVo>> completeList(CompleteListRequest request) {
        log.info("completeList request:{}", request);
        return Response.success(completeService.completeList(request));
    }

    @ApiOperation(value = "通关列表", notes = "通关列表")
    @Override
    public Response<List<CompleteTodayLatestListVo>> completeList1(@RequestBody CompleteTodayLatestRequest request) {
        return Response.success(completeService.completeList1(request));
    }

    @ApiOperation(value = "通关列表导出", notes = "通关列表导出")
    @Override
    public void completeListExport(CompleteListRequest request) {
        log.info("completeListExport request:{}", request);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, BizExceptionLanguageEnum.COMMON_SYSTEM_ERROR_MESSAGE.getTextMsg());
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        // 根据语言选择模板类和转换数据
        AccessLogContext accessLogContext = ControllerHandlerInterceptor.accessLogContextThreadLocal.get();
        List<CompleteListVo> sourceData = completeService.completeList(request).getRecords();
        
        Workbook wb;
        if (!CommonConstant.LANGUAGE_ENGLISH.equals(accessLogContext.getLanguage())) {
            // 中文版本 - 转换为 CompleteListChVo
            List<CompleteListChVo> convertedData = sourceData.stream().map(this::convertToChVo).collect(Collectors.toList());
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CompleteListChVo.class, convertedData);
        } else {
            // 英文版本 - 转换为 CompleteListEnVo
            List<CompleteListEnVo> convertedData = sourceData.stream().map(this::convertToEnVo).collect(Collectors.toList());
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CompleteListEnVo.class, convertedData);
        }

        try {
            String fileName = null;
            try {
                fileName = SpringContextHelper.getBean(LocalizedText.class).getValue(completeListName,"通关列表");
            } catch (Exception e) {
                fileName="通关列表";
            }
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @ApiOperation(value = "通关列表导出全组", notes = "通关列表导出全组")
    @Override
    public void completeListExportAll(CompleteListRequest request) {
        log.info("completeListExportAll request:{}", request);
        if(CommonUtil.StringUtils.isBlank(request.getCompleteNum())){
            throw new ApplicationException(BizExceptionLanguageEnum.COMPLETE_LIST_COMPLETE_NUM_MUST_NOT_NULL.getTextMsg());
        }
        request.setRows(Optional.ofNullable(settingsMapper.getSfaSettingsByCode("export_max")).map(Integer::parseInt).orElse(1000));
        request.setIsAllBusinessGroup(true);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, BizExceptionLanguageEnum.COMMON_SYSTEM_ERROR_MESSAGE.getTextMsg());
        HttpServletResponse response = servletRequestAttributes.getResponse();
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        // 根据语言选择模板类和转换数据 - 修复硬编码问题
        AccessLogContext accessLogContext = ControllerHandlerInterceptor.accessLogContextThreadLocal.get();
        List<CompleteListVo> sourceData = completeService.completeList(request).getRecords();
        
        Workbook wb;
        if (!CommonConstant.LANGUAGE_ENGLISH.equals(accessLogContext.getLanguage())) {
            // 中文版本 - 转换为 CompleteListChVo
            List<CompleteListChVo> convertedData = sourceData.stream().map(this::convertToChVo).collect(Collectors.toList());
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CompleteListChVo.class, convertedData);
        } else {
            // 英文版本 - 转换为 CompleteListEnVo
            List<CompleteListEnVo> convertedData = sourceData.stream().map(this::convertToEnVo).collect(Collectors.toList());
            wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CompleteListEnVo.class, convertedData);
        }

        try {
            String fileName = null;
            try {
                fileName = SpringContextHelper.getBean(LocalizedText.class).getValue(completeListName,"通关列表");
            } catch (Exception e) {
                fileName="通关列表";
            }
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @ApiOperation(value = "通关今日最新一条", notes = "通关今日最新一条")
    @Override
    @GlobalTimezone
    public Response<CompleteTodayLatestVo> completeTodayLatest(CompleteTodayLatestRequest request) {
        log.info("completeTodayLatest request:{}", request);
        return Response.success(completeService.completeTodayLatest(request));
    }

    @ApiOperation(value = "通关详情", notes = "通关详情")
    @Override
    public Response<CompleteDetailVo> completeDetail(CompleteDetailRequest request) {
        log.info("completeDetail request:{}", request);
        return Response.success(completeService.completeDetail(request));
    }

    @ApiOperation(value = "通关记录", notes = "通关记录")
    @Override
    public Response<List<CompleteListVo>> completeRecordList(CompleteListRequest request) {
        log.info("completeRecordList request:{}", request);
        return Response.success(completeService.completeRecordList(request));
    }

    @ApiOperation(value = "通关打卡", notes = "通关打卡")
    @Override
    public Response completeClock(CompleteClockRequest request) {
        log.info("completeClock request:{}", request);

        completeService.completeClock(request);
        try {
            JSONObject obj = new JSONObject();
            obj.put("nodeCode", "PARTICIPATE_COMPLETE");
            obj.put("person", request.getPerson());
            obj.put("businessId", request.getId());
            rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
        } catch (Exception ex) {
            log.error("completeClock 发送MQ失败", ex);
        }
        return Response.success();
    }

    @ApiOperation(value = "通关稽核", notes = "通关稽核")
    @Override
    public Response completeAudit(CompleteAuditRequest request) {
        log.info("completeAudit request:{}", request);

        if (!redisUtil.setLockIfAbsent(COMPLETE_AUDIT_LOCK, String.valueOf(request.getId()), 3, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            completeService.completeAudit(request);
        } finally {
            redisUtil.unLock(COMPLETE_AUDIT_LOCK, String.valueOf(request.getId()));
        }
        return Response.success();
    }

    @Override
    public Response batchAudit(List<CompleteAuditRequest> list) {
        log.info("batchAudit request:{}", JSON.toJSONString(list));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)||list.get(0)==null){
            throw new ApplicationException("数据不能为空");
        }
        if (list.size()>100){
            throw new ApplicationException("数据不能大于100条");
        }
        String person=list.get(0).getPerson();
        if (!redisUtil.setLockIfAbsent(COMPLETE_AUDIT_LOCK, String.valueOf(person), 13, TimeUnit.SECONDS)) {
            return Response.error("请求正在处理中！");
        }
        try {
            completeService.batchAudit(list);
        } finally {
            redisUtil.unLock(COMPLETE_AUDIT_LOCK, String.valueOf(person));
        }
        return Response.success();
    }

    @Override
    public Response<List<String>> getCompleteNumList(CompleteListRequest request) {
        log.info("getCompleteNumList request:{}", request);
        return Response.success(completeService.getCompleteNumList(request));
    }

    private List<ExcelExportEntity> buildCompleteExcelEntityList() {
        List<ExcelExportEntity> entityList = new ArrayList<>();
        
        // 1. 通关日期
        ExcelExportEntity completeDate = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_EXPORT_DATE.getTextMsg(), "completeDate");
        completeDate.setOrderNum(1);
        completeDate.setFormat(LocalDateTimeUtils.yyyy_MM_dd);
        entityList.add(completeDate);
        
        // 2. 战区名称
        ExcelExportEntity areaName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_AREA_NAME.getTextMsg(), "areaName");
        areaName.setOrderNum(2);
        entityList.add(areaName);
        
        // 3. 大区名称
        ExcelExportEntity vareaName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_VAREA_NAME.getTextMsg(), "vareaName");
        vareaName.setOrderNum(3);
        entityList.add(vareaName);
        
        // 4. 省区名称
        ExcelExportEntity provinceName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_PROVINCE_NAME.getTextMsg(), "provinceName");
        provinceName.setOrderNum(4);
        entityList.add(provinceName);
        
        // 5. 分公司名称
        ExcelExportEntity companyName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_COMPANY_NAME.getTextMsg(), "companyName");
        companyName.setOrderNum(5);
        entityList.add(companyName);
        
        // 6. 营业所名称
        ExcelExportEntity departmentName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_DEPARTMENT_NAME.getTextMsg(), "departmentName");
        departmentName.setOrderNum(6);
        entityList.add(departmentName);
        
        // 7. 岗位名称
        ExcelExportEntity positionName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_POSITION_NAME.getTextMsg(), "positionName");
        positionName.setOrderNum(7);
        entityList.add(positionName);
        
        // 8. 姓名
        ExcelExportEntity employeeName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_EMPLOYEE_NAME.getTextMsg(), "employeeName");
        employeeName.setOrderNum(8);
        entityList.add(employeeName);
        
        // 9. 入职日期
        ExcelExportEntity onboardTime = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_ONBOARD_TIME.getTextMsg(), "onboardTime");
        onboardTime.setOrderNum(9);
        onboardTime.setFormat(LocalDateTimeUtils.yyyy_MM_dd);
        entityList.add(onboardTime);
        
        // 10. 通关编号
        ExcelExportEntity completeNum = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_COMPLETE_NUM.getTextMsg(), "completeNum");
        completeNum.setOrderNum(10);
        entityList.add(completeNum);
        
        // 11. 通关时间段
        ExcelExportEntity completeTimePeriod = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_COMPLETE_TIME_PERIOD.getTextMsg(), "completeTimePeriod");
        completeTimePeriod.setOrderNum(11);
        entityList.add(completeTimePeriod);
        
        // 12. 通关打卡时间
        ExcelExportEntity completeTime = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_COMPLETE_TIME.getTextMsg(), "completeTime");
        completeTime.setOrderNum(12);
        completeTime.setFormat(LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
        entityList.add(completeTime);
        
        // 13. 通关状态名称
        ExcelExportEntity completeStatusName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_COMPLETE_STATUS_NAME.getTextMsg(), "completeStatusName");
        completeStatusName.setOrderNum(13);
        entityList.add(completeStatusName);
        
        // 14. 当时人员状态名称
        ExcelExportEntity employeeStatusName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_EMPLOYEE_STATUS_NAME.getTextMsg(), "employeeStatusName");
        employeeStatusName.setOrderNum(14);
        entityList.add(employeeStatusName);
        
        // 15. 稽核状态名称
        ExcelExportEntity auditStatusName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_AUDIT_STATUS_NAME.getTextMsg(), "auditStatusName");
        auditStatusName.setOrderNum(15);
        entityList.add(auditStatusName);
        
        // 16. 稽核异常原因
        ExcelExportEntity reason = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_REASON.getTextMsg(), "reason");
        reason.setOrderNum(16);
        entityList.add(reason);
        
        // 17. 稽核人
        ExcelExportEntity auditEmployeeName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_AUDIT_EMPLOYEE_NAME.getTextMsg(), "auditEmployeeName");
        auditEmployeeName.setOrderNum(17);
        entityList.add(auditEmployeeName);
        
        // 18. 组别
        ExcelExportEntity businessGroupName = new ExcelExportEntity(CompleteExcelEnum.COMPLETE_BUSINESS_GROUP_NAME.getTextMsg(), "businessGroupName");
        businessGroupName.setOrderNum(18);
        entityList.add(businessGroupName);
        
        return entityList;
    }

    /**
     * 转换CompleteListVo为CompleteListChVo（中文版本）
     */
    private CompleteListChVo convertToChVo(CompleteListVo vo) {
        CompleteListChVo chVo = new CompleteListChVo();
        // 复制所有字段
        chVo.setId(vo.getId());
        chVo.setCompleteDate(vo.getCompleteDate());
        chVo.setBusinessGroup(vo.getBusinessGroup());
        chVo.setOrganizationId(vo.getOrganizationId());
        chVo.setOrganizationCode(vo.getOrganizationCode());
        chVo.setOrganizationName(vo.getOrganizationName());
        chVo.setAreaName(vo.getAreaName());
        chVo.setVareaName(vo.getVareaName());
        chVo.setProvinceName(vo.getProvinceName());
        chVo.setCompanyName(vo.getCompanyName());
        chVo.setDepartmentName(vo.getDepartmentName());
        chVo.setPositionTypeId(vo.getPositionTypeId());
        chVo.setPositionName(vo.getPositionName());
        chVo.setAvatar(vo.getAvatar());
        chVo.setEmployeeName(vo.getEmployeeName());
        chVo.setEmployeeId(vo.getEmployeeId());
        chVo.setEmployeeInfoId(vo.getEmployeeInfoId());
        chVo.setOnboardTime(vo.getOnboardTime());
        chVo.setRuleId(vo.getRuleId());
        chVo.setCompleteNum(vo.getCompleteNum());
        chVo.setCompleteStartTime(vo.getCompleteStartTime());
        chVo.setCompleteEndTime(vo.getCompleteEndTime());
        chVo.setIntervalHours(vo.getIntervalHours());
        chVo.setIntervalMinutes(vo.getIntervalMinutes());
        chVo.setCompleteTimePeriod(vo.getCompleteTimePeriod());
        chVo.setCompleteTime(vo.getCompleteTime());
        chVo.setLongitude(vo.getLongitude());
        chVo.setLatitude(vo.getLatitude());
        chVo.setAddress(vo.getAddress());
        chVo.setCompleteStatus(vo.getCompleteStatus());
        chVo.setCompleteStatusName(vo.getCompleteStatusName());
        chVo.setEmployeeStatus(vo.getEmployeeStatus());
        chVo.setEmployeeStatusName(vo.getEmployeeStatusName());
        chVo.setImage(vo.getImage());
        chVo.setImageName(vo.getImageName());
        chVo.setAuditStatus(vo.getAuditStatus());
        chVo.setAuditStatusName(vo.getAuditStatusName());
        chVo.setReason(vo.getReason());
        chVo.setAuditEmployeeId(vo.getAuditEmployeeId());
        chVo.setAuditEmployeeName(vo.getAuditEmployeeName());
        chVo.setBusinessGroupName(vo.getBusinessGroupName());
        chVo.setBShowBtn1(vo.isBShowBtn1());
        chVo.setIsTrip(vo.getIsTrip());
        chVo.setSignUpPicUrl(vo.getSignUpPicUrl());
        chVo.setFaceSimilarScore(vo.getFaceSimilarScore());
        chVo.setPartTime(vo.getPartTime());
        return chVo;
    }

    /**
     * 转换CompleteListVo为CompleteListEnVo（英文版本）
     */
    private CompleteListEnVo convertToEnVo(CompleteListVo vo) {
        CompleteListEnVo enVo = new CompleteListEnVo();
        // 复制所有字段
        enVo.setId(vo.getId());
        enVo.setCompleteDate(vo.getCompleteDate());
        enVo.setBusinessGroup(vo.getBusinessGroup());
        enVo.setOrganizationId(vo.getOrganizationId());
        enVo.setOrganizationCode(vo.getOrganizationCode());
        enVo.setOrganizationName(vo.getOrganizationName());
        enVo.setAreaName(vo.getAreaName());
        enVo.setVareaName(vo.getVareaName());
        enVo.setProvinceName(vo.getProvinceName());
        enVo.setCompanyName(vo.getCompanyName());
        enVo.setDepartmentName(vo.getDepartmentName());
        enVo.setPositionTypeId(vo.getPositionTypeId());
        enVo.setPositionName(vo.getPositionName());
        enVo.setAvatar(vo.getAvatar());
        enVo.setEmployeeName(vo.getEmployeeName());
        enVo.setEmployeeId(vo.getEmployeeId());
        enVo.setEmployeeInfoId(vo.getEmployeeInfoId());
        enVo.setOnboardTime(vo.getOnboardTime());
        enVo.setRuleId(vo.getRuleId());
        enVo.setCompleteNum(vo.getCompleteNum());
        enVo.setCompleteStartTime(vo.getCompleteStartTime());
        enVo.setCompleteEndTime(vo.getCompleteEndTime());
        enVo.setIntervalHours(vo.getIntervalHours());
        enVo.setIntervalMinutes(vo.getIntervalMinutes());
        enVo.setCompleteTimePeriod(vo.getCompleteTimePeriod());
        enVo.setCompleteTime(vo.getCompleteTime());
        enVo.setLongitude(vo.getLongitude());
        enVo.setLatitude(vo.getLatitude());
        enVo.setAddress(vo.getAddress());
        enVo.setCompleteStatus(vo.getCompleteStatus());
        enVo.setCompleteStatusName(vo.getCompleteStatusName());
        enVo.setEmployeeStatus(vo.getEmployeeStatus());
        enVo.setEmployeeStatusName(vo.getEmployeeStatusName());
        enVo.setImage(vo.getImage());
        enVo.setImageName(vo.getImageName());
        enVo.setAuditStatus(vo.getAuditStatus());
        enVo.setAuditStatusName(vo.getAuditStatusName());
        enVo.setReason(vo.getReason());
        enVo.setAuditEmployeeId(vo.getAuditEmployeeId());
        enVo.setAuditEmployeeName(vo.getAuditEmployeeName());
        enVo.setBusinessGroupName(vo.getBusinessGroupName());
        enVo.setBShowBtn1(vo.isBShowBtn1());
        enVo.setIsTrip(vo.getIsTrip());
        enVo.setSignUpPicUrl(vo.getSignUpPicUrl());
        enVo.setFaceSimilarScore(vo.getFaceSimilarScore());
        enVo.setPartTime(vo.getPartTime());
        return enVo;
    }
}
