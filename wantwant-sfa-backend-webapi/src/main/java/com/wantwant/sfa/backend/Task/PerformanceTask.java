package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.PerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;

@Configuration
@Slf4j
public class PerformanceTask {

	@Autowired
	PerformanceService performanceService;

    //    @Scheduled(cron = "${task.performance.corn}")
    @Deprecated
    private void performanceTask() {
        LocalDate date = LocalDate.now();
        log.info("execute performanceTask date:{}", date.toString());
        performanceService.computePerformanceTask(date);
        log.info("end performanceTask date:{}", date.toString());
    }
}
