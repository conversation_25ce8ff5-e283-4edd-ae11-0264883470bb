package com.wantwant.sfa.backend.Task;


import com.wantwant.sfa.backend.service.impl.MessageServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDateTime;


/**
 * 需求:
 * 1、查询时间段内为完成且为发放消息的订单
 * 2、插入消息数据
 * 3、推送消息
 */


@Configuration
@Slf4j
public class OrderCompleteMessageTask {

    @Autowired
    private MessageServiceImpl messageService;
//    @Scheduled(cron = "0 23 2 * * ? ")
//    @Scheduled(fixedRate = 1*60*1000)
@Deprecated
    private void OrderCompleteMessageTaskexecute() {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.plusMinutes(-2);
        log.info("execute OrderCompleteMessageTaskexecute startDate:{},endDate:{}",startDate,endDate);
        messageService.pushOrderCompleteMessage(startDate, endDate);

    }
}
