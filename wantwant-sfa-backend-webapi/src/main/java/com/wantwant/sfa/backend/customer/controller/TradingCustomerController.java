package com.wantwant.sfa.backend.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.customer.request.TradingCustomerListRequest;
import com.wantwant.sfa.backend.customer.request.TradingOnboardCustomerRequest;
import com.wantwant.sfa.backend.customer.service.TradingCustomerService;
import com.wantwant.sfa.backend.customer.vo.TradingCustomerEnumsVO;
import com.wantwant.sfa.backend.customer.vo.TradingCustomerListVO;
import com.wantwant.sfa.backend.customer.vo.TradingOnboardCustomerListVO;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.customer.controller
 * @Description: 合伙人列表(交易客户)
 * @Date: 2024/6/19 13:24
 */
@Slf4j
@RestController
@Api(tags = "客户管理API-交易客户")
@RequestMapping("/tradingCustomer")
public class TradingCustomerController {
    @Resource
    private TradingCustomerService tradingCustomerService;

    @PostMapping("/queryPage")
    @ApiOperation(value = "交易客户列表查询",notes = "交易客户列表查询")
    public Response<IPage<TradingCustomerListVO>> queryList(@Validated @RequestBody TradingCustomerListRequest request){
        return Response.success(tradingCustomerService.queryList(request));
    }

    @PostMapping("/download")
    @ApiOperation(value = "交易客户列表查询下载",notes = "交易客户列表下载")
    public void download(@RequestBody @Validated TradingCustomerListRequest req, HttpServletRequest request, HttpServletResponse response){
        tradingCustomerService.download(req,request,response);
    }

    @PostMapping("/getTradingCustomerEnums")
    @ApiOperation(value = "交易客户枚举信息查询",notes = "交易客户枚举信息查询")
    public Response<TradingCustomerEnumsVO> getTradingCustomerEnums(){
        return Response.success(tradingCustomerService.getTradingCustomerEnums());
    }

    @PostMapping("/queryOnboardPage")
    @ApiOperation(value = "本组客户列表查询", notes = "本组客户列表查询")
    @GlobalTimezone
    public Response<IPage<TradingOnboardCustomerListVO>> queryOnboardList(@Validated @RequestBody TradingOnboardCustomerRequest request){
        return Response.success(tradingCustomerService.queryOnboardList(request));
    }
    @PostMapping("/downloadOnboard")
    @ApiOperation(value = "本组客户列表导出", notes = "本组客户列表导出")
    public void downloadOnboardList(@Validated @RequestBody TradingOnboardCustomerRequest req,HttpServletRequest request, HttpServletResponse response){
        tradingCustomerService.downloadOnboardList(req,request,response);
    }
    @PostMapping("/downloadCallBack")
    @ApiOperation(value = "回访导出",notes = "回访导出")
    public void downloadCallBack(@Validated @RequestBody TradingOnboardCustomerRequest req,HttpServletRequest request, HttpServletResponse response){
        tradingCustomerService.downloadCallBack(req,request,response);
    }



}
