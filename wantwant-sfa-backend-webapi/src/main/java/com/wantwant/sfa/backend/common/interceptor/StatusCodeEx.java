package com.wantwant.sfa.backend.common.interceptor;

import com.wantwant.commons.cons.IStatusCode;

public enum StatusCodeEx implements IStatusCode {
    SUCCESS(0, "cc_operate_success"),
    UNAUTHORIZED(401, "cc_unauthorized");

    private int code;
    private String desc;
    private String system;

    private StatusCodeEx(int code, String description) {
        this.code = code;
        this.desc = description;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
