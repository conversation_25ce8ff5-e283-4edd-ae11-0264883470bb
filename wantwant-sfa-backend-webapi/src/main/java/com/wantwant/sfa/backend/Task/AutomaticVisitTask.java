package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.service.AutomaticVisitTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2020/9/8 10:03
 * 智能推荐拜访定时任务
 */
@Configuration
@Slf4j
public class AutomaticVisitTask {

    @Autowired
    private AutomaticVisitTaskService automaticVisitTaskService;

//    @Scheduled(cron = "0 0 0 */1 * ?")
//    @Scheduled(cron = "${task.visit.corn}")
//    @Scheduled(cron = "0 56 13 * * ?")
    private void visitTask(){

        LocalDateTime date = LocalDateTime.now();
        log.info("execute AutomaticVisitTask date:{}", date.toString());
        automaticVisitTaskService.automaticVisit();
    }
}
