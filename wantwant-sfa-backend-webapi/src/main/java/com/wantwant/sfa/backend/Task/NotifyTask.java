package com.wantwant.sfa.backend.Task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.wantwant.sfa.backend.afterSales.vo.AfterSalesRejectVO;
import com.wantwant.sfa.backend.employeeInfo.service.IEmployeeInfoService;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.afterSales.AfterSalesInfoMapper;
import com.wantwant.sfa.backend.mapper.arch.DeptMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportMapper;
import com.wantwant.sfa.backend.meeting.vo.MeetingExportVO;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.NotifyPO;
import com.wantwant.sfa.backend.model.meeting.MeetingRecordPO;
import com.wantwant.sfa.backend.notify.dto.CompanyManagerDTO;
import com.wantwant.sfa.backend.notify.enums.NotifyTemplateTypeEnum;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.service.IRealtimeDataService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.service.PenaltyDeductionService;
import com.wantwant.sfa.backend.service.meeting.MeetingInfoService;
import com.wantwant.sfa.backend.service.meeting.MeetingRecordService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.DateWeekUtil;
import com.wantwant.sfa.backend.util.GeTuiUtil;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportEntity;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知定时任务
 *
 * @date 3/8/22 10:52 AM
 * @version 1.0
 */
@Configuration
@Slf4j
public class NotifyTask{

    @Autowired
    private IRealtimeDataService realtimeDataService;

    @Autowired
    private NotifyService notifyService;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper positionRelationMapper;

    @Autowired
    private IEmployeeInfoService employeeInfoService;

    @Autowired
    private GeTuiUtil geTuiUtil;

    @Autowired
    private WorkReportMapper workReportMapper;

    @Autowired
    private AfterSalesInfoMapper afterSalesInfoMapper;

    @Autowired
    private DeptMapper deptMapper;

    @Autowired
    private PenaltyDeductionService penaltyDeductionService;

    @Autowired
    private MeetingInfoService meetingInfoService;

    @Autowired
    private MeetingRecordService recordService;

    @Resource
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;

    private static final String MSG = "销售周报【{0}月第{1}周】数据推送";

    /**
     * 合伙人目标设置每月26号通知
     * 0 10 0 26 * ?
     *
     * @param
     * @return: void
     * @author: zhouxiaowen
     * @date: 3/8/22 10:52 AM
     */
//    @Scheduled(cron = "${task.notify.employeeGoal.cron}")
    @XxlJob("notifyTaskConfigureTasks")
    @Transactional
    public ReturnT<String> configureTasks(String param) {
        log.info("合伙人目标设置通知开始");
        int monthValue = LocalDate.now().getMonthValue()+1;
        long start = System.currentTimeMillis();
        //查询造旺所有分公司负责人(分公司没有取大区)
        List<CompanyManagerDTO> company=realtimeDataService.getAllCompany();
        //查询下月已设置合伙人目标的分公司
        List<String> completedCompany = realtimeDataService.getCompletedCompany();
        List<CompanyManagerDTO> filter = company.stream().filter(f -> !completedCompany.contains(f.getOrganizationId())).collect(Collectors.toList());
        List<NotifyPO> notifyPOS = new ArrayList<>();
        filter.forEach(f ->{
            NotifyPO po = new NotifyPO();
            po.setTitle("合伙人目标设置"+monthValue+"月");
            po.setType(1);
            po.setContent("合伙人目标设置"+monthValue+"月");
            po.setCode("/partnerGoal");
            po.setEmployeeId(f.getEmployeeId());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });
        notifyService.saveBatch(notifyPOS);
        long end = System.currentTimeMillis();
        log.info("合伙人目标设置通知结束，耗时{}",end-start);
        return ReturnT.SUCCESS;
    }

    /**
     * 核心指标预警周报
     * 时间:每周一09:00推送上周
     * 对象:总部、大区督导、区域总监、区域经理
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 4/20/23 2:14 PM
     */
    @XxlJob("notifySendWeekly")
    @Transactional
    public ReturnT<String> sendWeekly(String param){
        employeeInfoService.doWeeklySnapshot();
        LocalDate startDate = LocalDate.now().with(WeekFields.ISO.dayOfWeek(), 1L).minusWeeks(1);
        LocalDate endDate = LocalDate.now().with(WeekFields.ISO.dayOfWeek(), 7L).minusWeeks(1);
        Integer num = notifyService.getWeeklyNum();
        Integer num1 = 0;
        int monthValue = startDate.getMonthValue();
        Map<String, Integer> weekNoOfMonth = DateWeekUtil.getWeekNoOfMonth(startDate);
        if (null != weekNoOfMonth){
            num1 = weekNoOfMonth.get("week");
            monthValue = weekNoOfMonth.get("month");
        }
        log.info("核心指标预警周报开始推送:{}~{}~{}",num,startDate,endDate);
        List<CeoBusinessOrganizationPositionRelation> positionRelations = positionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .in("position_type_id", Arrays.asList(1, 2, 7, 10,11,12))
                .eq("channel", 3).isNotNull("employee_id"));
        List<NotifyPO> notifyPOS = new ArrayList<>();
        //总部的推送 只推00272473、00441211、00443203、00462947、00478768、00440515
        List<String> zbList = Arrays.asList("00272473", "00441211", "00443203", "00462947", "00478768", "00440515");
        for (CeoBusinessOrganizationPositionRelation p : positionRelations) {
            if ((p.getPositionTypeId() == 7 && zbList.contains(p.getEmployeeId())) || p.getPositionTypeId() != 7) {
                NotifyPO po = new NotifyPO();
                po.setTitle(MessageFormat.format(MSG, monthValue, num1));
                po.setType(3);
                po.setContent("核心指标推送");
                po.setEmployeeId(p.getEmployeeId());
                po.setOrganizationId(p.getOrganizationId());
                po.setStartDate(startDate);
                po.setEndDate(endDate);
                po.setCycle(num);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
                String payload = "{\"title\":\"系统推送\"}";
                geTuiUtil.AppPushToSingleSync(p.getEmployeeId(), "系统推送", "快来查看【核心指标预警】情况！", payload, 1);
            }
        }
        notifyService.saveBatch(notifyPOS);
        return ReturnT.SUCCESS;
    }


    /**
     * 周报填写及阅读进度
     * 周日早上（9:00）推送
     *
     * @param param
     * @return: com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * @date: 8/8/23 4:52 PM
     */
    @XxlJob("weeklyReportCompletion")
    @Transactional
    public ReturnT<String> weeklyReportCompletion(String param){
        log.info("周报填写及阅读进度");
        LocalDate now = LocalDate.now().plusDays(-7);
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String dateStr = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(now);
        //查询当前周报
        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new QueryWrapper<SfaWorkReportEntity>()
                .le("start_date",dateStr).ge("end_date",dateStr)
                .eq("report_type",1).eq("delete_flag",0)
                .orderByDesc("work_id")
                .last("limit 1"));
        log.info("周报信息:{}",sfaWorkReportEntity);
        if (Objects.nonNull(sfaWorkReportEntity)){
            List<CeoBusinessOrganizationPositionRelation> positionRelations = positionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("position_type_id",1)
                    .eq("channel", 3).isNotNull("employee_id"));
            //总部的推送 只推00272473、00441211、00443203、00462947、00462947
            List<String> zbList = Arrays.asList("00272473", "00441211", "00443203", "00462947");
            List<NotifyPO> notifyPOS = new ArrayList<>();
            Integer weeks = sfaWorkReportEntity.getWeeks();
            Integer month = sfaWorkReportEntity.getMonth();
            positionRelations.forEach(p -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("工作周报【"+month+"月第"+weeks+"周】阅读进度");
                po.setType(3);
                po.setContent("您好，系统提醒您"+sfaWorkReportEntity.getStartDate().toString()+"~"+sfaWorkReportEntity.getEndDate().toString()+"工作周报【第"+weeks+"周】团队人员对于下属的周报填写跟阅读进度情况如下，烦请进行查看。");
                po.setEmployeeId(p.getEmployeeId());
                po.setOrganizationId(p.getOrganizationId());
                po.setStartDate(sfaWorkReportEntity.getStartDate());
                po.setEndDate(sfaWorkReportEntity.getEndDate());
                po.setWorkId(sfaWorkReportEntity.getWorkId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            zbList.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("工作周报【"+month+"月第"+weeks+"周】阅读进度");
                po.setType(3);
                po.setContent("您好，系统提醒您"+sfaWorkReportEntity.getStartDate().toString()+"~"+sfaWorkReportEntity.getEndDate().toString()+"工作周报【第"+sfaWorkReportEntity.getWeeks()+"周】团队人员对于下属的周报填写跟阅读进度情况如下，烦请进行查看。");
                po.setEmployeeId(e);
                po.setOrganizationId("ZB_Z");
                po.setStartDate(sfaWorkReportEntity.getStartDate());
                po.setEndDate(sfaWorkReportEntity.getEndDate());
                po.setWorkId(sfaWorkReportEntity.getWorkId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            notifyService.saveBatch(notifyPOS);
        }
        return ReturnT.SUCCESS;
    }
    
    /** 
     * 周报填写情况,只推送直接下属进度
     * 每周二19:59，20:00进行推送
     *
     */
    @XxlJob("weeklyReportInfo")
    @Transactional
    public ReturnT<String> weeklyReportInfo(String param){
        log.info("周报填写情况");
        LocalDate now = LocalDate.now().plusDays(-7);;
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String dateStr = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(now);
        //查询当前周报
        SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new QueryWrapper<SfaWorkReportEntity>()
                .le("start_date",dateStr).ge("end_date",dateStr)
                .eq("report_type",1).eq("delete_flag",0)
                .orderByDesc("work_id")
                .last("limit 1"));
        log.info("周报信息:{}",sfaWorkReportEntity);
        if (Objects.nonNull(sfaWorkReportEntity)){
            //总部的推送 只推00272473、00441211、00443203、00462947、00462947
            List<String> zbList = Arrays.asList("00272473", "00441211", "00443203", "00462947");
            List<CeoBusinessOrganizationPositionRelation> positionRelations = positionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .in("position_type_id", Arrays.asList(1, 2,11,12))
                    .eq("channel", 3).isNotNull("employee_id"));
            List<NotifyPO> notifyPOS = new ArrayList<>();
            Integer month = sfaWorkReportEntity.getMonth();
            Integer weeks = sfaWorkReportEntity.getWeeks();
            positionRelations.forEach(p -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("工作周报【"+month+"月第"+weeks+"周】填写情况");
                po.setType(3);
                po.setContent("您好，系统提醒您"+sfaWorkReportEntity.getStartDate().toString()+"~"+sfaWorkReportEntity.getEndDate().toString()+"工作周报【第"+weeks+"周】下属周报填写情况如下，烦请进行查看。");
                po.setEmployeeId(p.getEmployeeId());
                po.setOrganizationId(p.getOrganizationId());
                po.setStartDate(sfaWorkReportEntity.getStartDate());
                po.setEndDate(sfaWorkReportEntity.getEndDate());
                po.setWorkId(sfaWorkReportEntity.getWorkId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });
            zbList.forEach(e -> {
                NotifyPO po = new NotifyPO();
                po.setTitle("工作周报【"+month+"月第"+weeks+"周】填写情况");
                po.setType(3);
                po.setContent("您好，系统提醒您"+sfaWorkReportEntity.getStartDate().toString()+"~"+sfaWorkReportEntity.getEndDate().toString()+"工作周报【第"+sfaWorkReportEntity.getWeeks()+"周】下属周报填写情况如下，烦请进行查看。");
                po.setEmployeeId(e);
                po.setOrganizationId("ZB_Z");
                po.setStartDate(sfaWorkReportEntity.getStartDate());
                po.setEndDate(sfaWorkReportEntity.getEndDate());
                po.setWorkId(sfaWorkReportEntity.getWorkId());
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            });

            notifyService.saveBatch(notifyPOS);
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 月报填写情况,只推送直接下属进度
     * 每月10号上午9:00推送
     *
     */
    @XxlJob("reviewReportInfo")
    @Transactional
    public ReturnT<String> reviewReportInfo(String param){
        log.info("月报填写情况");
        LocalDate now = LocalDate.now();
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        int monthValue = now.getMonthValue();
        String dateStr = DateTimeFormatter.ofPattern("yyyy年MM月dd日").format(now);
        //总部的推送 只推00272473、00441211、00443203、00462947、00462947
        List<String> zbList = Arrays.asList("00272473", "00441211", "00443203", "00462947");
        List<CeoBusinessOrganizationPositionRelation> positionRelations = positionRelationMapper.selectList(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .in("position_type_id", Arrays.asList(1, 2))
                .eq("channel", 3).isNotNull("employee_id"));
        List<NotifyPO> notifyPOS = new ArrayList<>();
        positionRelations.forEach(p -> {
            NotifyPO po = new NotifyPO();
            po.setTitle("工作月报【"+ monthValue +"月】填写情况");
            po.setType(3);
            po.setContent("您好，"+dateStr+"系统提醒您下属月报填写情况如下，烦请进行查看。");
            po.setEmployeeId(p.getEmployeeId());
            po.setOrganizationId(p.getOrganizationId());
            po.setStartDate(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()));
            po.setEndDate(LocalDate.now());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });
        zbList.forEach(e -> {
            NotifyPO po = new NotifyPO();
            po.setTitle("工作月报【"+ monthValue +"月】填写情况");
            po.setType(3);
            po.setContent("您好，"+dateStr+"系统提醒您下属月报填写情况如下，烦请进行查看。");
            po.setEmployeeId(e);
            po.setOrganizationId("ZB_Z");
            po.setStartDate(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()));
            po.setEndDate(LocalDate.now());
            po.setCreateBy("-1");
            po.setUpdateBy("-1");
            notifyPOS.add(po);
        });
        notifyService.saveBatch(notifyPOS);
        return ReturnT.SUCCESS;
    }


    /**
     * 今日售后驳回清单
     */
    @XxlJob("todayAfterSaleRejected")
    @Transactional
    public ReturnT<String> todayAfterSaleRejected(String param){
        log.info("今日售后驳回清单");
        LocalDate now = LocalDate.now();
        String date = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            date = param;
        }
        List<AfterSalesRejectVO> list = afterSalesInfoMapper.selectTodayRejected(date,null);
        String title = "临期售后驳回通知 " + now.getYear() + "年-" + now.getMonthValue() + "月-" + now.getDayOfMonth() + "日";
        String message = "您好，" + now.getYear() + "年-" + now.getMonthValue() + "月-" + now.getDayOfMonth() + "日，系统提醒您今日售后驳回清单如下，烦请进行查看。";
        if (CommonUtil.ListUtils.isNotEmpty(list)){
            Set<String> set = deptMapper.selectEmpByDept("D00000090");
            set.addAll(deptMapper.selectEmpByDept("D00000034"));
            for (String s : set) {
                NotifyPO notifyPO = notifyService.saveNotify(2, NotifyTemplateTypeEnum.AFTER_SALE_REJECTED.getType(), title, s, message);
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 每月扣罚
     */
    @XxlJob("penaltyDeduction")
    public ReturnT<String> doPenaltyDeduction(String param){
        log.info("每月扣罚");
        LocalDate now = LocalDate.now();
        if (CommonUtil.StringUtils.isNotBlank(param)) {
            now = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        penaltyDeductionService.doPenaltyDeduction(now);
        return ReturnT.SUCCESS;
    }

    /**
     * 会议个推15分钟执行一次
     */
    @XxlJob("meetingGeTui")
    @Transactional
    public ReturnT<String> meetingGeTui(String param){
        List<MeetingExportVO> geTuiList = meetingInfoService.getGeTuiList();
        if (CommonUtil.ListUtils.isNotEmpty(geTuiList)) {
            List<NotifyPO> notifyPOS = new ArrayList<>();

            List<String> empIds = geTuiList.stream().map(MeetingExportVO::getEmployeeId).distinct().collect(Collectors.toList());

            empIds.forEach(e -> {
                Optional<MeetingExportVO> first = geTuiList.stream().filter(f -> f.getEmployeeId().equals(e)).findFirst();
                if(first.isPresent()){
                    MeetingExportVO g = first.get();
                    String payload = "{\"title\":\"系统推送\",\"businessGroup\":\""+g.getBusinessGroup()+"\",\"title\":\""
                            +g.getSubclass()+"\",\"createUserName\":\""+g.getCreateName()+"\",\"infoId\":"+g.getInfoId()+",\"type\":602}";
                    geTuiUtil.AppPushToSingleSync(g.getEmployeeId(), "系统推送", g.getCreateName()+"邀请的会议即将开始，请进入！", payload, 1);



                    NotifyPO po = new NotifyPO();
                    po.setTitle(g.getCreateName()+"邀请的会议即将开始，请进入！");
                    po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
                    po.setContent(g.getCreateName()+"邀请的会议即将开始，请进入！");
                    String category = g.getCategory();
                    if(StringUtils.isNotBlank(category)){
                      po.setCode("/meetingsDetail?infoId="+g.getInfoId()+"&mode=detail&category="+ g.getCategory());
                    }
                    po.setEmployeeId(g.getEmployeeId());
                    po.setCreateBy("-1");
                    po.setUpdateBy("-1");
                    notifyPOS.add(po);
                }
            });

            List<Integer> ids = geTuiList.stream().map(MeetingExportVO::getRecordId).collect(Collectors.toList());
            MeetingRecordPO po = new MeetingRecordPO();
            po.setGetuiStatus(1);
            recordService.update(po, new UpdateWrapper<MeetingRecordPO>().in("record_id", ids));

            notifyService.saveBatch(notifyPOS);
        }


        return ReturnT.SUCCESS;
    }
    /**
     * 每月提醒人事查看汰换首页情况
     *
     */
    @XxlJob("remindReplaceEveryMonthRead")
    public ReturnT<String> remindReplaceEveryMonth(String param){
        log.info("remindReplaceEveryMonthRead:每月提醒人事查看汰换首页情况");
        if(StringUtils.isEmpty(param) || !param.matches("\\d+") ){
            ReturnT<String> fail = ReturnT.FAIL;
            fail.setMsg("请正确输入需提醒的人员角色");
            return ReturnT.FAIL;
        }
//        List<String> empIds = deptMapper.queryEmpIdBydDeptCode("D00000042");
        List<String> empIds = roleEmployeeRelationMapper.queryEmpIdsByRoleId(Integer.valueOf(param));
        log.info("remindReplaceEveryMonthRead:通知员工=[{}]查询汰换情况",empIds);
        if(!CollectionUtils.isEmpty(empIds)){
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH,-1);
            List<NotifyPO> notifyPOS = new ArrayList<>(empIds.size());
            String title = String.format("%d年%d月汰换预警人员情况，请查看！",calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH)+1);
            for(String employeeId:empIds){
                // 发送消息
                NotifyPO po = new NotifyPO();
                po.setTitle(title);
                po.setType(3);
                po.setCode("/zwwarn");
                po.setEmployeeId(employeeId);
                po.setCreateBy("-1");
                po.setUpdateBy("-1");
                notifyPOS.add(po);
            }
            notifyService.saveBatch(notifyPOS);
        }

        return ReturnT.SUCCESS;
    }

}
