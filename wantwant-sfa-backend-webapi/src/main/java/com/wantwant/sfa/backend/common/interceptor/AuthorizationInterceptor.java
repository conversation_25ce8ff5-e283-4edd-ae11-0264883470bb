package com.wantwant.sfa.backend.common.interceptor;

import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.loginUser.request.LoginToken;
import com.wantwant.sfa.backend.loginUser.vo.LoginUserVO;
import com.wantwant.sfa.backend.service.impl.LoginUserServiceImpl;
import com.wantwant.sfa.backend.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 权限（token）验证
 *
 * @version 1.0
 * @date 11/9/22 1:33 PM
 */
@Slf4j
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {

    @Autowired
    private LoginUserServiceImpl loginUserService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");
        log.info("拦截:{},token:{}", request.getRequestURI(), token);
        if (CommonUtil.StringUtils.isNotBlank(token) && token.startsWith("Bearer department")) {
            log.info("区域经理认证:{}", token);
            LoginToken loginToken = new LoginToken();
            loginToken.setToken(token);
            LoginUserVO info = loginUserService.getInfo(loginToken);
            if (Objects.isNull(info))
                throw new ApplicationException(StatusCodeEx.UNAUTHORIZED, "认证过期，请重新登录！");
        }
        return true;
    }

}
