package com.wantwant.sfa.backend.Task;

import com.wantwant.sfa.backend.mapper.BuyTerminalIpMapper;
import com.wantwant.sfa.backend.mapper.OrderExceptionMapper;
import com.wantwant.sfa.backend.mapper.OrderMapper;
import com.wantwant.sfa.backend.model.BuyTerminalIpModel;
import com.wantwant.sfa.backend.model.CustomerOrganizationModel;
import com.wantwant.sfa.backend.model.CustomerStoreModel;
import com.wantwant.sfa.backend.model.OrderExceptionModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;


/**
 * @Description: 同步异常订单Task。
 * @Auther: zhengxu
 * @Date: 2021/08/16/上午9:17
 */

@Slf4j
@Configuration
public class SynExceptionOrderTask {
    private static final String NOT_REPEAT = "未重复";
    private static final String SPLIT_COMMA = "、";
    private static final String SYS = "系统";
    @Autowired
    private BuyTerminalIpMapper buyTerminalIpMapper;

    @Autowired
    private OrderExceptionMapper orderExceptionMapper;

    @Autowired
    private OrderMapper orderMapper;
    /**
     * 同步大数据异常订单数据
     */
//    @Scheduled(cron = "${task.syn.exceptionOrder}")
    @Deprecated
    public void syn() {
        log.info("同步大数据异常订单start");
        LocalDate now = LocalDate.now();
        List<BuyTerminalIpModel> buyTerminalIpModels = buyTerminalIpMapper.selectBuyTerminalIps(now.toString());
        if(CollectionUtils.isEmpty(buyTerminalIpModels)){
            return;
        }

        // 存储memberkey对应员工号
        HashMap<Long,CustomerStoreModel> employeeIdMaps = new HashMap<>();

        HashMap<String,CustomerOrganizationModel> organizationModelMap = new HashMap<>();
        log.info("同步大数据异常订单获取数量:" + buyTerminalIpModels.size());
        // 冗余字段数据处理
        buyTerminalIpModels.forEach(e -> {
            int isExists = orderExceptionMapper.checkIsExists(e.getOrderNo());
            if(isExists > 0) {
                return;
            }

            Long memberKey = orderMapper.getMemberKeyWithOrderNo(e.getOrderNo());
            if(Objects.isNull(memberKey) ){
                log.warn("同步大数据异常订单，无法通过订单号获取memerKey。订单号:"+e.getOrderNo());
                return;
            }

            if(Objects.isNull(e.getMemberKey()) || !e.getMemberKey().equals(memberKey)){
                log.warn("同步大数据异常订单，memberKey不匹配。订单号:"+e.getOrderNo());
                return;
            }
            // 根据memberKey获取员工号
            CustomerStoreModel customerModel = getEmployeeId(employeeIdMaps, e, memberKey);
            if(Objects.isNull(customerModel)){
                log.warn("同步大数据异常订单，无法通过订单号获取客户信息。订单号:"+e.getOrderNo());
                return;
            }

            // 根据员工号获取组织信息
            CustomerOrganizationModel organizationInfo = getOrganizationInfo(organizationModelMap, customerModel.getMemberId());
            if(Objects.isNull(organizationInfo)){
                return;
            }

            // 封装订单异常表主表信息
            OrderExceptionModel exceptionOrderModel = createExceptionOrderModel(organizationInfo, customerModel, e.getExceptionSituation(), e.getOrderNo(), now);
            orderExceptionMapper.insert(exceptionOrderModel);
            String exceptionSituation = e.getExceptionSituation();
            if(!StringUtils.isBlank(exceptionSituation) || !NOT_REPEAT.equals(exceptionSituation)){
                Arrays.asList(exceptionSituation.split(SPLIT_COMMA)).forEach(s ->{
                    // 插入子表信息
                    orderExceptionMapper.subInsert(e.getOrderNo(),s);
                });
            }
        });
        log.info("同步大数据异常订单end");
    }

    private OrderExceptionModel createExceptionOrderModel(CustomerOrganizationModel organizationInfo, CustomerStoreModel customerModel, String exceptionSituation, String orderNo, LocalDate now) {
        OrderExceptionModel model = new OrderExceptionModel();
        model.setEmployeeId("");
        model.setEmployeeName(SYS);
        model.setMonth(formatMonth(now));
        model.setAreaId(organizationInfo.getAreaId());
        model.setAreaName(organizationInfo.getAreaName());
        model.setBranchId(organizationInfo.getBranchId());
        model.setBranchName(organizationInfo.getBranchName());
        model.setIsDelete(0);
        model.setOrderNo(orderNo);
        model.setStoreName(customerModel.getStoreName());
        if(!StringUtils.isBlank(exceptionSituation) || !NOT_REPEAT.equals(exceptionSituation)){
            model.setException(0);
        }
        model.setStatus(0);
        return model;
    }

    private String formatMonth(LocalDate now) {
        if(now.getMonthValue() < 10){
            return now.getYear()+"-0"+now.getMonthValue();
        }
        return now.getYear()+"-"+now.getMonthValue();
    }

    private CustomerOrganizationModel getOrganizationInfo(HashMap<String, CustomerOrganizationModel> organizationModelMap, String employeeId) {
        CustomerOrganizationModel organizationModel = null;
        if(organizationModelMap.containsKey(employeeId)){
            organizationModel = organizationModelMap.get(employeeId);
        }else{
            organizationModel = orderExceptionMapper.getOrganizationInfoWithMemberId(employeeId);
            if(Objects.nonNull(organizationModel)){
                organizationModelMap.put(employeeId,organizationModel);
            }
        }
        return organizationModel;
    }

    private CustomerStoreModel getEmployeeId(HashMap<Long, CustomerStoreModel> employeeIdMaps, BuyTerminalIpModel e, Long memberKey) {
        CustomerStoreModel customerStoreModel = null;
        if(employeeIdMaps.containsKey(e.getMemberKey())){
            customerStoreModel = employeeIdMaps.get(e.getMemberKey());
        }else{
            customerStoreModel = orderExceptionMapper.getMemberWithMemberKey(memberKey);
            if(Objects.nonNull(customerStoreModel)){
                employeeIdMaps.put(memberKey,customerStoreModel);
            }
        }
        return customerStoreModel;
    }
}
