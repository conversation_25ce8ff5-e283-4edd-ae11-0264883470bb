package com.wantwant.sfa.backend.config;

import com.wantwant.sfa.backend.common.interceptor.AuthorizationInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private AuthorizationInterceptor authorizationInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authorizationInterceptor)
                .order(2)
                .addPathPatterns("/**")
                .excludePathPatterns("/**/swagger-resources/**","/**/doc.html","/**/api-docs/**","/**/getChannel/**","/**/login/");
    }
}
