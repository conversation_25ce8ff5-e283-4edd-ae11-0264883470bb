package com.wantwant.sfa.backend.index.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.index.IndexAPI;
import com.wantwant.sfa.backend.index.service.IndexService;
import com.wantwant.sfa.backend.index.vo.DataOverviewVo;
import com.wantwant.sfa.backend.index.vo.NextAchievementVo;
import com.wantwant.sfa.backend.index.vo.PromiseVo;
import com.wantwant.sfa.backend.index.vo.UserInfoVo;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/26/上午9:16
 */
@RestController
@Slf4j
public class IndexController implements IndexAPI {
    @Autowired
    private IndexService indexService;

    @Override
    public Response<UserInfoVo> getUserName(String person) {
        log.info("【get user name】empId:{}",person);

        UserInfoVo userInfoVo = indexService.getUserName(person);
        return Response.success(userInfoVo);
    }


    @Override
    public Response<PromiseVo> getPromise(String person, String orgCode) {
        log.info("【get promise】empId:{},orgCode:{}",person,orgCode);
        PromiseVo promiseVo = indexService.getPromise(person,orgCode);
        return Response.success(promiseVo);
    }

    @Override
    public Response<DataOverviewVo> getDataOverview(String person, String orgCode) {
        log.info("【get data overview】empId:{},orgCode:{}",person,orgCode);

        DataOverviewVo dataOverview = indexService.getDataOverview(person,orgCode);
        return Response.success(dataOverview);
    }

    @Override
    public Response<List<NextAchievementVo>> getNextAchievement(String person,String orgCode,String orderField,String orderType) {
        log.info("【get next achievement】orgCode:{}",orgCode);

        List<NextAchievementVo> list = indexService.getNextAchievement(orgCode,person,orderField,orderType);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(e -> {
                e.setPosition(ComonLanguageEnum.getDescByEnv(e.getPosition()));
                e.setEmployeeStatus(EmployeeStatus.getDescByEnv(e.getEmployeeStatus()));
            });
        }
        return Response.success(list);
    }
}
