package com.wantwant.sfa.backend.customer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.customer.enums.TradingCustomerTagEnums;
import com.wantwant.sfa.backend.customer.request.CustomerManagementQueryCallBackInfoReq;
import com.wantwant.sfa.backend.customer.request.TradingCustomerListRequest;
import com.wantwant.sfa.backend.customer.request.TradingOnboardCustomerRequest;
import com.wantwant.sfa.backend.customer.service.TradingCustomerService;
import com.wantwant.sfa.backend.customer.vo.TradingCustomerEnumsVO;
import com.wantwant.sfa.backend.customer.vo.TradingCustomerListVO;
import com.wantwant.sfa.backend.customer.vo.TradingOnboardCustomerCallbackListVO;
import com.wantwant.sfa.backend.customer.vo.TradingOnboardCustomerListVO;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.mapper.CustomerManagementCallBackInfoMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.customer.TradingCustomerMapper;
import com.wantwant.sfa.backend.service.CustomerManagementDetailService;
import com.wantwant.sfa.backend.util.ExceptionHelper;
import com.wantwant.sfa.backend.util.ExportUtil;
import com.wantwant.sfa.backend.util.RealTimeUtils;
import com.wantwant.sfa.backend.vo.CustomerManagementQueryCallBackInfoSubVO;
import com.wantwant.sfa.backend.vo.CustomerManagementQueryCallBackInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.customer.service.impl
 * @Description:
 * @Date: 2024/6/19 13:45
 */
@Slf4j
@Service
public class TradingCustomerServiceImpl implements TradingCustomerService {
    @Resource
    private CustomerManagementDetailService customerManagementDetailService;
    @Resource
    private TradingCustomerMapper tradingCustomerMapper;
    @Resource
    private CustomerManagementCallBackInfoMapper customerManagementCallBackInfoMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private RealTimeUtils realTimeUtils;

    @Override
    public IPage<TradingCustomerListVO> queryList(TradingCustomerListRequest request) {
        log.info("start TradingCustomerService queryList request:{}", request);
        Page<TradingCustomerListVO> page = new Page<>(request.getPage(), request.getRows());
        request.setTheDate(realTimeUtils.getNearMonth(request.getDateTypeId(),request.getYearMonth()));
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        if(StringUtils.isNotBlank(request.getOrganizationId())){
            String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
            if(StringUtils.isBlank(organizationType)){
                log.error("请求参数[{}]查询不到organizationType",request);
                return page;
            }
            request.setOrganizationType(organizationType);
        }
        List<TradingCustomerListVO> vos = tradingCustomerMapper.queryTradingCustomerList(page, request);
        page.setRecords(vos);
        return page;
    }

    @Override
    public void download(TradingCustomerListRequest req, HttpServletRequest request, HttpServletResponse response) {
        log.info("start TradingCustomerService download request:{}", req);
        if(StringUtils.isNotBlank(req.getOrganizationId())){
            String organizationType = organizationMapper.getOrganizationType(req.getOrganizationId());
            if(StringUtils.isBlank(organizationType)){
                log.error("请求参数[{}]查询不到organizationType",request);
                throw new ApplicationException("查询不到organizationType");
            }
            req.setOrganizationType(organizationType);
        }
        req.setTheDate(realTimeUtils.getNearMonth(req.getDateTypeId(),req.getYearMonth()));
        req.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<TradingCustomerListVO> vos = tradingCustomerMapper.queryTradingCustomerList(null, req);
        ExportUtil.writeEasyExcelResponse(response, request, "交易客户列表", TradingCustomerListVO.class, vos);
    }

    @Override
    public TradingCustomerEnumsVO getTradingCustomerEnums() {
        TradingCustomerEnumsVO vo = new TradingCustomerEnumsVO();
        vo.setTag(TradingCustomerTagEnums.getTradingCustomerTag());
        //查询db trading_customer_employee_list
        List<String> allPosition = tradingCustomerMapper.getAllPosition();
        vo.setPartnerPositionInfos(generatePartnerPositionInfo(allPosition));
        //查询db trading_onboard_partner_list
        List<String> allOnboardPosition = tradingCustomerMapper.getAllOnboardPosition();
        vo.setOnboardPartnerPositionInfos(generatePartnerPositionInfo(allOnboardPosition));

        return vo;
    }

    private List<TradingCustomerEnumsVO.PartnerPositionInfo> generatePartnerPositionInfo(List<String> positionList){
        if(CollectionUtil.isNotEmpty(positionList)){
            List<TradingCustomerEnumsVO.PartnerPositionInfo> partnerPositionInfos = new ArrayList<>(positionList.size());
            positionList.stream().filter(position->StringUtils.isNotBlank(position)).forEach(position->{
                String[] strings = position.split("-");
                TradingCustomerEnumsVO.PartnerPositionInfo info = new TradingCustomerEnumsVO.PartnerPositionInfo();
                info.setPostId(strings[0]);
                info.setPostName(strings[1]);
                partnerPositionInfos.add(info);
            });
            return partnerPositionInfos;
        }
        return CollectionUtil.newArrayList();
    }


    @Override
    public IPage<TradingOnboardCustomerListVO> queryOnboardList(TradingOnboardCustomerRequest request) {
        log.info("start TradingCustomerService queryOnboardList request:{}", request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        Page<TradingOnboardCustomerListVO> page = new Page<>(request.getPage(), request.getRows());

        List<String> organizationIds = request.getOrganizationIds();
        if(!CollectionUtils.isEmpty(organizationIds)){

            String orgCode = organizationIds.stream().findFirst().get();
            String organizationType = organizationMapper.getOrganizationType(orgCode);
            if(StringUtils.isBlank(organizationType)){
                log.error("请求参数[{}]查询不到organizationType",request);
                return page;
            }
            request.setOrganizationType(organizationType);
        }



        //回访客户查询限制
        if(Objects.nonNull(request.getCallbackType()) || Objects.nonNull(request.getCallbackLimit())){
            List<String> customerIdList = customerManagementCallBackInfoMapper.queryCustomerIdList(request.getCallbackType(), request.getCallbackLimit());
            request.setCallBackCustomerIds(customerIdList);
        }
        request.setNearMonth(realTimeUtils.getNearMonth(request.getDateTypeId(), request.getYearMonth()));
        request.setNearDate(realTimeUtils.getNearDate(request.getDateTypeId(), request.getYearMonth()));
        List<TradingOnboardCustomerListVO> tradingOnboardCustomerList = tradingCustomerMapper.queryOnboardList(page, request);
        if (CollectionUtil.isNotEmpty(tradingOnboardCustomerList)) {
            tradingOnboardCustomerList.forEach(vo -> {
                vo.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(vo.getPostName()));
                vo.setManagerPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(vo.getManagerPostName()));
                vo.setPartnerLifeCycle(ComonLanguageEnum.getDescByEnv(vo.getPartnerLifeCycle()));
            });
        }
        page.setRecords(tradingOnboardCustomerList);
        return page;
    }

    @Override
    public void downloadOnboardList(TradingOnboardCustomerRequest req, HttpServletRequest request, HttpServletResponse response) {
        log.info("start TradingCustomerService downloadOnboardList request:{}", request);
        req.setBusinessGroup(RequestUtils.getBusinessGroup());
        if(StringUtils.isNotBlank(req.getOrganizationId())){
            String organizationType = organizationMapper.getOrganizationType(req.getOrganizationId());
            if(StringUtils.isBlank(organizationType)){
                log.error("请求参数[{}]查询不到organizationType",request);
                throw new ApplicationException("查询不到organizationType");
            }
            req.setOrganizationType(organizationType);
        }
        req.setNearDate(realTimeUtils.getNearDate(req.getDateTypeId(), req.getYearMonth()));
        req.setNearMonth(realTimeUtils.getNearMonth(req.getDateTypeId(), req.getYearMonth()));
        List<TradingOnboardCustomerListVO> vos = tradingCustomerMapper.queryOnboardList(null, req);
        if (CollectionUtil.isNotEmpty(vos)) {
            vos.forEach(vo -> {
                vo.setPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(vo.getPostName()));
                vo.setManagerPostName(OrganizationPositionRelationEnums.getPositionNameByEnv(vo.getManagerPostName()));
                vo.setPartnerLifeCycle(ComonLanguageEnum.getDescByEnv(vo.getPartnerLifeCycle()));
            });
        }
        ExportUtil.writeEasyExcelResponse(response, request, "本组客户列表", TradingOnboardCustomerListVO.class, vos);
    }

    @Override
    public void downloadCallBack(TradingOnboardCustomerRequest req, HttpServletRequest request, HttpServletResponse response) {
        log.info("start TradingCustomerService downloadCallBack request:{}", req);
        req.setBusinessGroup(RequestUtils.getBusinessGroup());
        if (StringUtils.isNotBlank(req.getOrganizationId())) {
            String organizationType = organizationMapper.getOrganizationType(req.getOrganizationId());
            if (StringUtils.isBlank(organizationType)) {
                log.error("请求参数[{}]查询不到organizationType", req);
                throw new ApplicationException("查询不到organizationType");
            }
            req.setOrganizationType(organizationType);
        }

        //回访客户查询限制
        if (Objects.nonNull(req.getCallbackType()) || Objects.nonNull(req.getCallbackLimit())) {
            List<String> customerIdList = customerManagementCallBackInfoMapper.queryCustomerIdList(req.getCallbackType(), req.getCallbackLimit());
            req.setCallBackCustomerIds(customerIdList);
        }
        req.setNearDate(realTimeUtils.getNearDate(req.getDateTypeId(), req.getYearMonth()));
        req.setNearMonth(realTimeUtils.getNearMonth(req.getDateTypeId(), req.getYearMonth()));
        List<TradingOnboardCustomerListVO> tradingOnboardCustomerList = tradingCustomerMapper.queryOnboardList(null, req);
        ExceptionHelper.checkAndThrow(CollectionUtils.isEmpty(tradingOnboardCustomerList), "当前暂无数据可导出!");

        List<TradingOnboardCustomerCallbackListVO> tradingOnboardCustomerCallbackList = new ArrayList<>();
        tradingOnboardCustomerList.forEach(customer -> {
            TradingOnboardCustomerCallbackListVO backInfoVO = BeanUtil.copyProperties(customer, TradingOnboardCustomerCallbackListVO.class);
            CustomerManagementQueryCallBackInfoReq callBackInfoReq = new CustomerManagementQueryCallBackInfoReq();
            callBackInfoReq.setCustomerId(customer.getMemberKey().toString());
            List<CustomerManagementQueryCallBackInfoVO> queryCallBackInfoVOS = customerManagementDetailService.queryCallBackList(callBackInfoReq);
            if (!CollectionUtils.isEmpty(queryCallBackInfoVOS)) {
                queryCallBackInfoVOS.forEach(callBackInfo -> {
                    //回访信息
                    TradingOnboardCustomerCallbackListVO infoVO = BeanUtil.copyProperties(backInfoVO, TradingOnboardCustomerCallbackListVO.class);
                    infoVO.setCallbackType(callBackInfo.getCallBackTypeDesc());
                    infoVO.setCallbackDate(DateUtil.format(callBackInfo.getCallBackDate(), LocalDateTimeUtils.yyyy_MM_dd));
                    infoVO.setRecordInfo(callBackInfo.getRecordInfo());
                    infoVO.setCallbackEmployeeInfo(callBackInfo.getEmployeeName() + "(" + callBackInfo.getEmployeeId() + ")");
                    infoVO.setRecordCreateTime(DateUtil.format(callBackInfo.getCreateTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
                    //追踪信息拼接
                    if (!CollectionUtils.isEmpty(callBackInfo.getSubInfoList())) {
                        StringBuilder stringBuilder = new StringBuilder();
                        for (int i = 1; i <= callBackInfo.getSubInfoList().size(); i++) {
                            CustomerManagementQueryCallBackInfoSubVO subInfo = callBackInfo.getSubInfoList().get(i - 1);
                            stringBuilder.append("追加" + i + ":");
                            //跟进人 跟进时间 跟进情况
                            stringBuilder.append(org.apache.commons.lang3.StringUtils
                                    .join(Arrays.asList(subInfo.getEmployeeName() + "(" + subInfo.getEmployeeId() + ")",
                                            DateUtil.format(subInfo.getCallBackDate(), LocalDateTimeUtils.yyyy_MM_dd),
                                            subInfo.getRecordInfo()
                                    ), "/"));
                            stringBuilder.append(";");
                        }
                        infoVO.setAttachInfo(stringBuilder.toString());
                    }
                    tradingOnboardCustomerCallbackList.add(infoVO);
                });
            } else {
                tradingOnboardCustomerCallbackList.add(backInfoVO);
            }
        });
        ExportUtil.writeEasyExcelResponse(response, request, "本组客户-回访导出", TradingOnboardCustomerCallbackListVO.class, tradingOnboardCustomerCallbackList);
    }

}
