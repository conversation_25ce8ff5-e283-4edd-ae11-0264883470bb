package com.wantwant.sfa.backend.daily.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.complete.vo.CompleteListVo;
import com.wantwant.sfa.backend.daily.entity.AdsProductsaleRealtimeInventoryDaily;
import com.wantwant.sfa.backend.daily.entity.SfaDailyPopup;
import com.wantwant.sfa.backend.daily.request.DailyEmployeeRequest;
import com.wantwant.sfa.backend.daily.request.DailyInventoryRequest;
import com.wantwant.sfa.backend.daily.request.DailyNewCustomerPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyOrderPageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformancePageRequest;
import com.wantwant.sfa.backend.daily.request.DailyPerformanceRequest;
import com.wantwant.sfa.backend.daily.request.DailyPopupRequest;
import com.wantwant.sfa.backend.daily.request.DailyProcessRequest;
import com.wantwant.sfa.backend.daily.request.DailySkuPageRequest;
import com.wantwant.sfa.backend.daily.service.DailyNewService;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeInfoVo;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeOrganizationTypeVo;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeVo;
import com.wantwant.sfa.backend.daily.vo.DailyInventoryCompanyVo;
import com.wantwant.sfa.backend.daily.vo.DailyInventoryCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailyInventorySkuChannelListVo;
import com.wantwant.sfa.backend.daily.vo.DailyMemberVo;
import com.wantwant.sfa.backend.daily.vo.DailyOrderVo;
import com.wantwant.sfa.backend.daily.vo.DailyPerformanceVo;
import com.wantwant.sfa.backend.daily.vo.DailyPopupVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuCrossListVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuCrossVo;
import com.wantwant.sfa.backend.daily.vo.DailySkuVo;
import com.wantwant.sfa.backend.dict.service.DictCodeService;
import com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationTreeEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessPositionType;
import com.wantwant.sfa.backend.mapper.AttendanceMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationTreeMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessPositionTypeMapper;
import com.wantwant.sfa.backend.mapper.CeoMapper;
import com.wantwant.sfa.backend.mapper.CustomerInfoMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteMapper;
import com.wantwant.sfa.backend.mapper.daily.AdsBigtableOrganizationDailyMeetingsMapper;
import com.wantwant.sfa.backend.mapper.daily.AdsBigtableOrganizationDailyMeetingsSkuMapper;
import com.wantwant.sfa.backend.mapper.daily.AdsBigtableOrganizationSkuMapper;
import com.wantwant.sfa.backend.mapper.daily.AdsProductsaleRealtimeInventoryDailyMapper;
import com.wantwant.sfa.backend.mapper.daily.DimEmpPosRoleOrgDayMapper;
import com.wantwant.sfa.backend.mapper.daily.SfaDailyPopupMapper;
import com.wantwant.sfa.backend.mapper.meeting.MeetingInfoMapper;
import com.wantwant.sfa.backend.mapper.order.AdsOrderItemDetailMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportBTMapper;
import com.wantwant.sfa.backend.mapper.workReport.DailyReportMapper;
import com.wantwant.sfa.backend.meeting.vo.MeetingInfoVO;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.attendanceTask.Attendance;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.visitCustomer.request.DailyListRequest;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitInfoForDailyVo;
import com.wantwant.sfa.backend.workReport.entity.SfaDailyReportEntity;
import com.wantwant.sfa.backend.workReport.vo.DailyVisitCustomerVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DailyNewServiceImpl implements DailyNewService {

    private static final Object NEXT_ORG_KEY = "sfa:next:org:";

    private static final String DAILY_VIEW_LOCK = "daily:view:lock";

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RealTimeUtils realTimeUtils;

    @Autowired
    private DictCodeService dictCodeService;
    @Autowired
    private SettingServiceImpl settingServiceImpl;

    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private CeoBusinessOrganizationTreeMapper ceoBusinessOrganizationTreeMapper;
    @Autowired
    private CeoBusinessPositionTypeMapper ceoBusinessPositionTypeMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Autowired
    private AdsOrderItemDetailMapper adsOrderItemDetailMapper;
    @Autowired
    private DimEmpPosRoleOrgDayMapper dimEmpPosRoleOrgDayMapper;
    @Autowired
    private CeoMapper ceoMapper;
    @Autowired
    private AdsBigtableOrganizationDailyMeetingsMapper adsBigtableOrganizationDailyMeetingsMapper;
    @Autowired
    private AdsBigtableOrganizationDailyMeetingsSkuMapper adsBigtableOrganizationDailyMeetingsSkuMapper;
    @Autowired
    private AdsProductsaleRealtimeInventoryDailyMapper adsProductsaleRealtimeInventoryDailyMapper;
    @Autowired
    private AdsBigtableOrganizationSkuMapper adsBigtableOrganizationSkuMapper;
    @Autowired
    private SfaDailyPopupMapper sfaDailyPopupMapper;
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Autowired
    private SfaCompleteMapper sfaCompleteMapper;
    @Autowired
    private MeetingInfoMapper meetingInfoMapper;
    @Autowired
    private DailyReportMapper dailyReportMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Resource
    private DailyReportBTMapper dailyReportBTMapper;


    @Override
    public DailyPopupVo dailyPopup(DailyPopupRequest request) {
        log.info("dailyPopup request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        if (Objects.isNull(request.getTheDate())) {
            request.setTheDate(LocalDate.now().minusDays(1));
        }
        DailyPopupVo vo = new DailyPopupVo();
        vo.setDailyDate(request.getTheDate());
        vo.setEmployeeId(request.getEmployeeId());

        // 获取配置的没有日报的组别
        String dailyExcludeGroup = settingServiceImpl.getValue(DictCodeConstants.DAILY_EXCLUDE_GROUP);
        if (StringUtils.isNotBlank(dailyExcludeGroup)) {
            String[] dailyExcludeGroupArray = dailyExcludeGroup.split(",");
            List<Integer> dailyExcludeGroupArrayList = Arrays.stream(dailyExcludeGroupArray)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (dailyExcludeGroupArrayList.contains(RequestUtils.getBusinessGroup())) {
                vo.setIsPopup(Boolean.FALSE);
                vo.setEntranceIsShow(Boolean.FALSE);
                return vo;
            }
        }
        // 获取配置的推送总部员工号
        List<String> dailyPushEmployeeList = new ArrayList<>();
        String dailyPushEmployee = settingServiceImpl.getValue(DictCodeConstants.DAILY_PUSH_EMPLOYEE);
        if (StringUtils.isNotBlank(dailyPushEmployee)) {
            dailyPushEmployeeList = Arrays.asList(dailyPushEmployee.split(","));
        }
        if (OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            if (!dailyPushEmployeeList.contains(request.getEmployeeId())) {
                vo.setEntranceIsShow(Boolean.FALSE);
            }
        }
        SfaDailyPopup popup = sfaDailyPopupMapper.selectOne(new LambdaQueryWrapper<SfaDailyPopup>()
                .eq(SfaDailyPopup::getDailyDate, request.getTheDate())
                .eq(SfaDailyPopup::getEmployeeId, request.getEmployeeId())
                .eq(SfaDailyPopup::getIsViewed, Boolean.TRUE)
                .eq(SfaDailyPopup::getDeleteFlag, 0)
        );
        if (Objects.isNull(popup)) {
            if (OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
                if (dailyPushEmployeeList.contains(request.getEmployeeId())) {
                    vo.setIsPopup(Boolean.TRUE);
                }
            } else {
                vo.setIsPopup(Boolean.TRUE);
            }
        }
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dailyViewed(DailyPopupRequest request) {
        log.info("dailyViewed request:{}", request);
        if (request.getPerson().equals(request.getEmployeeId())) {

            if (redisUtil.setLockIfAbsent(DAILY_VIEW_LOCK, request.getEmployeeId(), 1, TimeUnit.SECONDS)) {
                try {
                    // 操作人check
                    CeoBusinessOrganizationPositionRelation relationEntity = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                            .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getPerson())
                            .last("limit 1")
                    );

                    if (Objects.isNull(relationEntity)) {
                        throw new ApplicationException("操作人信息获取失败");
                    }
                    if (Objects.isNull(request.getTheDate())) {
                        request.setTheDate(LocalDate.now().minusDays(1));
                    }
                    SfaDailyPopup exist = sfaDailyPopupMapper.selectOne(new LambdaQueryWrapper<SfaDailyPopup>()
                            .eq(SfaDailyPopup::getDailyDate, request.getTheDate())
                            .eq(SfaDailyPopup::getEmployeeId, request.getEmployeeId())
                            .eq(SfaDailyPopup::getDeleteFlag, 0)
                    );
                    if (Objects.nonNull(exist)) {
                        exist.update(request.getPerson(), relationEntity.getEmployeeName());
                        exist.setIsViewed(Boolean.TRUE);
                        exist.setViewedTime(LocalDateTime.now());
                        sfaDailyPopupMapper.updateById(exist);
                    } else {
                        SfaDailyPopup entity = new SfaDailyPopup();
                        entity.init(request.getPerson(), relationEntity.getEmployeeName());
                        entity.setDailyDate(request.getTheDate());
                        entity.setEmployeeId(request.getEmployeeId());
                        entity.setIsViewed(Boolean.TRUE);
                        entity.setViewedTime(LocalDateTime.now());
                        sfaDailyPopupMapper.insert(entity);
                    }
                } finally {
                    redisUtil.unLock(DAILY_VIEW_LOCK, request.getEmployeeId());
                }
            }
        }
    }

    @Override
    public DailyEmployeeInfoVo dailyEmployee(DailyEmployeeRequest request) {
        log.info("dailyEmployee request:{}", request);
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getEmployeeId())
                .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                .last("limit 1")
        );
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw ExceptionConstants.FAILED_BUSINESS("查无此人");
        }

        DailyEmployeeInfoVo dailyEmployeeInfoVo = new DailyEmployeeInfoVo();
        if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 7) {
            EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectZbEmployee(request.getEmployeeId(), RequestUtils.getBusinessGroup());
            if (Objects.nonNull(employeeInfo)) {
                dailyEmployeeInfoVo.setAvatar(employeeInfo.getAvatar());
                dailyEmployeeInfoVo.setEmpId(employeeInfo.getEmployeeId());
                dailyEmployeeInfoVo.setEmployeeName(employeeInfo.getEmployeeName());
                dailyEmployeeInfoVo.setPositionTypeId(7L);
                dailyEmployeeInfoVo.setBusinessGroupId(employeeInfo.getBusinessGroup());
                dailyEmployeeInfoVo.setBusinessGroupName(employeeInfo.getBusinessGroupName());
                dailyEmployeeInfoVo.setOrganizationId(employeeInfo.getOrganizationId());
                dailyEmployeeInfoVo.setOrganizationName(employeeInfo.getOrganizationName());
                DailyEmployeeVo vo = new DailyEmployeeVo();
                vo.setBusinessGroupId(employeeInfo.getBusinessGroup());
                vo.setBusinessGroupName(employeeInfo.getBusinessGroupName());
                vo.setOrganizationId(employeeInfo.getOrganizationId());
                vo.setOrganizationName(employeeInfo.getOrganizationName());
                dailyEmployeeInfoVo.setOrganizationList(Collections.singletonList(vo));
                if (Objects.nonNull(employeeInfo.getOnboardTime())) {
                    dailyEmployeeInfoVo.setOnboardTime(employeeInfo.getOnboardTime());
                    dailyEmployeeInfoVo.setOnboardDays((int) LocalDateTimeUtils.betweenTwoTime(employeeInfo.getOnboardTime().atStartOfDay(), LocalDateTime.now(), ChronoUnit.DAYS));
                }
                List<String> childOrganizations = new ArrayList<>();
                List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + employeeInfo.getOrganizationId());
                if (!CollectionUtils.isEmpty(nextOrgCode)) {
                    childOrganizations.addAll(nextOrgCode);
                }
                setChild(childOrganizations, dailyEmployeeInfoVo);
            }
        } else {
            List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(request);
            if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
                List<String> organizationIdList = dailyEmployeeVoList.stream().map(DailyEmployeeVo::getOrganizationId).collect(Collectors.toList());
                Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                        .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
                ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));

                List<String> childOrganizations = new ArrayList<>();
                dailyEmployeeVoList.forEach(detail -> {
                    detail.setOrganizationName(organizationMap.get(detail.getOrganizationId()));
                    List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + detail.getOrganizationId());
                    if (!CollectionUtils.isEmpty(nextOrgCode)) {
                        childOrganizations.addAll(nextOrgCode);
                    }
                });
                BeanUtils.copyProperties(dailyEmployeeVoList.get(0), dailyEmployeeInfoVo);
                dailyEmployeeInfoVo.setOrganizationList(dailyEmployeeVoList);
                setChild(childOrganizations, dailyEmployeeInfoVo);
            }
        }
        return dailyEmployeeInfoVo;
    }

    @Override
    public DailyPerformanceVo dailyDetail(DailyPerformanceRequest request) {
        log.info("dailyDetail request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        Integer scale = Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE));
        request.setDailyScale(scale);

        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, request.getOrganizationId())
                .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                .last("limit 1")
        );
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw ExceptionConstants.FAILED_BUSINESS("查无此人");
        }
        // 参数前置 当日所在季度 所在月份
        request.setMonth(request.getTheDate().toString().substring(0, 7));
        request.setQuarter(realTimeUtils.getQuarterStr(request.getTheDate()));
        DailyPerformanceVo vo = new DailyPerformanceVo();
        if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 7) {
            vo = adsBigtableOrganizationDailyMeetingsMapper.queryZbDailyDetail(request);
        } else {
            vo = adsBigtableOrganizationDailyMeetingsMapper.queryDailyDetail(request);
        }

        if (Objects.nonNull(vo)) {
            vo.setTheYearMon(realTimeUtils.formatter("10", request.getTheDate()));
            vo.setDateRateMonth(realTimeUtils.dateMonthRate(request.getTheDate()).setScale(1, RoundingMode.HALF_UP));
            if (vo.getPerformanceAchievementRateMonth().compareTo(realTimeUtils.dateMonthRate(request.getTheDate())) < 0) {
                vo.setPerformanceAchievementRateIsRedMonth(Boolean.TRUE);
            }
            vo.setDateRateQuarter(realTimeUtils.dateQuarterRate(request.getTheDate()).setScale(1, RoundingMode.HALF_UP));
            if (vo.getPerformanceAchievementRateQuarter().compareTo(realTimeUtils.dateQuarterRate(request.getTheDate())) < 0) {
                vo.setPerformanceAchievementRateIsRedQuarter(Boolean.TRUE);
            }
            if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 7) {
                vo.setPositionTypeId(7L);
            }
            vo.setPerformanceIsShow(setPerformanceIsShow(loginInfo.getPositionTypeId(), vo.getPositionTypeId()));

            List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
            if (CollectionUtils.isEmpty(personOrganizationIds)) {
                throw new ApplicationException("操作人组织信息获取失败");
            }
            List<String> childOrganizations = setChildOrganizations(request.getTheDate(), request.getPerson(), loginInfo.getBusinessGroup());
            vo.setPerformanceBonusCombinedIsShow(setPerformanceBonusCombinedIsShow(vo.getOrganizationId(), childOrganizations, personOrganizationIds, Boolean.TRUE));

            // 获取昨日承诺业绩
            BigDecimal promisePerformance = Optional.ofNullable(dailyReportMapper.selectPromisePerformance(request.getOrganizationId(), request.getTheDate().minusDays(1L).toString())).orElse(BigDecimal.ZERO);
            vo.setGoalDay(promisePerformance);

            // 计算达成率
            BigDecimal achievementRate = CalculateUtils.ratioPercent(vo.getPerformancePerDay(), promisePerformance, scale);
            vo.setPerformanceAchievementRateDay(achievementRate);

            // 计算日维度 新老客户数
            vo.setNewTradingCustomerNumDealerDay(null);
            vo.setOldTradingCustomerNumDealerDay(null);
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
            );
            if (Objects.nonNull(ceoBusinessOrganizationEntity)) {
                DailyOrderPageRequest orderRequest = new DailyOrderPageRequest();
                orderRequest.setDateTypeId("1");
                orderRequest.setTheDate(request.getTheDate());
                orderRequest.setDailyScale(scale);
                orderRequest.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
                orderRequest.setOrganizationId(request.getOrganizationId());
                orderRequest.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
                Map<String, LocalDate> theDateMap = setTheDate(orderRequest.getDateTypeId(), orderRequest.getTheDate());
                orderRequest.setTheDateFrom(theDateMap.get("theDateFrom"));
                orderRequest.setTheDateTo(theDateMap.get("theDateTo"));
                if ("1".equals(orderRequest.getDateTypeId())) {
                    orderRequest.setLabelDateTypeId("10");
                } else {
                    orderRequest.setLabelDateTypeId(orderRequest.getDateTypeId());
                }
                orderRequest.setLabelTheDate(realTimeUtils.formatter(orderRequest.getLabelDateTypeId(), orderRequest.getTheDate()));
                List<DailyOrderVo> list = adsOrderItemDetailMapper.queryDailyOrderList(null, orderRequest);
                if (!CollectionUtils.isEmpty(list)) {
                    Map<String, List<DailyOrderVo>> tradingBehaviorMap = list.stream()
                            .filter(e -> e.getTradingBehavior() != null)
                            .collect(Collectors.groupingBy(DailyOrderVo::getTradingBehavior));
                    List<DailyOrderVo> newList = tradingBehaviorMap.get("新客户");
                    List<DailyOrderVo> oldList = tradingBehaviorMap.get("老客户");
                    vo.setNewTradingCustomerNumDealerDay(Optional.ofNullable(newList).map(List::size).orElse(0));
                    vo.setOldTradingCustomerNumDealerDay(Optional.ofNullable(oldList).map(List::size).orElse(0));
                    if (!CollectionUtils.isEmpty(newList)) {
                        Map<Integer, List<DailyOrderVo>> newMap = newList.stream()
                                .filter(e -> e.getOldCustomers() != null)
                                .collect(Collectors.groupingBy(DailyOrderVo::getOldCustomers));
                        vo.setOutTimeactivatedCustomerWithInThirtyNumDay(Optional.ofNullable(newMap.get(0)).map(List::size).orElse(0));
                        vo.setInTimeactivatedCustomerNumDay(Optional.ofNullable(newMap.get(1)).map(List::size).orElse(0));
                    }
                    if (!CollectionUtils.isEmpty(oldList)) {
                        Map<Integer, List<DailyOrderVo>> oldMap = oldList.stream()
                                .filter(e -> e.getOldCustomersClassify() != null)
                                .collect(Collectors.groupingBy(DailyOrderVo::getOldCustomersClassify));
                        vo.setRepeatPurchasesCustomerNumDay(Optional.ofNullable(oldMap.get(1)).map(List::size).orElse(0));
                        vo.setRecallCustomerNumDay(Optional.ofNullable(oldMap.get(2)).map(List::size).orElse(0));

                        Map<String, List<DailyOrderVo>> oldCustomersTradingCyclesMap = oldList.stream()
                                .filter(e -> e.getOldCustomersTradingCycles() != null)
                                .collect(Collectors.groupingBy(DailyOrderVo::getOldCustomersTradingCycles));
                        int size1 = Optional.ofNullable(oldCustomersTradingCyclesMap.get("A")).map(List::size).orElse(0);
                        vo.setWithinAMonthTradingCustomerNumDay(size1);
                        vo.setWithinAMonthTradingCustomerRatioDay(new BigDecimal(size1).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(oldList.size()), scale, RoundingMode.HALF_UP));
                        int size2 = Optional.ofNullable(oldCustomersTradingCyclesMap.get("B")).map(List::size).orElse(0);
                        vo.setWithinTwoMonthTradingCustomerNumDay(size2);
                        vo.setWithinTwoMonthTradingCustomerRatioDay(new BigDecimal(size2).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(oldList.size()), scale, RoundingMode.HALF_UP));
                        int size3 = Optional.ofNullable(oldCustomersTradingCyclesMap.get("C")).map(List::size).orElse(0);
                        vo.setWithinThreeMonthTradingCustomerNumDay(size3);
                        vo.setWithinThreeMonthTradingCustomerRatioDay(new BigDecimal(size3).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(oldList.size()), scale, RoundingMode.HALF_UP));
                        int size4 = Optional.ofNullable(oldCustomersTradingCyclesMap.get("D")).map(List::size).orElse(0);
                        vo.setBeforeThreeMonthTradingCustomerNumDay(size4);
                        vo.setBeforeThreeMonthTradingCustomerRatioDay(new BigDecimal(size4).multiply(BigDecimal.valueOf(100)).divide(new BigDecimal(oldList.size()), scale, RoundingMode.HALF_UP));
                    }
                }
            }
        }
        return vo;
    }

    @Override
    public IPage<DailyPerformanceVo> dailyList(DailyPerformancePageRequest request) {
        log.info("dailyList request:{}", request);
        request.setDailyScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE)));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }

        Page<DailyPerformanceVo> page = new Page<>(request.getPage(), request.getRows());
        if (StringUtils.isNotBlank(request.getEmployeeId())) {
            request.setChildOrganizations(getChildOrganizations(request.getTheDate(), request.getEmployeeId(), request.getPersonBusinessGroup()));
        }
        if (CollectionUtils.isEmpty(request.getChildOrganizations())) {
            return page;
        }
        // 参数前置 当日所在季度 所在月份
        request.setMonth(request.getTheDate().toString().substring(0, 7));
        request.setQuarter(realTimeUtils.getQuarterStr(request.getTheDate()));
        List<DailyPerformanceVo> list = adsBigtableOrganizationDailyMeetingsMapper.queryDailyList(page, request);

        if (!CollectionUtils.isEmpty(list)) {
            List<String> employeeIdList = list.stream().map(DailyPerformanceVo::getEmployeeId).collect(Collectors.toList());
            List<Integer> employeeInfoIdList = list.stream().map(DailyPerformanceVo::getEmployeeInfoId).collect(Collectors.toList());
            List<String> organizationIdList = list.stream().map(DailyPerformanceVo::getOrganizationId).collect(Collectors.toList());
            Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));

            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));

            List<String> childOrganizations = setChildOrganizations(request.getTheDate(), request.getPerson(), loginInfo.getBusinessGroup());

            DailyProcessRequest dailyProcessRequest = new DailyProcessRequest();
            dailyProcessRequest.setTheDate(request.getTheDate());
            dailyProcessRequest.setEmployeeInfoIdList(employeeInfoIdList);
            dailyProcessRequest.setEmployeeIdList(employeeIdList);
            dailyProcessRequest.setTheDateMonday(request.getTheDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
            dailyProcessRequest.setTheDateSunday(request.getTheDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)));


            Map<Integer, Attendance> attendanceMap;
            List<Attendance> attendanceList = attendanceMapper.selectAttendanceV2ListForDaily(dailyProcessRequest);
            if (!CollectionUtils.isEmpty(attendanceList)) {
                attendanceMap = attendanceList.stream().collect(Collectors.toMap(Attendance::getEmployeeInfoId, v -> v));
            } else {
                attendanceMap = Maps.newHashMap();
            }

            Map<Integer, List<CompleteListVo>> completeMap;
            List<CompleteListVo> completeList = sfaCompleteMapper.selectCompleteListForDaily(dailyProcessRequest);
            if (!CollectionUtils.isEmpty(completeList)) {
                completeMap = completeList.stream().collect(Collectors.groupingBy(CompleteListVo::getEmployeeInfoId));
            } else {
                completeMap = Maps.newHashMap();
            }

            Map<String, List<MeetingInfoVO>> meetingMap;
            List<MeetingInfoVO> meetingList = meetingInfoMapper.selectMeetingInfoListForDaily(dailyProcessRequest);
            if (!CollectionUtils.isEmpty(meetingList)) {
                meetingMap = meetingList.stream().collect(Collectors.groupingBy(x -> x.getConveneEmployeeInfoId() + "#" + x.getConveneOrganizationId()));
            } else {
                meetingMap = Maps.newHashMap();
            }

            Map<String, List<SfaDailyReportEntity>> dailyReportMap;
            List<SfaDailyReportEntity> dailyReportList = dailyReportMapper.selectList(new LambdaQueryWrapper<SfaDailyReportEntity>()
                    .eq(SfaDailyReportEntity::getDeleteFlag, 0)
                    .eq(SfaDailyReportEntity::getDay, dailyProcessRequest.getTheDate())
                    .in(SfaDailyReportEntity::getEmployeeInfoId, dailyProcessRequest.getEmployeeInfoIdList())
            );
            if (!CollectionUtils.isEmpty(dailyReportList)) {
                dailyReportMap = dailyReportList.stream().collect(Collectors.groupingBy(x -> x.getEmployeeInfoId() + "#" + x.getOrganizationId()));
            } else {
                dailyReportMap = Maps.newHashMap();
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");

            Map<String, BigDecimal> customerPerformanceInThreeMonMap = Maps.newHashMap();

            // 拜访列表-日
            Map<Integer, List<VisitInfoForDailyVo>> visitDayMap;
            Map<Integer, List<VisitInfoForDailyVo>> visitPotentialDayMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerVisitDayMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerPotentialDayMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerVisitDayMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerPotentialDayMap;
            List<VisitInfoForDailyVo> visitInfoDayList = getVisitInfoForDailyVos(request, "1", employeeInfoIdList);
            if (!CollectionUtils.isEmpty(visitInfoDayList)) {
                visitDayMap = visitInfoDayList.stream().collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                visitPotentialDayMap = visitInfoDayList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerVisitDayMap = visitInfoDayList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerPotentialDayMap = visitInfoDayList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerVisitDayMap = visitInfoDayList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerPotentialDayMap = visitInfoDayList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
            } else {
                visitDayMap = Maps.newHashMap();
                visitPotentialDayMap = Maps.newHashMap();
                partnerVisitDayMap = Maps.newHashMap();
                partnerPotentialDayMap = Maps.newHashMap();
                customerVisitDayMap = Maps.newHashMap();
                customerPotentialDayMap = Maps.newHashMap();
            }

            // 拜访列表-月
            Map<Integer, List<VisitInfoForDailyVo>> visitMonthMap;
            Map<Integer, List<VisitInfoForDailyVo>> visitPotentialMonthMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerVisitMonthMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerPotentialMonthMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerVisitMonthMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerPotentialMonthMap;
            List<VisitInfoForDailyVo> visitInfoMonthList = getVisitInfoForDailyVos(request, "10", employeeInfoIdList);
            if (!CollectionUtils.isEmpty(visitInfoMonthList)) {
                visitMonthMap = visitInfoMonthList.stream().collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                visitPotentialMonthMap = visitInfoMonthList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerVisitMonthMap = visitInfoMonthList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerPotentialMonthMap = visitInfoMonthList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerVisitMonthMap = visitInfoMonthList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerPotentialMonthMap = visitInfoMonthList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
            } else {
                visitMonthMap = Maps.newHashMap();
                visitPotentialMonthMap = Maps.newHashMap();
                partnerVisitMonthMap = Maps.newHashMap();
                partnerPotentialMonthMap = Maps.newHashMap();
                customerVisitMonthMap = Maps.newHashMap();
                customerPotentialMonthMap = Maps.newHashMap();
            }

            // 拜访列表-季
            Map<Integer, List<VisitInfoForDailyVo>> visitQuarterMap;
            Map<Integer, List<VisitInfoForDailyVo>> visitPotentialQuarterMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerVisitQuarterMap;
            Map<Integer, List<VisitInfoForDailyVo>> partnerPotentialQuarterMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerVisitQuarterMap;
            Map<Integer, List<VisitInfoForDailyVo>> customerPotentialQuarterMap;
            List<VisitInfoForDailyVo> visitInfoQuarterList = getVisitInfoForDailyVos(request, "11", employeeInfoIdList);
            if (!CollectionUtils.isEmpty(visitInfoQuarterList)) {
                visitQuarterMap = visitInfoQuarterList.stream().collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                visitPotentialQuarterMap = visitInfoQuarterList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerVisitQuarterMap = visitInfoQuarterList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                partnerPotentialQuarterMap = visitInfoQuarterList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 1).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerVisitQuarterMap = visitInfoQuarterList.stream().filter(e -> e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));
                customerPotentialQuarterMap = visitInfoQuarterList.stream().filter(e -> e.getOpenType() != null && e.getOpenType() == 1 && e.getVisitType() != null && e.getVisitType() == 0).collect(Collectors.groupingBy(VisitInfoForDailyVo::getEmployeeInfoId));

                List<String> customerIdlist = visitInfoQuarterList.stream().map(VisitInfoForDailyVo::getCustomerId).filter(Objects::nonNull).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(customerIdlist)) {
                    List<DailyVisitCustomerVo> customerPerformanceInThreeMonList = dailyReportBTMapper.queryCustomerPerformanceInThreeMon(customerIdlist, RequestUtils.getBusinessGroup());
                    if (!CollectionUtils.isEmpty(customerPerformanceInThreeMonList)) {
                        customerPerformanceInThreeMonMap = customerPerformanceInThreeMonList.stream()
                                .filter(e -> e.getCustomerId() != null)
                                .collect(
                                        Collectors.toMap(
                                                DailyVisitCustomerVo::getCustomerId,
                                                DailyVisitCustomerVo::getPerformance,
                                                (existingValue, newValue) -> existingValue // 保留第一个出现的值
                                        )
                                );

                    }
                }
            } else {
                visitQuarterMap = Maps.newHashMap();
                visitPotentialQuarterMap = Maps.newHashMap();
                partnerVisitQuarterMap = Maps.newHashMap();
                partnerPotentialQuarterMap = Maps.newHashMap();
                customerVisitQuarterMap = Maps.newHashMap();
                customerPotentialQuarterMap = Maps.newHashMap();
            }

            Map<String, BigDecimal> finalCustomerPerformanceInThreeMonMap = customerPerformanceInThreeMonMap;
            if (!CollectionUtils.isEmpty(visitInfoDayList)) {
                visitInfoDayList.forEach(item -> {
                    item.setPerformanceInThreeMon(finalCustomerPerformanceInThreeMonMap.get(item.getCustomerId()));
                });
            }
            if (!CollectionUtils.isEmpty(visitInfoMonthList)) {
                visitInfoMonthList.forEach(item -> {
                    item.setPerformanceInThreeMon(finalCustomerPerformanceInThreeMonMap.get(item.getCustomerId()));
                });
            }
            if (!CollectionUtils.isEmpty(visitInfoQuarterList)) {
                visitInfoQuarterList.forEach(item -> {
                    item.setPerformanceInThreeMon(finalCustomerPerformanceInThreeMonMap.get(item.getCustomerId()));
                });
            }

            list.forEach(item -> {
                item.setTheYearMon(realTimeUtils.formatter("10", request.getTheDate()));
                item.setOrganizationName(organizationMap.get(item.getOrganizationId()));
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setCanLink(getCanLink(loginInfo.getOrganizationType(), personOrganizationIds, item.getAreaCode(), item.getVareaCode(), item.getProvinceCode(), item.getCompanyCode(), item.getDepartmentCode()));
                if (item.getPerformanceAchievementRateMonth().compareTo(realTimeUtils.dateMonthRate(request.getTheDate())) < 0) {
                    item.setPerformanceAchievementRateIsRedMonth(Boolean.TRUE);
                }
                if (item.getPerformanceAchievementRateQuarter().compareTo(realTimeUtils.dateQuarterRate(request.getTheDate())) < 0) {
                    item.setPerformanceAchievementRateIsRedQuarter(Boolean.TRUE);
                }
                item.setPerformanceIsShow(setPerformanceIsShow(loginInfo.getPositionTypeId(), item.getPositionTypeId()));
                item.setPerformanceBonusCombinedIsShow(setPerformanceBonusCombinedIsShow(item.getOrganizationId(), childOrganizations, personOrganizationIds, Boolean.FALSE));
                Attendance attendance = attendanceMap.get(item.getEmployeeInfoId());
                if (Objects.nonNull(attendance)) {
                    item.setAttendanceId(attendance.getId());
                    item.setAttendanceImage(attendance.getPicUrl());
                }
                List<CompleteListVo> completeVoList = completeMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(completeVoList)) {
                    item.setCompleteImages(completeVoList.stream().map(CompleteListVo::getImage).collect(Collectors.toList()));
                }
                List<MeetingInfoVO> meetingVoList = meetingMap.get(item.getEmployeeInfoId() + "#" + item.getOrganizationId());
                if (!CollectionUtils.isEmpty(meetingVoList)) {
                    item.setMeetingInfoIdDay(meetingVoList.get(0).getInfoId());
                    item.setCategoryDay(meetingVoList.get(0).getCategory());
                }
                List<SfaDailyReportEntity> dailyReportVoList = dailyReportMap.get(item.getEmployeeInfoId() + "#" + item.getOrganizationId());
                if (!CollectionUtils.isEmpty(dailyReportVoList)) {
                    item.setDailyReportIdDay(dailyReportVoList.get(0).getReportId());
                    item.setDailyReportDayDay(dailyReportVoList.get(0).getDay());
                    item.setDailyReportOrganizationIdDay(dailyReportVoList.get(0).getOrganizationId());
                }

                List<VisitInfoForDailyVo> visitDayVoList = visitDayMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> visitPotentialDayVoList = visitPotentialDayMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(visitDayVoList)) {
                    item.setAllVisitCountDay(visitDayVoList.size());
                    item.setAllVisitCountPotentialRateDay(new BigDecimal(Optional.ofNullable(visitPotentialDayVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(visitDayVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setAllVisitCountAverageTimeCostDay(getAverageTimeCost(visitDayVoList, formatter));
                    item.setAllVisitListDay(visitDayVoList);
                } else {
                    item.setAllVisitCountDay(0);
                }

                List<VisitInfoForDailyVo> partnerVisitDayVoList = partnerVisitDayMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> partnerPotentialDayVoList = partnerPotentialDayMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(partnerVisitDayVoList)) {
                    item.setPartnerVisitCountDay(partnerVisitDayVoList.size());
                    item.setPartnerVisitCountPotentialRateDay(new BigDecimal(Optional.ofNullable(partnerPotentialDayVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(partnerVisitDayVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setPartnerVisitCountAverageTimeCostDay(getAverageTimeCost(partnerVisitDayVoList, formatter));
                    item.setPartnerVisitListDay(partnerVisitDayVoList);
                } else {
                    item.setPartnerVisitCountDay(0);
                }

                List<VisitInfoForDailyVo> customerVisitDayVoList = customerVisitDayMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> customerPotentialDayVoList = customerPotentialDayMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(customerVisitDayVoList)) {
                    item.setCustomerVisitCountDay(customerVisitDayVoList.size());
                    item.setCustomerVisitCountPotentialRateDay(new BigDecimal(Optional.ofNullable(customerPotentialDayVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(customerVisitDayVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setCustomerVisitCountAverageTimeCostDay(getAverageTimeCost(customerVisitDayVoList, formatter));
                    item.setCustomerVisitListDay(customerVisitDayVoList);
                } else {
                    item.setCustomerVisitCountDay(0);
                }

                List<VisitInfoForDailyVo> visitMonthVoList = visitMonthMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> visitPotentialMonthVoList = visitPotentialMonthMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(visitMonthVoList)) {
                    item.setAllVisitCountMonth(visitMonthVoList.size());
                    item.setAllVisitCountPotentialRateMonth(new BigDecimal(Optional.ofNullable(visitPotentialMonthVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(visitMonthVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setAllVisitCountAverageTimeCostMonth(getAverageTimeCost(visitMonthVoList, formatter));
                    item.setAllVisitListMonth(visitMonthVoList);
                } else {
                    item.setAllVisitCountMonth(0);
                }

                List<VisitInfoForDailyVo> partnerVisitMonthVoList = partnerVisitMonthMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> partnerPotentialMonthVoList = partnerPotentialMonthMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(partnerVisitMonthVoList)) {
                    item.setPartnerVisitCountMonth(partnerVisitMonthVoList.size());
                    item.setPartnerVisitCountPotentialRateMonth(new BigDecimal(Optional.ofNullable(partnerPotentialMonthVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(partnerVisitMonthVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setPartnerVisitCountAverageTimeCostMonth(getAverageTimeCost(partnerVisitMonthVoList, formatter));
                    item.setPartnerVisitListMonth(partnerVisitMonthVoList);
                } else {
                    item.setPartnerVisitCountMonth(0);
                }

                List<VisitInfoForDailyVo> customerVisitMonthVoList = customerVisitMonthMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> customerPotentialMonthVoList = customerPotentialMonthMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(customerVisitMonthVoList)) {
                    item.setCustomerVisitCountMonth(customerVisitMonthVoList.size());
                    item.setCustomerVisitCountPotentialRateMonth(new BigDecimal(Optional.ofNullable(customerPotentialMonthVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(customerVisitMonthVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setCustomerVisitCountAverageTimeCostMonth(getAverageTimeCost(customerVisitMonthVoList, formatter));
                    item.setCustomerVisitListMonth(customerVisitMonthVoList);
                } else {
                    item.setCustomerVisitCountMonth(0);
                }

                List<VisitInfoForDailyVo> visitQuarterVoList = visitQuarterMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> visitPotentialQuarterVoList = visitPotentialQuarterMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(visitQuarterVoList)) {
                    item.setAllVisitCountQuarter(visitQuarterVoList.size());
                    item.setAllVisitCountPotentialRateQuarter(new BigDecimal(Optional.ofNullable(visitPotentialQuarterVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(visitQuarterVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setAllVisitCountAverageTimeCostQuarter(getAverageTimeCost(visitQuarterVoList, formatter));
                    item.setAllVisitListQuarter(visitQuarterVoList);
                } else {
                    item.setAllVisitCountQuarter(0);
                }

                List<VisitInfoForDailyVo> partnerVisitQuarterVoList = partnerVisitQuarterMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> partnerPotentialQuarterVoList = partnerPotentialQuarterMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(partnerVisitQuarterVoList)) {
                    item.setPartnerVisitCountQuarter(partnerVisitQuarterVoList.size());
                    item.setPartnerVisitCountPotentialRateQuarter(new BigDecimal(Optional.ofNullable(partnerPotentialQuarterVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(partnerVisitQuarterVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setPartnerVisitCountAverageTimeCostQuarter(getAverageTimeCost(partnerVisitQuarterVoList, formatter));
                    item.setPartnerVisitListQuarter(partnerVisitQuarterVoList);
                } else {
                    item.setPartnerVisitCountQuarter(0);
                }

                List<VisitInfoForDailyVo> customerVisitQuarterVoList = customerVisitQuarterMap.get(item.getEmployeeInfoId());
                List<VisitInfoForDailyVo> customerPotentialQuarterVoList = customerPotentialQuarterMap.get(item.getEmployeeInfoId());
                if (!CollectionUtils.isEmpty(customerVisitQuarterVoList)) {
                    item.setCustomerVisitCountQuarter(customerVisitQuarterVoList.size());
                    item.setCustomerVisitCountPotentialRateQuarter(new BigDecimal(Optional.ofNullable(customerPotentialQuarterVoList).map(List::size).orElse(0))
                            .multiply(new BigDecimal(100))
                            .divide(new BigDecimal(customerVisitQuarterVoList.size()), 1, RoundingMode.HALF_UP));
                    item.setCustomerVisitCountAverageTimeCostQuarter(getAverageTimeCost(customerVisitQuarterVoList, formatter));
                    item.setCustomerVisitListQuarter(customerVisitQuarterVoList);
                } else {
                    item.setCustomerVisitCountQuarter(0);
                }
            });
            if (StringUtils.isNotBlank(request.getSortName())) {
                Comparator<DailyPerformanceVo> comparator = null;
                switch (request.getSortName()) {
                    case "allVisitCountDay":
                        comparator = Comparator.comparing(DailyPerformanceVo::getAllVisitCountDay, Comparator.nullsFirst(Integer::compareTo));
                        break;
                    case "allVisitCountMonth":
                        comparator = Comparator.comparing(DailyPerformanceVo::getAllVisitCountMonth, Comparator.nullsFirst(Integer::compareTo));
                        break;
                    case "allVisitCountQuarter":
                        comparator = Comparator.comparing(DailyPerformanceVo::getAllVisitCountQuarter, Comparator.nullsFirst(Integer::compareTo));
                        break;
                    default:
                        break;
                }
                if (comparator != null) {
                    if (request.getSortType() != null && StringUtils.isNotBlank(request.getSortType()) && request.getSortType().equals("desc")) {
                        list.sort(comparator.reversed());
                    } else {
                        list.sort(comparator);
                    }
                }
            }
        }
        page.setRecords(list);
        return page;
    }

    private List<VisitInfoForDailyVo> getVisitInfoForDailyVos(DailyPerformancePageRequest request, String dateTypeId, List<Integer> employeeInfoIdList) {
        DailyListRequest dailyListRequest = new DailyListRequest();
        Map<String, LocalDate> theDateMap = setTheDate(StringUtils.isNotBlank(dateTypeId) ? dateTypeId : "1", request.getTheDate());
        dailyListRequest.setTheDateFrom(theDateMap.get("theDateFrom"));
        dailyListRequest.setTheDateTo(theDateMap.get("theDateTo"));
        dailyListRequest.setEmployeeInfoIdList(employeeInfoIdList);
        return customerInfoMapper.selectVisitInfoListForDaily(dailyListRequest);
    }

    private String getAverageTimeCost(List<VisitInfoForDailyVo> visitList, DateTimeFormatter formatter) {
        if (!CollectionUtils.isEmpty(visitList)) {
            long totalSeconds = visitList.stream()
                    .filter(vo -> StringUtils.isNotBlank(vo.getTimeCost()))
                    .map(vo -> LocalTime.parse(vo.getTimeCost(), formatter))
                    .map(LocalTime::toSecondOfDay)
                    .mapToLong(Long::valueOf)
                    .reduce(0L, Long::sum);
            BigDecimal avgSeconds = new BigDecimal(totalSeconds)
                    .divide(new BigDecimal(visitList.size()), 2, RoundingMode.HALF_UP);
            String unit = "秒";
            if (avgSeconds.compareTo(new BigDecimal(60)) > 0) {
                avgSeconds = avgSeconds.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                unit = "分钟";
            }
            return avgSeconds + unit;
        }
        return "-";
    }

    @Override
    public IPage<DailyOrderVo> dailyOrderList(DailyOrderPageRequest request) {
        log.info("dailyOrderList request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId()) && StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号不能都为空");
        }
        if (StringUtils.isNotBlank(request.getOrganizationId()) && StringUtils.isNotBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号只能传一个");
        }
        request.setDailyScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE)));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }
        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
            );
            if (Objects.isNull(ceoBusinessOrganizationEntity)) {
                throw new ApplicationException("组织ID不存在");
            }
            request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
        }
        if (StringUtils.isNotBlank(request.getEmployeeId())) {
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getEmployeeId())
                    .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .last("limit 1")
            );
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                throw ExceptionConstants.FAILED_BUSINESS("查无此人");
            }

            if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() != 7) {
                DailyEmployeeRequest dailyEmployeeRequest = new DailyEmployeeRequest();
                dailyEmployeeRequest.setEmployeeId(request.getEmployeeId());
                dailyEmployeeRequest.setTheDate(request.getTheDate());
                dailyEmployeeRequest.setPersonBusinessGroup(request.getPersonBusinessGroup());
                List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(dailyEmployeeRequest);
                if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
                    List<CeoBusinessOrganizationEntity> ceoBusinessOrganizationEntityList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                            .in(CeoBusinessOrganizationEntity::getOrganizationId, dailyEmployeeVoList.stream().map(DailyEmployeeVo::getOrganizationId).collect(Collectors.toList()))
                    );
                    request.setOrganizationType(ceoBusinessOrganizationEntityList.get(0).getOrganizationType());
                    request.setOrganizationIds(dailyEmployeeVoList.stream().map(DailyEmployeeVo::getOrganizationId).collect(Collectors.toList()));
                } else {
                    throw new ApplicationException("所选日期目标人员组织不存在");
                }
            }
        }

        Map<String, LocalDate> theDateMap = setTheDate(request.getDateTypeId(), request.getTheDate());
        request.setTheDateFrom(theDateMap.get("theDateFrom"));
        request.setTheDateTo(theDateMap.get("theDateTo"));
        if ("1".equals(request.getDateTypeId())) {
            request.setLabelDateTypeId("10");
        } else {
            request.setLabelDateTypeId(request.getDateTypeId());
        }
        request.setLabelTheDate(realTimeUtils.formatter(request.getLabelDateTypeId(), request.getTheDate()));
        Page<DailyOrderVo> page = new Page<>(request.getPage(), request.getRows());
        List<DailyOrderVo> list = adsOrderItemDetailMapper.queryDailyOrderList(page, request);

        if (!CollectionUtils.isEmpty(list)) {
            List<String> organizationIdList = list.stream().map(DailyOrderVo::getOrganizationId).collect(Collectors.toList());
            Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));

            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));

            list.forEach(item -> {
                if (StringUtils.isNotBlank(item.getCode())) {
                    item.setCodes(Arrays.asList(item.getCode().replace(" ", "").split(",")));
                }
                item.setOrganizationName(organizationMap.get(item.getOrganizationId()));
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setCanLink(getCanLink(loginInfo.getOrganizationType(), personOrganizationIds, item.getAreaCode(), item.getVareaCode(), item.getProvinceCode(), item.getCompanyCode(), item.getDepartmentCode()));
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<DailySkuVo> dailySkuList(DailySkuPageRequest request) {
        log.info("dailySkuList request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId()) && StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号不能都为空");
        }
        if (StringUtils.isNotBlank(request.getOrganizationId()) && StringUtils.isNotBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号只能传一个");
        }
        request.setDailyScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE)));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }

        Page<DailySkuVo> page = new Page<>(request.getPage(), request.getRows());
        if (StringUtils.isNotBlank(request.getEmployeeId())) {
            request.setChildOrganizations(getChildOrganizations(request.getTheDate(), request.getEmployeeId(), request.getPersonBusinessGroup()));
            if (CollectionUtils.isEmpty(request.getChildOrganizations())) {
                return page;
            }
        }
        List<DailySkuVo> list;
        if ("1".equals(request.getDateTypeId())) {
            list = adsBigtableOrganizationDailyMeetingsSkuMapper.queryDailySkuList(page, request);
        } else {
            request.setYearMonth(realTimeUtils.formatter(request.getDateTypeId(), request.getTheDate()));
            list = adsBigtableOrganizationSkuMapper.queryDailySkuList(page, request);
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public DailySkuCrossVo dailySkuCross(DailySkuPageRequest request) {
        log.info("dailySkuCross request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId()) && StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号不能都为空");
        }
        if (StringUtils.isNotBlank(request.getOrganizationId()) && StringUtils.isNotBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号只能传一个");
        }
        request.setDailyScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE)));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }

        DailySkuCrossVo dailySkuCrossVo = new DailySkuCrossVo();
        if (StringUtils.isNotBlank(request.getEmployeeId())) {
            request.setChildOrganizations(getChildOrganizations(request.getTheDate(), request.getEmployeeId(), request.getPersonBusinessGroup()));
            if (CollectionUtils.isEmpty(request.getChildOrganizations())) {
                return dailySkuCrossVo;
            }
        }
        List<DailySkuVo> list;
        if ("1".equals(request.getDateTypeId())) {
            list = adsBigtableOrganizationDailyMeetingsSkuMapper.queryDailySkuCrossList(request);
        } else {
            request.setYearMonth(realTimeUtils.formatter(request.getDateTypeId(), request.getTheDate()));
            list = adsBigtableOrganizationSkuMapper.queryDailySkuCrossList(request);
        }
        if (!CollectionUtils.isEmpty(list)) {

            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));

            List<DailySkuVo> dailySkuCrossList = new ArrayList<>();
            Map<String, List<DailySkuVo>> skuIdDailySkuMap = list.stream().collect(Collectors.groupingBy(DailySkuVo::getProductId));
            Map<String, List<DailySkuVo>> organizationIdDailySkuMap = list.stream().collect(Collectors.groupingBy(DailySkuVo::getOrganizationId));
            Set<String> skuIdSet = list.stream().map(DailySkuVo::getProductId).collect(Collectors.toSet());
            Set<String> organizationIdSet = list.stream().map(DailySkuVo::getOrganizationId).collect(Collectors.toSet());
            organizationIdSet.forEach(organizationId -> {
                List<DailySkuVo> dailySkuVoList = organizationIdDailySkuMap.get(organizationId);
                dailySkuCrossList.addAll(dailySkuVoList);
                Set<String> organizationIdSkuIdSet = dailySkuVoList.stream().map(DailySkuVo::getProductId).collect(Collectors.toSet());
                DailySkuVo organization = dailySkuVoList.get(0);
                skuIdSet.forEach(skuId -> {
                    if (!organizationIdSkuIdSet.contains(skuId)) {
                        DailySkuVo sku = skuIdDailySkuMap.get(skuId).get(0);
                        DailySkuVo vo = new DailySkuVo();
                        BeanUtils.copyProperties(organization, vo);
                        vo.setProductId(sku.getProductId());
                        vo.setLineName(sku.getLineName());
                        vo.setSpuName(sku.getSpuName());
                        vo.setSkuImages(sku.getSkuImages());
                        vo.setSkuId(skuId);
                        vo.setSkuName(sku.getSkuName());
                        vo.setSkuSpec(sku.getSkuSpec());
                        vo.setFlavor(sku.getFlavor());
                        vo.setSkuNameSpecFlavor(sku.getSkuNameSpecFlavor());
                        vo.setPerformancePerSku(null);
                        vo.setCurrentBookingPerformance(null);
                        vo.setNormalOrderPerformance(null);
                        vo.setSpecialOrderPerformance(null);
                        vo.setGoldenCoinPerformance(null);
                        vo.setGoldenCoinPerformanceRate(null);
                        vo.setTradingCustomerNumSku(null);
                        vo.setPerCustomerSku(null);
                        dailySkuCrossList.add(vo);
                    }
                });
            });
            Map<String, List<DailySkuVo>> organizationMap = dailySkuCrossList.stream().collect(Collectors.groupingBy(DailySkuVo::getOrganizationId));
            List<DailySkuCrossListVo> dailySkuCrossVoList = new ArrayList<>();
            organizationMap.forEach((organizationId, dailySkuVoList) -> {
                dailySkuVoList.sort(Comparator.comparing(DailySkuVo::getProductId));
                DailySkuCrossListVo dailySkuCrossListVo = new DailySkuCrossListVo();
                DailySkuVo organization = dailySkuVoList.get(0);
                BeanUtils.copyProperties(organization, dailySkuCrossListVo);
                dailySkuCrossListVo.setPositionTypeName(positionTypeMap.get(dailySkuCrossListVo.getPositionTypeId()));
                dailySkuCrossListVo.setCanLink(getCanLink(loginInfo.getOrganizationType(), personOrganizationIds, dailySkuCrossListVo.getAreaCode(), dailySkuCrossListVo.getVareaCode(), dailySkuCrossListVo.getProvinceCode(), dailySkuCrossListVo.getCompanyCode(), dailySkuCrossListVo.getDepartmentCode()));
                dailySkuCrossListVo.setDailySkuVoList(dailySkuVoList);
                dailySkuCrossVoList.add(dailySkuCrossListVo);
            });
            dailySkuCrossVoList.sort(Comparator.comparing(DailySkuCrossListVo::getOrganizationId));
            dailySkuCrossVo.setDailySkuCrossVoList(dailySkuCrossVoList);
        }
        return dailySkuCrossVo;
    }

    @Override
    public IPage<DailyEmployeeVo> dailyNewCustomerList(DailyNewCustomerPageRequest request) {
        log.info("dailyNewCustomerList request:{}", request);
        if (StringUtils.isBlank(request.getOrganizationId()) && StringUtils.isBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号不能都为空");
        }
        if (StringUtils.isNotBlank(request.getOrganizationId()) && StringUtils.isNotBlank(request.getEmployeeId())) {
            throw new ApplicationException("组织ID、目标人员工号只能传一个");
        }
        request.setDailyScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.DAILY_SCALE)));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }
        if (StringUtils.isNotBlank(request.getOrganizationId())) {
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
            );
            if (Objects.isNull(ceoBusinessOrganizationEntity)) {
                throw new ApplicationException("组织ID不存在");
            }
            request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
        }
        if (StringUtils.isNotBlank(request.getEmployeeId())) {
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getEmployeeId())
                    .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                    .last("limit 1")
            );
            if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
                throw ExceptionConstants.FAILED_BUSINESS("查无此人");
            }

            if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() != 7) {
                DailyEmployeeRequest dailyEmployeeRequest = new DailyEmployeeRequest();
                dailyEmployeeRequest.setEmployeeId(request.getEmployeeId());
                dailyEmployeeRequest.setTheDate(request.getTheDate());
                dailyEmployeeRequest.setPersonBusinessGroup(request.getPersonBusinessGroup());
                List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(dailyEmployeeRequest);
                if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
                    List<CeoBusinessOrganizationEntity> ceoBusinessOrganizationEntityList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                            .in(CeoBusinessOrganizationEntity::getOrganizationId, dailyEmployeeVoList.stream().map(DailyEmployeeVo::getOrganizationId).collect(Collectors.toList()))
                    );
                    request.setOrganizationType(ceoBusinessOrganizationEntityList.get(0).getOrganizationType());
                    request.setOrganizationIds(dailyEmployeeVoList.stream().map(DailyEmployeeVo::getOrganizationId).collect(Collectors.toList()));
                } else {
                    throw new ApplicationException("所选日期目标人员组织不存在");
                }
            }
        }

        Page<DailyEmployeeVo> page = new Page<>(request.getPage(), request.getRows());
        //非周报使用时时间处理
        if (!"8".equals(request.getDateTypeId())) {
            Map<String, LocalDate> theDateMap = setTheDate(request.getDateTypeId(), request.getTheDate());
            request.setTheDateFrom(theDateMap.get("theDateFrom"));
            request.setTheDateTo(theDateMap.get("theDateTo"));
        }
        request.setQuarterMonths(realTimeUtils.quarterMonths(request.getTheDate()));
        List<DailyEmployeeVo> list = dimEmpPosRoleOrgDayMapper.queryDailyNewCustomer(page, request);
        if (!CollectionUtils.isEmpty(list)) {
            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            list.forEach(item -> {
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setCanLink(getCanLink(loginInfo.getOrganizationType(), personOrganizationIds, item.getAreaCode(), item.getVareaCode(), item.getProvinceCode(), item.getCompanyCode(), item.getDepartmentCode()));
            });
            List<DailyMemberVo> memberList = ceoMapper.queryMemberList(list.stream().map(DailyEmployeeVo::getMemberKey).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(memberList)) {
                Map<Long, List<DailyMemberVo>> memberMap = memberList.stream().collect(Collectors.groupingBy(DailyMemberVo::getMemberKey));
                list.forEach(item -> {
                    if (!CollectionUtils.isEmpty(memberMap.get(item.getMemberKey()))) {
                        DailyMemberVo member = memberMap.get(item.getMemberKey()).get(0);
                        if (Objects.nonNull(member)) {
                            item.setSubmitTime(member.getSubmitTime());
                            item.setWorkDate(member.getWorkDate());
                            item.setDoubleCheckStatusName(member.getDoubleCheckStatusName());
                            item.setCheckAbnormalType(member.getCheckAbnormalType());
                            item.setReviewDuration(member.getReviewDuration());
                        }
                    }
                });
            }
        }

        page.setRecords(list);
        return page;
    }

    @Override
    public DailyInventoryCrossVo dailyInventoryCross(DailyInventoryRequest request) {
        log.info("dailyInventoryCross request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }

        // 返回对象
        DailyInventoryCrossVo dailyInventoryCrossVo = new DailyInventoryCrossVo();

        CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
        );
        if (Objects.isNull(ceoBusinessOrganizationEntity)) {
            throw new ApplicationException("组织ID不存在");
        }
        if (!OrganizationTypeEnum.VARE.getOrganizationType().equals(ceoBusinessOrganizationEntity.getOrganizationType())
                && !OrganizationTypeEnum.COMPANY.getOrganizationType().equals(ceoBusinessOrganizationEntity.getOrganizationType())) {
//            throw new ApplicationException("无权限");
            return dailyInventoryCrossVo;
        }
        request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());

        if (OrganizationTypeEnum.COMPANY.getOrganizationType().equals(request.getOrganizationType())) {
            CeoBusinessOrganizationTreeEntity ceoBusinessOrganizationTreeEntity = ceoBusinessOrganizationTreeMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationTreeEntity>()
                    .eq(CeoBusinessOrganizationTreeEntity::getOrganizationId, request.getOrganizationId())
                    .eq(CeoBusinessOrganizationTreeEntity::getLevel, 2)
            );
            request.setVareaCode(ceoBusinessOrganizationTreeEntity.getOrganizationParentId());
        }
        if (OrganizationTypeEnum.VARE.getOrganizationType().equals(request.getOrganizationType())) {
            request.setVareaCode(request.getOrganizationId());
        }
//        List<CeoBusinessOrganizationTreeEntity> ceoBusinessOrganizationTreeEntityList = ceoBusinessOrganizationTreeMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationTreeEntity>()
//                .eq(CeoBusinessOrganizationTreeEntity::getOrganizationParentId, request.getVareaCode())
//                .eq(CeoBusinessOrganizationTreeEntity::getLevel, 2)
//        );
//        request.setCompanyCodes(ceoBusinessOrganizationTreeEntityList.stream().map(CeoBusinessOrganizationTreeEntity::getOrganizationId).collect(Collectors.toList()));


        // 获取各分公司库存数据
        List<AdsProductsaleRealtimeInventoryDaily> companyInventoryList = adsProductsaleRealtimeInventoryDailyMapper.selectList(new LambdaQueryWrapper<AdsProductsaleRealtimeInventoryDaily>()
                .eq(AdsProductsaleRealtimeInventoryDaily::getVareaId, request.getVareaCode())
                .eq(AdsProductsaleRealtimeInventoryDaily::getOrganizationType, "company")
                .eq(AdsProductsaleRealtimeInventoryDaily::getTheYearMonth, request.getTheDate())
        );

        if (!CollectionUtils.isEmpty(companyInventoryList)) {
            request.setCompanyCodes(new ArrayList<>(companyInventoryList.stream().map(AdsProductsaleRealtimeInventoryDaily::getOrganizationId).collect(Collectors.toSet())));
            Map<String, BigDecimal> canbeShipmentBoxesMap = new HashMap<>();
            request.setTheYearMon(realTimeUtils.formatter("10", request.getTheDate()));
            List<AdsProductsaleRealtimeInventoryDaily> canbeShipmentBoxesList = adsProductsaleRealtimeInventoryDailyMapper.queryCanbeShipmentBoxesList(request);
            if (!CollectionUtils.isEmpty(canbeShipmentBoxesList)) {
                canbeShipmentBoxesMap = canbeShipmentBoxesList.stream().collect(Collectors.toMap(x -> x.getOrganizationId() + "#" + x.getChannelId() + "#" + x.getSkuId(), AdsProductsaleRealtimeInventoryDaily::getCanbeShipmentBoxes));
            }

            Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, request.getCompanyCodes())
            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));

            List<DailyInventorySkuChannelListVo> dailyInventorySkuChannelList = new ArrayList<>();

            Map<String, BigDecimal> finalCanbeShipmentBoxesMap = canbeShipmentBoxesMap;
            Map<String, List<AdsProductsaleRealtimeInventoryDaily>> skuChannelMap = companyInventoryList.stream().collect(Collectors.groupingBy(x -> x.getChannelId() + "#" + x.getSkuId()));
            skuChannelMap.forEach((skuChannel, skuChannelList) -> {
                DailyInventorySkuChannelListVo dailyInventorySkuChannelListVo = new DailyInventorySkuChannelListVo();
                BeanUtils.copyProperties(skuChannelList.get(0), dailyInventorySkuChannelListVo);

//                Map<String, List<AdsProductsaleRealtimeInventoryDaily>> companyMap = skuChannelList.stream().collect(Collectors.groupingBy(AdsProductsaleRealtimeInventoryDaily::getOrganizationId));

                List<DailyInventoryCompanyVo> dailyInventoryCompanyList = new ArrayList<>();
                request.getCompanyCodes().forEach(companyCode -> {
                    DailyInventoryCompanyVo dailyInventoryCompanyVo = new DailyInventoryCompanyVo();
                    dailyInventoryCompanyVo.setOrganizationId(companyCode);
                    dailyInventoryCompanyVo.setOrganizationName(organizationMap.get(companyCode));
                    dailyInventoryCompanyVo.setCanbeShipmentBoxes(finalCanbeShipmentBoxesMap.get(companyCode + "#" + skuChannel));
//                    List<AdsProductsaleRealtimeInventoryDaily> companyList = companyMap.get(companyCode);
//                    if (!CollectionUtils.isEmpty(companyList)) {
//                        AdsProductsaleRealtimeInventoryDaily company = companyList.get(0);
//                        dailyInventoryCompanyVo.setCanbeShipmentBoxes(company.getCanbeShipmentBoxes());
//                    }
                    dailyInventoryCompanyList.add(dailyInventoryCompanyVo);
                });
                dailyInventoryCompanyList.sort(Comparator.comparing(DailyInventoryCompanyVo::getOrganizationId));
                dailyInventorySkuChannelListVo.setDailyInventoryCompanyVoList(dailyInventoryCompanyList);
                dailyInventorySkuChannelList.add(dailyInventorySkuChannelListVo);
            });
            dailyInventoryCrossVo.setDailyInventorySkuChannelListVo(dailyInventorySkuChannelList);
        }
        return dailyInventoryCrossVo;
    }

    private void setChild(List<String> childOrganizations, DailyEmployeeInfoVo dailyEmployeeInfoVo) {
        if (!CollectionUtils.isEmpty(childOrganizations)) {
            List<CeoBusinessOrganizationEntity> childOrganizationList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, childOrganizations)
            );
            childOrganizations.sort(Collections.reverseOrder());
            dailyEmployeeInfoVo.setChildOrganizations(childOrganizations);
            List<DailyEmployeeOrganizationTypeVo> childOrganizationTypes = new ArrayList<>();
            childOrganizationList.stream().map(CeoBusinessOrganizationEntity::getOrganizationType).collect(Collectors.toSet()).forEach(type -> {
                DailyEmployeeOrganizationTypeVo organizationTypeVo = new DailyEmployeeOrganizationTypeVo();
                organizationTypeVo.setOrganizationType(type);
                organizationTypeVo.setOrganizationTypeName(OrganizationTypeEnum.getPositionName(type));
                organizationTypeVo.setSearchType(OrganizationTypeEnum.getSearchType(type));
                childOrganizationTypes.add(organizationTypeVo);
            });
            dailyEmployeeInfoVo.setChildOrganizationTypes(childOrganizationTypes.stream().sorted(Comparator.comparing(DailyEmployeeOrganizationTypeVo::getOrganizationType,
                    Comparator.comparing(x -> new BigDecimal(OrganizationTypeEnum.getOrder(x))))).collect(Collectors.toList()));
        }
    }

    private List<String> getChildOrganizations(LocalDate theDate, String employeeId, Integer personBusinessGroup) {

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, employeeId)
                .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, personBusinessGroup)
                .last("limit 1")
        );
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw ExceptionConstants.FAILED_BUSINESS("查无此人");
        }

        List<String> childOrganizations = new ArrayList<>();
        if (ceoBusinessOrganizationPositionRelation.getPositionTypeId() == 7) {
            EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectZbEmployee(employeeId, personBusinessGroup);
            if (Objects.nonNull(employeeInfo)) {
                List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + employeeInfo.getOrganizationId());
                if (!CollectionUtils.isEmpty(nextOrgCode)) {
                    childOrganizations.addAll(nextOrgCode);
                }
            }
        } else {
            childOrganizations = setChildOrganizations(theDate, employeeId, personBusinessGroup);
        }
        return childOrganizations;
    }

    private List<String> setChildOrganizations(LocalDate theDate, String employeeId, Integer personBusinessGroup) {
        List<String> childOrganizations = new ArrayList<>();
        DailyEmployeeRequest dailyEmployeeRequest = new DailyEmployeeRequest();
        dailyEmployeeRequest.setEmployeeId(employeeId);
        dailyEmployeeRequest.setTheDate(theDate);
        dailyEmployeeRequest.setPersonBusinessGroup(personBusinessGroup);
        List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(dailyEmployeeRequest);
        if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
            dailyEmployeeVoList.forEach(detail -> {
                List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + detail.getOrganizationId());
                if (!CollectionUtils.isEmpty(nextOrgCode)) {
                    childOrganizations.addAll(nextOrgCode);
                }
            });
        }
        return childOrganizations;
    }

    private Boolean setPerformanceIsShow(Integer personPositionTypeId, Long positionTypeId) {
        if (Objects.isNull(personPositionTypeId) || Objects.isNull(positionTypeId)) {
            return Boolean.FALSE;
        }
        // 判断登录人的岗位类型，是否显示业绩目标
        switch (personPositionTypeId) {
            case 7:
                return Boolean.TRUE;
            case 1:
                if (positionTypeId == 7) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            case 12:
                if (positionTypeId == 7 || positionTypeId == 1) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            case 11:
                if (positionTypeId == 7 || positionTypeId == 1 || positionTypeId == 12) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            case 2:
                if (positionTypeId == 7 || positionTypeId == 1 || positionTypeId == 12 || positionTypeId == 11) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            case 10:
                if (positionTypeId == 7 || positionTypeId == 1 || positionTypeId == 12 || positionTypeId == 11 || positionTypeId == 2) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            default:
                return Boolean.FALSE;
        }
    }

    private Boolean setPerformanceBonusCombinedIsShow(String organizationId, List<String> childOrganizations, List<String> personOrganizationIds, Boolean isSelf) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            if (!CollectionUtils.isEmpty(childOrganizations) && childOrganizations.contains(organizationId)) {
                return Boolean.TRUE;
            } else if (!CollectionUtils.isEmpty(personOrganizationIds) && personOrganizationIds.contains(organizationId)) {
                return Boolean.TRUE;
            } else {
                return Boolean.FALSE;
            }
        } else {
            return !isSelf;
        }
    }

    private Boolean getCanLink(String personOrganizationType, List<String> personOrganizationIds, String areaCode, String vareaCode, String provinceCode, String companyCode, String departmentCode) {
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            return Boolean.FALSE;
        }
        if (OrganizationTypeEnum.ZB.getOrganizationType().equals(personOrganizationType)) {
            return Boolean.TRUE;
        } else if (OrganizationTypeEnum.AREA.getOrganizationType().equals(personOrganizationType) && personOrganizationIds.contains(areaCode)) {
            return Boolean.TRUE;
        } else if (OrganizationTypeEnum.VARE.getOrganizationType().equals(personOrganizationType) && personOrganizationIds.contains(vareaCode)) {
            return Boolean.TRUE;
        } else if (OrganizationTypeEnum.PROVINCE.getOrganizationType().equals(personOrganizationType) && personOrganizationIds.contains(provinceCode)) {
            return Boolean.TRUE;
        } else if (OrganizationTypeEnum.COMPANY.getOrganizationType().equals(personOrganizationType) && personOrganizationIds.contains(companyCode)) {
            return Boolean.TRUE;
        } else if (OrganizationTypeEnum.DEPARTMENT.getOrganizationType().equals(personOrganizationType) && personOrganizationIds.contains(departmentCode)) {
            return Boolean.TRUE;
        } else {
            return Boolean.FALSE;
        }
    }

    private Map<String, LocalDate> setTheDate(String dateTypeId, LocalDate theDate) {
        Map<String, LocalDate> theDateMap = new HashMap<>();
        if ("1".equals(dateTypeId)) {
            theDateMap.put("theDateFrom", theDate);
            theDateMap.put("theDateTo", theDate);
        } else if ("10".equals(dateTypeId)) {
            theDateMap.put("theDateFrom", theDate.with(TemporalAdjusters.firstDayOfMonth()));
            theDateMap.put("theDateTo", theDate.with(TemporalAdjusters.lastDayOfMonth()));
        } else if ("11".equals(dateTypeId)) {
            theDateMap.put("theDateFrom", theDate.withMonth((theDate.getMonthValue() - 1) / 3 * 3 + 1).with(TemporalAdjusters.firstDayOfMonth()));
            theDateMap.put("theDateTo", theDate.withMonth(((theDate.getMonthValue() - 1) / 3 + 1) * 3).with(TemporalAdjusters.lastDayOfMonth()));
        }
        return theDateMap;
    }

}
