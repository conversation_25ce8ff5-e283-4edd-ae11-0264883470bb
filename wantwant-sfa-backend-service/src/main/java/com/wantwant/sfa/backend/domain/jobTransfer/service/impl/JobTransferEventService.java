package com.wantwant.sfa.backend.domain.jobTransfer.service.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.wantwant.sfa.backend.arch.service.impl.AccountService;
import com.wantwant.sfa.backend.domain.emp.DO.EstablishSearchDO;
import com.wantwant.sfa.backend.domain.emp.repository.po.BusinessBdConfigPO;
import com.wantwant.sfa.backend.domain.emp.service.IBusinessBDRuleService;
import com.wantwant.sfa.backend.domain.jobTransfer.service.selector.JobTransferSelector;
import com.wantwant.sfa.backend.domain.jobTransfer.DO.*;
import com.wantwant.sfa.backend.domain.recruit.service.IRecruitService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.interview.service.ICheckInterviewService;
import com.wantwant.sfa.backend.interview.service.InterviewService;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.marketAndPersonnel.EmployeeSalaryStructureMapper;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryStructurePO;
import com.wantwant.sfa.backend.salary.service.ISalaryMiddlewareService;
import com.wantwant.sfa.backend.salary.service.impl.SalaryBigTableService;
import com.wantwant.sfa.backend.service.EmployeeSalaryService;
import org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.jobTransfer.constants.JobTransferConstants;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.ChangeColumnEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.enums.JobTransferErrEnum;
import com.wantwant.sfa.backend.domain.jobTransfer.repository.facade.IJobTransferRepository;
import com.wantwant.sfa.backend.domain.jobTransfer.service.IJobTransferEventService;
import com.wantwant.sfa.backend.domain.jobTransfer.service.factory.JobTransferFactory;
import com.wantwant.sfa.backend.interview.entity.SfaJobPositionTask;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.model.marketAndPersonnel.EmployeeSalaryPO;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionActionEntity;
import com.wantwant.sfa.backend.transaction.entity.SfaTransactionApplyEntity;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.domain.flow.service.impl.GradeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/07/31/下午2:51
 */
@Service
@Slf4j
public class JobTransferEventService implements IJobTransferEventService {
    @Resource
    private IJobTransferRepository jobTransferRepository;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private JobTransferSelector jobTransferSelector;
    @Resource
    private AccountService accountService;
    @Resource
    private EmployeeSalaryService employeeSalaryService;
    @Resource
    private IBusinessBDRuleService businessBDRuleService;
    @Resource
    private ISalaryMiddlewareService salaryMiddlewareService;
    @Resource
    private EmployeeSalaryStructureMapper employeeSalaryStructureMapper;
    @Resource
    private InterviewService interviewService;
    @Resource
    private IRecruitService recruitService;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private SalaryBigTableService salaryBigTableService;
    @Resource
    private ConfigMapper configMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private ICheckInterviewService checkInterviewService;


    @Override
    @Transactional
    public JobTransferDO saveTransactionApply(JobTransferDO jobTransferDO, ProcessUserDO processUserDO) {

        List<Integer> businessGroups = jobTransferRepository.selectBusinessGroupByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(businessGroups.size() > 1){
            throw new ApplicationException(JobTransferErrEnum.NOT_SUPPORT_MULTIPLE_BUSINESS_GROUP.getMsg());
        }

        // 设置产品组，如改变了产品组，则使用改变后的产品组信息
        ChangeBusinessGroupDO changeBusinessGroupDO = jobTransferDO.getChangeBusinessGroupDO();
        if(Objects.nonNull(changeBusinessGroupDO)){
            jobTransferDO.setBusinessGroup(changeBusinessGroupDO.getBusinessGroup());
        }else{
            jobTransferDO.setBusinessGroup(RequestUtils.getBusinessGroup());
        }

        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }
        // 异动前岗位
        PositionEnum oldPosition = PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        jobTransferDO.setOldPosition(oldPosition);
        // 异动后岗位
        jobTransferDO.initTransactionPosition();

        SfaTransactionApplyEntity transactionApplyEntity = JobTransferFactory.build(jobTransferDO, processUserDO);
        jobTransferRepository.saveTransactionApply(transactionApplyEntity);

        jobTransferDO.setTransactionId(transactionApplyEntity.getId());
        return jobTransferDO;
    }

    @Override
    @Transactional
    public void saveAction(JobTransferDO jobTransferDO) {
        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(jobTransferDO.getType());
        if(Objects.isNull(jobTransfer)){
            throw new ApplicationException(JobTransferErrEnum.NOT_SUPPORT_TRANSFER.getMsg());
        }

        Integer employeeInfoId = jobTransferDO.getEmployeeInfoId();
        SfaEmployeeInfoModel sfaEmployeeInfoModel = null;
        if(Objects.nonNull(employeeInfoId)){
            sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(jobTransferDO.getEmployeeInfoId());
        }

        switch (jobTransfer){
                // 记录更改产品组
            case CHANGE_BUSINESS_GROUP:
                saveChangeBusinessGroupAction(jobTransferDO,sfaEmployeeInfoModel);
                break;
            case CHANGE_MAIN_POSITION:
                saveChangeMainPosition(jobTransferDO,sfaEmployeeInfoModel);
                break;
            case CHANGE_SALARY:
                saveChangeSalary(jobTransferDO,null);
                break;
            case CHANGE_PART_TIME_POSITION:
                savePartTImePosition(jobTransferDO,sfaEmployeeInfoModel);
                break;
                // 签入/
            case CHANGE_COMPANY_WANT_WANT:
                saveCompany(jobTransferDO);
                break;
            case CHANGE_COMPANY:
                // 签出旺旺申请时不做保存
                break;
            case CHANGE_SERVER_OBJ:
                saveChangeServerObj(jobTransferDO);
                break;
            case CHANGE_BD_POSITION:
                saveChangeBDPosition(jobTransferDO);
                break;
            default: throw new ApplicationException(JobTransferErrEnum.NOT_SUPPORT_TRANSFER.getMsg());
        }
    }

    private void saveChangeBDPosition(JobTransferDO jobTransferDO) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }

        PositionEnum oldPosition = PositionEnum.getEnum(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
        PositionEnum transactionPosition = PositionEnum.getEnumById(jobTransferDO.getChangeBusinessBDType().getId());

        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.CEO_TYPE.getColumn(), String.valueOf(oldPosition.getCeoType()),String.valueOf(transactionPosition.getCeoType())));
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.POSITION.getColumn(), String.valueOf(oldPosition.getPosition()),String.valueOf(transactionPosition.getPosition())));
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.JOBS_TYPE.getColumn(), String.valueOf(oldPosition.getJobsType()),String.valueOf(transactionPosition.getJobsType())));

        if(Objects.equals(oldPosition.getJobsType(), transactionPosition.getJobsType()) && transactionPosition.getJobsType() == 2){
            SfaTransactionApplyEntity sfaTransactionApplyEntity = jobTransferRepository.selectTransactionApplyById(jobTransferDO.getTransactionId());
            sfaTransactionApplyEntity.setAdviceExecuteDate(LocalDate.now());
            jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);
        }

        ChangeSalaryDO changeSalaryDO = jobTransferDO.getChangeSalaryDO();
        // 承揽BD需要判断是否有选择薪资支付方式
        if(transactionPosition.getId().equals(PositionEnum.BUSINESS_BD_CONTRACT.getId())){
            if(Objects.isNull(changeSalaryDO) || Objects.isNull(changeSalaryDO.getPaymentType())){
                throw new ApplicationException("请选择薪资支付方式");
            }
        }

        // 全职检查编制数
        if((transactionPosition.getId().equals(PositionEnum.BUSINESS_BD.getId()) || transactionPosition.getId().equals(PositionEnum.BUSINESS_BD_CONTRACT.getId()))){

            String serverEmpIds = jobTransferRepository.selectOldServerObj(jobTransferDO.getEmployeeInfoId());
            if(StringUtils.isNotBlank(serverEmpIds)){
                String memberKey = Arrays.stream(serverEmpIds.split(",")).findFirst().get();

                String theYearMonth = LocalDate.now().toString().substring(0,7);

                Integer employeeInfoId = jobTransferRepository.selectEmployeeInfoIdByMemberKey(memberKey);

                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(jobTransferDO.getEmployeeInfoId());
                // 更具组织选择编织检查模式
                BusinessBdConfigPO businessBdConfigPO = businessBDRuleService.getConfig(sfaEmployeeInfoModel.getDepartmentCode());
                if(Objects.isNull(businessBdConfigPO)){
                    throw new ApplicationException("当前组织未配置规则");
                }

                BigDecimal newSalary = BigDecimal.ZERO;
                if(Objects.nonNull(changeSalaryDO) && Objects.nonNull(changeSalaryDO.getId())){
                    newSalary = employeeSalaryService.selectTotalSalaryById(changeSalaryDO.getId());
                }

                BigDecimal oldSalary = BigDecimal.ZERO;
                EmployeeSalaryPO employeeSalaryPO = Optional.ofNullable(jobTransferRepository.getSalaryIdByEmployeeInfoId(jobTransferDO.getEmployeeInfoId())).orElse(new EmployeeSalaryPO());
                BigDecimal employeeBaseSalary = Optional.ofNullable(employeeSalaryPO.getEmployeeBaseSalary()).orElse(BigDecimal.ZERO);
                BigDecimal employeeAllowance = Optional.ofNullable(employeeSalaryPO.getEmployeeAllowance()).orElse(BigDecimal.ZERO);
                BigDecimal socialSecurityBase = Optional.ofNullable(employeeSalaryPO.getSocialSecurityBase()).orElse(BigDecimal.ZERO);
                BigDecimal employeeBonus = Optional.ofNullable(employeeSalaryPO.getEmployeeBonus()).orElse(BigDecimal.ZERO);
                BigDecimal travelExpenses = Optional.ofNullable(employeeSalaryPO.getTravelExpenses()).orElse(BigDecimal.ZERO);
                BigDecimal fullRiskFee = Optional.ofNullable(employeeSalaryPO.getFullRiskFee()).orElse(BigDecimal.ZERO);
                oldSalary = employeeBaseSalary.add(employeeAllowance).add(socialSecurityBase)
                        .add(employeeBonus).add(travelExpenses).add(fullRiskFee);

                EstablishSearchDO establishSearch = EstablishSearchDO.builder()
                        .serverEmployeeInfoId(employeeInfoId)
                        .applyId(applyMemberPo.getId())
                        .positionEnum(transactionPosition)
                        .theYearMonth(theYearMonth)
                        .oldPositionEnum(oldPosition)
                        .businessGroup(applyMemberPo.getBusinessGroup())
                        .changeServer(false)
                        .oldSalary(oldSalary)
                        .newSalary(newSalary)
                        .departmentId(applyMemberPo.getBranchOrganizationId())
                        .checkType(businessBdConfigPO.getCheckType()).build();
                businessBDRuleService.checkOvershootEstablished(establishSearch);

            }else{
                throw new ApplicationException("无服务对象");
            }


        }


        if(Objects.nonNull(changeSalaryDO)){
            // 记录薪资变动
            EmployeeSalaryPO employeeSalaryPO = Optional.ofNullable(jobTransferRepository.getSalaryIdByEmployeeInfoId(jobTransferDO.getEmployeeInfoId())).orElse(new EmployeeSalaryPO());
            String oldSalaryId = String.valueOf(Optional.ofNullable(employeeSalaryPO.getStructureId()).orElse(0));

            jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.SALARY.getColumn(), String.valueOf(oldSalaryId),String.valueOf(changeSalaryDO.getId())));

            if(Objects.nonNull(changeSalaryDO.getPaymentType())){
                String paymentType = Objects.isNull(employeeSalaryPO.getPaymentType())?null:String.valueOf(employeeSalaryPO.getPaymentType());

                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PAYMENT_TYPE.getColumn(), paymentType,String.valueOf(changeSalaryDO.getPaymentType())));

                // 保存薪资中间表
                salaryMiddlewareService.addSalaryMiddleware(applyMemberPo.getId(),2,null,changeSalaryDO.getId());
            }
        }

    }

    private void saveChangeServerObj(JobTransferDO jobTransferDO) {
        ChangeServerObjDO changeServerObjDO = jobTransferDO.getChangeServerObj();
        List<Long> memberKeys = changeServerObjDO.getMemberKeys();
        if(CollectionUtils.isEmpty(memberKeys)){
            throw new ApplicationException(JobTransferErrEnum.SERVER_OBJ_EMPTY.getMsg());
        }
        // 异动后服务对象
        String transactionServerObj = String.join(",", memberKeys.stream().map(String::valueOf).collect(Collectors.toList()));
        // 获取异动前服务对象
        String oldServerObj = jobTransferRepository.selectOldServerObj(jobTransferDO.getEmployeeInfoId());

        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.SERVER_OBJ.getColumn(), oldServerObj,transactionServerObj));
    }

    @Override
    @Transactional
    public void postProcessing(Long transactionId, ProcessUserDO processUserDO) {
        // 获取异动类型
        SfaTransactionApplyEntity sfaTransactionApplyEntity = jobTransferRepository.selectTransactionApplyById(transactionId);
        if(Objects.isNull(sfaTransactionApplyEntity)){
            throw new ApplicationException(JobTransferErrEnum.TRANSACTION_APPLY_NOT_EXIST.getMsg());
        }

        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(sfaTransactionApplyEntity.getTransactionType());

        // 检查当前流程是否是完结状态
        Integer processResult = jobTransferRepository.getProcessResult(transactionId);
        if(Objects.isNull(processResult) || processResult != 1){
            return;
        }

        switch (jobTransfer){
            case CHANGE_BD_POSITION:
                List<SfaTransactionActionEntity> sfaTransactionActionEntities = jobTransferRepository.selectActionByColumn(transactionId, ChangeColumnEnum.JOBS_TYPE.getColumn());
                if(CollectionUtils.isEmpty(sfaTransactionActionEntities)){
                    break;
                }
                SfaTransactionActionEntity sfaTransactionActionEntity = sfaTransactionActionEntities.stream().findFirst().get();
                if(!sfaTransactionActionEntity.getOldValue().equals(sfaTransactionActionEntity.getTransactionValue()) && !sfaTransactionActionEntity.getOldValue().equals("2")){
                    break;
                }
            case CHANGE_PART_TIME_POSITION:
            case CHANGE_SERVER_OBJ:
            case CHANGE_SALARY:
                jobTransferSelector.process(jobTransfer,sfaTransactionApplyEntity,processUserDO);
                break;

        }

        // 重新获取申请
        sfaTransactionApplyEntity = jobTransferRepository.selectTransactionApplyById(transactionId);
        if(Objects.nonNull(sfaTransactionApplyEntity) && Objects.isNull(sfaTransactionApplyEntity.getExecuteDate())){
            // 插入jobTask表
            SfaJobPositionTask sfaJobPositionTask = JobTransferFactory.initTaskJob(sfaTransactionApplyEntity.getId(), 3, 0, sfaTransactionApplyEntity.getAdviceExecuteDate());
            jobTransferRepository.saveJobPositionTask(sfaJobPositionTask);
        }

    }

    @Override
    public void saveSalaryAction(Long transactionId, ChangeSalaryDO changeSalaryDO) {
        // 更具transactionId获取employeeInfoId
        SfaTransactionApplyEntity sfaTransactionApplyEntity = jobTransferRepository.selectTransactionApplyById(transactionId);
        if(Objects.isNull(sfaTransactionApplyEntity)){
            throw new ApplicationException(JobTransferErrEnum.TRANSACTION_APPLY_NOT_EXIST.getMsg());
        }


        // 删除已有的薪资action
        jobTransferRepository.deleteAction(transactionId,ChangeColumnEnum.SALARY.getColumn());

        // 记录薪资变动
        EmployeeSalaryPO employeeSalaryPO = Optional.ofNullable(jobTransferRepository.getSalaryIdByEmployeeInfoId(sfaTransactionApplyEntity.getEmployeeInfoId())).orElse(new EmployeeSalaryPO());
        String oldSalaryId = String.valueOf(Optional.ofNullable(employeeSalaryPO.getStructureId()).orElse(0));
        jobTransferRepository.saveAction(JobTransferFactory.initAction(transactionId,ChangeColumnEnum.SALARY.getColumn(),oldSalaryId,String.valueOf(changeSalaryDO.getId())));
    }

    @Override
    @Transactional
    public void transact(JobTransferTransactDO transact, String person) {
        // 更具transactionId获取employeeInfoId
        SfaTransactionApplyEntity sfaTransactionApplyEntity = jobTransferRepository.selectTransactionApplyById(transact.getTransactionId());
        if(Objects.isNull(sfaTransactionApplyEntity)){
            throw new ApplicationException(JobTransferErrEnum.TRANSACTION_APPLY_NOT_EXIST.getMsg());
        }

        JobTransferEnum jobTransfer = JobTransferEnum.getJobTransfer(sfaTransactionApplyEntity.getTransactionType());
        if(Objects.isNull(jobTransfer)){
            throw new ApplicationException(JobTransferErrEnum.NOT_SUPPORT_TRANSFER.getMsg());
        }

        switch (jobTransfer){
            case CHANGE_MAIN_POSITION:
            case CHANGE_BUSINESS_GROUP:
                TransactBModelDO bModel = transact.getBModel();
                LocalDate executeDate = bModel.getExecuteDate();
                if(Objects.isNull(executeDate)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                sfaTransactionApplyEntity.setAdviceExecuteDate(executeDate);

                SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(sfaTransactionApplyEntity.getEmployeeInfoId());
                if(Objects.isNull(sfaEmployeeInfoModel)){
                    throw new ApplicationException("员工信息获取失败");
                }

                // 检查主岗
                List<SfaTransactionActionEntity> mainOrgAction = Optional.ofNullable(jobTransferRepository.selectActionByColumn(sfaTransactionApplyEntity.getId(), ChangeColumnEnum.MAIN_ORG_CODE.getColumn())).orElse(new ArrayList<>());
                Optional<SfaTransactionActionEntity> mainOptional = mainOrgAction.stream().filter(f -> StringUtils.isNotBlank(f.getTransactionValue())).findFirst();
                if(mainOptional.isPresent()){
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, mainOptional.get().getTransactionValue()));
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
                        throw new ApplicationException("岗位获取失败");
                    }
                    // 检查是否时间冲突
                    checkInterviewService.checkDateConflict(executeDate,ceoBusinessOrganizationPositionRelation.getPositionId(),sfaEmployeeInfoModel.getEmployeeId());
                }

                // 检查兼岗
                List<SfaTransactionActionEntity> partTimeOrgAction = Optional.ofNullable(jobTransferRepository.selectActionByColumn(sfaTransactionApplyEntity.getId(), ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn())).orElse(new ArrayList<>());
                Optional<SfaTransactionActionEntity> partTimeOptional = partTimeOrgAction.stream().filter(f -> StringUtils.isNotBlank(f.getTransactionValue())).findFirst();
                if(partTimeOptional.isPresent()){
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, partTimeOptional.get().getTransactionValue()));
                    if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
                        throw new ApplicationException("岗位获取失败");
                    }
                    // 检查是否时间冲突
                    checkInterviewService.checkDateConflict(executeDate,ceoBusinessOrganizationPositionRelation.getPositionId(),sfaEmployeeInfoModel.getEmployeeId());
                }


                jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);
                break;
            case CHANGE_COMPANY_WANT_WANT:
                TransactCModelDO cModel = transact.getCModel();
                LocalDate checkInDate = cModel.getCheckInDate();
                LocalDate checkOutDate = cModel.getCheckOutDate();
                String empId = cModel.getEmpId();
                if(Objects.isNull(checkInDate) || Objects.isNull(checkOutDate) || StringUtils.isBlank(empId)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                sfaTransactionApplyEntity.setAdviceExecuteDate(checkInDate);
                sfaTransactionApplyEntity.setOldPositionExecuteDate(checkOutDate);
                jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);

                // 保存action，保存岗位
                SfaEmployeeInfoModel employeeInfoById = jobTransferRepository.getEmployeeInfoById(sfaTransactionApplyEntity.getEmployeeInfoId());
                if(Objects.isNull(employeeInfoById)){
                    throw new ApplicationException(JobTransferErrEnum.EMPLOYEE_INFO_ERROR.getMsg());
                }
                // 保存工号
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.EMP_ID.getColumn(),employeeInfoById.getEmployeeId(),empId));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.JOINING_COMPANY.getColumn(),employeeInfoById.getJoiningCompany(),JobTransferConstants.WANT_WANT_COMPANY));
                break;
            case CHANGE_COMPANY:
                TransactDModelDO dModel = transact.getDModel();
                LocalDate wantCheckOutDate = dModel.getCheckOutDate();
                LocalDate otherCheckInDate = dModel.getCheckInDate();
                String joiningCompany = dModel.getJoiningCompany();
                String actualJoiningCompany = dModel.getActualJoiningCompany();

                String socialInsuranceProvince = dModel.getSocialInsuranceProvince();
                String socialInsuranceCity = dModel.getSocialInsuranceCity();
                String socialInsuranceDistrict = dModel.getSocialInsuranceDistrict();


                sfaTransactionApplyEntity.setAdviceExecuteDate(otherCheckInDate);
                sfaTransactionApplyEntity.setOldPositionExecuteDate(wantCheckOutDate);
                jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);

                SfaEmployeeInfoModel employeeInfoModel = jobTransferRepository.getEmployeeInfoById(sfaTransactionApplyEntity.getEmployeeInfoId());
                if(Objects.isNull(employeeInfoModel)){
                    throw new ApplicationException(JobTransferErrEnum.EMPLOYEE_INFO_ERROR.getMsg());
                }

                // 获取报名信息
                ApplyMemberPo applyMemberPo = jobTransferRepository.getApplyMemberById(employeeInfoModel.getApplicationId());
                if(Objects.isNull(applyMemberPo)){
                    throw new ApplicationException(JobTransferErrEnum.APPLY_NOT_EXIST.getMsg());
                }

                // 保存工号/入职公司
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.EMP_ID.getColumn(), employeeInfoModel.getEmployeeId(), employeeInfoModel.getMobile()));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.JOINING_COMPANY.getColumn(),employeeInfoModel.getJoiningCompany(),joiningCompany));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.ACTUAL_JOINING_COMPANY.getColumn(),employeeInfoModel.getActualJoiningCompany(),actualJoiningCompany));

                // 记录社保缴纳地
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_PROVINCE.getColumn(), applyMemberPo.getSocialInsuranceProvince(),socialInsuranceProvince));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_CITY.getColumn(),applyMemberPo.getSocialInsuranceCity(),socialInsuranceCity));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_DISTRICT.getColumn(),applyMemberPo.getSocialInsuranceDistrict(),socialInsuranceDistrict));

                // 保存劳动合同
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.LABOR_CONTRACT.getColumn(),StringUtils.EMPTY,dModel.getLaborContract()));
                break;
            case CHANGE_BD_POSITION:
                TransactEModelDO eModel = transact.getEModel();
                if(Objects.isNull(eModel)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                // 检查异动岗位类型
                List<SfaTransactionActionEntity> ceoTypeActionList = jobTransferRepository.selectActionByColumn(transact.getTransactionId(), ChangeColumnEnum.CEO_TYPE.getColumn());
                if(CollectionUtils.isEmpty(ceoTypeActionList)){
                   throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                SfaTransactionActionEntity ceoTypeAction = ceoTypeActionList.stream().findFirst().get();
                int ceoType = Integer.parseInt(ceoTypeAction.getTransactionValue());

                List<SfaTransactionActionEntity> jobsTypeActionList = jobTransferRepository.selectActionByColumn(transact.getTransactionId(), ChangeColumnEnum.JOBS_TYPE.getColumn());
                if(CollectionUtils.isEmpty(jobsTypeActionList)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                SfaTransactionActionEntity jobsTypeAction = jobsTypeActionList.stream().findFirst().get();
                int jobsType = Integer.parseInt(jobsTypeAction.getTransactionValue());

                List<SfaTransactionActionEntity> positionActionList = jobTransferRepository.selectActionByColumn(transact.getTransactionId(), ChangeColumnEnum.POSITION.getColumn());
                if(CollectionUtils.isEmpty(positionActionList)){
                    throw new ApplicationException(JobTransferErrEnum.PARAMETER_ERROR.getMsg());
                }
                SfaTransactionActionEntity positionAction = positionActionList.stream().findFirst().get();
                int position = Integer.parseInt(positionAction.getTransactionValue());

                SfaEmployeeInfoModel employeeInfoModel1 = jobTransferRepository.getEmployeeInfoById(sfaTransactionApplyEntity.getEmployeeInfoId());
                if(Objects.isNull(employeeInfoModel1)){
                    throw new ApplicationException(JobTransferErrEnum.EMPLOYEE_INFO_ERROR.getMsg());
                }

                String joiningCompany1 = eModel.getJoiningCompany();
                String actualJoiningCompany1 = eModel.getActualJoiningCompany();
                // 入职公司
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.JOINING_COMPANY.getColumn(),employeeInfoModel1.getJoiningCompany(),joiningCompany1));
                jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.ACTUAL_JOINING_COMPANY.getColumn(),employeeInfoModel1.getActualJoiningCompany(),actualJoiningCompany1));


                PositionEnum positionEnum = PositionEnum.getEnum(ceoType, jobsType, position);
                if(Objects.equals(positionEnum.getId(), PositionEnum.BUSINESS_BD.getId())){
                    // 全职展示E区域
                    LocalDate date = eModel.getCheckInDate();


                    String socialInsuranceProvince1 = eModel.getSocialInsuranceProvince();
                    String socialInsuranceCity1 = eModel.getSocialInsuranceCity();
                    String socialInsuranceDistrict1 = eModel.getSocialInsuranceDistrict();


                    sfaTransactionApplyEntity.setAdviceExecuteDate(date);
                    jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);



                    // 获取报名信息
                    ApplyMemberPo applyMemberPo1 = jobTransferRepository.getApplyMemberById(employeeInfoModel1.getApplicationId());
                    if(Objects.isNull(applyMemberPo1)){
                        throw new ApplicationException(JobTransferErrEnum.APPLY_NOT_EXIST.getMsg());
                    }


                    // 记录社保缴纳地
                    jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_PROVINCE.getColumn(), applyMemberPo1.getSocialInsuranceProvince(),socialInsuranceProvince1));
                    jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_CITY.getColumn(),applyMemberPo1.getSocialInsuranceCity(),socialInsuranceCity1));
                    jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.SOCIAL_INSURANCE_DISTRICT.getColumn(),applyMemberPo1.getSocialInsuranceDistrict(),socialInsuranceDistrict1));

                    // 保存劳动合同
                    jobTransferRepository.saveAction(JobTransferFactory.initAction(sfaTransactionApplyEntity.getId(),ChangeColumnEnum.LABOR_CONTRACT.getColumn(),StringUtils.EMPTY,eModel.getLaborContract()));

                }else{
                    LocalDate date = eModel.getCheckOutDate();
                    sfaTransactionApplyEntity.setAdviceExecuteDate(date);
                    jobTransferRepository.updateTransactionApply(sfaTransactionApplyEntity);
                }

                break;

            default: throw new ApplicationException(JobTransferErrEnum.NOT_SUPPORT_TRANSFER.getMsg());
        }


    }

    @Override
    @Transactional
    public void closePosition(List<SfaTransactionActionEntity> transferAction, SfaEmployeeInfoModel sfaEmployeeInfoModel, int partTime, LocalDateTime executeTime, ProcessUserDO processUserDO) {
        if(CollectionUtils.isEmpty(transferAction)){
            return;
        }

        transferAction.forEach(e -> {
            // 停用原岗位
            jobTransferRepository.closeCeoBusinessPosition(e.getOldValue(),sfaEmployeeInfoModel.getEmployeeId(),executeTime);
            // 关闭原兼岗表
            jobTransferRepository.closePositionRelation(e.getOldValue(),sfaEmployeeInfoModel.getId(),executeTime);
            // 停用原角色
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, e.getOldValue()).last("limit 1"));
            if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
                jobTransferRepository.closeEmployeeRole(sfaEmployeeInfoModel.getEmployeeId(),ceoBusinessOrganizationPositionRelation.getPositionId(),processUserDO.getEmployeeId(),processUserDO.getEmployeeName());
            }
            interviewService.wantCoinsRetrieve(e.getOldValue());

        });
    }

    @Override
    @Transactional
    public void addPosition(List<SfaTransactionActionEntity> transferAction, SfaEmployeeInfoModel sfaEmployeeInfoModel, int partTime, LocalDateTime atStartOfDay, ProcessUserDO processUserDO) {
        if(CollectionUtils.isEmpty(transferAction)){
            return;
        }

        String employeeId = sfaEmployeeInfoModel.getEmployeeId();

        List<SfaPositionRelationEntity> positionRelationEntityList = jobTransferRepository.selectPositionRelationByEmployeeInfoId(sfaEmployeeInfoModel.getId(), null);

        int currentPartTimeCount = positionRelationEntityList.stream().filter(f -> f.getPartTime() == 1).collect(Collectors.toList()).size();

        transferAction.forEach(e -> {
            // 检查岗位是否是空的
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, e.getTransactionValue()).last("limit 1"));
            if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
                throw new ApplicationException("岗位错误");
            }
            if(StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
                throw new ApplicationException("岗位暂无空缺");
            }

            PositionEnum positionEnum = null;
            String organizationType = organizationMapper.getOrganizationType(e.getTransactionValue());
            Integer businessGroupId = organizationMapper.getBusinessGroupById(e.getTransactionValue());
            if(organizationType.equals("area")){
                positionEnum = PositionEnum.AREA_MANAGER;
            }else if(organizationType.equals("varea")){
                positionEnum = PositionEnum.VAREA_MANAGER;
            }else if(organizationType.equals("province")){
                positionEnum = PositionEnum.PROVINCE_MANAGER;
            }else if(organizationType.equals("company")){
                positionEnum = PositionEnum.MANAGER;
            }else if(organizationType.equals("department")){
                positionEnum = PositionEnum.CITY_MANAGER;
            }


            boolean flag = recruitService.checkNotAllowCurrentWithOther(businessGroupId,e.getTransactionValue(),positionEnum);
            if(flag && (transferAction.size() > 1 || currentPartTimeCount > 0)){
                throw new ApplicationException("当前岗位无法兼职其他岗位");
            }

            // 检查当前岗位是否可被兼岗
            if(partTime == 1){
                boolean notAllowOtherPosition = recruitService.checkNotAllowOtherPosition(businessGroupId,e.getTransactionValue(),positionEnum);
                if(notAllowOtherPosition){
                    throw new ApplicationException("当前岗位无法被兼岗");
                }
            }



            // 保存工号和岗位
            ceoBusinessOrganizationPositionRelation.setEmployeeId(employeeId);
            ceoBusinessOrganizationPositionRelation.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
            ceoBusinessOrganizationPositionRelation.setOnboardTime(atStartOfDay);
            ceoBusinessOrganizationPositionRelation.setOffTime(null);
            ceoBusinessOrganizationPositionRelationMapper.updateById(ceoBusinessOrganizationPositionRelation);

            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, e.getTransactionValue()).last("limit 1"));
            if(Objects.isNull(viewEntity)){
                throw new ApplicationException("组织信息错误");
            }



            // 修改主岗需要改applyMember和employeeInfo
            if(partTime == 0){
                saveApplyMember(sfaEmployeeInfoModel.getId(),viewEntity, ceoBusinessOrganizationPositionRelation.getPositionTypeId());
                saveEmployeeInfo(sfaEmployeeInfoModel,viewEntity,ceoBusinessOrganizationPositionRelation);
            }

            // 插入兼岗表
            SfaPositionRelationEntity sfaPositionRelationEntity = JobTransferFactory.initPositionRelation(viewEntity, sfaEmployeeInfoModel, ceoBusinessOrganizationPositionRelation, partTime, atStartOfDay);
            jobTransferRepository.insertSfaPositionRelation(sfaPositionRelationEntity);


            // 插入角色
            int roleId = PositionTypeEnum.findRoleIdByPositionTypeId(ceoBusinessOrganizationPositionRelation.getPositionTypeId());
            accountService.bindRole(employeeId, Arrays.asList(roleId),processUserDO,ceoBusinessOrganizationPositionRelation.getPositionId());

        });

    }



    @Override
    public void changeSalary(SfaEmployeeInfoModel sfaEmployeeInfoModel, Integer structureId, LocalDate adviceExecuteDate,LocalDate prevPositionExecuteDate, ProcessUserDO processUserDO) {
        // 薪资方案填写结束时间
        employeeSalaryService.updateSalaryByEmpId(sfaEmployeeInfoModel.getId(),prevPositionExecuteDate.atStartOfDay());


        // 插入新的薪资方案
        employeeSalaryService.addSalaryByEmpId(sfaEmployeeInfoModel,sfaEmployeeInfoModel.getId(),structureId,processUserDO.getEmployeeId(),adviceExecuteDate,1);

    }

    @Override
    public void saveChangePartTime(Long transactionId, List<ChangeOrganizationDO> changePartTimeOrg) {
        // 删除原兼职
        jobTransferRepository.deleteAction(transactionId,ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn());
        jobTransferRepository.deleteAction(transactionId,ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn());

        changePartTimeOrg.forEach(e -> {
            
            checkPositionEmpty(e.getOrganizationId());

            // 记录需要增加的组织
            jobTransferRepository.saveAction(JobTransferFactory.initAction(transactionId,
                    ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn(),StringUtils.EMPTY, e.getOrganizationId()));
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = getCeoBusinessOrganizationPositionRelation(e.getOrganizationId());
            // 记录需要增加的岗位信息
            jobTransferRepository.saveAction(JobTransferFactory.initAction(transactionId,
                    ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn(),
                    StringUtils.EMPTY, ceoBusinessOrganizationPositionRelation.getPositionId()));

        });
    }


    private void saveCompany(JobTransferDO jobTransferDO) {
        String oldCompany = jobTransferRepository.selectJoiningCompanyByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                ChangeColumnEnum.JOINING_COMPANY.getColumn(),
                oldCompany, JobTransferConstants.WANT_WANT_COMPANY));
    }

    private void savePartTImePosition(JobTransferDO jobTransferDO, SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        List<SfaPositionRelationEntity> positionRelationEntityList = jobTransferRepository.selectPositionRelationByEmployeeInfoId(jobTransferDO.getEmployeeInfoId(),RequestUtils.getBusinessGroup());
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException(JobTransferErrEnum.POSITION_ERROR.getMsg());
        }

        List<ChangeOrganizationDO> changePartTimeOrg = Optional.ofNullable(jobTransferDO.getChangePartTimeOrg()).orElse(new ArrayList<>());

        positionRelationEntityList.stream().filter(f -> f.getPartTime() == 1).forEach(e -> {
            Optional<ChangeOrganizationDO> changeOrganizationOptional = changePartTimeOrg.stream().filter(cf -> cf.getOrganizationId().equals(e.getOrganizationCode())).findFirst();

            if(!changeOrganizationOptional.isPresent()){
                // 记录删除的岗位信息
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn(),e.getOrganizationCode(),StringUtils.EMPTY));
                // 记录删除当前组织
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn(), e.getPositionId(),StringUtils.EMPTY));
            }else{
                // 记录不变的组织信息
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn(),e.getOrganizationCode(),e.getOrganizationCode()));
                // 记录删除当前组织
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn(), e.getPositionId(),e.getPositionId()));
            }
        });


        changePartTimeOrg.forEach(e -> {
            Optional<SfaPositionRelationEntity> positionRelationEntityOptional = positionRelationEntityList.stream().filter(f -> f.getOrganizationCode().equals(e.getOrganizationId())).findFirst();
            if(!positionRelationEntityOptional.isPresent()){

//                checkPositionEmpty(e.getOrganizationId());
                // 检查时间是否冲突
                checkConflict(sfaEmployeeInfoModel, e.getOrganizationId(), jobTransferDO.getAdviceExecuteDate());
                
                // 记录需要增加的组织
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                        ChangeColumnEnum.PART_TIME_ORG_CODE.getColumn(),StringUtils.EMPTY, e.getOrganizationId()));
                CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = getCeoBusinessOrganizationPositionRelation(e.getOrganizationId());

                // 记录需要增加岗位信息
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                        ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn(),
                        StringUtils.EMPTY, ceoBusinessOrganizationPositionRelation.getPositionId()));
            }
        });
    }

    @Override
    public void saveChangeSalary(JobTransferDO jobTransferDO,String organizationId) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }

        // 记录薪资变动
        EmployeeSalaryPO employeeSalaryPO = Optional.ofNullable(jobTransferRepository.getSalaryIdByEmployeeInfoId(jobTransferDO.getEmployeeInfoId())).orElse(new EmployeeSalaryPO());
        String oldSalaryId = String.valueOf(Optional.ofNullable(employeeSalaryPO.getStructureId()).orElse(0));

        Integer changeSalaryId = jobTransferDO.getChangeSalaryDO().getId();

        Integer position = applyMemberPo.getPosition();

        String oldLevel = "S00";
        String currentLevel = employeeSalaryStructureMapper.selectById(changeSalaryId).getGrade();
        if(StringUtils.isNotBlank(oldSalaryId)){
            EmployeeSalaryStructurePO employeeSalaryStructurePO = employeeSalaryStructureMapper.selectById(oldSalaryId);
            if(Objects.nonNull(employeeSalaryStructurePO) && StringUtils.isNotBlank(employeeSalaryStructurePO.getGrade())){
                oldLevel = employeeSalaryStructurePO.getGrade();
            }
        }

        if(StringUtils.isBlank(organizationId)){
            if(position == 2 || position == 4){
                organizationId = applyMemberPo.getCompanyOrganizationId();
            }else if(position == 11){
                organizationId = applyMemberPo.getProvinceOrganizationId();
            }else if(position == 5){
                organizationId = applyMemberPo.getVareaOrganizationId();
            }
        }


        String theYearMonth = LocalDate.now().toString().substring(0,7);
        String lastYearMonth = LocalDate.now().minusMonths(1L).toString().substring(0,7);


        // 临时方案，如果当前日期是15号之前，则取上上月
        String baseTimeConfig = configMapper.getValueByCode("base_time");
        LocalDateTime currentDate = LocalDateTime.now();
        if (org.apache.commons.lang.StringUtils.isNotBlank(baseTimeConfig)) {
            currentDate = LocalDateTime.parse(baseTimeConfig).minusMonths(1L);
        }
        if(currentDate.getDayOfMonth() < 15){
            currentDate = currentDate.minusMonths(2L);
        }

        BigDecimal salaryTotal = Optional.ofNullable(employeeSalaryPO.getEmployeeAllowance()).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(employeeSalaryPO.getEmployeeBaseSalary()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(employeeSalaryPO.getEmployeeBonus()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(employeeSalaryPO.getSocialSecurityBase()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(employeeSalaryPO.getTravelExpenses()).orElse(BigDecimal.ZERO));



        BigDecimal currentSalary = Optional.ofNullable(salaryBigTableService.selectLastMonthSalaryById(jobTransferDO.getEmployeeInfoId(),currentDate.toLocalDate().toString().substring(0,7)))
                .orElse(salaryTotal);
        if(currentSalary.compareTo(BigDecimal.ZERO) == 0){
            // 获取理论值
            currentSalary = employeeSalaryPO.getEmployeeBaseSalary().add(Optional.ofNullable(employeeSalaryPO.getEmployeeAllowance()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(employeeSalaryPO.getEmployeeBonus()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(employeeSalaryPO.getSocialSecurityBase()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(employeeSalaryPO.getTravelExpenses()).orElse(BigDecimal.ZERO));
        }

        ChangePositionTypeDO changePositionTypeDO = jobTransferDO.getChangePositionTypeDO();

        if(Objects.nonNull(changePositionTypeDO) && Objects.nonNull(changePositionTypeDO.getPositionTypeId())){
            Integer positionTypeId = changePositionTypeDO.getPositionTypeId();
            switch (positionTypeId){

                // 战区
                case 1:
                    position = 3;
                    break;
                // 大区
                case 12:
                    position = 5;
                    break;
                // 省区
                case 11:
                    position = 6;
                    break;
                // 分公司
                case 2:
                    position = 2;
                    break;
                // 营业所
                case 10:
                    position = 4;
                    break;
            }
        }


        // 大区/战区不做薪资检查
        if(GradeUtils.extractGradeNumberAsInteger(currentLevel) > GradeUtils.extractGradeNumberAsInteger(oldLevel)){
            if(position == 2 || position == 4){
                salaryMiddlewareService.checkSalaryController(changeSalaryId,organizationId,null, currentSalary,theYearMonth);
            }else if(position == 6){
                salaryMiddlewareService.checkSalaryController(changeSalaryId,organizationId,organizationId, currentSalary,theYearMonth);
            }
        }


        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),ChangeColumnEnum.SALARY.getColumn(),oldSalaryId,String.valueOf(jobTransferDO.getChangeSalaryDO().getId())));
    }

    @Override
    public void changeWorkPlace(Integer employeeInfoId, String workPlace) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(employeeInfoId);
        applyMemberPo.setWorkPlace(workPlace);
        jobTransferRepository.updateApplyMember(applyMemberPo);
    }


    private void saveChangeMainPosition(JobTransferDO jobTransferDO, SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }

        List<SfaPositionRelationEntity> positionRelationEntityList = jobTransferRepository.selectPositionRelationByEmployeeInfoId(jobTransferDO.getEmployeeInfoId(),RequestUtils.getBusinessGroup());
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException(JobTransferErrEnum.POSITION_ERROR.getMsg());
        }

        if(positionRelationEntityList.size() > 1){
            throw new ApplicationException("岗位存在兼岗位，请先关闭兼岗");
        }

        positionRelationEntityList.stream().filter(f -> f.getPartTime() == 0).forEach(e -> {
                // 记录删除当前岗位ID
            jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                    ChangeColumnEnum.MAIN_POSITION_ID.getColumn(),e.getPositionId(), StringUtils.EMPTY));
                // 记录删除当前组织
            jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                    ChangeColumnEnum.MAIN_ORG_CODE.getColumn(),e.getOrganizationCode(), StringUtils.EMPTY));
        });
        SfaPositionRelationEntity sfaPositionRelationEntity = positionRelationEntityList.stream().findFirst().get();


        Integer oldPositionId = sfaPositionRelationEntity.getPositionTypeId();
        Integer newPositionId = jobTransferDO.getChangePositionTypeDO().getPositionTypeId();

        // 异动前后岗位不一致，判断是否可招聘
        if(!oldPositionId.equals(newPositionId)){
            PositionEnum positionEnum = null;
            if(newPositionId == 10){
                positionEnum = PositionEnum.CITY_MANAGER;
            }else if(newPositionId == 2){
                positionEnum = PositionEnum.MANAGER;
            }else if(newPositionId == 11){
                positionEnum = PositionEnum.PROVINCE_MANAGER;
            }else if(newPositionId == 12){
                positionEnum = PositionEnum.VAREA_MANAGER;
            }else if(newPositionId == 1){
                positionEnum = PositionEnum.AREA_MANAGER;
            }
            boolean restrict = recruitService.checkPositionRestrict(applyMemberPo.getBusinessGroup(), jobTransferDO.getChangeMainOrg().getOrganizationId(), positionEnum);
            if(restrict){
                throw new ApplicationException("招聘岗位已关闭");
            }
        }


        // 记录更换的岗位类型
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                ChangeColumnEnum.POSITION_TYPE.getColumn(),String.valueOf(oldPositionId)
                ,String.valueOf(newPositionId)));

        checkConflict(sfaEmployeeInfoModel,jobTransferDO.getChangeMainOrg().getOrganizationId(),jobTransferDO.getAdviceExecuteDate());

        // 检查岗位是否有人
//        checkPositionEmpty(jobTransferDO.getChangeMainOrg().getOrganizationId());

        // 记录新的组织
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                ChangeColumnEnum.MAIN_ORG_CODE.getColumn(),StringUtils.EMPTY,jobTransferDO.getChangeMainOrg().getOrganizationId()));
        // 记录新的岗位ID
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, jobTransferDO.getChangeMainOrg().getOrganizationId()));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException(JobTransferErrEnum.POSITION_TABLE_ERROR.getMsg());
        }
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                ChangeColumnEnum.MAIN_POSITION_ID.getColumn(),StringUtils.EMPTY,
                ceoBusinessOrganizationPositionRelation.getPositionId()));

        // 记录更换办公地点
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),
                ChangeColumnEnum.WORK_PLACE.getColumn(),applyMemberPo.getWorkPlace(),
                jobTransferDO.getChangeWorkPlace().getOrganizationId()));

        // 记录薪资变动
        saveChangeSalary(jobTransferDO,jobTransferDO.getChangeMainOrg().getOrganizationId());

    }

    private void checkConflict(SfaEmployeeInfoModel sfaEmployeeInfoModel,String orgCode,LocalDate adviceExecuteDate) {

        String positionId = ceoBusinessOrganizationPositionRelationMapper.getPositionIdByOrgCode(orgCode);
        if(StringUtils.isBlank(positionId)){
            throw new ApplicationException("岗位获取失败");
        }
        // 检查时间是否冲突
        checkInterviewService.checkDateConflict(adviceExecuteDate,positionId,sfaEmployeeInfoModel.getEmployeeId());
    }

    private void checkPositionEmpty(String organizationId) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId).last("limit 1"));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("错误的岗位信息");
        }

        String employeeId = ceoBusinessOrganizationPositionRelation.getEmployeeId();
        if(StringUtils.isNotBlank(employeeId)){
            throw new ApplicationException("岗位暂无空缺");
        }


        Integer positionTypeId = ceoBusinessOrganizationPositionRelation.getPositionTypeId();
        PositionEnum positionEnum = null;
        if(positionTypeId == 10){
            positionEnum = PositionEnum.CITY_MANAGER;
        }else if(positionTypeId == 2){
            positionEnum = PositionEnum.MANAGER;
        }else if(positionTypeId == 11){
            positionEnum = PositionEnum.PROVINCE_MANAGER;
        }else if(positionTypeId == 12){
            positionEnum = PositionEnum.VAREA_MANAGER;
        }else if(positionTypeId == 1){
            positionEnum = PositionEnum.AREA_MANAGER;
        }


        boolean restrict = recruitService.checkPositionRestrict(ceoBusinessOrganizationPositionRelation.getBusinessGroup(), organizationId, positionEnum);
        if(restrict){
            throw new ApplicationException("招聘岗位已关闭");
        }

    }

    private void saveChangeBusinessGroupAction(JobTransferDO jobTransferDO,SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(jobTransferDO.getEmployeeInfoId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名信息获取失败");
        }

        // 记录删除当前产品组
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.BUSINESS_GROUP.getColumn(),String.valueOf(RequestUtils.getBusinessGroup()), StringUtils.EMPTY));
        List<SfaPositionRelationEntity> positionRelationEntityList = jobTransferRepository.selectPositionRelationByEmployeeInfoId(jobTransferDO.getEmployeeInfoId(),RequestUtils.getBusinessGroup());
        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException(JobTransferErrEnum.POSITION_ERROR.getMsg());
        }

        positionRelationEntityList.forEach(e -> {
            Integer partTime = e.getPartTime();
            if(partTime == 1){
                // 记录删除当前岗位ID
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_POSITION_ID.getColumn(),e.getPositionId(), StringUtils.EMPTY));
                // 记录删除当前组织
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.PART_TIME_ORGANIZATION_ID.getColumn(),e.getOrganizationCode(), StringUtils.EMPTY));
            }else{
                // 记录删除当前岗位ID
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.MAIN_POSITION_ID.getColumn(),e.getPositionId(), StringUtils.EMPTY));
                // 记录删除当前组织
                jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.MAIN_ORG_CODE.getColumn(),e.getOrganizationCode(), StringUtils.EMPTY));
            }
        });
        PositionEnum positionEnum = null;
        Integer positionTypeId = jobTransferDO.getChangePositionTypeDO().getPositionTypeId();
        if(positionTypeId == PositionTypeEnum.AREA.getPositionTypeId()){
            positionEnum = PositionEnum.AREA_MANAGER;
        }else if(positionTypeId == PositionTypeEnum.VARE.getPositionTypeId()){
            positionEnum = PositionEnum.VAREA_MANAGER;
        }else if(positionTypeId == PositionTypeEnum.PROVINCE.getPositionTypeId()){
            positionEnum = PositionEnum.PROVINCE_MANAGER;
        }else if(positionTypeId == PositionTypeEnum.COMPANY.getPositionTypeId()){
            positionEnum = PositionEnum.MANAGER;
        }else if(positionTypeId == PositionTypeEnum.DEPARTMENT.getPositionTypeId()){
            positionEnum = PositionEnum.CITY_MANAGER;
        }


        boolean restrict = recruitService.checkPositionRestrict(jobTransferDO.getBusinessGroup(), jobTransferDO.getChangeMainOrg().getOrganizationId(), positionEnum);
        if(restrict){
            throw new ApplicationException("岗位已停止招聘");
        }

        checkConflict(sfaEmployeeInfoModel,  jobTransferDO.getChangeMainOrg().getOrganizationId(),  jobTransferDO.getAdviceExecuteDate());


        SfaPositionRelationEntity sfaPositionRelationEntity = positionRelationEntityList.stream().findFirst().get();
        // 记录新的产品组
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),ChangeColumnEnum.BUSINESS_GROUP.getColumn(),StringUtils.EMPTY,String.valueOf(jobTransferDO.getBusinessGroup())));
        // 记录更换的岗位类型
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(), ChangeColumnEnum.POSITION_TYPE.getColumn(),String.valueOf(sfaPositionRelationEntity.getPositionTypeId()),String.valueOf(jobTransferDO.getChangePositionTypeDO().getPositionTypeId())));
        // 记录新的组织
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),ChangeColumnEnum.MAIN_ORG_CODE.getColumn(),StringUtils.EMPTY,jobTransferDO.getChangeMainOrg().getOrganizationId()));
        // 检查岗位是否有人
        checkPositionEmpty(jobTransferDO.getChangeMainOrg().getOrganizationId());

        // 记录新的岗位ID
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = getCeoBusinessOrganizationPositionRelation(jobTransferDO.getChangeMainOrg().getOrganizationId());
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),ChangeColumnEnum.MAIN_POSITION_ID.getColumn(),StringUtils.EMPTY,ceoBusinessOrganizationPositionRelation.getPositionId()));
        // 记录更换办公地点
        jobTransferRepository.saveAction(JobTransferFactory.initAction(jobTransferDO.getTransactionId(),ChangeColumnEnum.WORK_PLACE.getColumn(),applyMemberPo.getWorkPlace(),jobTransferDO.getChangeWorkPlace().getOrganizationId()));

    }

    private CeoBusinessOrganizationPositionRelation getCeoBusinessOrganizationPositionRelation(String orgCode) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, orgCode));
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation)) {
            throw new ApplicationException(JobTransferErrEnum.POSITION_TABLE_ERROR.getMsg());
        }
        return ceoBusinessOrganizationPositionRelation;
    }

    private SfaEmployeeInfoModel saveEmployeeInfo(SfaEmployeeInfoModel sfaEmployeeInfoModel, CeoBusinessOrganizationViewEntity viewEntity, CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation) {
        sfaEmployeeInfoModel.setDepartmentCode(viewEntity.getDepartmentId());
        sfaEmployeeInfoModel.setDepartmentName(viewEntity.getDepartmentName());
        sfaEmployeeInfoModel.setCompanyCode(viewEntity.getOrgId2());
        sfaEmployeeInfoModel.setCompanyName(viewEntity.getOrgName2());
        sfaEmployeeInfoModel.setProvinceOrganizationId(viewEntity.getProvinceId());
        sfaEmployeeInfoModel.setProvinceOrganizationName(viewEntity.getProvinceName());
        sfaEmployeeInfoModel.setVareaOrganizationId(viewEntity.getVirtualAreaId());
        sfaEmployeeInfoModel.setVareaOrganizationName(viewEntity.getVirtualAreaName());
        sfaEmployeeInfoModel.setAreaName(viewEntity.getOrgName3());
        sfaEmployeeInfoModel.setAreaCode(viewEntity.getOrgId3());
        sfaEmployeeInfoModel.setPositionId(ceoBusinessOrganizationPositionRelation.getPositionId());
        jobTransferRepository.updateEmployeeInfo(sfaEmployeeInfoModel);
        return sfaEmployeeInfoModel;
    }

    private void saveApplyMember(Integer employeeInfoId, CeoBusinessOrganizationViewEntity viewEntity, Integer positionTypeId) {
        ApplyMemberPo applyMemberPo = jobTransferRepository.selectApplyMemberByEmployeeInfoId(employeeInfoId);
        applyMemberPo.setBranchOrganizationId(viewEntity.getDepartmentId());
        applyMemberPo.setBranchOrganizationName(viewEntity.getDepartmentName());
        applyMemberPo.setCompanyOrganizationId(viewEntity.getOrgId2());
        applyMemberPo.setCompany(viewEntity.getOrgName2());
        applyMemberPo.setProvinceOrganizationId(viewEntity.getProvinceId());
        applyMemberPo.setProvinceOrganizationName(viewEntity.getProvinceName());
        applyMemberPo.setVareaOrganizationId(viewEntity.getVirtualAreaId());
        applyMemberPo.setVareaOrganizationName(viewEntity.getVirtualAreaName());
        applyMemberPo.setArea(viewEntity.getOrgName3());
        applyMemberPo.setAreaOrganizationId(viewEntity.getOrgId3());
        applyMemberPo.setBusinessGroup(viewEntity.getBusinessGroup());

        PositionEnum positionEnum = null;
        if(positionTypeId == 10){
            positionEnum = PositionEnum.CITY_MANAGER;
        }else if(positionTypeId == 2){
            positionEnum = PositionEnum.MANAGER;
        }else if(positionTypeId == 11){
            positionEnum = PositionEnum.PROVINCE_MANAGER;
        }else if(positionTypeId == 12){
            positionEnum = PositionEnum.VAREA_MANAGER;
        }else if(positionTypeId == 1){
            positionEnum = PositionEnum.AREA_MANAGER;
        }
        if(Objects.isNull(positionEnum)){
            throw new ApplicationException("异动岗位类型错误");
        }
        applyMemberPo.setJobsType(positionEnum.getJobsType());
        applyMemberPo.setCeoType(positionEnum.getCeoType());
        applyMemberPo.setPosition(positionEnum.getPosition());
        jobTransferRepository.updateApplyMember(applyMemberPo);
    }
}
