package com.wantwant.sfa.backend.interview.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.service.impl.AccountService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.domain.applyMember.dto.ApplyMemberBuildDTO;
import com.wantwant.sfa.backend.domain.applyMember.service.IApplyMemberBuildService;
import com.wantwant.sfa.backend.domain.ceoModify.enums.GenderEnum;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.interview.dto.DistributionInfoRequest;
import com.wantwant.sfa.backend.interview.dto.OverseaMemberManagerOpenAccountRequest;
import com.wantwant.sfa.backend.interview.dto.OverseaMemberSaveResponse;
import com.wantwant.sfa.backend.interview.enums.*;
import com.wantwant.sfa.backend.interview.model.InternationalEmpImportModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessModel;
import com.wantwant.sfa.backend.interview.model.SfaInterviewProcessRecordModel;
import com.wantwant.sfa.backend.interview.process.constants.InterviewProcessConstants;
import com.wantwant.sfa.backend.interview.service.ICheckInterviewService;
import com.wantwant.sfa.backend.interview.service.IInternationalEmpBuildService;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessRecordMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.ApplyMemberAdditionPo;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.LoginUserService;
import com.wantwant.sfa.backend.transaction.dto.InternationalEmpValidResultDto;
import com.wantwant.sfa.backend.transaction.enums.InternationalPositionEnum;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.InternationalConnectorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class InternationalEmpBuildService implements IInternationalEmpBuildService {

    @Resource
    private ICheckInterviewService checkInterviewService;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private OrganizationBindRelationMapper organizationBindRelationMapper;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private IApplyMemberBuildService applyMemberBuildService;
    @Resource
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;
    @Resource
    private SfaInterviewProcessRecordMapper sfaInterviewProcessRecordMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private SfaApplyMemberAdditionMapper sfaApplyMemberAdditionMapper;
    @Resource
    private EmployeeMapper employeeMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private InternationalConnectorUtil internationalConnectorUtil;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private LoginUserService loginUserService;

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public void createPersonnelInfo(InternationalEmpImportModel internationalEmpImportModel, ProcessUserDO processUserDO) {
        // 检查参数
        log.info("start check param for international employee: {}", internationalEmpImportModel);
        InternationalEmpValidResultDto internationalEmpValidResultDto = checkParam(internationalEmpImportModel);
        log.info("check param for international employee success: {}", internationalEmpValidResultDto);

        // 生成基础的applyMember信息
        log.info("start init base applyMemberPo for international employee: {}", internationalEmpImportModel);
        ApplyMemberPo applyMemberPo = initBaseApplyMember(internationalEmpImportModel,internationalEmpValidResultDto,processUserDO);
        log.info("init base applyMemberPo for international employee success: {}", applyMemberPo);

        String employeeId = applyMemberPo.getUserMobile();
        // 绑定工号与ceoBusinessOrganizationPositionRelation,新表和老表
        log.info("position transaction action bind position");
        bindPosition(employeeId,applyMemberPo.getUserName(),internationalEmpValidResultDto,processUserDO);
        log.info("bind position success");

        // 绑定角色
        log.info("position transaction action bind role");
        accountService.bindRole(employeeId, Collections.singletonList(internationalEmpValidResultDto.getRoleId()),processUserDO,internationalEmpValidResultDto.getCeoBusinessOrganizationPositionRelation().getPositionId());
        log.info("bind role success");



        // 调用旺铺接口,获取memberKey
        log.info("start call account service to get memberKey for international employee: {}", internationalEmpImportModel);
        OverseaMemberSaveResponse overseaMemberSaveResponse = initMemberKey(internationalEmpImportModel, internationalEmpValidResultDto,processUserDO);
        log.info("call account service to get memberKey success, memberKey: {}", overseaMemberSaveResponse);

        // 生成sfaEmployeeInfo
        log.info("start create sfaEmployeeInfo for international employee: {}", internationalEmpImportModel);
        SfaEmployeeInfoModel sfaEmployeeInfoModel = createSfaEmployeeInfo(applyMemberPo, overseaMemberSaveResponse, internationalEmpValidResultDto, processUserDO);
        log.info("create sfaEmployeeInfo success, sfaEmployeeInfoModel: {}", sfaEmployeeInfoModel);

        // 创建账号
        log.info("create account, sfaEmployeeInfoModel: {}", sfaEmployeeInfoModel);
        createAccount(sfaEmployeeInfoModel);
        log.info("create account success");

        // 生成sfaPositionRelation
        log.info("start create sfaPositionRelation for international employee: {}", internationalEmpImportModel);
        createPositionRelation(sfaEmployeeInfoModel,internationalEmpValidResultDto, processUserDO);
        log.info("create sfaPositionRelation success");

    }

    private void createAccount(SfaEmployeeInfoModel sfaEmployeeInfoModel) {
        // 检查账号是否存在
        boolean isExist = loginUserService.checkAccountExist(sfaEmployeeInfoModel.getMobile());
        if(!isExist){
            loginUserService.createAccount(sfaEmployeeInfoModel.getMobile(),sfaEmployeeInfoModel.getId());
        }
    }

    private void createPositionRelation(SfaEmployeeInfoModel sfaEmployeeInfoModel, InternationalEmpValidResultDto internationalEmpValidResultDto, ProcessUserDO processUserDO) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = internationalEmpValidResultDto.getCeoBusinessOrganizationPositionRelation();


        SfaPositionRelationEntity sfaPositionRelationEntity = new SfaPositionRelationEntity();
        sfaPositionRelationEntity.setPositionId(sfaEmployeeInfoModel.getPositionId());
        sfaPositionRelationEntity.setBusinessGroup(internationalEmpValidResultDto.getSfaBusinessGroupEntity().getId());
        sfaPositionRelationEntity.setChannel(sfaEmployeeInfoModel.getChannel());
        sfaPositionRelationEntity.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
        sfaPositionRelationEntity.setEmpId(sfaEmployeeInfoModel.getEmployeeId());
        sfaPositionRelationEntity.setPartTime(0);
        sfaPositionRelationEntity.setPositionTypeId(internationalEmpValidResultDto.getCeoBusinessOrganizationPositionRelation().getPositionTypeId());
        sfaPositionRelationEntity.setParentOrganizationCode(ceoBusinessOrganizationPositionRelation.getOrganizationParentId());
        sfaPositionRelationEntity.setOrganizationCode(ceoBusinessOrganizationPositionRelation.getOrganizationId());
        sfaPositionRelationEntity.setAreaCode(sfaEmployeeInfoModel.getAreaCode());
        sfaPositionRelationEntity.setAreaName(sfaEmployeeInfoModel.getAreaName());
        sfaPositionRelationEntity.setVareaCode(sfaEmployeeInfoModel.getVareaOrganizationId());
        sfaPositionRelationEntity.setVareaName(sfaEmployeeInfoModel.getVareaOrganizationName());
        sfaPositionRelationEntity.setProvinceCode(sfaEmployeeInfoModel.getProvinceOrganizationId());
        sfaPositionRelationEntity.setProvinceName(sfaEmployeeInfoModel.getProvinceOrganizationName());
        sfaPositionRelationEntity.setCompanyCode(sfaEmployeeInfoModel.getCompanyCode());
        sfaPositionRelationEntity.setCompanyName(sfaEmployeeInfoModel.getCompanyName());
        sfaPositionRelationEntity.setDepartmentCode(sfaEmployeeInfoModel.getDepartmentCode());
        sfaPositionRelationEntity.setDepartmentName(sfaEmployeeInfoModel.getDepartmentName());
        sfaPositionRelationEntity.setBranchCode(sfaEmployeeInfoModel.getBranchCode());
        sfaPositionRelationEntity.setBranchName(sfaEmployeeInfoModel.getBranchName());
        LocalDate hireDate = internationalEmpValidResultDto.getHireDate();
        // 默认开始时间为5点
        sfaPositionRelationEntity.setStartValidDate(hireDate.atStartOfDay().plusHours(5));
        sfaPositionRelationEntity.setStatus(1);
        sfaPositionRelationEntity.setDeleteFlag(0);
        sfaPositionRelationEntity.setEndValidDate(LocalDateTime.of(2099,1,1,0,0,0));
        sfaPositionRelationMapper.insert(sfaPositionRelationEntity);
    }

    private SfaEmployeeInfoModel createSfaEmployeeInfo(ApplyMemberPo applyMemberPo, OverseaMemberSaveResponse overseaMemberSaveResponse, InternationalEmpValidResultDto internationalEmpValidResultDto, ProcessUserDO processUserDO) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = new SfaEmployeeInfoModel();
        sfaEmployeeInfoModel.setEmployeeName(applyMemberPo.getUserName());
        sfaEmployeeInfoModel.setApplicationId(applyMemberPo.getId());
        sfaEmployeeInfoModel.setGender(applyMemberPo.getGender());
        sfaEmployeeInfoModel.setMobile(applyMemberPo.getUserMobile());
        sfaEmployeeInfoModel.setEmployeeStatus(EmployeeStatus.ONBOARD.getType());
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = internationalEmpValidResultDto.getCeoBusinessOrganizationPositionRelation();
        sfaEmployeeInfoModel.setPositionId(ceoBusinessOrganizationPositionRelation.getPositionId());
        sfaEmployeeInfoModel.setAffiliation(2);
        sfaEmployeeInfoModel.setPostType(1);
        // 手机号做为工号
        sfaEmployeeInfoModel.setEmployeeId(applyMemberPo.getUserMobile());
        sfaEmployeeInfoModel.setOnboardTime(internationalEmpValidResultDto.getHireDate().atStartOfDay().plusHours(5));
        sfaEmployeeInfoModel.setAreaCode(applyMemberPo.getAreaOrganizationId());
        sfaEmployeeInfoModel.setAreaName(applyMemberPo.getArea());
        sfaEmployeeInfoModel.setVareaOrganizationId(applyMemberPo.getVareaOrganizationId());
        sfaEmployeeInfoModel.setVareaOrganizationName(applyMemberPo.getVareaOrganizationName());
        sfaEmployeeInfoModel.setProvinceOrganizationId(applyMemberPo.getProvinceOrganizationId());
        sfaEmployeeInfoModel.setProvinceOrganizationName(applyMemberPo.getProvinceOrganizationName());
        sfaEmployeeInfoModel.setCompanyCode(applyMemberPo.getCompanyOrganizationId());
        sfaEmployeeInfoModel.setCompanyName(applyMemberPo.getCompany());
        sfaEmployeeInfoModel.setDepartmentCode(applyMemberPo.getBranchOrganizationId());
        sfaEmployeeInfoModel.setDepartmentName(applyMemberPo.getBranchOrganizationName());
        sfaEmployeeInfoModel.setChannel(3);
        sfaEmployeeInfoModel.setCity(applyMemberPo.getAgentCity());
        sfaEmployeeInfoModel.setDistrict(applyMemberPo.getAgentDistrict());
        sfaEmployeeInfoModel.setProvince(applyMemberPo.getAgentProvince());
        sfaEmployeeInfoModel.setCreateTime(new Date());
        sfaEmployeeInfoModel.setCreateUserId(processUserDO.getEmployeeId());
        sfaEmployeeInfoModel.setCreateUserName(processUserDO.getEmployeeName());
        sfaEmployeeInfoModel.setUpdateTime(new Date());
        sfaEmployeeInfoModel.setUpdateUserId(processUserDO.getEmployeeId());
        sfaEmployeeInfoModel.setUpdateUserName(processUserDO.getEmployeeName());
        sfaEmployeeInfoModel.setStatus(1);
        sfaEmployeeInfoModel.setJoiningCompany("Want Want Group");
        sfaEmployeeInfoModel.setType(internationalEmpValidResultDto.getPositionEnum().getCeoType());
        sfaEmployeeInfoModel.setMemberKey(overseaMemberSaveResponse.getMemberKey());
        sfaEmployeeInfoMapper.insert(sfaEmployeeInfoModel);
        return sfaEmployeeInfoModel;
    }


    private OverseaMemberSaveResponse initMemberKey(InternationalEmpImportModel internationalEmpImportModel, InternationalEmpValidResultDto internationalEmpValidResultDto, ProcessUserDO processUserDO) {
        int ex1 = internationalEmpValidResultDto.getCeoExEnum().getEx1();
        CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity = internationalEmpValidResultDto.getCeoBusinessOrganizationViewEntity();

        OverseaMemberManagerOpenAccountRequest overseaMemberManagerOpenAccountRequest = OverseaMemberManagerOpenAccountRequest.builder()
                .areaCode(ceoBusinessOrganizationViewEntity.getOrgId3())
                .areaName(ceoBusinessOrganizationViewEntity.getOrgName3())
                .regionCode(ceoBusinessOrganizationViewEntity.getVirtualAreaId())
                .regionName(ceoBusinessOrganizationViewEntity.getVirtualAreaName())
                .provinceCode(ceoBusinessOrganizationViewEntity.getProvinceId())
                .provinceName(ceoBusinessOrganizationViewEntity.getProvinceName())
                .companyCode(ceoBusinessOrganizationViewEntity.getOrgId2())
                .companyName(ceoBusinessOrganizationViewEntity.getOrgName2())
                .branchCode(ceoBusinessOrganizationViewEntity.getDepartmentId())
                .branchName(ceoBusinessOrganizationViewEntity.getDepartmentName())
                .province(internationalEmpImportModel.getHomeProvince())
                .city(internationalEmpImportModel.getHomeCity())
                .district(internationalEmpImportModel.getHomeDistrict())
                .street(internationalEmpImportModel.getHomeStreet())
                .productGroupId(internationalEmpValidResultDto.getSfaBusinessGroupEntity().getBusinessGroupCode())
                .roleType(String.valueOf(ex1))
                .operator(processUserDO.getEmployeeId())
                .memberName(internationalEmpImportModel.getEmployeeName())
                .mobilePhone(internationalEmpImportModel.getMobile())
                .rangeInfos(internationalEmpValidResultDto.getDistributionInfoRequestList()).build();


        OverseaMemberSaveResponse overseaMemberSaveResponse = internationalConnectorUtil.openAccount(overseaMemberManagerOpenAccountRequest);
        if(Objects.isNull(overseaMemberSaveResponse)){
            throw new ApplicationException("Failed to get memberKey from international service");
        }
        return overseaMemberSaveResponse;
    }

    private void bindPosition(String mobile, String userName, InternationalEmpValidResultDto internationalEmpValidResultDto, ProcessUserDO processUserDO) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = internationalEmpValidResultDto.getCeoBusinessOrganizationPositionRelation();

        LocalDate hireDate = internationalEmpValidResultDto.getHireDate();

        String positionId = ceoBusinessOrganizationPositionRelation.getPositionId();
        // 添加relation业务员相关信息
        employeeMapper.onBoardFor123(positionId, mobile, userName, hireDate.atStartOfDay().plusHours(5),
                processUserDO.getEmployeeId(), InterviewProcessConstants.CHANNEL_SFA);
        // 更新客户表业务员相关信息
        employeeMapper.updateCustomerEmployee(positionId);
    }

    private ApplyMemberPo initBaseApplyMember(InternationalEmpImportModel internationalEmpImportModel, InternationalEmpValidResultDto internationalEmpValidResultDto, ProcessUserDO processUserDO) {
        // 保存applyMember信息
        ApplyMemberPo applyMemberPo = saveApplyMember(internationalEmpImportModel,internationalEmpValidResultDto);

        // 生成interviewProcess,interviewProcessRecord
        initProcess(applyMemberPo,processUserDO,internationalEmpValidResultDto);

        // 保存家庭住址
        saveAddress(applyMemberPo.getId(), internationalEmpImportModel);

        return applyMemberPo;
    }

    private void saveAddress(Integer applyId, InternationalEmpImportModel internationalEmpImportModel) {
        ApplyMemberAdditionPo applyMemberAdditionPo = new ApplyMemberAdditionPo();
        applyMemberAdditionPo.setApplyId(applyId);
        applyMemberAdditionPo.setProvince(internationalEmpImportModel.getHomeProvince());
        applyMemberAdditionPo.setCity(internationalEmpImportModel.getHomeCity());
        applyMemberAdditionPo.setDistrict(internationalEmpImportModel.getHomeDistrict());
        applyMemberAdditionPo.setStreet(internationalEmpImportModel.getHomeStreet());
        applyMemberAdditionPo.setCreateTime(LocalDateTime.now());
        applyMemberAdditionPo.setIsDelete(0);
        sfaApplyMemberAdditionMapper.insert(applyMemberAdditionPo);
    }

    private void initProcess(ApplyMemberPo applyMemberPo, ProcessUserDO processUserDO, InternationalEmpValidResultDto internationalEmpValidResultDto) {
        // 创建sfa_interview_process
        SfaInterviewProcessModel sfaInterviewProcessModel = new SfaInterviewProcessModel();
        sfaInterviewProcessModel.setProcessType(ProcessType.DO_ONBOARD.getProcessCode());
        sfaInterviewProcessModel.setProcessResult(ProcessResult.PASS.getResultCode());
        sfaInterviewProcessModel.setApplicationId(applyMemberPo.getId());
        LocalDate hireDate = internationalEmpValidResultDto.getHireDate();
        // 入职时间为凌晨5点，避免跨日
        Date onBoardTime = LocalDateTimeUtils.convertLDTToDate(hireDate.atStartOfDay().plusHours(5));
        sfaInterviewProcessModel.setCreateTime(new Date());
        sfaInterviewProcessModel.setOnboardTime(onBoardTime);
        sfaInterviewProcessMapper.insert(sfaInterviewProcessModel);


        // 创建sfa_interview_process_record
        SfaInterviewProcessRecordModel sfaInterviewProcessRecordModel = new SfaInterviewProcessRecordModel();
        sfaInterviewProcessRecordModel.setInterviewProcessId(sfaInterviewProcessModel.getId());
        sfaInterviewProcessRecordModel.setProcessUserId(processUserDO.getEmployeeId());
        sfaInterviewProcessRecordModel.setProcessUserName(processUserDO.getEmployeeName());
        sfaInterviewProcessRecordModel.setProcessType(ProcessType.DO_ONBOARD.getProcessCode());
        sfaInterviewProcessRecordModel.setProcessResult(ProcessResult.PASS.getResultCode());

        // 设置操作人组织
        String zbOrganization = organizationMapper.getZbOrganizationIdByBusinessGroup(applyMemberPo.getBusinessGroup());
        sfaInterviewProcessRecordModel.setOrganizationId(zbOrganization);
        sfaInterviewProcessRecordMapper.insert(sfaInterviewProcessRecordModel);

        // 绑定关系
        sfaInterviewProcessModel.setInterviewRecordId(sfaInterviewProcessRecordModel.getId());
        sfaInterviewProcessMapper.updateById(sfaInterviewProcessModel);
    }

    private ApplyMemberPo saveApplyMember(InternationalEmpImportModel internationalEmpImportModel, InternationalEmpValidResultDto internationalEmpValidResultDto) {
        // 构建通用DTO
        ApplyMemberBuildDTO buildDTO = ApplyMemberBuildDTO.builder()
                .userName(internationalEmpImportModel.getEmployeeName())
                .userMobile(internationalEmpImportModel.getMobile())
                .gender(GenderEnum.getGenderEnumByEn(internationalEmpImportModel.getGender()).getId())
                .idCardNum(internationalEmpImportModel.getIdCard())
                .positionEnum(internationalEmpValidResultDto.getPositionEnum())
                .businessGroup(internationalEmpValidResultDto.getSfaBusinessGroupEntity().getId())
                .agentProvince(internationalEmpImportModel.getSalesProvince())
                .agentCity(internationalEmpImportModel.getSalesCity())
                .agentDistrict(internationalEmpImportModel.getSalesDistrict())
                .workPlace(internationalEmpValidResultDto.getOfficeLocationCode())
                .channel(internationalEmpImportModel.getChannel())
                .ceoBusinessOrganizationViewEntity(internationalEmpValidResultDto.getCeoBusinessOrganizationViewEntity())
                .build();
        
        // 使用通用构建服务
        return applyMemberBuildService.buildAndSaveApplyMember(buildDTO);
    }

    private InternationalEmpValidResultDto checkParam(InternationalEmpImportModel internationalEmpImportModel) {
        // 检查手机号是否重复
        checkMobile(internationalEmpImportModel.getMobile());

        // 检查性别是否正确
        checkGender(Optional.ofNullable(internationalEmpImportModel.getGender()).orElse(StringUtils.EMPTY));

        // 检查岗位是否存在
        InternationalPositionEnum internationalPositionEnum = checkPosition(Optional.ofNullable(internationalEmpImportModel.getPosition()).orElse(StringUtils.EMPTY));

        // 获取旺铺角色
        CeoExEnum ceoExEnum = checkEx(internationalPositionEnum.getPositionEnum());

        // 检查渠道是否存在
        Integer workType;
        if(ceoExEnum.getPosition() == CeoExEnum.AREA_MANAGER.getPosition()){
            workType = InternationalChannelEnum.GT.getCode();
        }else{
            workType = checkChannel(internationalEmpImportModel.getChannel());
        }


        // 检查四级地是否正确并获取对应的position
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = checkLocation(internationalEmpImportModel.getSalesProvince(), internationalEmpImportModel.getSalesCity(), internationalEmpImportModel.getSalesDistrict(),internationalPositionEnum.getPositionEnum(),workType);

        // 检查产品组是否存在
        SfaBusinessGroupEntity sfaBusinessGroupEntity = checkBusinessGroup(ceoBusinessOrganizationPositionRelation.getBusinessGroup());

        // 检查办公地点是否正确
        String officeLocationCode = checkOfficeLocation(internationalEmpImportModel.getOfficeLocation(),sfaBusinessGroupEntity.getId());


        // 根据组织code检查view表是否存在
        CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity = checkView(ceoBusinessOrganizationPositionRelation.getOrganizationId());

        // 检查入职日期格式是否正确
        LocalDate hireDate = checkHireDate(internationalEmpImportModel.getHireDate());

        // 检查入职时间是否冲突
        checkInterviewService.checkDateConflict(hireDate,ceoBusinessOrganizationPositionRelation.getPositionId(), internationalEmpImportModel.getMobile());

        // 获取管辖四级地
        List<DistributionInfoRequest> distributionInfo = searchDistributionInfo(ceoBusinessOrganizationPositionRelation.getOrganizationId());

        return InternationalEmpValidResultDto.builder().ceoBusinessOrganizationViewEntity(ceoBusinessOrganizationViewEntity)
                .positionEnum(internationalPositionEnum.getPositionEnum()).roleId(internationalPositionEnum.getRoleId()).hireDate(hireDate).ceoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation)
                .sfaBusinessGroupEntity(sfaBusinessGroupEntity).distributionInfoRequestList(distributionInfo).ceoExEnum(ceoExEnum).officeLocationCode(officeLocationCode).build();
    }


    private String checkOfficeLocation(String officeLocation,Integer businessGroup) {
        String organizationIdByNameType = organizationMapper.getOrganizationIdByNameType(Optional.ofNullable(officeLocation).orElse(StringUtils.EMPTY), OrganizationTypeEnum.COMPANY.getOrganizationType(), businessGroup);
        if(StringUtils.isBlank(organizationIdByNameType)){
            throw new ApplicationException("Failed to get office location");
        }
        return organizationIdByNameType;
    }

    private void checkGender(String gender) {
        GenderEnum genderEnumByEn = GenderEnum.getGenderEnumByEn(gender.trim());
        if(Objects.isNull(genderEnumByEn) || genderEnumByEn.getId() == 0){
            throw new ApplicationException("Gender does not exist");
        }
    }

    private CeoExEnum checkEx(PositionEnum positionEnum) {
        CeoExEnum ceoExEnum = CeoExEnum.findByPosition(positionEnum.getCeoType(), positionEnum.getPosition(), positionEnum.getJobsType());
        if(Objects.isNull(ceoExEnum)){
            throw new ApplicationException("Position does not exist in ceoExEnum");
        }
        return ceoExEnum;
    }

    private List<DistributionInfoRequest> searchDistributionInfo(String organizationId) {
        String organizationType = organizationMapper.getOrganizationType(organizationId);
        if(StringUtils.isBlank(organizationType)){
            throw new ApplicationException("Organization Data error");
        }

        return organizationBindRelationMapper.searchDistributionInfo(organizationId,organizationType);
    }

    private Integer checkChannel(String channel) {
        Integer code = InternationalChannelEnum.getCode(channel);
        if(Objects.isNull(code)){
            throw new ApplicationException("channel does not exist");
        }
        return code;
    }

    private SfaBusinessGroupEntity checkBusinessGroup(Integer businessGroup) {
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("Business group does not exist");
        }

        return sfaBusinessGroupEntity;
    }

    private LocalDate checkHireDate(String hireDate) {
        // 检查日期格式是否是 dd-MM-yyyy的格式
        if(StringUtils.isBlank(hireDate)){
            throw new ApplicationException("Hire date is required");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(hireDate, formatter);
    }

    private CeoBusinessOrganizationViewEntity checkView(String organizationId) {
        CeoBusinessOrganizationViewEntity ceoBusinessOrganizationViewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                .eq(CeoBusinessOrganizationViewEntity::getOrganizationId, organizationId).last("limit 1"));
        if(Objects.isNull(ceoBusinessOrganizationViewEntity)){
            throw new ApplicationException("Organization information exception");
        }

        return ceoBusinessOrganizationViewEntity;
    }

    private CeoBusinessOrganizationPositionRelation checkLocation(String salesProvince, String salesCity, String salesDistrict, PositionEnum positionEnum, Integer workType) {
        String organizationId = organizationBindRelationMapper.selectSfaOrgCodeByLocation(salesProvince, salesCity, salesDistrict,positionEnum,workType);
        if(StringUtils.isBlank(organizationId)){
            throw new ApplicationException("No corresponding position information found for the sales region");
        }

        // 通过组织ID获取岗位信息
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId).last("limit 1"));
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            throw new ApplicationException("No corresponding position information found for the sales region");
        }
        return ceoBusinessOrganizationPositionRelation;
    }

    private InternationalPositionEnum checkPosition(String position) {
        InternationalPositionEnum internationalPositionEnum = InternationalPositionEnum.getEnumByPositionName(position.trim());
        if(Objects.isNull(internationalPositionEnum)){
            throw new ApplicationException("Position does not exist");
        }
        return internationalPositionEnum;
    }


    private void checkMobile(String mobile) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getMobile, mobile).last("limit 1"));
        if(Objects.nonNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("Mobile number already exists");
        }
    }
}
