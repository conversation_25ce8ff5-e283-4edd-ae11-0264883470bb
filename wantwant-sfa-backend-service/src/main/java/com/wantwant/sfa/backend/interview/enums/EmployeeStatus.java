package com.wantwant.sfa.backend.interview.enums;

import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * @Description: 员工状态枚举。
 * @Auther: zhengxu
 * @Date: 2021/11/19/上午11:07
 */
@Getter
public enum EmployeeStatus {
    PROBATION(1, "试岗中", "On Probation"),
    ONBOARD(2, "在职", "Employed"),
    PROBATION_FAILED(3, "试岗失败", "Probation Failed"),
    ELIMINATE(4, "主动汰换", "Voluntary Elimination"),
    ELIMINATE_AUTO(5, "自主汰换", "Self Elimination"),
    RESIGN(6, "离职", "Resigned"),
    ONBOARD_TERMINATE(7, "入职终止", "Onboarding Terminated");

    public static String findNameByType(int type){
        EmployeeStatus[] values = EmployeeStatus.values();
        for(EmployeeStatus v : values){
            if(v.getType() == type){
                return v.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static String getDescription(Integer type) {
        if (Objects.isNull(type)) {
            return StringUtils.EMPTY;
        }
        EmployeeStatus[] values = EmployeeStatus.values();
        String flag = RequestUtils.getLanguage();
        for (EmployeeStatus v : values) {
            if (Objects.equals(v.getType(), type)) {
                if (CommonConstant.LANGUAGE_ENGLISH.equals(flag)) {
                    return v.getEnDescription();
                }
                return v.getName();
            }
        }
        return StringUtils.EMPTY;
    }

    public static EmployeeStatus findEnumByType(int type){
        EmployeeStatus[] values = EmployeeStatus.values();
        for(EmployeeStatus v : values){
            if(v.getType() == type){
                return v;
            }
        }
        return null;
    }

    public static String getDescByEnv(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }
        String language = RequestUtils.getLoginInfo().getLanguage();
        for (EmployeeStatus value : EmployeeStatus.values()) {
            if (value.getName().equals(desc)) {
                if (CommonConstant.LANGUAGE_ENGLISH.equals(language)) {
                    return value.getEnDescription();
                }
                return value.getName();
            }
        }
        return null;
    }

    EmployeeStatus(int type, String name,String enDescription) {
        this.type = type;
        this.name = name;
        this.enDescription = enDescription;
    }

    private final Integer type;
    private final String name;
    private final String enDescription;


}
