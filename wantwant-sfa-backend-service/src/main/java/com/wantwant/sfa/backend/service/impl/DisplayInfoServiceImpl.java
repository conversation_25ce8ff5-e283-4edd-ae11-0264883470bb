package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gexin.fastjson.JSON;
import com.gexin.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.afterSales.vo.OrgQuotaSurplusVO;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.barcode.dto.BranchDisplayLimitDTO;
import com.wantwant.sfa.backend.barcode.dto.DisplayLimitSkuDTO;
import com.wantwant.sfa.backend.barcode.dto.SpuQuota;
import com.wantwant.sfa.backend.barcode.service.IBarcodeSearchService;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.display.dto.DisplayProcessInfoDTO;
import com.wantwant.sfa.backend.display.dto.ParentOrganizationDTO;
import com.wantwant.sfa.backend.display.enums.ProcessType;
import com.wantwant.sfa.backend.display.request.*;
import com.wantwant.sfa.backend.display.vo.*;
import com.wantwant.sfa.backend.employeeInfo.model.EmployeeInfoModel;
import com.wantwant.sfa.backend.employeeInfo.service.IEmployeeInfoService;
import com.wantwant.sfa.backend.enums.DisplayApplyTypeEnum;
import com.wantwant.sfa.backend.enums.ShopChannelTypeEnums;
import com.wantwant.sfa.backend.enums.ShopSalesRoomTypeEnums;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.activityQuota.QuotaBigDataMapper;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.display.*;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.display.*;
import com.wantwant.sfa.backend.service.*;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.CollectorUtil;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.wallet.service.IWalletSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;

/**
* 特陈信息 服务实现类
*
* @since 2022-05-18
*/
@Slf4j
@Service
public class DisplayInfoServiceImpl extends ServiceImpl<DisplayInfoMapper, DisplayInfoPO> implements DisplayInfoService {

	@Autowired
	private DisplayProcessService processService;

	@Autowired
	private OrganizationMapper organizationMapper;

	@Autowired
	private SettingServiceImpl settingService;

	@Autowired
	private QuotaBigDataMapper quotaBigDataMapper;

	@Autowired
	private DisplayDetailMapper displayDetailMapper;

	@Autowired
	private DisplaySkuMapper displaySkuMapper;

	@Autowired
	private CeoBusinessOrganizationPositionRelationMapper relationMapper;

	@Autowired
	private InterfaceLogMapper interfaceLogMapper;


    @Autowired
    private DisplayProcessDetailMapper processDetailMapper;


	@Autowired
	private IBarcodeSearchService barcodeSearchService;

	@Autowired
	private DisplayExclusiveService displayExclusiveService;

	@Autowired
	private ICheckCustomerService checkCustomerService;

	@Autowired
	private DisplayInfoCheckService displayInfoCheckService;

	@Autowired
	private SfaBusinessGroupMapper sfaBusinessGroupMapper;

	@Autowired
	private IEmployeeInfoService employeeInfoService;
	@Autowired
	private IWalletSearchService walletSearchService;
	@Resource
	private DisplayProcessService displayProcessService;
	@Resource
	private DisplayInfoProtocolFileMapper displayInfoProtocolFileMapper;
	@Resource
	private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
	@Resource
	private ConfigMapper configMapper;
	@Resource
	private DisplayWarningConfigMapper displayWarningConfigMapper;
	@Resource
	private WantWalletLogMapper wantWalletLogMapper;

	/**
	 * 根据申请编号更新或保存特陈数据，发起流程
	 *
	 * @param request
	 * @return: int
	 * @date: 5/19/22 2:37 PM
	 */
	@Override
	@Transactional(rollbackFor = ApplicationException.class)
	public int saveOrUpdateByNo(DisplayInfoRequest request) {
		log.info("特称发起入参:{}", JSONObject.toJSONString(request));
		try {
			interfaceLogMapper.insert(new InterfaceLogPO(request.getApplicationNo(), 1, JSON.toJSONString(request), "", LocalDateTime.now()));
		} catch (Exception e) {
			log.error("特陈接口信息保存失败！{}",e.getMessage());
		}
		Integer id = 0;
		DisplayInfoPO displayInfoPO = baseMapper.selectOne(new QueryWrapper<DisplayInfoPO>().
				eq("application_no", request.getApplicationNo()).
				eq("is_delete", 0));
		List<DisplayDetail> details = request.getDetails();
		BigDecimal total = BigDecimal.ZERO;
		if (CommonUtil.ListUtils.isNotEmpty(details)){
			total = details.stream().filter(f -> Objects.nonNull(f.getApplyQuota())).collect(CollectorUtil.summingBigDecimal(DisplayDetail::getApplyQuota));
		}

		Integer businessGroupId = sfaBusinessGroupMapper.queryBusinessGroupId(request.getProductGroupId());
		String orgCode = "";
		String orgName = "";
		if (CommonUtil.StringUtils.isNotBlank(request.getBranchName())) {
			organizationMapper.getOrganizationIdByName(request.getBranchName(),3,businessGroupId);
			orgCode = organizationMapper.selectOrgByName(request.getBranchName(),request.getProductGroupId());
			orgName = request.getBranchName();
		}else{
			orgCode = employeeInfoService.selectParentOrgCode(request.getPartnerMemberKey(),businessGroupId);
			orgName = organizationMapper.getOrganizationName(orgCode);
		}

		String organizationType = organizationMapper.getOrganizationType(orgCode);

		String expenseReceiptImagesStr = StringUtils.EMPTY;
		List<String> expenseReceiptImages = request.getExpenseReceiptImages();
		if(!CollectionUtils.isEmpty(expenseReceiptImages)){
			expenseReceiptImagesStr = String.join(",",expenseReceiptImages);
		}

		if (Objects.isNull(displayInfoPO)) {
			DisplayInfoPO po = new DisplayInfoPO();
			BeanUtils.copyProperties(request,po);
			po.setExpenseReceiptImages(expenseReceiptImagesStr);
//			request.setBranchCode(orgCode);
//			po.setBranchCode(orgCode);
			request.setOrganizationId(orgCode);
			po.setStoreArea(request.getStoreAreaDesc());
			po.setOrganizationId(orgCode);
			po.setOrganizationName(orgName);
			po.setOrganizationId(orgCode);
			po.setOrganizationName(orgName);
			po.setOrganizationType(organizationType);
			po.setQuota(total);
			po.setDeptQuota(total);

			// 根据申请人获取上级组织
			String parentOrgCode = employeeInfoService.selectParentOrgCode(request.getPartnerMemberKey(),businessGroupId);
			// 根据上级组织类型选择旺金币类型
			po.setQuotaType(DisplayApplyTypeEnum.findQuotaType(request.getRuleType(),organizationMapper.getOrganizationType(parentOrgCode)));
			baseMapper.insert(po);
			id = po.getId();
		}else {
			BeanUtils.copyProperties(request,displayInfoPO);
//			request.setBranchCode(orgCode);
//			displayInfoPO.setBranchCode(orgCode);
			request.setOrganizationId(orgCode);
			displayInfoPO.setExpenseReceiptImages(expenseReceiptImagesStr);
			displayInfoPO.setStoreArea(request.getStoreAreaDesc());
			displayInfoPO.setOrganizationId(orgCode);
			displayInfoPO.setOrganizationName(orgName);
			displayInfoPO.setOrganizationType(organizationMapper.getOrganizationType(orgCode));
			displayInfoPO.setQuota(total);
			displayInfoPO.setDeptQuota(total);
			displayInfoPO.setIsGrant(0);
			displayInfoPO.setGrantFailureReason("");
			displayInfoPO.setAuditStatus(0);
			displayInfoPO.setAuditTime(null);
			displayInfoPO.setDepartmentImageUrl("");
			displayInfoPO.setPassCode("");
			baseMapper.updateById(displayInfoPO);
			id = displayInfoPO.getId();
			List<DisplayDetailPO> detailList = displayDetailMapper.selectList(new QueryWrapper<DisplayDetailPO>().eq("info_id", id));
			if (CommonUtil.ListUtils.isNotEmpty(detailList)){
				detailList.forEach(d -> {
					displayDetailMapper.deleteById(d.getId());
					displaySkuMapper.delete(new QueryWrapper<DisplaySkuPO>().eq("d_id",d.getId()));
				});
			}

			// 删除特陈协议
			displayInfoProtocolFileMapper.delete(new LambdaQueryWrapper<DisplayInfoProtocolFilePO>()
					.eq(DisplayInfoProtocolFilePO::getInfoId,id).eq(DisplayInfoProtocolFilePO::getDeleteFlag,0));

			//设置驳回流程状态关闭
			DisplayProcessPO processPO = new DisplayProcessPO();
			processPO.setState(1);
			processService.update(processPO,new QueryWrapper<DisplayProcessPO>().eq("info_id",id)
					.eq("state",0));
		}
		if (CommonUtil.ListUtils.isNotEmpty(details)) {
			for (DisplayDetail d : details) {
				DisplayDetailPO detailPO = new DisplayDetailPO();
				BeanUtils.copyProperties(d,detailPO);
				detailPO.setInfoId(id);
				BigDecimal applyQuota = detailPO.getApplyQuota();
				detailPO.setDeptItemQuota(applyQuota);
				displayDetailMapper.insert(detailPO);
				List<DisplaySku> skuList = d.getSkuList();
				int size = skuList.size();
				if (CommonUtil.ListUtils.isNotEmpty(skuList)){
					for (DisplaySku sku : skuList) {
						DisplaySkuPO skuPO = new DisplaySkuPO();
						BeanUtils.copyProperties(sku,skuPO);
						skuPO.setInfoId(id);
						skuPO.setDId(detailPO.getId());
						//sku平摊金额
						if(applyQuota.compareTo(BigDecimal.ZERO) > 0 && size > 0){
							skuPO.setMilkQuota(applyQuota.divide(new BigDecimal(size),2,BigDecimal.ROUND_HALF_UP));
						}
						displaySkuMapper.insert(skuPO);
					}
				}
				//专属助陈品
				List<DisplayExclusive> exclusiveList = d.getExclusiveList();
                if (CommonUtil.ListUtils.isNotEmpty(exclusiveList)) {
                    List<DisplayExclusivePO> exclusivePOList = new ArrayList<>();
                    for (DisplayExclusive exclusive : exclusiveList) {
                        DisplayExclusivePO de = new DisplayExclusivePO();
                        BeanUtils.copyProperties(exclusive,de);
                        de.setInfoId(id);
                        de.setDId(detailPO.getId());
                        exclusivePOList.add(de);
                    }
                    displayExclusiveService.saveBatch(exclusivePOList);
                }
            }
		}

		List<DisplayProtocolFile> displayProtocolFiles = request.getDisplayProtocolFiles();
		if(CommonUtil.ListUtils.isNotEmpty(displayProtocolFiles)){
			Integer finalId = id;
			displayProtocolFiles.forEach(e -> {
				DisplayInfoProtocolFilePO displayInfoProtocolFilePO = new DisplayInfoProtocolFilePO();
				displayInfoProtocolFilePO.setInfoId(finalId);
				displayInfoProtocolFilePO.setFileName(e.getFileName());
				displayInfoProtocolFilePO.setType(e.getType());
				displayInfoProtocolFilePO.setUrl(e.getUrl());
				displayInfoProtocolFileMapper.insert(displayInfoProtocolFilePO);
			});
		}

		//Integer businessGroupId = sfaBusinessGroupMapper.queryBusinessGroupId(request.getProductGroupId());
		//创建初始流程
		processService.createInitProcess(id,request);
		return id;
	}

	/**
	 * 根据memberKey获取分公司剩余额度
	 * 提供旺铺-王苏斌
	 *
	 * @param memberKey
	 * @return: com.wantwant.sfa.backend.display.vo.SurplusQuotaVO
	 * @date: 3/8/23 6:28 PM
	 */
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public SurplusQuotaVO querySurplusQuota(String memberKey) {
		SurplusQuotaVO vo = new SurplusQuotaVO();
		ParentOrganizationDTO orgDto = relationMapper.selectCompanyOrganizationByMemberkey(memberKey);

		if (Objects.nonNull(orgDto) && CommonUtil.StringUtils.isNotBlank(orgDto.getCompanyOrganizationId())){
			vo.setCompanyOrganizationId(orgDto.getCompanyOrganizationId());
			vo.setCompanyOrganizationName(orgDto.getCompanyOrganizationName());
//			BranchDisplayLimitDTO limitDTO = displayInfoCheckService.getBranchDisplayLimit(orgDto.getCompanyOrganizationId());
			// V11.5.0获取新表使用额度
			String year = configMapper.getValueByCode("display_festival_year");
			if(StringUtils.isBlank(year)){
				return vo;
			}

			BranchDisplayLimitDTO limitDTO = displayInfoCheckService.getNewBranchDisplayLimit(orgDto.getCompanyOrganizationId(),year);
			CompanyQuotaVO companyQuota = baseMapper.getCompanyUsed(orgDto.getCompanyOrganizationId());


			//减去条码费已锁定，已使用
            // V11.5.0去除条码费及试吃
//			BarcodeDisplayCostDTO costDTO = barcodeSearchService.queryDisplayQuota(orgDto.getCompanyOrganizationId());
			vo.setSurplus(limitDTO.getTotalLimit().subtract(companyQuota.getUsed()).subtract(companyQuota.getLock()));
			//财年纯奶业绩
			MilkQuotaVO milkUsed = baseMapper.getMilkUsed(orgDto.getCompanyOrganizationId());
			if (Objects.nonNull(milkUsed)) {
				vo.setMilkSurplus(limitDTO.getMilkLimit().subtract(milkUsed.getUsed().add(milkUsed.getLock())));
			}
		}
		return vo;
	}

	/**
	 * 根据申请编号获取审批节点
	 * 提供旺铺-王苏斌
	 *
	 * @param applicationNo
	 * @return: com.wantwant.sfa.backend.display.vo.DisplayProcessInfoVO
	 * @date: 7/10/23 7:04 PM
	 */
	@Override
	public DisplayProcessInfoVO processInfo(String applicationNo) {
		DisplayProcessInfoVO info = baseMapper.queryProcessInfo(applicationNo);
		if (Objects.nonNull(info) && CommonUtil.StringUtils.isNotBlank(info.getReviewerName())) {
			String[] split = info.getReviewerName().split("-");
			info.setPosition(split[split.length -2]);
			info.setReviewerName(split[split.length-1]);
		}
		return info;
	}

	/**
	 * 特陈取消
     *
	 * @param request
	 * @return: java.lang.Integer
	 * @date: 7/21/22 9:54 AM
	 */
    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public Integer cancel(DisplayCancelRequest request) {
		log.info("特称取消入参:{}",request);
        DisplayInfoPO displayInfoPO = baseMapper.selectOne(new QueryWrapper<DisplayInfoPO>().
                eq("application_no", request.getApplicationNo()).
                eq("is_delete", 0));
		Integer id = 0;
        if (Objects.nonNull(displayInfoPO)) {
			Integer businessGroundId = sfaBusinessGroupMapper.queryBusinessGroupId(displayInfoPO.getProductGroupId());
			processService.cancelProcess(displayInfoPO.getId(),request.getPartnerMemberKey(),businessGroundId);
			id = displayInfoPO.getId();
        }else{
			throw new ApplicationException("不存在申请编号：" + request.getApplicationNo() + "的特称");
		}
		try {
			interfaceLogMapper.insert(new InterfaceLogPO(request.getApplicationNo(), 2, JSON.toJSONString(request), String.valueOf(id), LocalDateTime.now()));
		} catch (Exception e) {
			log.error("特陈接口信息取消失败！{}",e.getMessage());
		}
        return id;
    }

    /**
	 * 获取分公司额度
	 *
	 * @param companyOrganizationId 分公司OrganizationId
	 * @return: com.wantwant.sfa.backend.display.vo.CompanyQuotaVO
	 * @date: 5/19/22 3:12 PM
	 */
	@Override
	public CompanyQuotaVO queryCompanyQuota(String companyOrganizationId) {
		//区域经理查分公司维度
		String organizationType = organizationMapper.getOrganizationType(companyOrganizationId);
		if ("department".equals(organizationType)){
			companyOrganizationId = organizationMapper.getOrganizationParentId(companyOrganizationId);
		}else if (!"company".equals(organizationType)) {
			return null;
		}

        CompanyQuotaVO companyQuotaVO = new CompanyQuotaVO();

		BigDecimal surplus = wantWalletLogMapper.selectSurplus(companyOrganizationId);
		companyQuotaVO.setOrgQuota(surplus);

		OrganizationQuotaNewVO companyQuotaNew = getSkuQuota(companyOrganizationId, null,"company");
		if (Objects.nonNull(companyQuotaNew)) {
			BeanUtils.copyProperties(companyQuotaNew,companyQuotaVO);
			companyQuotaVO.setUsed(companyQuotaNew.getUsedAll());
			companyQuotaVO.setCompanyName(companyQuotaNew.getOrganizationName());

			List<OrgSpuQuotaVO> specSpu = companyQuotaNew.getSpecSpu();
		}
		log.info("获取分公司额度:{}", com.alibaba.fastjson.JSON.toJSONString(companyQuotaVO));
		return companyQuotaVO;
	}



	/**
	 * 获取分公司/合伙人/陈列客户额度
	 *
	 * @param request
	 * @return: com.wantwant.sfa.backend.display.vo.QuotaVO
	 * @date: 9/20/22 3:00 PM
	 */
	@Override
	public QuotaVO queryQuota(QuotaRequest request) {
		QuotaVO quota = new QuotaVO();



		if (CommonUtil.StringUtils.isNotBlank(request.getCompanyOrganizationId())){
			OrganizationQuotaNewVO companyQuota = getSkuQuota(request.getCompanyOrganizationId(),null,"company");
			quota.setCompanyQuota(companyQuota);
		}
		if (CommonUtil.StringUtils.isNotBlank(request.getDepartmentId())){
			OrganizationQuotaNewVO departmentQuota = getSkuQuota(request.getDepartmentId(),null,"department");
			quota.setDepartmentQuota(departmentQuota);
		}
		if (CommonUtil.StringUtils.isNotBlank(request.getMemberKey())){
			OrganizationQuotaNewVO empQuota = getSkuQuota(null,request.getMemberKey(),null);
			quota.setEmpQuota(empQuota);

		}

		DisplayWarningConfigPO displayWarningConfigPO = displayWarningConfigMapper.selectOne(new LambdaQueryWrapper<DisplayWarningConfigPO>().eq(DisplayWarningConfigPO::getBusinessGroup, RequestUtils.getBusinessGroup())
				.eq(DisplayWarningConfigPO::getDeleteFlag, 0).last("limit 1"));
		if(Objects.nonNull(displayWarningConfigPO)){
			quota.setWarningRate(displayWarningConfigPO.getRate());
		}


		return quota;
	}

	/**
	 * 无规则显示费用分析
	 */
	@Override
	public CostAnalysisInfoVO costAnalysis(QuotaRequest request) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		CostAnalysisInfoVO infoVO = new CostAnalysisInfoVO();
		if (CommonUtil.StringUtils.isNotBlank(request.getCompanyOrganizationId())){
			CostAnalysisDetailVO company = baseMapper.getOrganizationCostUsed(request.getCompanyOrganizationId(),"company",request.getCreateTime());
			infoVO.setCompany(company);
		}
		if (CommonUtil.StringUtils.isNotBlank(request.getDepartmentId())){
			CostAnalysisDetailVO department = baseMapper.getOrganizationCostUsed(request.getDepartmentId(),"department",request.getCreateTime());
			infoVO.setDepartment(department);
		}
		if (CommonUtil.StringUtils.isNotBlank(request.getMemberKey())){
			CostAnalysisDetailVO emp = baseMapper.getEmpCostUsed(request.getMemberKey(),request.getCreateTime(),loginInfo.getBusinessGroup());
			infoVO.setEmp(emp);
			List<CostAnalysisDetailVO> spuList = baseMapper.getEmpCostUsedSpu(request.getMemberKey(),request.getCreateTime(),loginInfo.getBusinessGroup());
			infoVO.setSpuList(spuList);
		}
		return infoVO;
	}

	/**
	 * 根据组织id获取特陈明细
	 *
	 * @param orgId
	 * @return: com.wantwant.sfa.backend.display.vo.OrganizationQuotaNewVO
	 * @date: 6/26/23 7:32 PM
	 */
	@Override
	public OrganizationQuotaNewVO getSkuQuota(String orgId,String memberKey,String orgType) {
		log.info("【display_getSkuQuota】orgId:{},memberKey:{},orgType:{}",orgId,memberKey,orgType);
		OrganizationQuotaNewVO orgQuota = new OrganizationQuotaNewVO();
        List<DisplayLimitSkuDTO> displaySkuLimit = Lists.newArrayList();
		String festival = configMapper.getValueByCode("display_festival_year");

		if(StringUtils.isBlank(festival)){
			return null;
		}

		if (CommonUtil.StringUtils.isNotBlank(orgId)) {
			orgQuota = baseMapper.getOrganizationUsed(orgId,orgType);
			String organizationName = organizationMapper.getOrganizationName(orgId);
			orgQuota.setOrganizationName(organizationName);
            //查询ads.display_limit_sku_department
//			displaySkuLimit = displayInfoCheckService.getDisplaySkuLimit(orgId,"company".equalsIgnoreCase(orgType)?"分公司":"营业所");

			displaySkuLimit = displayInfoCheckService.getNewDisplaySkuLimit(orgId,"company".equalsIgnoreCase(orgType)?"分公司":"营业所",festival);
		}else{
			orgQuota = baseMapper.getEmpUsed(memberKey);
			if (Objects.isNull(orgQuota)) orgQuota = new OrganizationQuotaNewVO();
			if (Objects.nonNull(orgQuota) && null != orgQuota.getBranchCode()) {
				orgId = orgQuota.getBranchCode();
			}
			displaySkuLimit = displayInfoCheckService.getNewDisplaySkuLimit(memberKey,"合伙人",festival);
        }
		log.info("【display_getSkuQuota_orgQuota】orgQuota:{}",orgQuota);
        BigDecimal usedAll = orgQuota.getUsed();
		//查询条码费
        // V11.5.0取消条码费用检查
//		BarcodeDisplayCostDTO costDTO = barcodeSearchService.queryDisplayQuota(orgId);
//		if (null != costDTO.getUsedQuota() && costDTO.getUsedQuota().compareTo(BigDecimal.ZERO) > 0) {
//			usedAll = orgQuota.getUsed().add(costDTO.getUsedQuota());
//		}
		orgQuota.setLockedQuotaTm(BigDecimal.ZERO);
		orgQuota.setUsedQuotaTm(BigDecimal.ZERO);
		orgQuota.setLock(orgQuota.getLock());

		List<OrgSpuQuotaVO> specSpu = Lists.newArrayList();
        //总额度
        BigDecimal displayCost = displaySkuLimit.stream().collect(CollectorUtil.summingBigDecimal(DisplayLimitSkuDTO::getControlDistributionLimit));
		if (CommonUtil.ListUtils.isNotEmpty(displaySkuLimit)){
			//设置spu已使用
			Map<String, SpuQuota> spuQuotaMap = Maps.newHashMap();
            List<SpuQuota> spuQuotaList = baseMapper.getSpuUsed(orgId,orgType);
            spuQuotaList.forEach(s -> spuQuotaMap.put(s.getSpuId(),s));
            //设置spu已锁定
			Map<String, SpuQuota> spuInitMap = Maps.newHashMap();
			List<SpuQuota> spuInitList = baseMapper.getSpuInit(orgId);
			spuInitList.forEach(s -> spuInitMap.put(s.getSpuId(),s));
            displaySkuLimit.forEach(d -> {
                if (MapUtils.isNotEmpty(spuQuotaMap)) {
                    SpuQuota spuQuota = spuQuotaMap.get(d.getSpuId());
                    SpuQuota spuInitQuota = spuInitMap.get(d.getSpuId());
					BigDecimal spuInit = BigDecimal.ZERO;
                    if (Objects.nonNull(spuInitQuota)){
                    	spuInit = Optional.ofNullable(spuInitQuota.getInitQuota()).orElse(BigDecimal.ZERO);
						d.setSpuInit(spuInit);
					}
                    //设置剩余额度
                    if (Objects.nonNull(spuQuota)){
						BigDecimal spuUsed = Optional.ofNullable(spuQuota.getUsed()).orElse(BigDecimal.ZERO);
						BigDecimal spuLock = Optional.ofNullable(spuQuota.getLock()).orElse(BigDecimal.ZERO);
                        d.setSpuUsed(spuUsed);
                        d.setSpuLock(spuLock);
						d.setControlDistributionLimit(d.getControlDistributionLimit().add(spuInit));
                        d.setSpuSurplus(d.getControlDistributionLimit().subtract(d.getSpuUsed()).subtract(d.getSpuLock()));
                    }else{
                        d.setSpuSurplus(d.getControlDistributionLimit());
                    }
                }
                //特殊品
				if (null != d.getWriteOffLimit() && d.getWriteOffLimit().compareTo(BigDecimal.ZERO) > 0){
					OrgSpuQuotaVO orgSpuQuota = new OrgSpuQuotaVO();
					BeanUtils.copyProperties(d,orgSpuQuota);
					specSpu.add(orgSpuQuota);
				}
            });
			orgQuota.setSpecSpu(specSpu);
            //试吃已使用
            // V11.5.0取消试吃已使用
//            BigDecimal usedQuotaSc = displaySkuLimit.stream().findFirst().orElseGet(DisplayLimitSkuDTO::new).getRecommendedForetasteLimit();

            orgQuota.setAmount(displaySkuLimit.stream().collect(CollectorUtil.summingBigDecimal(DisplayLimitSkuDTO::getTotalGmv)));
            if (null == displayCost){
                displayCost = BigDecimal.ZERO;
            }


            //添加总期初
            if (null != orgQuota.getInitial()) {
                displayCost = displayCost.add(orgQuota.getInitial());
            }
            orgQuota.setDisplayCost(displayCost);
//            orgQuota.setUsedQuotaSc(usedQuotaSc);
//            if (null != usedQuotaSc && usedQuotaSc.compareTo(BigDecimal.ZERO) > 0) {
//                usedAll = usedAll.add(usedQuotaSc);
//            }
			orgQuota.setUsedAll(usedAll);
            orgQuota.setSurplus(displayCost.subtract(orgQuota.getUsed()).subtract(orgQuota.getLock()));
			if (null != orgQuota.getAmount() && orgQuota.getAmount().compareTo(BigDecimal.ZERO) > 0) {
				orgQuota.setRate(orgQuota.getUsedAll().divide(orgQuota.getAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
			}
		}else if (null != orgQuota){
			orgQuota.setUsedAll(usedAll);
			orgQuota.setDisplayCost(orgQuota.getInitial());
			if (null != orgQuota && null != orgQuota.getInitial()) {
				orgQuota.setSurplus(orgQuota.getInitial().subtract(orgQuota.getUsed()).subtract(orgQuota.getLock()));
			}
		}
		return orgQuota;
	}

	/**
	 * 特称审批列表
	 *
	 * @param request
	 * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.wantwant.sfa.backend.display.vo.DisplayInfoVO>
	 * @date: 5/19/22 6:08 PM
	 */
	@Override
	public IPage<DisplayInfoVO> queryByPage(DisplayQueryRequest request) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		String businessGroupCode = sfaBusinessGroupMapper.queryBusinessGroupCode(loginInfo.getBusinessGroup());
		request.setBusinessGroup(loginInfo.getBusinessGroup());
		request.setBusinessGroupCode(businessGroupCode);
		if (CommonUtil.StringUtils.isBlank(request.getOrganizationType())) {
			request.setOrganizationType(loginInfo.getOrganizationType());
		}
		if (CommonUtil.StringUtils.isBlank(request.getOrganizationId())){
			List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(),loginInfo);
			if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {
				request.setOrganizationId(employeeOrganizationId.get(0));
			}else{
				request.setOrganizationIds(employeeOrganizationId);
			}
		}
		if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {

            Integer operateRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
                    .eq(RoleEmployeeRelationEntity::getEmployeeId, request.getEmployeeId()).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
                    .eq(RoleEmployeeRelationEntity::getRoleId, 63))).orElse(0);
            if(operateRole > 0){
                request.setProcessType(ProcessType.CUSTOMER_SERVICE_AUDIT.getProcessCode());
            }


            Integer businessRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
                    .eq(RoleEmployeeRelationEntity::getEmployeeId, request.getEmployeeId()).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
                    .eq(RoleEmployeeRelationEntity::getRoleId, 35))).orElse(0);
            if(businessRole > 0){
                request.setProcessType(ProcessType.BUSINESS_AUDIT.getProcessCode());
            }
		}

		Page<DisplayInfoVO> page = new Page<>(request.getPage(), request.getRows());
		List<DisplayInfoVO> list = baseMapper.selectPageBySql(page, request);
		if(CollectionUtils.isNotEmpty(list)){
			list.forEach(e -> {
				Integer quotaType = e.getQuotaType();
				if(Objects.nonNull(quotaType)){
					DisplayApplyTypeEnum displayApplyTypeEnum = DisplayApplyTypeEnum.findQuota(quotaType);
					if(Objects.nonNull(displayApplyTypeEnum)){
						e.setQuotaTypeStr(displayApplyTypeEnum.getName());
					}
				}
			});
		}
		/*list.forEach(d ->
			d.setMonthDisplayQuota(quotaBigDataMapper.selectEmployeeDisplayAuota(d.getPartnerMemberKey(),LocalDateTimeUtils.formatNow("yyyy-MM")))
		);*/
		page.setRecords(list);
		return page;
	}

	/**
	 * 特陈审批列表导出
	 *
	 * @param request
	 * @return: void
	 * @date: 6/1/22 9:36 AM
	 */
	@Override
	public void exportList(DisplayQueryRequest request) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		String businessGroupCode = sfaBusinessGroupMapper.queryBusinessGroupCode(loginInfo.getBusinessGroup());
		request.setBusinessGroup(loginInfo.getBusinessGroup());
		request.setBusinessGroupCode(businessGroupCode);
		if (CommonUtil.StringUtils.isBlank(request.getOrganizationType())) {
			request.setOrganizationType(loginInfo.getOrganizationType());
		}
		if (CommonUtil.StringUtils.isBlank(request.getOrganizationId())){
			List<String> employeeOrganizationId = organizationMapper.getEmployeeOrganizationId(request.getEmployeeId(),loginInfo);
			if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {
				request.setOrganizationId(employeeOrganizationId.get(0));
			}else{
				request.setOrganizationIds(employeeOrganizationId);
			}
		}
		if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {
			//判断角色
			String businessEmployee = settingService.getValue("business_employee");//业务支持
			String customerServiceEmployee = settingService.getValue("customer_service_employee");//客服
			if (customerServiceEmployee.contains(request.getEmployeeId())){
				request.setProcessType(ProcessType.CUSTOMER_SERVICE_AUDIT.getProcessCode());
			}else if(businessEmployee.contains(request.getEmployeeId())){
				request.setProcessType(ProcessType.BUSINESS_AUDIT.getProcessCode());
			}else{
				request.setProcessType(ProcessType.UNKNOWN.getProcessCode());
			}
		}
		log.info("特称导出入参:{}",request);
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		Assert.notNull(servletRequestAttributes,"系统错误！！！！");
		HttpServletResponse response = servletRequestAttributes.getResponse();

		List<DisplayInfoExportVO> list = baseMapper.listDisplayInfo(request);
		list.forEach(e -> {
			String agentEmpName = e.getAgentEmpName();
			if(StringUtils.isNotBlank(agentEmpName)){
				e.setAgentPosition(PositionEnum.getPositionName(e.getCeoType(),e.getJobsType(),e.getPosition()));
			}
			DisplayApplyTypeEnum quotaEnum = DisplayApplyTypeEnum.findQuota(e.getQuotaType());
			e.setQuotaTypeStr(quotaEnum.getName());
		});

		String fileName = "特称列表导出";
		String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
		Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), DisplayInfoExportVO.class, list);
		try {
			if(wb instanceof HSSFWorkbook){
				fileName = fileName+".xls";
			}else{
				fileName = fileName+".xlsx";
			}
			String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
			// 针对IE或者以IE为内核的浏览器：
			if (userAgent.contains("msie") || userAgent.contains("trident") ) {
				fileName = URLEncoder.encode(fileName, "UTF-8");
			} else {
				// 非IE浏览器的处理：
				fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
			}
			response.setContentType("application/octet-stream");
			response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
			OutputStream outputStream = response.getOutputStream();
			wb.write(outputStream);
			outputStream.flush();
			outputStream.close();
		} catch (IOException e) {
			response.setStatus(500);
		}
	}

	/**
	 * 根据工号获取特陈任务数
	 *
	 * @param employeeId 工号
	 * @return: java.lang.Integer
	 * @date: 7/8/22 2:48 PM
	 */
	@Override
	public Integer queryTaskCount(String employeeId) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();

        Integer processType = null;
		if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {

			Integer operateRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
					.eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
					.eq(RoleEmployeeRelationEntity::getRoleId, 63))).orElse(0);
			if(operateRole > 0){
                processType = ProcessType.CUSTOMER_SERVICE_AUDIT.getProcessCode();
			}


			Integer businessRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
					.eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
					.eq(RoleEmployeeRelationEntity::getRoleId, 35))).orElse(0);
			if(businessRole > 0){
                processType = ProcessType.BUSINESS_AUDIT.getProcessCode();
			}
		}


		return baseMapper.selectTaskCount(employeeId,processType,loginInfo.getBusinessGroup());
	}

	/**
	 * 区域总监离职待审核流程转为大区督导
     *
	 * @param employeeId
	 * @param position 2:区域总监,4:区域经理
	 * @return: java.lang.Integer
	 * @date: 9/27/22 3:13 PM
	 */
    public void resignTask(String employeeId,Integer position) {
        List<DisplayProcessDetailPO> list = processDetailMapper.selectList(new QueryWrapper<DisplayProcessDetailPO>()
                .eq("reviewer_id", employeeId).eq("process_result", 1));
		if (CommonUtil.ListUtils.isNotEmpty(list)) {
			ParentOrganizationDTO pod = relationMapper.getParentOrganizationByEmpId(employeeId);
			if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getAreaEmployeeId()) && 2 == position){
				list.forEach(d -> {
					d.setReviewerId(pod.getCompanyEmployeeId());
					d.setReviewerName(pod.getAreaOrganizationName()+"-"+pod.getCompanyOrganizationName()+"-区域总监-"+pod.getCompanyEmployeeName());
					d.setOrganizationId(pod.getCompanyOrganizationId());
					processDetailMapper.updateById(d);
				});
			}else if (Objects.nonNull(pod) && CommonUtil.StringUtils.isNotBlank(pod.getAreaEmployeeId()) && 4 == position) {
				list.forEach(d -> {
					d.setReviewerId(pod.getAreaEmployeeId());
					d.setReviewerName(pod.getAreaOrganizationName() +"-督导-"+pod.getAreaEmployeeName());
					d.setOrganizationId(pod.getAreaOrganizationId());
					processDetailMapper.updateById(d);
				});
			} else {
				String zb = organizationMapper.selectOrganizationByProductGroupAndType(RequestUtils.getLoginInfo().getBusinessGroup(), "zb");
				//大区没有督导审核,张远审核
				list.forEach(d -> {
					d.setReviewerId("00441211");
					d.setReviewerName("总部-运营-张远");
					d.setOrganizationId(zb);
					processDetailMapper.updateById(d);
				});
			}
		}
	}

	/**
	 * 审批详情
	 *
	 * @param id
	 * @param employeeId
	 * @return: com.wantwant.sfa.backend.display.vo.DisplayAuditDetailVO
	 * @date: 5/21/22 10:23 AM
	 */
	@Override
	public DisplayAuditDetailVO auditDetails(Integer id,String employeeId) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		Integer processType = 0;


		if ("zb".equalsIgnoreCase(loginInfo.getOrganizationType())) {

			Integer operateRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
					.eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
					.eq(RoleEmployeeRelationEntity::getRoleId, 63))).orElse(0);
			processType =  ProcessType.UNKNOWN.getProcessCode();

			if(operateRole > 0){
				processType = ProcessType.CUSTOMER_SERVICE_AUDIT.getProcessCode();
			}

			Integer businessRole = Optional.ofNullable(roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>()
					.eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0)
					.eq(RoleEmployeeRelationEntity::getRoleId, 35))).orElse(0);

			if(businessRole > 0){
				processType = ProcessType.BUSINESS_AUDIT.getProcessCode();
			}

		}


		DisplayAuditDetailVO vo = new DisplayAuditDetailVO();
		DisplayInfoVO info = baseMapper.selectDetailById(id,employeeId,loginInfo.getOrganizationType(),processType);
		if(Objects.isNull(info)){
			throw new ApplicationException("历史数据,无法查看");
		}
		String evidenceUrl = info.getEvidenceUrl();
		if(StringUtils.isNotBlank(evidenceUrl)){
			info.setEvidenceUrls(Arrays.asList(evidenceUrl.split(",")));
		}

		String expenseReceiptImagesStr = info.getExpenseReceiptImagesStr();
		if(StringUtils.isNotBlank(expenseReceiptImagesStr)){
			info.setExpenseReceiptImages(Arrays.asList(expenseReceiptImagesStr.split(",")));
		}


		if (Objects.nonNull(info)) {

			info.setChannelTypeStr(ShopChannelTypeEnums.getShopChannelTypeDescByCode(info.getChannelType()));
			info.setSalesroomTypeStr(ShopSalesRoomTypeEnums.getSaleRoomTypeDesc(info.getSalesroomType()));

			if (null != info.getApplyMemberKey()) {
				EmployeeInfoModel employeeInfoModel = employeeInfoService.selectEmployeeInfoByMemberKey(info.getApplyMemberKey());
				if (Objects.nonNull(employeeInfoModel)) {
					String pre = "";
					if(PositionEnum.BUSINESS_BD.getCeoType().equals(employeeInfoModel.getType())) {
						pre = "全职BD";
					}else if(PositionEnum.BUSINESS_BD.getCeoType().equals(employeeInfoModel.getType())) {
						pre = "承揽BD";
					}
					info.setApplyName(pre + employeeInfoModel.getEmployeeName());
				}
			}
			info.setIsNeedSite(0);

			Integer ruleType = info.getRuleType();

			CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(employeeId,loginInfo);
			if (ruleType == 1 && Objects.nonNull(relation) && null != relation.getPositionTypeId() && 10 == relation.getPositionTypeId()){
				DisplayTaskVO taskVO = getTaskByEmpId(employeeId);
				// 区域经理线下审核限制:当前待审核数是否 <= (需现场审核-已完成现场审核)
				if (taskVO.getWaitAudit() <= (taskVO.getNeedSceneAudit() - taskVO.getCompleteSceneAudit())){
					info.setIsNeedSite(1);
				}
			}
			List<DisplayDetail> details = displayDetailMapper.selectListByInfoId(info.getId());
			if (CommonUtil.ListUtils.isNotEmpty(details)){
				details.forEach(d -> {
					d.setSkuList(displaySkuMapper.selectListByDid(d.getId()));
					d.setExclusiveList(displayExclusiveService.selectListByDid(d.getId()));
				});
				DisplayDetail detailFirst = details.stream().findFirst().get();
				if (Objects.nonNull(detailFirst)) {
					info.setUseFeeTypeCode(detailFirst.getUseFeeTypeCode());
					info.setUseFeeSubType(detailFirst.getUseFeeSubType());
					info.setUseFeeSubTypeCode(detailFirst.getUseFeeSubTypeCode());
				}
			}
			// 查询协议文件
			List<DisplayInfoProtocolFilePO> displayInfoProtocolFilePOS = displayInfoProtocolFileMapper.selectList(new LambdaQueryWrapper<DisplayInfoProtocolFilePO>()
					.eq(DisplayInfoProtocolFilePO::getInfoId, id).eq(DisplayInfoProtocolFilePO::getDeleteFlag, 0));
			if(CommonUtil.ListUtils.isNotEmpty(displayInfoProtocolFilePOS)){
				List<DisplayProtocolFileVO> displayProtocolFileVOList = new ArrayList<>();
				displayInfoProtocolFilePOS.forEach(e -> {
					DisplayProtocolFileVO displayProtocolFileVO = new DisplayProtocolFileVO();
					displayProtocolFileVO.setFileName(e.getFileName());
					displayProtocolFileVO.setUrl(e.getUrl());
					displayProtocolFileVO.setType(e.getType());
					displayProtocolFileVOList.add(displayProtocolFileVO);
				});
				info.setDisplayProtocolFileVOList(displayProtocolFileVOList);
			}

			info.setMonthDisplayQuota(quotaBigDataMapper.selectEmployeeDisplayAuota(info.getPartnerMemberKey(),LocalDateTimeUtils.formatNow("yyyy-MM")));
			List<DisplayProcessVO> process = processService.listProcessDetail(info.getProcessId());



			vo.setInfo(info);
			vo.setDetails(details);
			vo.setProcess(process);
		}
		return vo;
	}

	/**
	 * 根据employeeId获取任务数
	 *
	 * @param employeeId
	 * @return: com.wantwant.sfa.backend.display.vo.DisplayTaskVO
	 * @date: 2/23/23 4:16 PM
	 */
	private DisplayTaskVO getTaskByEmpId(String employeeId) {
		BigDecimal departmentSceneAuditRate = new BigDecimal(settingService.getValue("display_department_scene_audit_rate"));
		DisplayTaskVO taskCount = baseMapper.selectQueryTaskCount(employeeId);
		log.info("区域经理-{}当前任务数:{}",employeeId,taskCount);
		if (Objects.nonNull(taskCount)){
			//需现场审核数量 = 按照当前申请数量 * N%（可配置），向下取整
			BigDecimal needSceneAudit = new BigDecimal(taskCount.getTotal()).multiply(departmentSceneAuditRate).setScale(0, BigDecimal.ROUND_DOWN);
			taskCount.setNeedSceneAudit(needSceneAudit.intValue());
		}
		return taskCount;
	}

	/**
	 * 获取岗位信息
	 * 1:大区主管,2:区域经理,3:客服,4:业务支持,5:运营
	 *
	 * @param employeeId
	 * @return: java.lang.Integer
	 * @date: 7/7/22 10:33 AM
	 */
	@Override
	public Integer queryPosition(String employeeId) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		String businessEmployee = settingService.getValue("business_employee");//业务支持
		String customerServiceEmployee = settingService.getValue("customer_service_employee");//客服
		if ("00441211".equals(employeeId)) {
			return 5;
		}else if (businessEmployee.contains(employeeId)){
			return 4;
		}else if(customerServiceEmployee.contains(employeeId)){
			return 3;
		}else{
			CeoBusinessOrganizationPositionRelation relation = checkCustomerService.getPersonInfo(employeeId,loginInfo);
			if (Objects.nonNull(relation)){
				return relation.getPositionTypeId();
			}
		}
		return null;
	}

	@Override
	public OrgQuotaSurplusVO quotaSurplus(Integer id) {
		OrgQuotaSurplusVO surplusVO = new OrgQuotaSurplusVO();
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		DisplayInfoVO info = baseMapper.selectDetailById(id, "", loginInfo.getOrganizationType(), 0);
		if (Objects.nonNull(info)) {

			String organizationId = info.getOrganizationId();
			Integer processType = info.getProcessType();
			String sourceOrganizationId = displayProcessService.getSourceOrganizationId(processType,organizationId);


			surplusVO.setOrganizationId(sourceOrganizationId);
			surplusVO.setOrganizationName(organizationMapper.getOrganizationName(sourceOrganizationId));
			BigDecimal surplus = walletSearchService.getSurplus(sourceOrganizationId,1);
			surplusVO.setSurplus(surplus);

		}
		return surplusVO;
	}

	/**
	 * 合伙人离职完成后触发陈列
	 * 1.待审批的流程自动驳回,原因备注:合伙人已离职;
	 *
	 * @param positionId
	 * @return: void
	 * @date: 12/7/22 7:03 PM
	 */
	@Override
	public void dismissalProcess(String positionId){
		log.info("合伙人离职自动驳回特陈positionId:{}",positionId);
		List<DisplayProcessInfoDTO> displayProcessList = baseMapper.selectDismissalWaitAudit(positionId);
		processService.updateRejectProcess(displayProcessList,"合伙人已离职自动驳回");
//		baseMapper.updateDismissalWaitAudit(positionId);
		//baseMapper.updateDismissalWaitGrant(positionId);
	}


}
