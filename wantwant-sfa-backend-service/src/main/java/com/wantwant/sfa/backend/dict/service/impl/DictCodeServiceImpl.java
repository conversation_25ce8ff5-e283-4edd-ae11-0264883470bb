package com.wantwant.sfa.backend.dict.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.dict.entity.SfaDictCode;
import com.wantwant.sfa.backend.dict.request.DictCodeRequest;
import com.wantwant.sfa.backend.dict.service.DictCodeService;
import com.wantwant.sfa.backend.dict.vo.DictCodeVo;
import com.wantwant.sfa.backend.mapper.dict.SfaDictCodeMapper;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DictCodeServiceImpl extends ServiceImpl<SfaDictCodeMapper, SfaDictCode> implements DictCodeService {

    @Autowired
    private SfaDictCodeMapper sfaDictCodeMapper;

    private Map<String, List<SfaDictCode>> sfaDictCodeListMap = Maps.newConcurrentMap();
    private Map<String, Map<Integer, String>> sfaDictCodeMapMap = Maps.newConcurrentMap();
    private Map<String, Map<String, String>> sfaDictCodeStringMapMap = Maps.newConcurrentMap();

    @PostConstruct
    private void init() {
        List<SfaDictCode> sfaDictCodeList = sfaDictCodeMapper.selectList(new LambdaQueryWrapper<SfaDictCode>()
                .eq(SfaDictCode::getDeleteFlag, 0)
        );
        sfaDictCodeListMap = sfaDictCodeList.stream().collect(Collectors.groupingBy(SfaDictCode::getClassCd));
        sfaDictCodeListMap.forEach((classCd, list) -> {
            sfaDictCodeMapMap.put(classCd, list.stream().collect(Collectors.toMap(dict -> Integer.valueOf(dict.getItemValue()), SfaDictCode::getItemContent)));
            sfaDictCodeStringMapMap.put(classCd, list.stream().collect(Collectors.toMap(SfaDictCode::getItemValue, SfaDictCode::getItemContent)));
        });
    }

    public List<SfaDictCode> getListByClassCd(String classCd) {
        return sfaDictCodeListMap.getOrDefault(classCd, new ArrayList<>());
    }

    public Map<Integer, String> getMapByClassCd(String classCd) {
        return sfaDictCodeMapMap.getOrDefault(classCd, new HashMap<>());
    }

    public Map<Integer, String> getMapByClassCdIncludeEn(String classCd) {
        if (!CommonConstant.LANGUAGE_CHINESE.equals(RequestUtils.getLanguage())) {
            classCd = classCd + "_en";
        }
        return sfaDictCodeMapMap.getOrDefault(classCd, new HashMap<>());
    }

    public Map<String, String> getMapByClassCdString(String classCd) {
        return sfaDictCodeStringMapMap.getOrDefault(classCd, new HashMap<>());
    }

    @Override
    public List<DictCodeVo> getCodeItem(DictCodeRequest request) {
        List<DictCodeVo> list = new ArrayList<>();
        List<SfaDictCode> sfaDictCodeList = sfaDictCodeMapper.selectList(new LambdaQueryWrapper<SfaDictCode>()
                .eq(SfaDictCode::getClassCd, request.getClassCd()));
        BeanUtils.copyProperties(sfaDictCodeList, list, SfaDictCode.class, DictCodeVo.class);
        return list;
    }

    public void fresh(){
        sfaDictCodeListMap.clear();
        sfaDictCodeMapMap.clear();
        sfaDictCodeStringMapMap.clear();
        init();
    }
}
