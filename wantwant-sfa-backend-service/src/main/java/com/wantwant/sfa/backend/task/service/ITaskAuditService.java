package com.wantwant.sfa.backend.task.service;

import com.wantwant.sfa.backend.task.dto.TaskRedoneDTO;
import com.wantwant.sfa.backend.task.dto.TaskRefuseDTO;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/下午5:08
 */
public interface ITaskAuditService {

    /**
     * 任务完结
     *
     * @param taskId
     * @param processUserId
     * @param processUserName
     */
    void finish(Long taskId,String processUserId,String processUserName,String taskTag,String remark);

    /**
     * 任务重办
     *
     * @param taskRedoneDTO
     */
    void redone(TaskRedoneDTO taskRedoneDTO);
    /**
     * 任务驳回
     *
     * @param taskRefuseDTO
     */
    void refuse(TaskRefuseDTO taskRefuseDTO);
}
