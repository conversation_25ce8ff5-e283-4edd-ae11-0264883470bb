package com.wantwant.sfa.backend.task.dto;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 任务流程处理入参封装
 */
@Data
@Builder
@ToString
public class TaskProcessCommand {

    private Long taskId;

    private LocalDate deadline;

    private String processUserId;

    private String processUserName;

    private String remark;

    private int result;

    private String taskTag;

    private List<TaskAnnexRequest> appendix;
}


