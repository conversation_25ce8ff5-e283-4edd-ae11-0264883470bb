package com.wantwant.sfa.backend.marketZone.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.enums.RankingListPositionEnums;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.interview.SfaInterviewProcessMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.marketZone.dto.CountryDTO;
import com.wantwant.sfa.backend.marketZone.dto.MarketDTO;
import com.wantwant.sfa.backend.marketZone.service.IMarketZoneService;
import com.wantwant.sfa.backend.model.employee.EmployeeDTO;
import com.wantwant.sfa.backend.organizationGoal.request.MarketZoneFoldRequest;
import com.wantwant.sfa.backend.organizationGoal.request.MarketZoneRequest;
import com.wantwant.sfa.backend.realData.vo.MarketInfoEnVo;
import com.wantwant.sfa.backend.realData.vo.MarketInfoVo;
import com.wantwant.sfa.backend.realData.vo.MarketZoneVo;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.RealTimeUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/23/下午3:49
 */
@Service
@Slf4j
public class MarketZoneService implements IMarketZoneService {

    @Resource
    private RealtimeMapper realtimeMapper;
    @Resource
    private MarketZoneMapper marketZoneMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Resource
    private ApplyMemberMapper applyMemberMapper;
    @Resource
    private SfaInterviewProcessMapper sfaInterviewProcessMapper;

    @Resource
    private RealTimeUtils realTimeUtils;
    @Resource
    private RedisUtil redisUtil;

    private static final Object NEXT_ORG_KEY = "sfa:next:org:";

    @Override
    public MarketZoneVo searchMarketZone(MarketZoneRequest marketZoneRequest) {
        //头部市场信息
        MarketZoneVo marketZoneVo = new MarketZoneVo();
        CountryDTO countryInfo = marketZoneMapper.getCountryInfo(marketZoneRequest.getOrganizationId(),marketZoneRequest.getYearMonth(),marketZoneRequest.getDateTypeId());
        if(Objects.nonNull(countryInfo)){
            marketZoneVo.setMarketCoverage(countryInfo.getMarketCoverage());
            marketZoneVo.setCountyMarketCount(countryInfo.getCountyMarketCount());
        }

        String organizationType = organizationMapper.getOrganizationType(marketZoneRequest.getOrganizationId());
//        Integer businessGroupById = organizationMapper.getBusinessGroupById(marketZoneRequest.getOrganizationId());
        marketZoneRequest.setOrganizationType(organizationType);
        marketZoneRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        Page<MarketDTO> page = new Page<>(marketZoneRequest.getPage(), marketZoneRequest.getRows());
        //绑定人员在岗状态
        marketZoneRequest.setTheDate(realTimeUtils.getNearMonth(marketZoneRequest.getDateTypeId(),marketZoneRequest.getYearMonth()));

        LinkedList<MarketDTO> list = Optional.ofNullable(marketZoneMapper.selectPageBySql(page, marketZoneRequest)).orElse(new LinkedList<>());

        page.setRecords(list);

        if(CollectionUtils.isEmpty(list)){
            marketZoneVo.setMarketInfoVoPage(new Page<MarketInfoVo>().setRecords(ListUtils.EMPTY_LIST));
        }
        //新增合计信息
        /**
         * 全部的岗位条件下  没有汇总合计信息
         * 岗位类型和查询类型重复时 不再补充汇总信息  0 全部  2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理
         * 明细包含 总督导到区域总监的合计 + 区域经理层级的合计+区域经理层级的区县
         */
        if(StringUtils.isEmpty(marketZoneRequest.getCountryName())
                && list.size()!=0
                && 0 != marketZoneRequest.getSearchType()
                && (!(2 == marketZoneRequest.getSearchType()&& RankingListPositionEnums.AREA.getOrganizationType().equals(organizationType)))
                &&(!(3 == marketZoneRequest.getSearchType()&& RankingListPositionEnums.V_AREA.getOrganizationType().equals(organizationType)))
                &&(!(4 == marketZoneRequest.getSearchType()&& RankingListPositionEnums.PROVINCE.getOrganizationType().equals(organizationType)))
                &&(!(5 == marketZoneRequest.getSearchType()&& RankingListPositionEnums.COMPANY.getOrganizationType().equals(organizationType)))
                &&(!(6 == marketZoneRequest.getSearchType()&& RankingListPositionEnums.DEPARTMENT.getOrganizationType().equals(organizationType)))
        ){
            MarketDTO marketDTO = marketZoneMapper.selectCombine(marketZoneRequest);
            list.add(0,marketDTO);
        }
        List<EmployeeDTO> employeeDTOS = null;
        List<Integer> employeeInfoIds = list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId())).map(MarketDTO::getEmployeeInfoId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(employeeInfoIds)){
            employeeDTOS = sfaEmployeeInfoMapper.selectEmployeeInfoByIds(employeeInfoIds);
        }

        // 转换
        List<MarketInfoVo> marketInfoVos = convertToVO(list, employeeDTOS, marketZoneRequest.getDateTypeId(), marketZoneRequest.getYearMonth());


        Page<MarketInfoVo> result = new Page<>(marketZoneRequest.getPage(), marketZoneRequest.getRows());
        result.setPages(page.getPages());
        result.setTotal(page.getTotal());
        result.setRecords(marketInfoVos);
        marketZoneVo.setMarketInfoVoPage(result);
        return marketZoneVo;
    }

    @Override
    public void exportMarketZone(MarketZoneRequest marketZoneRequest) {
        String organizationType = organizationMapper.getOrganizationType(marketZoneRequest.getOrganizationId());
//        Integer businessGroupById = organizationMapper.getBusinessGroupById(marketZoneRequest.getOrganizationId());
        marketZoneRequest.setOrganizationType(organizationType);
        marketZoneRequest.setBusinessGroup(RequestUtils.getBusinessGroup());

        //绑定人员在岗状态
        marketZoneRequest.setTheDate(realTimeUtils.getNearMonth(marketZoneRequest.getDateTypeId(),marketZoneRequest.getYearMonth()));

        LinkedList<MarketDTO> list = Optional.of(marketZoneMapper.selectPageBySql(null, marketZoneRequest)).orElse(new LinkedList<>());

        List<EmployeeDTO> employeeDTOS = null;
        List<Integer> employeeInfoIds = list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId())).map(MarketDTO::getEmployeeInfoId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(employeeInfoIds)){
            employeeDTOS = sfaEmployeeInfoMapper.selectEmployeeInfoByIds(employeeInfoIds);
        }


        List<MarketInfoVo> marketInfoVos = convertToVO(list, employeeDTOS, marketZoneRequest.getDateTypeId(), marketZoneRequest.getYearMonth());


        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        String name = "市场空间" + sheetName;
        Workbook workbook;
        if (CommonConstant.REGION_CHINESE.equals(RequestUtils.getRegion())) {
            workbook = ExcelExportUtil.exportExcel(new ExportParams(null, name), MarketInfoVo.class, marketInfoVos);
        } else {
            workbook = ExcelExportUtil.exportExcel(new ExportParams(null, name), MarketInfoEnVo.class, BeanUtil.copyToList(marketInfoVos, MarketInfoEnVo.class));
        }
        

        response.setContentType("application/vnd.ms-excel");

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
                    .encode(name + ".xls"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @Override
    public List<MarketInfoVo> searchMarketZoneFold(MarketZoneFoldRequest marketZoneFoldRequest) {

        String organizationType = organizationMapper.getOrganizationType(marketZoneFoldRequest.getOrganizationId());
//        Integer businessGroupById = organizationMapper.getBusinessGroupById(marketZoneFoldRequest.getOrganizationId());
        marketZoneFoldRequest.setOrganizationType(organizationType);
        marketZoneFoldRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        Integer positionTypeId = getPositionTypeId(organizationType);
        marketZoneFoldRequest.setPositionTypeId(positionTypeId);

        marketZoneFoldRequest.setTheDate(realTimeUtils.getNearMonth(marketZoneFoldRequest.getDateTypeId(),marketZoneFoldRequest.getYearMonth()));

        LinkedList<MarketDTO> list = Optional.ofNullable(marketZoneMapper.searchMarketZoneFold(marketZoneFoldRequest)).orElse(new LinkedList<>());
        //新增合计信息
        if(StringUtils.isEmpty(marketZoneFoldRequest.getCountryName()) && list.size()!=0 && Boolean.FALSE.equals(marketZoneFoldRequest.getHasChildren())){
            MarketZoneRequest request = new MarketZoneRequest();
            BeanUtils.copyProperties(marketZoneFoldRequest,request);
            MarketDTO marketDTO = marketZoneMapper.selectCombine(request);
            list.add(0,marketDTO);
        }

        List<EmployeeDTO> employeeDTOS = null;
        List<Integer> employeeInfoIds = list.stream().filter(f -> Objects.nonNull(f.getEmployeeInfoId())).map(MarketDTO::getEmployeeInfoId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(employeeInfoIds)){
            employeeDTOS = sfaEmployeeInfoMapper.selectEmployeeInfoByIds(employeeInfoIds);
        }


        List<MarketInfoVo> marketInfoVos = convertToVO(list, employeeDTOS, marketZoneFoldRequest.getDateTypeId(), marketZoneFoldRequest.getYearMonth());
        marketInfoVos.forEach(e -> {
            if(positionTypeId != 10){
                e.setId(e.getOrganizationId());
                if(Boolean.TRUE.equals(e.getTotalInfo())){
                    e.setHasChildren(false);
                }else {
                    e.setHasChildren(true);
                }
            }else{
                e.setId(e.getOrganizationId()+"_"+e.getCountry());
            }
        });

        return marketInfoVos;
    }

    private Integer getPositionTypeId(String organizationType) {

        if(organizationType.equals("area")){
            organizationType = "varea";
        }else if(organizationType.equals("varea")){
            organizationType = "province";
        }else if(organizationType.equals("province")){
            organizationType = "company";
        }else if(organizationType.equals("company")){
            organizationType = "department";
        }else if(organizationType.equals("zb")){
            organizationType = "area";
        }



        int positionTypeId = 10;
        if(organizationType.equals("area")){
            positionTypeId = 1;
        }else if(organizationType.equals("varea")){
            positionTypeId = 12;
        }else if(organizationType.equals("province")){
            positionTypeId = 11;
        }else if(organizationType.equals("company")){
            positionTypeId = 2;
        }

        return positionTypeId;
    }

    private List<MarketInfoVo> convertToVO(List<MarketDTO> list, List<EmployeeDTO> employeeDTOS, String dateTypeId, String yearMonth) {
        List<MarketInfoVo> result = new ArrayList<>();

        if(CollectionUtils.isEmpty(list)){
            return result;
        }

        List<EmployeeDTO> finalEmployeeDTOS = Optional.ofNullable(employeeDTOS).orElse(new ArrayList<>());

        list.forEach(e -> {
            MarketInfoVo marketVo = new MarketInfoVo();
            BeanUtils.copyProperties(e,marketVo);

            EmployeeDTO employeeDTO = finalEmployeeDTOS.stream().filter(f ->Objects.nonNull(e.getEmployeeInfoId()) && f.getEmployeeInfoId().equals(e.getEmployeeInfoId())).findFirst().orElse(null);
            if(Objects.nonNull(employeeDTO)){
                marketVo.setPositionTypeId(employeeDTO.getPositionTypeId());
                marketVo.setInterviewRecordId(employeeDTO.getInterviewRecordId());
            }

            marketVo.setEmployeeStatus(EmployeeStatus.getDescByEnv(marketVo.getEmployeeStatus()));
            marketVo.setPosition(OrganizationPositionRelationEnums.getPositionNameByEnv(e.getPosition()));
            marketVo.setSaleGoalAchievementColor(realTimeUtils.saleGoalAchievementColor(dateTypeId, yearMonth, e.getPerformanceAchievementRate()));

            CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, e.getOrganizationId()));
            if(Objects.nonNull(viewEntity)){

//                marketVo.setAreaName(viewEntity.getOrgName3());
//                marketVo.setVareaName(viewEntity.getVirtualAreaName());
//                marketVo.setProvinceName(viewEntity.getProvinceName());
//                marketVo.setCompanyName(viewEntity.getOrgName2());
//                marketVo.setDepartmentName(viewEntity.getDepartmentName());
//                //拼接头部数据
//                List<String> arrayList = new ArrayList<>();
//                if(org.apache.commons.lang3.StringUtils.isNotBlank(marketVo.getAreaName())){
//                    arrayList.add(marketVo.getAreaName());
//                }
//                if(org.apache.commons.lang3.StringUtils.isNotBlank(marketVo.getVareaName())){
//                    arrayList.add(marketVo.getVareaName());
//                }
//                if(org.apache.commons.lang3.StringUtils.isNotBlank(marketVo.getProvinceName())){
//                    arrayList.add(marketVo.getProvinceName());
//                }
//                if(org.apache.commons.lang3.StringUtils.isNotBlank(marketVo.getCompanyName())){
//                    arrayList.add(marketVo.getCompanyName());
//                }
//                if(org.apache.commons.lang3.StringUtils.isNotBlank(marketVo.getDepartmentName())){
//                    arrayList.add(marketVo.getDepartmentName());
//                }
//                marketVo.setFullOrganizationName(org.apache.commons.lang3.StringUtils.join(arrayList,"/"));
//
//                String positionName = OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(e.getOrganizationId()));
//                marketVo.setPosition(positionName);
//                marketVo.setCountryMarketCount(e.getCountryMarketCount());
                if (ComonLanguageEnum.TOTAL2.getZhDesc().equals(marketVo.getCountry()) && CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
                    marketVo.setCountry(ComonLanguageEnum.TOTAL2.getEnDesc());
                }
                if(Boolean.TRUE.equals(e.getTotalInfo())){
                    if (CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
                        marketVo.setAreaName(ComonLanguageEnum.TOTAL2.getEnDesc());
                    } else {
                        marketVo.setAreaName("合计");
                    }

                }
//                else {
//                    // 获取姓名和头像
//                    SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, e.getOrganizationId()).eq(SfaPositionRelationEntity::getStatus, 1).ne(SfaPositionRelationEntity::getPositionTypeId,7).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
//                    if(Objects.nonNull(sfaPositionRelationEntity)){
//                        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(sfaPositionRelationEntity.getEmployeeInfoId());
//                        if(Objects.nonNull(sfaEmployeeInfoModel)){
//                            marketVo.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
//
//                            ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
//                            if(Objects.nonNull(applyMemberPo)){
//                                marketVo.setAvatar(applyMemberPo.getPicUrl());
//
//                                SfaInterviewProcessModel sfaInterviewProcessModel = sfaInterviewProcessMapper.selectOne(new LambdaQueryWrapper<SfaInterviewProcessModel>().eq(SfaInterviewProcessModel::getApplicationId, applyMemberPo.getId()));
//                                if(Objects.nonNull(sfaInterviewProcessModel) && Objects.nonNull(sfaInterviewProcessModel.getOnboardTime())){
//                                    LocalDateTime onboardTime = LocalDateTimeUtils.convertDateToLDT(sfaInterviewProcessModel.getOnboardTime());
//                                    Duration between = Duration.between(onboardTime, LocalDateTime.now());
//
//                                    marketVo.setOnBoardDate(between.toDays());
//                                }
//
//                            }
//                        }
//
//                    }
//                }

            }
            result.add(marketVo);




        });

        return result;

    }
}
