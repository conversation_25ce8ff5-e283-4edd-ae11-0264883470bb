package com.wantwant.sfa.backend.enums;

import com.wantwant.sfa.common.architecture.SpringContextHelper;
import com.wantwant.sfa.common.architecture.global.LocalizedText;
import com.wantwant.sfa.common.base.enums.StringEnum;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum BizExceptionLanguageEnum implements StringEnum {
    COMMON_SYSTEM_ERROR_MESSAGE("common.system.error.message", "系统异常，请联系管理员！"),
    COMMON_PERSON_MUST_NOT_NULL("sfa.backend.common.person.must.not.null", "缺少操作人ID！"),
    LANGUAGE_MUST_NOT_NULL("sfa.backend.common.language.must.not.null", "缺少语言参数"),
    POSITION_NOT_EXIST("sfa.backend.emp.not.exist", "岗位信息获取失败"),
    ROLE_NOT_EXIST("sfa.backend.emp.role.not.exist", "角色信息获取失败"),
    RESOURCE_NOT_EXIST("sfa.backend.resource.not.exist", "无可用菜单"),
    REGION_LIST_LEVEL_MUST_NOT_NULL("sfa.backend.region.list.level.must.not.null", "缺少层级！"),
    EMPLOYEE_ORGANIZATION_EMPLOYEEID_MUST_NOT_NULL("sfa.backend.employee.organization.employeeid.must.not.null", "请传入工号！"),
    EMPLOYEE_ORGANIZATION_EMPTY("sfa.backend.employee.organization.empty", "未配置造旺渠道权限！"),
    LOGIN_USER_ACCOUNT_MUST_NOT_NULL("sfa.backend.login.user.accout.must.not.null", "账号不能为空！"),
    LOGIN_USER_PASSWORD_MUST_NOT_NULL("sfa.backend.login.user.password.must.not.null", "密码不能为空！"),
    LOGIN_USER_NEWPASSWORD_MUST_NOT_NULL("sfa.backend.login.user.newpassword.must.not.null", "新密码不能为空！"),
    LOGIN_USER_ACCOUNT_PASSWORD_ERROR("sfa.backend.login.user.accout.password.error", "账号或密码不正确！"),
    LOGIN_USER_ACCOUNT_PASSWORD_ERROR_TOO_MANY("sfa.backend.login.user.accout.password.error.too.many", "连续多次输入错误密码，请稍后再试！！"),
    LOGIN_USER_ACCOUNT_UPDATE_CAPTCHA("sfa.backend.login.user.accout.update.captcha", "图形验证码不能为空！"),
    LOGIN_USER_ACCOUNT_CAPTCHA_ERROR("sfa.backend.login.user.accout.captcha.error", "图形验证码不正确或已过期,请重新获取！"),
    LOGIN_USER_ACCOUNT_NOT_EXIST("sfa.backend.login.user.accout.not.exist", "用户不存在！"),
    LOGIN_USER_ACCOUNT_UPDATE_SUCCESS("sfa.backend.login.user.accout.update.success", "修改成功！"),
    LOGIN_USER_ACCOUNT_CAPTCHA_CREATE_ERROR("sfa.backend.login.user.accout.captcha_create_error", "生成验证码图片时发生错误！"),
    ORGANIZATION_INFO_NOT_EXIST_OPERATOR("sfa.backend.organization.info.not.exist.operator", "操作人组织信息获取失败"),
    ORGANIZATION_INFO_NOT_EXIST_TARGET("sfa.backend.organization.info.not.exist.target", "目标人员组织信息获取失败"),
    COMPLETE_LIST_COMPLETE_NUM_MUST_NOT_NULL("sfa.backend.complete.list.complete.num.must.not.null", "请传入工号！"),

    MONTH_MUST_NOT_NULL("sfa.backend.month.must.not.null", "请输入正确的月份"),
    QUARTER_MUST_NOT_NULL("sfa.backend.quarter.must.not.null", "请输入正确的季度"),
    NO_ATTENDANCE_RECORD("sfa.backend.no.attendance.record", "无考勤记录"),

    INTERVIEW_OFF_BOARD_EMP_CONFLICT("sfa.backend.interview_off_board_emp_conflict","办理失败：当前岗位人员{0}，人员状态离职中，实际离职日期：{1}，需晚于该人员实际离职日期。"),
    INTERVIEW_OFF_POSITION_CONFLICT("sfa.backend.interview_off_position_conflict","办理失败：当前岗位人员{0}，人员状态离岗中，实际离岗日期：{1}，需晚于该人员实际离职日期。"),
    INTERVIEW_ON_BOARD_CONFLICT("sfa.backend..interview_on_board_conflict","办理失败：当前岗位人员{0}，人员状态在职。"),
    INTERVIEW_ON_BOARD_PROCESSING_CONFLICT("sfa.interview_on_board_processing_conflict","办理失败：当前岗位人员{0}，人员状态入职中，入职日期：{1}"),
    INTERVIEW_TRANSACTION_CONFLICT("sfa.backend.interview_transaction_conflict","办理失败：当前岗位人员{0}，人员状态异动中，异动生效日期：{1}，需晚于该人员异动生效日期。"),
    INTERVIEW_ORG_NOT_EXIST("sfa.backend..interview.org.not.exist","组织不存在，组织ID：%s"),
    INTERVIEW_DATA_EXCEPTION("sfa.backend.interview.data.exception","面试数据异常"),
    CONVENIENT_MENU("sfa.menu.convenient.menu","快捷菜单"),

    // clearance - No clearance punch permission
    CLEARANCE_NO_PUNCH_PERMISSION("sfa.backend.clearance.no.punch.permission", "无通关打卡权限"),
    // clearance - Clearance does not exist
    CLEARANCE_NOT_EXIST("sfa.backend.clearance.not.exist", "通关不存在"),
    // clearance - Current status does not allow punch
    CLEARANCE_STATUS_NOT_ALLOW_PUNCH("sfa.backend.clearance.status.not.allow.punch", "当前状态不可打卡"),
    // clearance - Not within clearance time period
    CLEARANCE_NOT_IN_TIME_PERIOD("sfa.backend.clearance.not.in.time.period", "当前不在通关时间段内"),
    // clearance - No clearance audit permission
    CLEARANCE_NO_AUDIT_PERMISSION("sfa.backend.clearance.no.audit.permission", "没有稽核通关权限"),
    // clearance - Clearance code does not exist
    CLEARANCE_CODE_NOT_EXIST("sfa.backend.clearance.code.not.exist", "通关编号不存在"),
    // clearance - Current status does not allow audit
    CLEARANCE_STATUS_NOT_ALLOW_AUDIT("sfa.backend.clearance.status.not.allow.audit", "当前状态不可稽核"),
    // clearance - {0} clearance audit exception
    CLEARANCE_AUDIT_EXCEPTION("sfa.backend.clearance.audit.exception", "{0}的通关打卡稽核异常，请查看！"),

    // 人员选择器-规则不存在
    PERSON_SELECTOR_RULE_NOT_EXIST("sfa.backend.person.selector.rule.not.exist", "人员选择器规则不存在"),
    // 人员选择器-组织、人员、角色必须选择至少一条数据
    PERSON_SELECTOR_ORG_EMP_ROLE_MUST_SELECT_AT_LEAST_ONE("sfa.backend.person.selector.org.emp.role.must.select.at.least.one", "组织、人员、角色必须选择至少一条数据"),
    // 人员选择器-场景key不能为空
    PERSON_SELECTOR_SCENE_KEY_MUST_NOT_NULL("sfa.backend.person.selector.scene.key.must.not.null", "场景key不能为空"),
    // 人员选择器-规则id必输
    PERSON_SELECTOR_RULE_ID_MUST_NOT_NULL("sfa.backend.person.selector.rule.id.must.not.null", "规则id必输"),


    ATTENDANCE_DATE_MUST_NOT_NULL("sfa.backend.attendance.date.must.not.null","请选择查询的起始日期" ),

    // 数据生成中
    DATA_GENERATION_IN_PROGRESS("sfa.backend.data.generation.in.progress", "数据生成中,请稍后"),

    ;

    private final String type;
    private final String desc;

    BizExceptionLanguageEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public String getTextMsg() {
        try {
            return SpringContextHelper.getBean(LocalizedText.class).getValue(this.getType(),this.getDesc());
        } catch (Exception e) {
            log.error("获取国际化文本信息异常,{},{}",getType(),getDesc(),e);
            return this.getDesc();
        }
    }
}
