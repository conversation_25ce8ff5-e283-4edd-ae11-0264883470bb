package com.wantwant.sfa.backend.personscopeselect.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.applyMember.vo.ApplyMemberVO;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.personscopeselect.SfaPersonScopeSelectRuleDetailMapper;
import com.wantwant.sfa.backend.mapper.personscopeselect.SfaPersonScopeSelectRuleMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.personscopeselect.dto.*;
import com.wantwant.sfa.backend.personscopeselect.entity.SfaPersonScopeSelectRuleDetailEntity;
import com.wantwant.sfa.backend.personscopeselect.entity.SfaPersonScopeSelectRuleEntity;
import com.wantwant.sfa.backend.personscopeselect.enums.ScopeSelectRuleCompareTypeEnum;
import com.wantwant.sfa.backend.personscopeselect.enums.ScopeSelectRuleRelationTypeEnum;
import com.wantwant.sfa.backend.personscopeselect.request.*;
import com.wantwant.sfa.backend.personscopeselect.service.IPersonScopeSelectRuleService;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.service.impl
 * @Description:
 * @Date: 2025/2/19 14:40
 */
@Service
@Slf4j
public class PersonScopeSelectRuleService implements IPersonScopeSelectRuleService {
    @Resource
    private SfaPersonScopeSelectRuleMapper ruleMapper;
    @Resource
    private SfaPersonScopeSelectRuleDetailMapper ruleDetailMapper;

    @Resource
    private ICheckCustomerService checkCustomerService;

    @Resource
    private ApplyMemberMapper applyMemberMapper;

    @Resource
    private PersonScopeSelectRuleService scopeSelectRuleService;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(AddPersonScopeSelectRuleRequest request) {
        log.info("新增范围选择器请求参数[{}]", request);
        // 参数校验
        validParams(request);

        SfaPersonScopeSelectRuleEntity ruleEntity = new SfaPersonScopeSelectRuleEntity();
        ruleEntity.setServiceKey(request.getServiceKey());
        // 查询人员信息
        if (StringUtils.isEmpty(request.getEmployeeName())) {
            CeoBusinessOrganizationPositionRelation employeeInfo = checkCustomerService.getPersonInfo(request.getEmployeeId(), RequestUtils.getLoginInfo());
            request.setEmployeeName(employeeInfo.getEmployeeName());
        }
        // 产品组信息
        if (Objects.isNull(request.getBusinessGroupId())) {
            request.setBusinessGroupId(RequestUtils.getLoginInfo().getBusinessGroup());
        }
        SfaBusinessGroupEntity groupEntity = sfaBusinessGroupMapper.selectById(request.getBusinessGroupId());
        if (Objects.isNull(groupEntity)) {
            throw new ApplicationException("产品组信息获取失败");
        }
        ruleEntity.setI18nFlag(groupEntity.getI18nFlag());
        ruleEntity.init(request.getEmployeeId(), request.getEmployeeName());
        ruleMapper.insert(ruleEntity);
        Long ruleId = ruleEntity.getId();
        // 规则详情
        initDetails(ruleId, request);

        return ruleId;
    }

    @Override
    public Long copy(CopyPersonScopeSelectRuleRequest request) {
        log.info("复制范围选择器请求参数[{}]", request);
        PersonScopeSelectRuleInfoDto selectRuleInfoDto = scopeSelectRuleService.queryDetail(request.getId());
        if(Objects.isNull(selectRuleInfoDto)){
            throw new ApplicationException(BizExceptionLanguageEnum.PERSON_SELECTOR_RULE_NOT_EXIST.getTextMsg());
        }
        // 查询人员信息
        if (StringUtils.isEmpty(request.getEmployeeName())) {
            CeoBusinessOrganizationPositionRelation employeeInfo = checkCustomerService.getPersonInfo(request.getEmployeeId(), RequestUtils.getLoginInfo());
            request.setEmployeeName(employeeInfo.getEmployeeName());
        }
        AddPersonScopeSelectRuleRequest req = new AddPersonScopeSelectRuleRequest();
        BeanUtils.copyProperties(request, req);
        req.setEmpInfos(BeanUtil.copyToList(selectRuleInfoDto.getEmpInfos(), PersonScopeSelectRuleEmployeeInfoRequest.class));
        req.setOrganizationInfos(BeanUtil.copyToList(selectRuleInfoDto.getOrganizationInfos(), PersonScopeSelectRuleOrganizationInfoRequest.class));
        req.setPositionTypeInfos(BeanUtil.copyToList(selectRuleInfoDto.getPositionTypeInfos(), PersonScopeSelectRulePositionTypeInfoRequest.class));
        return scopeSelectRuleService.add(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UpdatePersonScopeSelectRuleRequest request) {
        log.info("修改范围选择器请求参数[{}]", request);
        // 校验参数
        validParams(request);
        // 修改规则基本信息
        SfaPersonScopeSelectRuleEntity ruleEntity = ruleMapper.selectById(request.getId());
        if (Objects.isNull(ruleEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.PERSON_SELECTOR_RULE_NOT_EXIST.getTextMsg());
        }
        if (StringUtils.isEmpty(request.getEmployeeName())) {
            CeoBusinessOrganizationPositionRelation employeeInfo = checkCustomerService.getPersonInfo(request.getEmployeeId(), RequestUtils.getLoginInfo());
            request.setEmployeeName(employeeInfo.getEmployeeName());
        }
        ruleEntity.update(request.getEmployeeId(), request.getEmployeeName());
        ruleMapper.updateById(ruleEntity);
        // 删除明细
        ruleDetailMapper.deleteByRuleId(request.getId());
        // 新增明细
        initDetails(request.getId(), request);
    }

    @Override
    public PersonScopeSelectRuleInfoDto queryDetail(Long id) {
        PersonScopeSelectRuleInfoDto dto = new PersonScopeSelectRuleInfoDto();
        LambdaQueryWrapper<SfaPersonScopeSelectRuleDetailEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfaPersonScopeSelectRuleDetailEntity::getRelationRuleId, id);
        queryWrapper.eq(SfaPersonScopeSelectRuleDetailEntity::getDeleteFlag, 0);
        List<SfaPersonScopeSelectRuleDetailEntity> detailEntities = ruleDetailMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(detailEntities)) {
            // 人员
            List<SfaPersonScopeSelectRuleDetailEntity> empInfoRules = detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.PERSON.getCode() == detailEntity.getRelationType()).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(empInfoRules)){
                // 查询所有人的头像
                List<String> employeeIds = empInfoRules.stream().map(SfaPersonScopeSelectRuleDetailEntity::getEmployeeId).collect(Collectors.toList());
                Map<String, ApplyMemberVO> avatarByEmployeeIds = applyMemberMapper.getAvatarByEmployeeIds(employeeIds);
                if(CollectionUtil.isEmpty(avatarByEmployeeIds)){
                    avatarByEmployeeIds = Maps.newHashMap();
                }
                Map<String, ApplyMemberVO> finalAvatarByEmployeeIds = avatarByEmployeeIds;
                dto.setEmpInfos(empInfoRules.stream().map(detailEntity -> {
                    PersonScopeSelectRuleEmployeeInfoDto employeeInfo = new PersonScopeSelectRuleEmployeeInfoDto();
                    employeeInfo.setEmployeeId(detailEntity.getEmployeeId());
                    employeeInfo.setDescription(detailEntity.getDescription());
                    employeeInfo.setAvatar(finalAvatarByEmployeeIds.get(detailEntity.getEmployeeId()).getPicUrl());
                    return employeeInfo;
                }).collect(Collectors.toList()));

            }
            // 组织
            dto.setOrganizationInfos(detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.ORGANIZATION.getCode() == detailEntity.getRelationType()).map(detailEntity -> {
                PersonScopeSelectRuleOrganizationInfoDto organizationInfo = new PersonScopeSelectRuleOrganizationInfoDto();
                organizationInfo.setOrganizationId(detailEntity.getOrganizationId());
                organizationInfo.setOrgType(detailEntity.getOrgType());
                organizationInfo.setDescription(detailEntity.getDescription());
                return organizationInfo;
            }).collect(Collectors.toList()));
            // 岗位
            dto.setPositionTypeInfos(detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.POSITION_TYPE.getCode() == detailEntity.getRelationType()).map(detailEntity -> {
                PersonScopeSelectRulePositionTypeInfoDto positionTypeInfo = new PersonScopeSelectRulePositionTypeInfoDto();
                positionTypeInfo.setPositionTypeId(detailEntity.getPositionTypeId());
                positionTypeInfo.setBusinessGroupId(detailEntity.getBusinessGroupId());
                positionTypeInfo.setShowKey(detailEntity.getShowKey());
                positionTypeInfo.setDescription(detailEntity.getDescription());
                return positionTypeInfo;
            }).collect(Collectors.toList()));
        }
        // null置空
        dto.setEmpInfos(CollectionUtil.isEmpty(dto.getEmpInfos()) ? ListUtil.empty() : dto.getEmpInfos());
        dto.setOrganizationInfos(CollectionUtil.isEmpty(dto.getOrganizationInfos()) ? ListUtil.empty() : dto.getOrganizationInfos());
        dto.setPositionTypeInfos(CollectionUtil.isEmpty(dto.getPositionTypeInfos()) ? ListUtil.empty() : dto.getPositionTypeInfos());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(DeletePersonScopeSelectRuleInfoRequest request) {
        log.info("删除范围选择器入参:{}", request);
        SfaPersonScopeSelectRuleEntity ruleEntity = ruleMapper.selectById(request.getId());
        if (Objects.isNull(ruleEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.PERSON_SELECTOR_RULE_NOT_EXIST.getTextMsg());
        }
        if (StringUtils.isEmpty(request.getEmployeeName())) {
            CeoBusinessOrganizationPositionRelation employeeInfo = checkCustomerService.getPersonInfo(request.getEmployeeId(), RequestUtils.getLoginInfo());
            request.setEmployeeName(employeeInfo.getEmployeeName());
        }
        ruleEntity.delete(request.getEmployeeId(), request.getEmployeeName());
        ruleMapper.updateById(ruleEntity);
        // 删除明细
        ruleDetailMapper.deleteByRuleId(request.getId());
    }

    @Override
    public List<PersonScopeSelectRuleDetailDto> queryRuleDetails(QueryPersonScopeSelectRuleDetailRequest request) {
        log.info("queryRuleDetails请求参数[{}]", request);
        // 查询规则本身是否是国际版本
        SfaPersonScopeSelectRuleEntity ruleEntity = ruleMapper.selectById(request.getId());
        if (Objects.isNull(ruleEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.PERSON_SELECTOR_RULE_NOT_EXIST.getTextMsg());
        }
        request.setI18nFlag(ruleEntity.getI18nFlag());
        return Stream.of(
                        // 组织
                        // 1.产品组额外处理
                        ruleDetailMapper.queryOrganizationBusinessGroupConfigRuleDetails(request),
                        // 2.其余组织处理
                        ruleDetailMapper.queryOrganizationRuleDetails(request),
                        // 角色--有产品组 -- 业务人员
                        ruleDetailMapper.queryPositionTypeRuleDetails(request),
                        // 角色--有产品组 -- 业务 bd
                        ruleDetailMapper.queryBdPositionTypeRuleDetails(request),
                        // 角色--无产品组 -- 业务人员
                        ruleDetailMapper.queryPositionTypeWithOutBusinessGroupRuleDetails(request),
                        // 角色--无产品组 -- 业务 bd
                        ruleDetailMapper.queryBdPositionTypeWithOutBusinessGroupRuleDetails(request),
                        // 人员
                        ruleDetailMapper.queryEmployeeIdRuleDetails(request)
                ).filter(Objects::nonNull)
                .flatMap(List::stream)
                .peek(detailEntity
                        -> detailEntity.setPostName(OrganizationPositionRelationEnums.getPositionName(detailEntity.getPositionTypeId(), request.getI18nFlag())))
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                PersonScopeSelectRuleDetailDto::getOrganizationId,
                                person -> person,
                                (x, y) -> x
                        ),
                        map -> new ArrayList<>(map.values())
                ));
    }

    @Override
    public PersonScopeSelectRuleCompareDto compared(ComparePersonScopeSelectRuleDetailRequest request) {
        log.info("compared请求参数:[{}]", request);
        PersonScopeSelectRuleCompareDto result = new PersonScopeSelectRuleCompareDto();
        result.setBaseId(request.getBaseId());
        QueryPersonScopeSelectRuleDetailRequest detailRequest = new QueryPersonScopeSelectRuleDetailRequest();
        detailRequest.setId(request.getBaseId());
        detailRequest.setPartTime(request.getPartTime());
        Set<String> baseList = queryRuleDetails(detailRequest).stream().map(baseDto -> baseDto.getOrganizationId() + baseDto.getEmployeeId()).collect(Collectors.toSet());
        result.setComparedIdList(getcompareResultList(request.getCompareType(), request.getPartTime(), baseList, request.getComparedIdList()));
        log.info("compared返回参数[{}]", result);
        return result;
    }

    @Override
    public PersonScopeSelectRuleCompareBeforeAddDto comparedBeforeAdd(CompareBeforeAddPersonScopeSelectRuleDetailRequest request) {
        log.info("comparedBeforeAdd请求参数:[{}]", request);
        PersonScopeSelectRuleCompareBeforeAddDto result = new PersonScopeSelectRuleCompareBeforeAddDto();
        Set<String> baseList = new HashSet<>();
        if(CollectionUtil.isNotEmpty(request.getEmpInfos())){
            // 人员
            CollUtil.addAll(baseList, ruleDetailMapper.queryEmployeeIdDetails(request.getEmpInfos().stream()
                    .map(PersonScopeSelectRuleEmployeeInfoRequest::getEmployeeId).collect(Collectors.toSet()), request.getPartTime()));
        }

        if (CollectionUtil.isNotEmpty(request.getOrganizationInfos())) {
            // 产品组
            Set<Integer> businessGroupInfos = request.getOrganizationInfos().stream()
                    .filter(organizationInfo -> "businessGroup".equals(organizationInfo.getOrgType()))
                    .map(info -> Integer.valueOf(info.getOrganizationId())).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(businessGroupInfos)) {
                CollUtil.addAll(baseList, ruleDetailMapper.queryOrganizationBusinessGroupDetails(businessGroupInfos, request.getPartTime()));
            }
            // 组织
            Set<String> organizationInfos = request.getOrganizationInfos().stream()
                    .filter(organizationInfo -> !"businessGroup".equals(organizationInfo.getOrgType()))
                    .map(PersonScopeSelectRuleOrganizationInfoRequest::getOrganizationId).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(organizationInfos)) {
                CollUtil.addAll(baseList, ruleDetailMapper.queryOrganizationDetails(organizationInfos, request.getPartTime()));
            }
        }
        // 角色
        if (CollectionUtil.isNotEmpty(request.getPositionTypeInfos())) {
            // 与产品组无关
            Set<PersonScopeSelectRulePositionTypeInfoRequest> withoutBusinessGroupSet = request.getPositionTypeInfos().stream()
                    .filter(positionTypeInfo -> positionTypeInfo.getBusinessGroupId() == null).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(withoutBusinessGroupSet)) {
                CollUtil.addAll(baseList, ruleDetailMapper.queryPositionTypeWithOutBusinessGroupDetails(withoutBusinessGroupSet, request.getPartTime()));
            }
            // 关联产品组
            Set<PersonScopeSelectRulePositionTypeInfoRequest> set = request.getPositionTypeInfos().stream()
                    .filter(positionTypeInfo -> positionTypeInfo.getBusinessGroupId() != null).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(set)) {
                CollUtil.addAll(baseList, ruleDetailMapper.queryPositionTypeDetails(set, request.getPartTime()));
            }

        }

        result.setComparedIdList(getcompareResultList(request.getCompareType(), request.getPartTime(), baseList, request.getComparedIdList()));
        log.info("comparedBeforeAdd返回参数[{}]", result);
        return result;
    }

    @Override
    public QueryPersonScopeSelectRuleListDto queryRuleListForPerson(QueryPersonScopeSelectRuleListRequest request) {
        QueryPersonScopeSelectRuleListDto result = new QueryPersonScopeSelectRuleListDto();
        // 查询人员的全组织信息-产品组、岗位、组织信息
        LambdaQueryWrapper<SfaPositionRelationEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfaPositionRelationEntity::getEmpId, request.getEmployeeId());
        queryWrapper.eq(SfaPositionRelationEntity::getDeleteFlag, 0);
        queryWrapper.eq(SfaPositionRelationEntity::getStatus, 1);
        List<SfaPositionRelationEntity> relationEntities = sfaPositionRelationMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(relationEntities)) {
            return result;
        }
        // 数据匹配
        QueryPersonInfoDto dto = new QueryPersonInfoDto();
        // service_key
        dto.setServiceKey(request.getServiceKey());
        // 员工工号
        dto.setEmployeeId(request.getEmployeeId());
        // 岗位类型
        dto.setPositionTypeIds(relationEntities.stream().map(SfaPositionRelationEntity::getPositionTypeId).distinct().collect(Collectors.toList()));
        // 组织
        dto.setOrganizationIds(Stream.of(
                relationEntities.stream().map(SfaPositionRelationEntity::getAreaCode).distinct().collect(Collectors.toList()),
                relationEntities.stream().map(SfaPositionRelationEntity::getVareaCode).distinct().collect(Collectors.toList()),
                relationEntities.stream().map(SfaPositionRelationEntity::getProvinceCode).distinct().collect(Collectors.toList()),
                relationEntities.stream().map(SfaPositionRelationEntity::getCompanyCode).distinct().collect(Collectors.toList()),
                relationEntities.stream().map(SfaPositionRelationEntity::getDepartmentCode).distinct().collect(Collectors.toList())
        ).filter(CollectionUtil::isNotEmpty).flatMap(List::stream).distinct().collect(Collectors.toList()));
        // 业务组
        dto.setBusinessGroupIds(relationEntities.stream()
                .map(SfaPositionRelationEntity::getBusinessGroup)
                .distinct()
                .filter(it -> request.getBusinessGroupId() == null || it.equals(request.getBusinessGroupId()))
                .collect(Collectors.toList()));
        // 业务组岗位
        dto.setPositionTypeBusinessGroupIds(relationEntities.stream().map(f -> StringUtils.join(f.getBusinessGroup(), f.getPositionTypeId())).distinct().collect(Collectors.toList()));

        List<Long> ruleList = ruleDetailMapper.queryRuleListForPerson(dto);
        if (CollectionUtil.isNotEmpty(ruleList)) {
            result.setRuleIds(ruleList.stream().distinct().collect(Collectors.toList()));
        } else {
            result.setRuleIds(ListUtil.empty());
        }
        return result;
    }

    /**
     * 返回比较相同的规则id
     */
    private List<Long> getcompareResultList(Integer compareType, Integer partTime, Set<String> baseList, List<Long> comparedIdList) {
        List<Long> result = new ArrayList<>();
        if(ScopeSelectRuleCompareTypeEnum.EQUAL.getCode()== compareType){
            for(Long comparedId:comparedIdList){
                if (comparedEqual(baseList, comparedId, partTime)) {
                    result.add(comparedId);
                }
            }
        }else {
            for(Long comparedId:comparedIdList){
                if (comparedIntersection(baseList, comparedId, partTime)) {
                    result.add(comparedId);
                }
            }
        }

        return result;
    }
    /**
     * 有交集
     * @param baseList
     * @param comparedId
     * @return
     */
    private Boolean comparedIntersection(Set<String> baseList, Long comparedId, Integer partTime) {
        QueryPersonScopeSelectRuleDetailRequest detailRequest = new QueryPersonScopeSelectRuleDetailRequest();
        detailRequest.setId(comparedId);
        detailRequest.setPartTime(partTime);
        Set<String> comparedList = queryRuleDetails(detailRequest).stream().map(baseDto -> baseDto.getOrganizationId() + baseDto.getEmployeeId()).collect(Collectors.toSet());
        if (baseList.stream().anyMatch(comparedList::contains)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 两个集合完全相同
     * @param baseList
     * @param comparedId
     * @return
     */
    private Boolean comparedEqual(Set<String> baseList, Long comparedId, Integer partTime) {
        QueryPersonScopeSelectRuleDetailRequest detailRequest = new QueryPersonScopeSelectRuleDetailRequest();
        detailRequest.setId(comparedId);
        detailRequest.setPartTime(partTime);
        Set<String> comparedList = queryRuleDetails(detailRequest).stream().map(baseDto -> baseDto.getOrganizationId() + baseDto.getEmployeeId()).collect(Collectors.toSet());
        if (baseList.equals(comparedList)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
    private void validParams(AddPersonScopeSelectRuleRequest request) {
        if (CollectionUtil.isEmpty(request.getEmpInfos()) && CollectionUtil.isEmpty(request.getPositionTypeInfos()) && CollectionUtil.isEmpty(request.getOrganizationInfos())) {
            throw new ApplicationException(BizExceptionLanguageEnum.PERSON_SELECTOR_ORG_EMP_ROLE_MUST_SELECT_AT_LEAST_ONE.getTextMsg());
        }
    }

    /**
     * 不查询人员的头像信息 详情接口
     * @param id
     * @return
     */
    public PersonScopeSelectRuleInfoDto queryDetailNotExtraInfo(Long id) {
        PersonScopeSelectRuleInfoDto dto = new PersonScopeSelectRuleInfoDto();
        LambdaQueryWrapper<SfaPersonScopeSelectRuleDetailEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfaPersonScopeSelectRuleDetailEntity::getRelationRuleId, id);
        queryWrapper.eq(SfaPersonScopeSelectRuleDetailEntity::getDeleteFlag, 0);
        List<SfaPersonScopeSelectRuleDetailEntity> detailEntities = ruleDetailMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(detailEntities)) {
            // 人员
            dto.setEmpInfos(detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.PERSON.getCode() == detailEntity.getRelationType()).map(detailEntity -> {
                PersonScopeSelectRuleEmployeeInfoDto empInfo = new PersonScopeSelectRuleEmployeeInfoDto();
                empInfo.setEmployeeId(detailEntity.getEmployeeId());
                empInfo.setDescription(detailEntity.getDescription());
                return empInfo;
            }).collect(Collectors.toList()));
            // 组织
            dto.setOrganizationInfos(detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.ORGANIZATION.getCode() == detailEntity.getRelationType()).map(detailEntity -> {
                PersonScopeSelectRuleOrganizationInfoDto organizationInfo = new PersonScopeSelectRuleOrganizationInfoDto();
                organizationInfo.setOrganizationId(detailEntity.getOrganizationId());
                return organizationInfo;
            }).collect(Collectors.toList()));
            // 岗位
            dto.setPositionTypeInfos(detailEntities.stream().filter(detailEntity -> ScopeSelectRuleRelationTypeEnum.POSITION_TYPE.getCode() == detailEntity.getRelationType()).map(detailEntity -> {
                PersonScopeSelectRulePositionTypeInfoDto positionTypeInfo = new PersonScopeSelectRulePositionTypeInfoDto();
                positionTypeInfo.setPositionTypeId(detailEntity.getPositionTypeId());
                positionTypeInfo.setBusinessGroupId(detailEntity.getBusinessGroupId());
                return positionTypeInfo;
            }).collect(Collectors.toList()));
        }
        // null置空
        dto.setEmpInfos(CollectionUtil.isEmpty(dto.getEmpInfos()) ? ListUtil.empty() : dto.getEmpInfos());
        dto.setOrganizationInfos(CollectionUtil.isEmpty(dto.getOrganizationInfos()) ? ListUtil.empty() : dto.getOrganizationInfos());
        dto.setPositionTypeInfos(CollectionUtil.isEmpty(dto.getPositionTypeInfos()) ? ListUtil.empty() : dto.getPositionTypeInfos());
        return dto;
    }

    /**
     * 初始化详情信息
     *
     * @param ruleId
     * @param request
     */
    private void initDetails(Long ruleId, AddPersonScopeSelectRuleRequest request) {
        // 人员
        List<SfaPersonScopeSelectRuleDetailEntity> detailEntities = new ArrayList<>();
        List<PersonScopeSelectRuleEmployeeInfoRequest> empInfos = request.getEmpInfos();
        if (CollectionUtil.isNotEmpty(empInfos)) {
            detailEntities.addAll(empInfos.stream().map(empInfo -> {
                SfaPersonScopeSelectRuleDetailEntity entity = new SfaPersonScopeSelectRuleDetailEntity();
                entity.setRelationRuleId(ruleId);
                entity.setRelationType(ScopeSelectRuleRelationTypeEnum.PERSON.getCode());
                entity.setEmployeeId(empInfo.getEmployeeId());
                entity.setDescription(empInfo.getDescription());
                return entity;
            }).collect(Collectors.toList()));
        }

        // 组织
        List<PersonScopeSelectRuleOrganizationInfoRequest> organizationInfos = request.getOrganizationInfos();
        if (CollectionUtil.isNotEmpty(organizationInfos)) {
            detailEntities.addAll(organizationInfos.stream().map(organizationInfo -> {
                SfaPersonScopeSelectRuleDetailEntity entity = new SfaPersonScopeSelectRuleDetailEntity();
                entity.setRelationRuleId(ruleId);
                entity.setRelationType(ScopeSelectRuleRelationTypeEnum.ORGANIZATION.getCode());
                entity.setOrganizationId(organizationInfo.getOrganizationId());
                entity.setOrgType(organizationInfo.getOrgType());
                entity.setDescription(organizationInfo.getDescription());
                return entity;
            }).collect(Collectors.toList()));
        }
        // 岗位类型
        List<PersonScopeSelectRulePositionTypeInfoRequest> positionTypeInfos = request.getPositionTypeInfos();
        if (CollectionUtil.isNotEmpty(positionTypeInfos)) {
            detailEntities.addAll(positionTypeInfos.stream().map(positionTypeInfo -> {
                SfaPersonScopeSelectRuleDetailEntity entity = new SfaPersonScopeSelectRuleDetailEntity();
                entity.setRelationRuleId(ruleId);
                entity.setRelationType(ScopeSelectRuleRelationTypeEnum.POSITION_TYPE.getCode());
                entity.setBusinessGroupId(positionTypeInfo.getBusinessGroupId());
                entity.setPositionTypeId(positionTypeInfo.getPositionTypeId());
                entity.setShowKey(positionTypeInfo.getShowKey());
                entity.setDescription(positionTypeInfo.getDescription());
                return entity;
            }).collect(Collectors.toList()));
        }
        // 批量插入 -- 分批插入500
        List<List<SfaPersonScopeSelectRuleDetailEntity>> partition = ListUtils.partition(detailEntities, 500);
        partition.forEach(e -> {
            ruleDetailMapper.batchInsert(e);
        });
    }
}
