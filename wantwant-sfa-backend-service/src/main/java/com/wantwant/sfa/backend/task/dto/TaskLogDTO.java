package com.wantwant.sfa.backend.task.dto;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/09/上午8:56
 */
@Data
@ToString
public class TaskLogDTO {

    private Long taskId;
    /** 处理人名称 */
    private String processUserName;
    /** 处理人工号 */
    private String processUserId;
    /** 处理类型 */
    private Integer type;
    /** 操作对象 */
    private List<String> processObj;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
    /** 备注 */
    private String remark;

    private String diff;

    private Long traceId;
}
