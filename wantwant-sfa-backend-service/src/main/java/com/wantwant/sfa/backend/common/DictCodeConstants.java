package com.wantwant.sfa.backend.common;

/**
 * 字典服务常量.
 */
public class DictCodeConstants {


    /**
     * 请假类型.
     */
    public static final String CLASSCD_LEAVE_TYPE = "leave_type";
    /**
     * 请假类型: 事假.
     */
    public static final String CLASSCD_LEAVE_TYPE_ITEMVALUE_0 = "0";
    /**
     * 请假类型: 年假.
     */
    public static final Integer CLASSCD_LEAVE_TYPE_ITEMVALUE_1 = 1;

    /**
     * 请假审核状态.
     */
    public static final String CLASSCD_LEAVE_STATUS = "leave_status";
    /**
     * 请假审核状态: 待审核.
     */
    public static final String CLASSCD_LEAVE_STATUS_ITEMVALUE_0 = "0";
    /**
     * 请假审核状态: 审核通过.
     */
    public static final String CLASSCD_LEAVE_STATUS_ITEMVALUE_1 = "1";
    /**
     * 请假审核状态: 审核驳回.
     */
    public static final String CLASSCD_LEAVE_STATUS_ITEMVALUE_2 = "2";
    /**
     * 请假审核状态: 已撤回.
     */
    public static final String CLASSCD_LEAVE_STATUS_ITEMVALUE_3 = "3";
    /**
     * 请假审核状态: 待人资审核.
     */
    public static final String CLASSCD_LEAVE_STATUS_ITEMVALUE_4 = "4";

    /**
     * 请假处理动作.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE = "leave_operator_type";
    /**
     * 请假处理动作: 待审核.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_0 = "0";
    /**
     * 请假处理动作: 审核通过.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_1 = "1";
    /**
     * 请假处理动作: 审核驳回.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_2 = "2";
    /**
     * 请假处理动作: 已撤回.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_3 = "3";
    /**
     * 请假处理动作: 待人资审核.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_4 = "4";
    /**
     * 请假处理动作: 提交.
     */
    public static final String CLASSCD_LEAVE_OPERATOR_TYPE_ITEMVALUE_9 = "9";

    /**
     * 配额类型.
     */
    public static final String CLASSCD_LEAVE_QUOTA_TYPE = "leave_quota_type";
    /**
     * 配额类型: 年假固定配额.
     */
    public static final Integer CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_1 = 1;
    /**
     * 配额类型: 年假浮动配额.
     */
    public static final Integer CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_2 = 2;
    /**
     * 配额类型: 年假固定配额（去年）.
     */
    public static final Integer CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_3 = 3;
    /**
     * 配额类型: 年假浮动配额（去年）.
     */
    public static final Integer CLASSCD_LEAVE_QUOTA_TYPE_ITEMVALUE_4 = 4;

    /**
     * 合伙人目标设置审核状态.
     */
    public static final String CLASSCD_PARTNER_GOAL_SET_STATUS = "partner_goal_set_status";
    /**
     * 合伙人目标设置审核状态: 待审核.
     */
    public static final Integer CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_0 = 0;
    /**
     * 合伙人目标设置审核状态: 已通过.
     */
    public static final Integer CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_1 = 1;
    /**
     * 合伙人目标设置审核状态: 已驳回.
     */
    public static final Integer CLASSCD_PARTNER_GOAL_SET_STATUS_ITEMVALUE_2 = 2;

    /**
     * 通关状态.
     */
    public static final String CLASSCD_COMPLETE_STATUS = "complete_status";
    /**
     * 通关状态: 未打卡.
     */
    public static final Integer CLASSCD_COMPLETE_STATUS_ITEMVALUE_0 = 0;
    /**
     * 通关状态: 已打卡.
     */
    public static final Integer CLASSCD_COMPLETE_STATUS_ITEMVALUE_1 = 1;
    /**
     * 通关状态: 无需通关.
     */
    public static final Integer CLASSCD_COMPLETE_STATUS_ITEMVALUE_2 = 2;

    /**
     * 稽核状态.
     */
    public static final String CLASSCD_COMPLETE_AUDIT_STATUS = "complete_audit_status";
    /**
     * 稽核状态: 未稽核.
     */
    public static final Integer CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_0 = 0;
    /**
     * 稽核状态: 成功.
     */
    public static final Integer CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_1 = 1;
    /**
     * 稽核状态: 异常.
     */
    public static final Integer CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_2 = 2;

    /**
     * 当时人员状态.
     */
    public static final String CLASSCD_COMPLETE_EMPLOYEE_STATUS = "complete_employee_status";
    /**
     * 当时人员状态: 在岗.
     */
    public static final Integer CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_0 = 0;
    /**
     * 当时人员状态: 请假.
     */
    public static final Integer CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_1 = 1;
    /**
     * 当时人员状态: 试岗.
     */
    public static final Integer CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_2 = 2;
    /**
     * 当时人员状态: 离职.
     */
    public static final Integer CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_3 = 3;
    /**
     * 当时人员状态: 拜访中.
     */
    public static final Integer CLASSCD_COMPLETE_EMPLOYEE_STATUS_ITEMVALUE_4 = 4;

    /**
     * 打卡类型.
     */
    public static final String CLASSCD_ATTENDANCE_TYPE = "attendance_type";
    /**
     * 打卡类型: 拜访打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_0 = 0;
    /**
     * 打卡类型: 上班打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_1 = 1;
    /**
     * 打卡类型: 下班打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_2 = 2;
    /**
     * 打卡类型: 通关打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_3 = 3;

    /**
     * 考勤状态.
     */
    public static final String CLASSCD_ATTENDANCE_STATUS = "attendance_status";
    /**
     * 考勤状态: 正常.
     */
    public static final Integer CLASSCD_ATTENDANCE_STATUS_ITEMVALUE_0 = 0;
    /**
     * 考勤状态: 异常.
     */
    public static final Integer CLASSCD_ATTENDANCE_STATUS_ITEMVALUE_1 = 1;

    /**
     * 考勤打卡状态.
     */
    public static final String CLASSCD_ATTENDANCE_EXECPTION_TYPE = "attendance_execption_type";
    /**
     * 考勤打卡状态: 未打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_EXECPTION_TYPE_ITEMVALUE_1 = 1;
    /**
     * 考勤打卡状态: 迟到.
     */
    public static final Integer CLASSCD_ATTENDANCE_EXECPTION_TYPE_ITEMVALUE_2 = 2;
    /**
     * 考勤打卡状态: 无效打卡.
     */
    public static final Integer CLASSCD_ATTENDANCE_EXECPTION_TYPE_ITEMVALUE_3 = 3;

    /**
     * 考勤稽核状态.
     */
    public static final String CLASSCD_ATTENDANCE_AUDIT_STATUS = "attendance_audit_status";
    /**
     * 考勤稽核状态: 未稽核.
     */
    public static final Integer CLASSCD_ATTENDANCE_AUDIT_STATUS_ITEMVALUE_0 = 0;
    /**
     * 考勤稽核状态: 正常.
     */
    public static final Integer CLASSCD_ATTENDANCE_AUDIT_STATUS_ITEMVALUE_1 = 1;
    /**
     * 考勤稽核状态: 异常.
     */
    public static final Integer CLASSCD_ATTENDANCE_AUDIT_STATUS_ITEMVALUE_2 = 2;

    /**
     * 考勤稽核原因.
     */
    public static final String CLASSCD_ATTENDANCE_AUDIT_REASON = "attendance_audit_reason";

    /**
     * 旺铺-考勤打卡状态.
     */
    public static final String CLASSCD_MEMBER_CLOCK_STATE = "member_clock_state";
    /**
     * 旺铺-考勤打卡状态: 正常.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_0 = 0;
    /**
     * 旺铺-考勤打卡状态: 迟到.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_1 = 1;
    /**
     * 旺铺-考勤打卡状态: 旷工.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_2 = 2;
    /**
     * 旺铺-考勤打卡状态: 系统打卡.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_3 = 3;
    /**
     * 旺铺-考勤打卡状态: 稽核异常.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_4 = 4;
    /**
     * 旺铺-考勤打卡状态: 早退.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_11 = 11;
    /**
     * 旺铺-考勤打卡状态: 无效.
     */
    public static final Integer CLASSCD_MEMBER_CLOCK_STATE_ITEMVALUE_12 = 12;

    /**
     * 地图打卡类型.
     */
    public static final String CLASSCD_MAP_ATTENDANCE_TYPE = "map_attendance_type";
    /**
     * 地图打卡类型: 考勤.
     */
    public static final Integer CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_1 = 1;
    /**
     * 地图打卡类型: 通关.
     */
    public static final Integer CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_2 = 2;
    /**
     * 地图打卡类型: 拜访.
     */
    public static final Integer CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_3 = 3;
    /**
     * 地图打卡类型: 会议.
     */
    public static final Integer CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_4 = 4;
    /**
     * 地图打卡类型: 动态定位.
     */
    public static final Integer CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_5 = 5;

    /**
     * 客户类型.
     */
    public static final String CLASSCD_CUSTOMER_TYPE = "customer_type";
    /**
     * 客户类型: 批发.
     */
    public static final Integer CLASSCD_CUSTOMER_TYPE_ITEMVALUE_0 = 0;
    /**
     * 客户类型: 终端.
     */
    public static final Integer CLASSCD_CUSTOMER_TYPE_ITEMVALUE_1 = 1;
    /**
     * 客户类型: 合伙人.
     */
    public static final Integer CLASSCD_CUSTOMER_TYPE_ITEMVALUE_2 = 2;

    /**
     * 拜访稽核状态.
     */
    public static final String CLASSCD_VISIT_AUDIT_STATUS = "visit_audit_status";
    /**
     * 拜访稽核状态: 正常.
     */
    public static final Integer CLASSCD_VISIT_AUDIT_STATUS_ITEMVALUE_0 = 0;
    /**
     * 拜访稽核状态: 异常.
     */
    public static final Integer CLASSCD_VISIT_AUDIT_STATUS_ITEMVALUE_1 = 1;

    /**
     * 月报承诺业绩-合伙人类型.
     */
    public static final String CLASSCD_PROMISE_PARTNER_TYPE = "promise_partner_type";
    /**
     * 月报承诺业绩-合伙人类型: 已建档.
     */
    public static final Integer CLASSCD_PROMISE_PARTNER_TYPE_ITEMVALUE_1 = 1;
    /**
     * 月报承诺业绩-合伙人类型: 未建档.
     */
    public static final Integer CLASSCD_PROMISE_PARTNER_TYPE_ITEMVALUE_2 = 2;
    /**
     * 月报承诺业绩-合伙人类型: 无法确定.
     */
    public static final Integer CLASSCD_PROMISE_PARTNER_TYPE_ITEMVALUE_3 = 3;

    /**
     * 岗位角色类型.
     */
    public static final String CLASSCD_USER_IDENTITY = "user_identity";

    public static final String CLASSCD_USER_IDENTITY_EN = "user_identity_en";
    /**
     * 岗位角色类型: 全职合伙人.
     */
    public static final Integer CLASSCD_USER_IDENTITY_ITEMVALUE_0 = 0;

    /**
     * 登录异常类型.
     */
    public static final String CLASSCD_LOGIN_ABNORMAL_TYPE = "login_abnormal_type";

    /**
     * 登录异常原因.
     */
    public static final String CLASSCD_LOGIN_ABNORMAL_REASON = "login_abnormal_reason";

    /**
     * 稽核结果.
     */
    public static final String CLASSCD_LOGIN_ABNORMAL_AUDIT_RESULT = "login_abnormal_audit_result";
    /**
     * 稽核结果: 未稽核.
     */
    public static final Integer CLASSCD_LOGIN_ABNORMAL_AUDIT_RESULT_ITEMVALUE_0 = 0;
    /**
     * 稽核结果: 异常.
     */
    public static final Integer CLASSCD_LOGIN_ABNORMAL_AUDIT_RESULT_ITEMVALUE_2 = 2;

    /**
     * 稽核异常原因.
     */
    public static final String CLASSCD_LOGIN_ABNORMAL_AUDIT_REASON = "login_abnormal_audit_reason";

    /**
     * 复核结果.
     */
    public static final String CLASSCD_LOGIN_ABNORMAL_LEO_AUDIT_RESULT = "login_abnormal_leo_audit_result";
    /**
     * 复核结果: 待复核.
     */
    public static final Integer CLASSCD_LOGIN_ABNORMAL_LEO_AUDIT_RESULT_ITEMVALUE_0 = 0;

    /**
     * 异常程度.
     */
    public static final String CLASSCD_ABNORMAL_DEGREE = "abnormal_degree";
    /**
     * 异常程度: 低风险.
     */
    public static final Integer CLASSCD_ABNORMAL_DEGREE_ITEMVALUE_1 = 1;
    /**
     * 异常程度: 中风险.
     */
    public static final Integer CLASSCD_ABNORMAL_DEGREE_ITEMVALUE_2 = 2;
    /**
     * 异常程度: 高风险.
     */
    public static final Integer CLASSCD_ABNORMAL_DEGREE_ITEMVALUE_3 = 3;

    /**
     * 异常等级.
     */
    public static final String CLASSCD_ABNORMAL_LEVEL = "abnormal_level";
    /**
     * 异常等级: -.
     */
    public static final Integer CLASSCD_ABNORMAL_LEVEL_ITEMVALUE_0 = 0;
    /**
     * 异常等级: 1级.
     */
    public static final Integer CLASSCD_ABNORMAL_LEVEL_ITEMVALUE_1 = 1;
    /**
     * 异常等级: 2级.
     */
    public static final Integer CLASSCD_ABNORMAL_LEVEL_ITEMVALUE_2 = 2;

    /**
     * 回访类型.
     */
    public static final String CLASSCD_CALLBACK_TYPE = "callback_type";

    /**
     * 特殊行政区划.
     */
    public static final String CLASSCD_SPEICAL_REGION = "special_region";

    /**
     * 轨迹地图目标人员行政区划.
     */
    public static final String CLASSCD_MAP_QUERY_REGION_TYPE = "map_query_region_type";

    /**
     * 任务类型.
     */
    public static final String CLASSCD_TASK_TYPE = "task_type";

    /**
     * 任务子类.
     */
    public static final String CLASSCD_TASK_SUB_TYPE = "task_sub_type";

    /**
     * 任务性质.
     */
    public static final String CLASSCD_TASK_NATURE = "task_nature";

    /**
     * 任务优先级.
     */
    public static final String CLASSCD_TASK_PRIORITY = "task_priority";

    public static final String BH_ORDER_CHANNEL = "sfa_project_bh";

    public static final String DAILY_SCALE = "daily_scale";
    public static final String DAILY_EXCLUDE_GROUP = "daily_exclude_group";
    public static final String DAILY_PUSH_EMPLOYEE = "daily_push_employee";
    public static final String YEAR_FESTIVAL_SCALE = "year_festival_scale";
    public static final String YEAR_FESTIVAL_DATE = "year_festival_date";
    public static final String YEAR_FESTIVAL_CURRENT = "year_festival_current";
    public static final String LOGIN_ABNORMAL_WHITE_LIST = "login_abnormal_white_list";
    public static final String WORK_REPORT_DISPLAY_IMAGE_SIZE = "work_report_display_image_size";
    public static final String SFA_PERFORMANCE_BONUS_CAREER = "sfa_performance_bonus_career";
    public static final String LEAVE_AUDIT_COUNTDOWN_DAYS = "leave_audit_countdown_days";


    /**
     * 销售管理流程奖惩交换机
     */
    public final static String WORKFLOW_REWARD_PUNISHMENT_EXCHANGE = "workflow.reward.punishment.exchange";
    /**
     * 销售管理流程奖惩队列
     */
    public final static String WORKFLOW_REWARD_PUNISHMENT_QUEUE = "workflow.reward.punishment.queue";
}
