package com.wantwant.sfa.backend.task.dto;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/10/下午4:00
 */
@Data
@ApiModel("任务完成并送审DTO")
@ToString
public class TaskCompleteDTO {

    private Long taskId;

    private String processUserId;

    private String processUserName;

    private String remark;

    private String taskTag;

}
