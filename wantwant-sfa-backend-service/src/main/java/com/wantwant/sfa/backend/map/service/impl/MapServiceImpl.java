package com.wantwant.sfa.backend.map.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qcloud.cos.utils.Jackson;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.attendance.service.impl.AttendanceService;
import com.wantwant.sfa.backend.attendance.vo.MemberCalendarVO;
import com.wantwant.sfa.backend.businessGroup.request.BusinessGroupRequest;
import com.wantwant.sfa.backend.businessGroup.service.impl.BusinessGroupService;
import com.wantwant.sfa.backend.businessGroup.vo.BusinessGroupVo;
import com.wantwant.sfa.backend.common.*;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.complete.vo.CompleteListVo;
import com.wantwant.sfa.backend.daily.request.DailyEmployeeRequest;
import com.wantwant.sfa.backend.daily.service.DailyNewService;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeInfoVo;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeVo;
import com.wantwant.sfa.backend.dict.entity.SfaDictCode;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.employee.request.BrotherRequest;
import com.wantwant.sfa.backend.employee.request.ChildRequest;
import com.wantwant.sfa.backend.employee.request.ParentRequest;
import com.wantwant.sfa.backend.employee.vo.BrotherVo;
import com.wantwant.sfa.backend.employee.vo.ChildVo;
import com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO;
import com.wantwant.sfa.backend.employee.vo.ParentVo;
import com.wantwant.sfa.backend.employeeInfo.service.IEmployeeInfoService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessPositionType;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.RankingListPositionEnums;
import com.wantwant.sfa.backend.leave.entity.SfaLeave;
import com.wantwant.sfa.backend.map.entity.SfaMapRealtimePositioning;
import com.wantwant.sfa.backend.map.request.MapListRequest;
import com.wantwant.sfa.backend.map.request.PeriodMapListRequest;
import com.wantwant.sfa.backend.map.request.RealtimePositioningCommitRequest;
import com.wantwant.sfa.backend.map.request.RealtimePositioningListRequest;
import com.wantwant.sfa.backend.map.service.MapService;
import com.wantwant.sfa.backend.map.vo.MapDetail1Vo;
import com.wantwant.sfa.backend.map.vo.MapDetail2Vo;
import com.wantwant.sfa.backend.map.vo.MapDetail3Vo;
import com.wantwant.sfa.backend.map.vo.MapDetail4MeetingVo;
import com.wantwant.sfa.backend.map.vo.MapDetail4NeedConveneVo;
import com.wantwant.sfa.backend.map.vo.MapDetail4NeedParticipateVo;
import com.wantwant.sfa.backend.map.vo.MapDetail4Vo;
import com.wantwant.sfa.backend.map.vo.MapDetail5Vo;
import com.wantwant.sfa.backend.map.vo.MapDetailListVo;
import com.wantwant.sfa.backend.map.vo.MapListVo;
import com.wantwant.sfa.backend.map.vo.RealtimePositioningVo;
import com.wantwant.sfa.backend.mapper.AttendanceMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationViewMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessPositionTypeMapper;
import com.wantwant.sfa.backend.mapper.CustomerInfoMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.WwOrganizationAreaMapper;
import com.wantwant.sfa.backend.mapper.businessTrip.BusinessTripMapper;
import com.wantwant.sfa.backend.mapper.complete.SfaCompleteMapper;
import com.wantwant.sfa.backend.mapper.daily.DimEmpPosRoleOrgDayMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper;
import com.wantwant.sfa.backend.mapper.map.SfaMapRealtimePositioningMapper;
import com.wantwant.sfa.backend.mapper.meeting.MeetingInfoMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.meeting.vo.MeetingInfoVO;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.attendanceTask.Attendance;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.LocalDateUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.visitCustomer.vo.VisitInfoForMapVo;
import com.wantwant.sfa.common.architecture.utils.LocalDateTimeUtils;
import com.wantwant.sfa.common.base.CommonConstant;
import com.wantwant.sfa.common.base.DateTimeUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RefreshScope
public class MapServiceImpl implements MapService {

    private static final String MAP_REALTIME_POSITIONING_LOCK = "map:realtime:positioning:lock";

    // 定义常量
    // 日纬度
    private static final int PERIOD_DAY = 1;
    // 月纬度
    private static final int PERIOD_MONTH = 2;
    // 季纬度
    private static final int PERIOD_QUARTER = 3;
    // 日纬度默认分片长度
    public static final int CHUNK_SIZE_DAY = 1;
    // 月纬度默认分片长度
    @Value("${zw.map.chunkSize.month:8}")
    public int CHUNK_SIZE_MONTH;
    // 季纬度默认分片长度
    @Value("${zw.map.chunkSize.quarter:13}")
    public int CHUNK_SIZE_QUARTER;

    private static final String PERIOD_MONTH_REGEX = "^\\d{4}-(0[1-9]|1[0-2])$";

    private static final String PERIOD_QUARTER_REGEX = "^\\d{4}-Q[1-4]$";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private DictCodeServiceImpl dictCodeServiceImpl;
    @Autowired
    private SettingServiceImpl settingService;
    @Autowired
    private BusinessGroupService businessGroupService;
    @Autowired
    private IEmployeeInfoService employeeInfoService;
    @Autowired
    private DailyNewService dailyNewService;
    @Autowired
    private AttendanceService attendanceService;

    @Autowired
    private DimEmpPosRoleOrgDayMapper dimEmpPosRoleOrgDayMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
    @Autowired
    private WwOrganizationAreaMapper wwOrganizationAreaMapper;
    @Autowired
    private CeoBusinessPositionTypeMapper ceoBusinessPositionTypeMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private SfaCompleteMapper sfaCompleteMapper;
    @Autowired
    private AttendanceMapper attendanceMapper;
    @Autowired
    private MeetingInfoMapper meetingInfoMapper;
    @Autowired
    private SfaMapRealtimePositioningMapper sfaMapRealtimePositioningMapper;
    @Autowired
    private CustomerInfoMapper customerInfoMapper;
    @Resource
    private SfaLeaveMapper sfaLeaveMapper;
    @Resource
    private BusinessTripMapper businessTripMapper;

    @Resource
    private MapService mapService;

    @Qualifier("geTuiSmsSend")
    @Resource
    private AsyncTaskExecutor taskExecutor;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void realtimePositioningCommit(RealtimePositioningCommitRequest request) {
        log.info("realtimePositioningCommit request:{}", request);
        if (redisUtil.setLockIfAbsent(MAP_REALTIME_POSITIONING_LOCK, request.getPerson(), 1, TimeUnit.SECONDS)) {
            try {

                SfaMapRealtimePositioning last = sfaMapRealtimePositioningMapper.selectOne(new LambdaQueryWrapper<SfaMapRealtimePositioning>()
                        .eq(SfaMapRealtimePositioning::getDeleteFlag, 0)
                        .eq(SfaMapRealtimePositioning::getEmployeeId, request.getPerson())
                        .eq(SfaMapRealtimePositioning::getPositioningDate, LocalDate.now())
                        .orderByDesc(SfaMapRealtimePositioning::getPositioningTime)
                        .last("limit 1")
                );
                if (Objects.isNull(last) || LocalDateTime.now().isAfter(last.getPositioningTime().plusMinutes(Long.parseLong(settingService.getValue("map_realtime_positioning_interval_minutes"))))) {

                    SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                            .eq(SfaEmployeeInfoModel::getEmployeeId, request.getPerson()));

                    SfaMapRealtimePositioning entity = new SfaMapRealtimePositioning();
                    if (Objects.nonNull(sfaEmployeeInfoEntity)) {
                        entity.init(request.getPerson(), sfaEmployeeInfoEntity.getEmployeeName());
                        entity.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
                    } else {
                        entity.init(request.getPerson(), "");
                    }
                    entity.setEmployeeId(request.getPerson());
                    entity.setPositioningDate(LocalDate.now());
                    entity.setPositioningTime(LocalDateTime.now());
                    entity.setProvince(request.getProvince());
                    entity.setCity(request.getCity());
                    entity.setDistrict(request.getDistrict());
                    entity.setAddress(request.getAddress());
                    entity.setLongitude(request.getLongitude());
                    entity.setLatitude(request.getLatitude());
                    sfaMapRealtimePositioningMapper.insert(entity);
                }
            } finally {
                redisUtil.unLock(MAP_REALTIME_POSITIONING_LOCK, request.getPerson());
            }
        }
    }

    @Override
    public List<RealtimePositioningVo> realtimePositioningList(RealtimePositioningListRequest request) {
        log.info("realtimePositioningList request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setBusinessGroup(loginInfo.getBusinessGroup());
        if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            return Collections.emptyList();
        }
        if (Objects.isNull(request.getPositioningStartDate())) {
            request.setPositioningStartDate(LocalDate.now());
            request.setPositioningEndDate(LocalDate.now());
        }
        List<RealtimePositioningVo> resultList;

        List<RealtimePositioningVo> list = sfaMapRealtimePositioningMapper.selectNewestRealtimePositioningAllList(request);
        if (!CollectionUtils.isEmpty(list)) {
            // 获取每个employeeInfoId下的最新positioningTime记录
            Map<Integer, RealtimePositioningVo> latestRecords = list.stream()
                    .collect(Collectors.toMap(
                            RealtimePositioningVo::getEmployeeInfoId, // key为employeeInfoId
                            record -> record,                  // value为record本身
                            (record1, record2) -> record1.getPositioningTime().isAfter(record2.getPositioningTime()) ? record1 : record2, // 合并函数，选择positioningTime较晚的记录
                            LinkedHashMap::new                 // 使用LinkedHashMap保持插入顺序
                    ));

            // 将Map转换为List
            resultList = new ArrayList<>(latestRecords.values());
        } else {
            resultList = new ArrayList<>();
        }

        return resultList;
    }

    @Override
    public List<MapListVo> attendanceListForSome(MapListRequest request) {
        log.info("attendanceListForSome request:{}", request);
        List<MapListVo> returnList = new ArrayList<>();

        // 参数处理
        if (StringUtils.isBlank(request.getParentEmployeeId())) {
            throw new ApplicationException("上级工号不能为空");
        }
        if (Objects.isNull(request.getAttendanceDate())) {
            throw new ApplicationException("指定日期不能为空");
        }

        DailyEmployeeRequest parentDailyEmployeeRequest = new DailyEmployeeRequest();
        parentDailyEmployeeRequest.setEmployeeId(request.getParentEmployeeId());
        parentDailyEmployeeRequest.setTheDate(request.getAttendanceDate());
        DailyEmployeeInfoVo vo = dailyNewService.dailyEmployee(parentDailyEmployeeRequest);
        List<String> childOrganizations = vo.getChildOrganizations();
        if (!CollectionUtils.isEmpty(childOrganizations)) {
            if (StringUtils.isNotBlank(request.getChildOrganizationType())) {
                List<CeoBusinessOrganizationEntity> childOrganizationList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                        .in(CeoBusinessOrganizationEntity::getOrganizationId, childOrganizations)
                        .eq(CeoBusinessOrganizationEntity::getOrganizationType, request.getChildOrganizationType())
                );
                childOrganizations = childOrganizationList.stream().map(CeoBusinessOrganizationEntity::getOrganizationId).collect(Collectors.toList());
            }
            DailyEmployeeRequest dailyEmployeeRequest = new DailyEmployeeRequest();
            dailyEmployeeRequest.setOrganizationIds(childOrganizations);
            dailyEmployeeRequest.setTheDate(request.getAttendanceDate());
            List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(dailyEmployeeRequest);
            if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
                Set<Integer> employeeInfoIdSet = dailyEmployeeVoList.stream().map(DailyEmployeeVo::getEmployeeInfoId).collect(Collectors.toCollection(LinkedHashSet::new));
                employeeInfoIdSet.forEach(employeeInfoId -> {
                    MapListRequest request1 = new MapListRequest();
                    request1.setPerson(request.getPerson());
                    request1.setAttendanceDate(request.getAttendanceDate());
                    request1.setEmployeeInfoId(employeeInfoId);
                    returnList.addAll(attendanceList(request1));
                });
            }
        }
        return returnList;
    }

    @Override
    public List<MapListVo> attendanceList(MapListRequest request) {
        log.info("attendanceList request:{}", Jackson.toJsonString(request));
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        log.info("attendanceList loginInfo:{} ", loginInfo);
        // 操作人处理
        request.setBusinessGroup(loginInfo.getBusinessGroup());
        request.setTimezone(loginInfo.getTimezone());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_OPERATOR.getTextMsg());
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setOrganizationIds(personOrganizationIds);
        }
        // 现在时间
        LocalDateTime nowTime = DateTimeUtility.changeTimezone(LocalDateTime.now(), null, request.getTimezone());
        // 参数处理-获取并校验目标人员信息
        SfaEmployeeInfoModel sfaEmployeeInfoEntity = getEmployeeInfoEntity(request);

        // 判断是否总部
        boolean isZB = OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType());
        // 判断是否BD
        boolean isBD = sfaEmployeeInfoEntity.getType() == 6 || sfaEmployeeInfoEntity.getType() == 7;
        // 目标人员组织id（会议用）
        List<String> employeeOrganizationIds;

        // 组别 --获取所有组别
        Map<Integer, String> businessGroupMap = businessGroupService.getBusinessGroupList(new BusinessGroupRequest())
                .stream().collect(Collectors.toMap(BusinessGroupVo::getBusinessGroup, BusinessGroupVo::getBusinessGroupName));
        // 目标人员区县
        List<String> districtCodeList;
        // 目标人员组织详情（会议用）
        Map<String, CeoBusinessOrganizationEntity> employeeOrganizationMap;
        // 目标人员岗位类型名称（all用）
        List<CeoBusinessPositionType> employeePositionTypeList;
        // 目标人员组织详情（all用）
        List<CeoBusinessOrganizationEntity> employeeOrganizationList;
        // 目标人员的同组同岗-- 计算排行(同组同岗优秀和同组同岗位平均)
        Set<Integer> sameEmployeeInfoIdList = new HashSet<>();
        Set<String> sameEmployeeIdList = new HashSet<>();
        Set<Long> sameMemberKeyList = new HashSet<>();
        Map<Long, Integer> bdMemberKeyEmployeeInfoIdMap;

        if (!isBD) {
            bdMemberKeyEmployeeInfoIdMap = new HashMap<>();
            // 目标人员的所有组织
            List<SfaPositionRelationEntity> sfaPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, request.getEmployeeInfoId())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .eq(SfaPositionRelationEntity::getStatus, 1)
            );
            if (CollectionUtils.isEmpty(sfaPositionRelationList)) {
                throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_TARGET.getTextMsg());
            }
            employeeOrganizationIds = sfaPositionRelationList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
            employeeOrganizationList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, employeeOrganizationIds)
            );
            employeeOrganizationMap = employeeOrganizationList.stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, v -> v));
            Set<Long> employeePositionTypeIds = sfaPositionRelationList.stream().map(item -> Long.valueOf(item.getPositionTypeId())).collect(Collectors.toSet());
            employeePositionTypeList = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>()
                    .in(CeoBusinessPositionType::getId, employeePositionTypeIds)
            );
            districtCodeList = getDistrictCodeList(request, sfaPositionRelationList);

            // 目标人员的当前组所有组织
            List<SfaPositionRelationEntity> sfaPositionRelationList1 = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, request.getEmployeeInfoId())
                    .eq(SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroup())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .eq(SfaPositionRelationEntity::getStatus, 1)
            );
            if (CollectionUtils.isEmpty(sfaPositionRelationList1)) {
                throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_TARGET.getTextMsg());
            }

            // 目标人员的同组同岗(包含目标人员)
            List<SfaPositionRelationEntity> samePositionList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .in(SfaPositionRelationEntity::getPositionTypeId, sfaPositionRelationList1.stream().map(SfaPositionRelationEntity::getPositionTypeId).collect(Collectors.toSet()))
                    .eq(SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroup())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .eq(SfaPositionRelationEntity::getStatus, 1)
            );
            if (!CollectionUtils.isEmpty(samePositionList)) {
                sameEmployeeInfoIdList = samePositionList.stream()
                        .map(SfaPositionRelationEntity::getEmployeeInfoId)
                        .filter(Objects::nonNull).collect(Collectors.toSet());
                sameEmployeeIdList = samePositionList.stream()
                        .map(SfaPositionRelationEntity::getEmpId)
                        .filter(Objects::nonNull).collect(Collectors.toSet());

                if (!CollectionUtils.isEmpty(sameEmployeeInfoIdList)) {
                    List<SfaEmployeeInfoModel> sameEmployeeInfoEntityList = sfaEmployeeInfoMapper.selectList(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                            .in(SfaEmployeeInfoModel::getId, sameEmployeeInfoIdList)
                    );
                    if (!CollectionUtils.isEmpty(sameEmployeeInfoEntityList)) {
                        sameMemberKeyList = sameEmployeeInfoEntityList.stream()
                                .map(SfaEmployeeInfoModel::getMemberKey)
                                .filter(Objects::nonNull).collect(Collectors.toSet());
                    }
                }
            }
        } else {
            districtCodeList = new ArrayList<>();

            // 目标人员的所有组织--业务bd
            List<SfaPositionRelationEntity> sfaPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, request.getEmployeeInfoId())
                    .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                    .eq(SfaPositionRelationEntity::getStatus, 1)
            );
            if (CollectionUtils.isEmpty(sfaPositionRelationList)) {
                throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_TARGET.getTextMsg());
            }
            // 业务BD 与合伙人共用
            request.setPositionTypeId(RankingListPositionEnums.BRANCH.getPositionTypeId());
            // 业务bd使用城市经理的code
            employeeOrganizationIds = sfaPositionRelationList.stream().map(SfaPositionRelationEntity::getDepartmentCode).collect(Collectors.toList());
            employeeOrganizationList = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, employeeOrganizationIds)
            );
            employeeOrganizationMap = employeeOrganizationList.stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, v -> v));
            // 业务bd岗位名称直接使用positionRelation表中的 post_type + type
            employeePositionTypeList = sfaPositionRelationList.stream()
                    .map(item -> {
                        CeoBusinessPositionType ceoBusinessPositionType = new CeoBusinessPositionType();
                        ceoBusinessPositionType.setPositionName(EmployeeInfoPostTypeEnum.getNameByCode(item.getPostType()) + EmployeeInfoTypeEnum.getNameByCode(item.getType()));
                        return ceoBusinessPositionType;
                    })
                    .collect(Collectors.toList());

            // 目标人员的同组同岗(包含目标人员)--即同组所有BD
            List<BrotherVo> bdList = sfaEmployeeInfoMapper.queryBrotherBD(BrotherRequest.builder().businessGroup(request.getBusinessGroup()).build());
            if (!CollectionUtils.isEmpty(bdList)) {
                sameEmployeeInfoIdList = bdList.stream()
                        .map(BrotherVo::getEmployeeInfoId)
                        .filter(Objects::nonNull).collect(Collectors.toSet());
                sameEmployeeIdList = bdList.stream()
                        .map(BrotherVo::getEmployeeId)
                        .filter(Objects::nonNull).collect(Collectors.toSet());
                sameMemberKeyList = bdList.stream()
                        .map(BrotherVo::getMemberKey)
                        .filter(Objects::nonNull).collect(Collectors.toSet());

                bdMemberKeyEmployeeInfoIdMap = bdList.stream().collect(Collectors.toMap(BrotherVo::getMemberKey, BrotherVo::getEmployeeInfoId));
            } else {
                bdMemberKeyEmployeeInfoIdMap = new HashMap<>();
            }
        }
        request.setSameEmployeeInfoIdList(sameEmployeeInfoIdList);
        request.setSameEmployeeIdList(sameEmployeeIdList);
        request.setSameMemberKeyList(sameMemberKeyList);

        // 参数处理-日期-获取请求日期范围中所有日期
        List<LocalDate> dateList = getDateList(request);

        // 参数处理-打卡类型-确定最终所需的打卡类型(大类) 且 确认排除的子类
        List<Integer> finalAttendanceTypeList = getAttendanceTypeList(request, isBD);

        // 每日汇总数据
        // 1 考勤
        Map<LocalDate, MapDetail1Vo> detail1Map = new HashMap<>();
        // 2 通关
        Map<LocalDate, List<MapDetail2Vo>> detail2ListMap = new HashMap<>();
        // 3 拜访
        Map<LocalDate, List<MapDetail3Vo>> detail3ListMap = new HashMap<>();
        // 4 会议
        Map<LocalDate, MapDetail4Vo> detail4Map = new HashMap<>();
        // 5 动态定位
        Map<LocalDate, MapDetail5Vo> detail5Map = new HashMap<>();

        // 作业情况--考勤 通关 拜访 会议
        Map<LocalDate, Boolean> attendanceIsNormalMap = new HashMap<>();
        Map<LocalDate, Boolean> completeIsNormalMap = new HashMap<>();
        Map<LocalDate, Boolean> visitIsNormalMap = new HashMap<>();
        Map<LocalDate, Boolean> meetingIsNormalMap = new HashMap<>();

        // 考勤点排序计算间距用
        List<MapDetail1Vo> detail1SortList = new ArrayList<>();

        // 指定时间范围内同组同岗所有数据
        List<MapDetailListVo> allListSame = new ArrayList<>();
        // 指定时间范围内同组同岗的每日最远总距离
        Map<String, Double> farthestDistanceMap = new HashMap<>();
        // 指定时间范围内同组同岗的每日总距离
        Map<String, Double> totalDistanceMap = new HashMap<>();

        // 指定时间范围内所有数据
        List<MapDetailListVo> allList = new ArrayList<>();
        // 过滤出所筛选的打卡类型的数据
        List<MapDetailListVo> allFilterList = new ArrayList<>();
        // 最终返回的数据结构
        List<MapListVo> returnList = new ArrayList<>();

        // I、考勤打卡
        List<MapDetailListVo> attendanceList = new ArrayList<>();
//        if (!isBD) {
        List<Attendance> attendanceV2List = attendanceMapper.selectAttendanceV2ListForMap(request);
        if (!CollectionUtils.isEmpty(attendanceV2List)) {
            // 获取指定日期前最近一次打卡记录
            Attendance attendanceV2Lately = attendanceMapper.selectAttendanceV2LatelyForMap(request);
            if (Objects.nonNull(attendanceV2Lately)) {
                MapDetail1Vo lately = new MapDetail1Vo();
                lately.setAttendanceDate(attendanceV2Lately.getCalendarDate());
                lately.setLongitude(attendanceV2Lately.getLongitude());
                lately.setLatitude(attendanceV2Lately.getLatitude());
                detail1SortList.add(lately);
            }
            // 枚举值-->描述
            Map<Integer, String> attendanceStatusMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_ATTENDANCE_STATUS);
            Map<Integer, String> attendanceExecptionTypeMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_ATTENDANCE_EXECPTION_TYPE);
            Map<Integer, String> attendanceAuditStatusMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_ATTENDANCE_AUDIT_STATUS);
            Map<Integer, String> attendanceAuditReasonMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_ATTENDANCE_AUDIT_REASON);

            attendanceV2List.forEach(vo -> {
                MapDetailListVo map = new MapDetailListVo();
                map.setId(Long.valueOf(vo.getId()));
                map.setAttendanceType(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_1);
                map.setAttendanceChildType(Objects.nonNull(vo.getAttendanceExecptionType()) && Objects.equals(vo.getAttendanceExecptionType(), DictCodeConstants.CLASSCD_ATTENDANCE_EXECPTION_TYPE_ITEMVALUE_2) ? MapAttendanceTypeEnum.CHILD11.getMapAttendanceChildType() : (Objects.nonNull(vo.getIsTrip()) && vo.getIsTrip() ? MapAttendanceTypeEnum.CHILD12.getMapAttendanceChildType() : MapAttendanceTypeEnum.CHILD10.getMapAttendanceChildType()));
                boolean isOnWork = Objects.equals(vo.getAttendanceType(), DictCodeConstants.CLASSCD_ATTENDANCE_TYPE_ITEMVALUE_1);
                map.setAttendanceName(isOnWork ? ComonLanguageEnum.ON_WORK.getDesc() : ComonLanguageEnum.ATTENDANCE.getDesc());
                map.setAttendanceStatusName(attendanceStatusMap.get(vo.getAttendanceStatus()));
                map.setAttendanceExecptionTypeName(attendanceExecptionTypeMap.get(vo.getAttendanceExecptionType()));
                map.setAttendanceAuditStatusName(attendanceAuditStatusMap.get(vo.getZbAuditStatus() == 0 ? vo.getBusinessAuditStatus() : vo.getZbAuditStatus()));
                map.setAttendanceAuditReasonName(attendanceAuditReasonMap.get(vo.getZbAuditStatus() == 0 ? vo.getBusinessAuditReason() : vo.getZbAuditReason()));
                map.setAttendanceTime(vo.getAttendanceTime());
                map.setAttendanceTimeSort(Objects.nonNull(vo.getAttendanceTime()) ? vo.getAttendanceTime() : LocalDateTime.of(vo.getCalendarDate(), LocalTime.of(8, 30)));
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setTime(map.getAttendanceTimeSort().toLocalTime());
                map.setAddress(vo.getAddress());
                map.setImage(vo.getPicUrl());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                attendanceList.add(map);

                // 汇总数据
                if (isOnWork) {
                    MapDetail1Vo detail1 = detail1Map.get(map.getAttendanceDate());
                    if (Objects.isNull(detail1)) {
                        // 标记为上班第一次打卡点
                        attendanceIsNormalMap.put(map.getAttendanceDate(), vo.getAttendanceStatus() == 0 && (vo.getZbAuditStatus() == 0 ? vo.getBusinessAuditStatus() : vo.getZbAuditStatus()) != 2);
                        map.setIsFirstOnWork(Boolean.TRUE);
                        detail1 = new MapDetail1Vo();
                        detail1.setAttendanceDate(map.getAttendanceDate());
                        detail1.setLongitude(map.getLongitude());
                        detail1.setLatitude(map.getLatitude());
                        detail1.setAttendanceNameOnWork(map.getAttendanceName());
                        detail1.setAttendanceTimeOnWork(map.getAttendanceTime());
                        detail1.setTimeOnWork(map.getTime());
                        detail1.setAttendanceStatusName(map.getAttendanceStatusName());
                        detail1.setAttendanceExecptionTypeName(map.getAttendanceExecptionTypeName());
                        detail1Map.put(map.getAttendanceDate(), detail1);
                        detail1SortList.add(detail1);
                    }
                }
            });
        }
        // 同组同岗位人员考勤
        List<Attendance> attendanceV2ListSame = attendanceMapper.selectAttendanceV2ListForMapSame(request);
        if (!CollectionUtils.isEmpty(attendanceV2ListSame)) {
            attendanceV2ListSame.forEach(vo -> {
                MapDetailListVo map = new MapDetailListVo();
                map.setEmployeeInfoId(vo.getEmployeeInfoId());
                map.setAttendanceTimeSort(Objects.nonNull(vo.getAttendanceTime()) ? vo.getAttendanceTime() : LocalDateTime.of(vo.getCalendarDate(), LocalTime.of(8, 30)));
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                allListSame.add(map);
            });
        }
        // 计算每日上班点（第一条考勤点）间距离
        if (!CollectionUtils.isEmpty(detail1SortList) && detail1SortList.size() > 1) {
            detail1SortList.sort(Comparator.comparing(MapDetail1Vo::getAttendanceDate));
            detail1SortList.forEach(vo -> {
                int index = detail1SortList.indexOf(vo);
                if (index > 0) {
                    if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                        MapDetail1Vo preVo = findPrevious1(detail1SortList, index);
                        if (Objects.nonNull(preVo)) {
                            vo.setPrePointDistance(calculateDistance(Double.parseDouble(preVo.getLatitude()), Double.parseDouble(preVo.getLongitude()), Double.parseDouble(vo.getLatitude()), Double.parseDouble(vo.getLongitude())));
                        }
                    }
                }
            });
            Map<LocalDate, MapDetail1Vo> detail1SortMap = detail1SortList.stream().collect(Collectors.toMap(MapDetail1Vo::getAttendanceDate, v -> v));
            attendanceList.forEach(vo -> {
                if (Objects.nonNull(vo.getIsFirstOnWork()) && vo.getIsFirstOnWork()) {
                    MapDetail1Vo detail1 = detail1SortMap.get(vo.getAttendanceDate());
                    if (Objects.nonNull(detail1)) {
                        vo.setPreFirstOnWorkPointDistance(detail1.getPrePointDistance());
                    }
                }
            });
        }
        allList.addAll(attendanceList);

        // II、通关打卡
        List<MapDetailListVo> completeList = new ArrayList<>();

        List<CompleteListVo> completeListVoList = sfaCompleteMapper.selectCompleteListForMap(request);
        if (!CollectionUtils.isEmpty(completeListVoList)) {
            // 枚举-->描述
            Map<Integer, String> completeStatusMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_COMPLETE_STATUS);
            Map<Integer, String> completeAuditStatusMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS);

            for (int i = 0; i < completeListVoList.size(); i++) {
                CompleteListVo vo = completeListVoList.get(i);
                MapDetailListVo map = new MapDetailListVo();
                map.setId(vo.getId());
                map.setEmployeeName(vo.getEmployeeName());
                map.setAttendanceType(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_2);
                map.setAttendanceChildType((Objects.nonNull(vo.getCompleteStatus()) && Objects.equals(vo.getCompleteStatus(), DictCodeConstants.CLASSCD_COMPLETE_STATUS_ITEMVALUE_0)) || (Objects.nonNull(vo.getAuditStatus()) && Objects.equals(vo.getAuditStatus(), DictCodeConstants.CLASSCD_COMPLETE_AUDIT_STATUS_ITEMVALUE_2)) ? MapAttendanceTypeEnum.CHILD21.getMapAttendanceChildType() : MapAttendanceTypeEnum.CHILD20.getMapAttendanceChildType());
                map.setAttendanceName(ComonLanguageEnum.COMPLETE.getDesc() + (i + 1));
                map.setCompleteStatusName(completeStatusMap.get(vo.getCompleteStatus()));
                map.setCompleteAuditStatusName(completeAuditStatusMap.get(vo.getAuditStatus()));
                map.setCompleteReason(vo.getReason());
                map.setCompleteTimePeriod(LocalDateTimeUtils.formatTime(vo.getCompleteStartTime(), "yyyy-MM-dd HH:mm") + "~" + LocalDateTimeUtils.formatTime(vo.getCompleteEndTime(), "HH:mm"));
                map.setCompleteNum(vo.getCompleteNum());
                map.setAttendanceTime(vo.getCompleteTime());
                map.setAttendanceTimeSort(Objects.nonNull(vo.getCompleteTime()) ? vo.getCompleteTime() : vo.getCompleteStartTime());
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setTime(map.getAttendanceTimeSort().toLocalTime());
                map.setImage(vo.getImage());
                map.setAddress(vo.getAddress());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                completeList.add(map);
                if (Objects.isNull(completeIsNormalMap.get(map.getAttendanceDate())) || completeIsNormalMap.get(map.getAttendanceDate())) {
                    completeIsNormalMap.put(map.getAttendanceDate(), Objects.nonNull(vo.getCompleteStatus()) && vo.getCompleteStatus() != 0 && (Objects.isNull(vo.getAuditStatus()) || vo.getAuditStatus() != 2));
                }
                // 汇总数据--以日纬度汇总
                List<MapDetail2Vo> detail2List = detail2ListMap.get(map.getAttendanceDate());
                if (CollectionUtils.isEmpty(detail2List)) {
                    detail2List = new ArrayList<>();
                }
                MapDetail2Vo detail2 = new MapDetail2Vo();
                detail2.setCompleteNum(map.getCompleteNum());
                detail2.setCompleteStatusName(map.getCompleteStatusName());
                detail2.setCompleteAuditStatusName(map.getCompleteAuditStatusName());
                detail2.setCompleteReason(map.getCompleteReason());
                detail2List.add(detail2);
                detail2ListMap.put(map.getAttendanceDate(), detail2List);
            }
        }
        // 同组同岗通关
        List<CompleteListVo> completeListVoListSame = sfaCompleteMapper.selectCompleteListForMapSame(request);
        if (!CollectionUtils.isEmpty(completeListVoListSame)) {
            for (int i = 0; i < completeListVoListSame.size(); i++) {
                CompleteListVo vo = completeListVoListSame.get(i);
                MapDetailListVo map = new MapDetailListVo();
                map.setEmployeeInfoId(vo.getEmployeeInfoId());
                map.setAttendanceTimeSort(Objects.nonNull(vo.getCompleteTime()) ? vo.getCompleteTime() : vo.getCompleteStartTime());
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                allListSame.add(map);
            }
        }

        allList.addAll(completeList);

        // III、拜访打卡
        List<MapDetailListVo> visitList = new ArrayList<>();

        List<SfaDictCode> customerTypeList = dictCodeServiceImpl.getListByClassCd(DictCodeConstants.CLASSCD_CUSTOMER_TYPE);
        Map<Integer, String> customerTypeMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_CUSTOMER_TYPE);
        Map<Integer, Integer> customerTypeChildMap = customerTypeList.stream().collect(Collectors.toMap(model -> Integer.valueOf(model.getItemValue()), model -> Integer.valueOf(model.getItemInfo())));
//        if (!isBD) {
        List<VisitInfoForMapVo> visitInfoList = customerInfoMapper.selectVisitInfoListForMap(request);
        if (!CollectionUtils.isEmpty(visitInfoList)) {
            Map<Integer, String> visitAuditStatusMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_VISIT_AUDIT_STATUS);

            for (int i = 0; i < visitInfoList.size(); i++) {
                VisitInfoForMapVo vo = visitInfoList.get(i);
                MapDetailListVo map = new MapDetailListVo();
                map.setId(vo.getId());
                map.setAttendanceType(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_3);
                map.setAttendanceChildType(Objects.nonNull(vo.getAuditStatus()) && Objects.equals(vo.getAuditStatus(), DictCodeConstants.CLASSCD_VISIT_AUDIT_STATUS_ITEMVALUE_1) ? MapAttendanceTypeEnum.CHILD34.getMapAttendanceChildType() : customerTypeChildMap.get(vo.getCustomerType()));
                map.setAttendanceName(ComonLanguageEnum.VISIT.getDesc() + (i + 1));
                map.setVisitCustomerName(vo.getCustomerName());
                map.setVisitCustomerTypeName(customerTypeMap.get(vo.getCustomerType()));
                map.setVisitAuditStatusName(visitAuditStatusMap.get(vo.getAuditStatus()));
                map.setVisitReason(vo.getAuditReason());
                map.setVisitTimePeriod(LocalDateTimeUtils.formatTime(vo.getStartTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss) + "~" + LocalDateTimeUtils.formatTime(vo.getEndTime(), "HH:mm:ss"));
                map.setVisitTimeDuration(vo.getTimeCost());
                map.setVisitTimeDurationMinutes(LocalDateUtils.timeCostToMinutes(vo.getTimeCost()));
                map.setAttendanceTime(vo.getStartTime());
                map.setAttendanceTimeSort(vo.getStartTime());
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setTime(map.getAttendanceTimeSort().toLocalTime());
                map.setAddress(vo.getAddress());
                map.setImage(vo.getStoreImageUrl());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                map.setCustomerType(vo.getCustomerType());
                map.setOpenType(vo.getOpenType());
                visitList.add(map);
                if (Objects.isNull(visitIsNormalMap.get(map.getAttendanceDate())) || visitIsNormalMap.get(map.getAttendanceDate())) {
                    visitIsNormalMap.put(map.getAttendanceDate(), Objects.isNull(vo.getAuditStatus()) || vo.getAuditStatus() != 1);
                }
            }
        }
        List<VisitInfoForMapVo> visitInfoListSame = customerInfoMapper.selectVisitInfoListForMapSame(request);
        if (!CollectionUtils.isEmpty(visitInfoListSame)) {
            for (int i = 0; i < visitInfoListSame.size(); i++) {
                VisitInfoForMapVo vo = visitInfoListSame.get(i);
                MapDetailListVo map = new MapDetailListVo();
                map.setEmployeeInfoId(vo.getEmployeeInfoId());
                map.setAttendanceTimeSort(vo.getStartTime());
                map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                map.setLongitude(vo.getLongitude());
                map.setLatitude(vo.getLatitude());
                allListSame.add(map);
            }
        }
//        } else {
//
//        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        Map<LocalDate, List<MapDetailListVo>> visitMap;
        if (!CollectionUtils.isEmpty(visitList)) {
            visitMap = visitList.stream().collect(Collectors.groupingBy(MapDetailListVo::getAttendanceDate));
        } else {
            visitMap = new HashMap<>();
        }
        // 汇总数据
        dateList.forEach(date -> {
            List<MapDetailListVo> list = visitMap.get(date);

            List<MapDetail3Vo> detail3List = detail3ListMap.get(date);
            if (CollectionUtils.isEmpty(detail3List)) {
                detail3List = new ArrayList<>();
            }
            if (!CollectionUtils.isEmpty(list)) {

                // 合计
                MapDetail3Vo detail3All = new MapDetail3Vo();
                detail3All.init(null, ComonLanguageEnum.TOTAL.getDesc(), ComonLanguageEnum.SECONDS.getDesc());
                setDetail3Vo(list, detail3All, formatter);
                detail3List.add(detail3All);

                // 建档
                MapDetail3Vo detail3CustomerType3 = new MapDetail3Vo();
                detail3CustomerType3.init(3, ComonLanguageEnum.FILE_CREATION.getDesc(), ComonLanguageEnum.SECONDS.getDesc());
                List<MapDetailListVo> filterCustomerType3List = list.stream().filter(vo -> vo.getCustomerType() == 0 || vo.getCustomerType() == 1).collect(Collectors.toList());
                setDetail3Vo(filterCustomerType3List, detail3CustomerType3, formatter);
                detail3List.add(detail3CustomerType3);

                // 分类统计-按客户类型
                Map<Integer, List<MapDetailListVo>> visitCustomerTypeMap = list.stream().collect(Collectors.groupingBy(MapDetailListVo::getCustomerType));
                for (Integer customerTypeId : customerTypeMap.keySet()) {
                    MapDetail3Vo detail3CustomerType = new MapDetail3Vo();
                    detail3CustomerType.init(customerTypeId, customerTypeMap.get(customerTypeId), ComonLanguageEnum.SECONDS.getDesc());
                    List<MapDetailListVo> visitCustomerTypeList = visitCustomerTypeMap.get(customerTypeId);
                    setDetail3Vo(visitCustomerTypeList, detail3CustomerType, formatter);
                    detail3List.add(detail3CustomerType);
                }

            } else {
                // 合计
                MapDetail3Vo detail3All = new MapDetail3Vo();
                detail3All.init(null, ComonLanguageEnum.TOTAL.getDesc(), ComonLanguageEnum.SECONDS.getDesc());
                detail3List.add(detail3All);
                // 建档
                MapDetail3Vo detail3CustomerType3 = new MapDetail3Vo();
                detail3CustomerType3.init(3, ComonLanguageEnum.FILE_CREATION.getDesc(), ComonLanguageEnum.SECONDS.getDesc());
                detail3List.add(detail3CustomerType3);
                // 分类统计-按客户类型
                for (Integer customerTypeId : customerTypeMap.keySet()) {
                    MapDetail3Vo detail3CustomerType = new MapDetail3Vo();
                    detail3CustomerType.init(customerTypeId, customerTypeMap.get(customerTypeId), ComonLanguageEnum.SECONDS.getDesc());
                    detail3List.add(detail3CustomerType);
                }
            }
            detail3List.sort(new CustomComparator());
            detail3ListMap.put(date, detail3List);
        });
        allList.addAll(visitList);

        if (CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(request.getTimezone())) {
            // IV、会议打卡
            List<MapDetailListVo> meetingList = new ArrayList<>();
            // 1、获取所有需参与的会议（已召开&需参与）（列表用）
            // 2、筛选出所有日周月季会议（已召开&需参与）（汇总用）
            // 3、获取会议日程表中的日会（需召开）
            // 4、获取请求日期范围中所有日期列表
            List<MeetingInfoVO> meetingInfoList = meetingInfoMapper.selectMeetingInfoListForMap(request);
            if (!CollectionUtils.isEmpty(meetingInfoList)) {
                List<String> matchList = Arrays.asList("日", "周", "月", "季");
                // 获取当前目标人员在登录人的下级组织
                ChildRequest childRequest = new ChildRequest();
                childRequest.setPerson(request.getPerson());
                childRequest.setEmployeeInfoId(request.getEmployeeInfoId());
                List<ChildVo> childList = employeeInfoService.queryChildListByParent(childRequest);
                List<String> childOrganizationIdList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(childList)) {
                    childOrganizationIdList = childList.stream().map(ChildVo::getOrganizationId).collect(Collectors.toList());
                }

                for (MeetingInfoVO vo : meetingInfoList) {
                    MapDetailListVo map = new MapDetailListVo();
                    map.setId(Long.valueOf(vo.getInfoId()));
                    // 本人可跳转，非本人按以下逻辑判断
                    // 业务组     不同 相同 相同
                    // 是否为下级   -  否   是
                    // 能否跳转   不能 不能  能
                    map.setMeetingCanLink(vo.getEmployeeId().equals(request.getPerson()) || (vo.getBusinessGroup().equals(request.getBusinessGroup()) && childOrganizationIdList.contains(vo.getOrganizationId())));
                    map.setMeetingBusinessGroup(vo.getBusinessGroup());
                    map.setMeetingBusinessGroupName(businessGroupMap.get(vo.getBusinessGroup()));
                    map.setMeetingOrganizationId(vo.getOrganizationId());
                    map.setMeetingOrganizationName(vo.getOrganizationName());
                    map.setAttendanceType(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_4);
                    map.setAttendanceChildType(MapAttendanceTypeEnum.findMapAttendanceChildType(vo.getMode()));
                    map.setAttendanceName(vo.getSubclass());
                    map.setMeetingCategory(vo.getCategory());
                    map.setMeetingMode(vo.getMode());
                    map.setMeetingActualTimePeriod(Objects.nonNull(vo.getActualStartTime()) ? LocalDateTimeUtils.formatTime(vo.getActualStartTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss) + (Objects.nonNull(vo.getActualEndTime()) ? "~" + LocalDateTimeUtils.formatTime(vo.getActualEndTime(), "HH:mm:ss") : "~") : "");
                    map.setMeetingActualIntervalMinutes(Objects.nonNull(vo.getActualStartTime()) && Objects.nonNull(vo.getActualEndTime()) ? vo.getActualStartTime().until(vo.getActualEndTime(), ChronoUnit.MINUTES) : 0);
                    map.setMeetingTimePeriod(Objects.nonNull(vo.getStartTime()) ? LocalDateTimeUtils.formatTime(vo.getStartTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss) + (Objects.nonNull(vo.getEndTime()) ? "~" + LocalDateTimeUtils.formatTime(vo.getEndTime(), "HH:mm:ss") : "~") : "");
                    map.setMeetingIntervalMinutes(Objects.nonNull(vo.getStartTime()) && Objects.nonNull(vo.getEndTime()) ? vo.getStartTime().until(vo.getEndTime(), ChronoUnit.MINUTES) : 0);
                    map.setMeetingParticipationRate(vo.getParticipationRate());
                    map.setImage(vo.getCheckPic());
                    map.setAttendanceTime(vo.getCheckTime());
                    map.setAttendanceTimeSort(Objects.nonNull(vo.getCheckTime()) ? vo.getCheckTime() : (Objects.nonNull(vo.getActualStartTime()) ? vo.getActualStartTime() : vo.getStartTime()));
                    map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                    map.setTime(map.getAttendanceTimeSort().toLocalTime());
                    map.setMeetingCheckStatusName(vo.getCheckStatus() == 1 ? "已签到" : "未签到");
                    map.setAddress(vo.getAddress());
                    map.setLongitude(vo.getLongitude());
                    map.setLatitude(vo.getLatitude());
                    meetingList.add(map);

                    // 汇总数据
                    if (matchList.stream().anyMatch(s -> vo.getCategory().contains(s))) {
                        MapDetail4Vo detail4 = detail4Map.get(map.getAttendanceDate());
                        if (Objects.isNull(detail4)) {
                            detail4 = new MapDetail4Vo();
                        }

                        MapDetail4MeetingVo meetingVo = new MapDetail4MeetingVo();
                        meetingVo.setId(Long.valueOf(vo.getInfoId()));
                        meetingVo.setCanLink(map.getMeetingCanLink());
                        meetingVo.setBusinessGroup(vo.getBusinessGroup());
                        meetingVo.setBusinessGroupName(businessGroupMap.get(vo.getBusinessGroup()));
                        meetingVo.setOrganizationId(vo.getOrganizationId());
                        meetingVo.setOrganizationName(vo.getOrganizationName());
                        meetingVo.setCategory(vo.getCategory());
                        meetingVo.setSubclass(vo.getSubclass());
                        meetingVo.setCheckStatusName(map.getMeetingCheckStatusName());
                        meetingVo.setStartTime(LocalDateTimeUtils.formatTime(vo.getStartTime(), "HH:mm"));
                        meetingVo.setEndTime(LocalDateTimeUtils.formatTime(vo.getEndTime(), "HH:mm"));
                        meetingVo.setIntervalMinutes(map.getMeetingIntervalMinutes());
                        meetingVo.setActualStartTime(Objects.nonNull(vo.getActualStartTime()) ? LocalDateTimeUtils.formatTime(vo.getActualStartTime(), "HH:mm") : "");
                        meetingVo.setActualEndTime(Objects.nonNull(vo.getActualEndTime()) ? LocalDateTimeUtils.formatTime(vo.getActualEndTime(), "HH:mm") : "");
                        meetingVo.setActualIntervalMinutes(map.getMeetingActualIntervalMinutes());
                        meetingVo.setParticipationRate(vo.getParticipationRate());
                        meetingVo.setImage(vo.getCheckPic());
                        meetingVo.setConveneBusinessGroup(vo.getConveneBusinessGroup());
                        meetingVo.setConveneBusinessGroupName(businessGroupMap.get(vo.getConveneBusinessGroup()));
                        meetingVo.setConveneOrganizationId(vo.getConveneOrganizationId());
                        meetingVo.setConveneOrganizationName(vo.getConveneOrganizationName());

                        if (vo.getIsConvene()) {
                            List<MapDetail4MeetingVo> conveneList = detail4.getConveneList();
                            if (CollectionUtils.isEmpty(conveneList)) {
                                conveneList = new ArrayList<>();
                            }
                            conveneList.add(meetingVo);
                            detail4.setConveneList(conveneList);

                            if (Objects.isNull(vo.getActualStartTime())) {
                                map.setMeetingNeedConveneTypeName("未召开");
                            } else {
                                map.setMeetingNeedConveneTypeName("已召开");
                            }
                        } else {
                            List<MapDetail4MeetingVo> participateAllList = detail4.getParticipateAllList();
                            if (CollectionUtils.isEmpty(participateAllList)) {
                                participateAllList = new ArrayList<>();
                            }
                            participateAllList.add(meetingVo);
                            detail4.setParticipateAllList(participateAllList);

                            List<MapDetail4NeedParticipateVo> needParticipateAllList = detail4.getNeedParticipateAllList();
                            if (CollectionUtils.isEmpty(needParticipateAllList)) {
                                needParticipateAllList = new ArrayList<>();
                            }
                            MapDetail4NeedParticipateVo needParticipate = new MapDetail4NeedParticipateVo();
                            needParticipate.setBusinessGroup(vo.getConveneBusinessGroup());
                            needParticipate.setBusinessGroupName(businessGroupMap.get(vo.getConveneBusinessGroup()));
                            needParticipate.setOrganizationId(vo.getConveneOrganizationId());
                            needParticipate.setOrganizationName(vo.getConveneOrganizationName());
                            needParticipateAllList.add(needParticipate);
                            detail4.setNeedParticipateAllList(needParticipateAllList.stream().distinct().collect(Collectors.toList()));
                        }
                        detail4Map.put(map.getAttendanceDate(), detail4);
                    }
                }
            }
            List<MeetingInfoVO> meetingInfoListSame = meetingInfoMapper.selectMeetingInfoListForMapSame(request);
            if (!CollectionUtils.isEmpty(meetingInfoListSame)) {
                for (MeetingInfoVO vo : meetingInfoListSame) {
                    MapDetailListVo map = new MapDetailListVo();
                    map.setEmployeeInfoId(vo.getEmployeeInfoId());
                    map.setAttendanceTimeSort(Objects.nonNull(vo.getCheckTime()) ? vo.getCheckTime() : (Objects.nonNull(vo.getActualStartTime()) ? vo.getActualStartTime() : vo.getStartTime()));
                    map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                    map.setLongitude(vo.getLongitude());
                    map.setLatitude(vo.getLatitude());
                    allListSame.add(map);
                }
            }

            // 获取会议日程（需召开）
            List<MeetingInfoVO> meetingScheduleList = meetingInfoMapper.selectMeetingScheduleForMap(request);
            Map<Integer, List<MeetingInfoVO>> meetingScheduleTypeMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(meetingScheduleList)) {
                meetingScheduleTypeMap = meetingScheduleList.stream().collect(Collectors.groupingBy(MeetingInfoVO::getType));
            }

            // 获取日会（需召开）（日期#组织）
            Map<String, List<MeetingInfoVO>> dayMeetingScheduleMap = new HashMap<>();
            List<MeetingInfoVO> dayMeetingScheduleList = meetingScheduleTypeMap.get(1);
            if (CollectionUtil.isNotEmpty(dayMeetingScheduleList)) {
                dayMeetingScheduleMap = dayMeetingScheduleList.stream().collect(Collectors.groupingBy(x -> x.getScheduleStartDate() + "#" + x.getConveneOrganizationId()));
            }
            Map<String, List<MeetingInfoVO>> finalDayMeetingScheduleMap = dayMeetingScheduleMap;

            // 获取周会（需召开）（周一日期#组织）
            Map<String, List<MeetingInfoVO>> weekMeetingScheduleMap = new HashMap<>();
            List<MeetingInfoVO> weekMeetingScheduleList = meetingScheduleTypeMap.get(2);
            if (CollectionUtil.isNotEmpty(weekMeetingScheduleList)) {
                weekMeetingScheduleMap = weekMeetingScheduleList.stream().collect(Collectors.groupingBy(x -> x.getScheduleStartDate() + "#" + x.getConveneOrganizationId()));
            }
            Map<String, List<MeetingInfoVO>> finalWeekMeetingScheduleMap = weekMeetingScheduleMap;

            // 获取目标人员的上级组织
            Set<String> parentOrganizationIds = new HashSet<>();
            Map<String, ParentVo> parentMap = new HashMap<>();
            List<ParentVo> parentVoList = sfaEmployeeInfoMapper.queryParentListByEmployeeInfoIds2(ParentRequest.builder().isExclude(1).status(1).employeeInfoIds(Collections.singletonList(request.getEmployeeInfoId())).build());
            if (!CollectionUtils.isEmpty(parentVoList)) {
                // 目标人员的每个组织的直接上级（各组各兼岗）
                Map<String, List<ParentVo>> parentVoMap = parentVoList.stream().collect(Collectors.groupingBy(ParentVo::getOrganizationId));
                parentVoMap.forEach((k, v) -> {
                    v.sort(Comparator.comparing(ParentVo::getLevel));
                    if (parentOrganizationIds.add(v.get(0).getParentOrganizationId())) {
                        parentMap.put(v.get(0).getParentOrganizationId(), v.get(0));
                    }
                });
            }
            Map<String, CeoBusinessOrganizationEntity> finalEmployeeOrganizationMap = employeeOrganizationMap;
            dateList.forEach(date -> {
                MapDetail4Vo detail4 = detail4Map.get(date);
                if (Objects.isNull(detail4)) {
                    detail4 = new MapDetail4Vo();
                }

                // 召开

                // 会议状态统一设定：      无需召开 未召开 已召开
                // sfa_meeting_schedule  无       有    有
                // sfa_meeting_info      -        无    有
                List<MapDetail4NeedConveneVo> needConveneList = detail4.getNeedConveneList();
                if (CollectionUtils.isEmpty(needConveneList)) {
                    needConveneList = new ArrayList<>();
                }

                for (String organizationId : employeeOrganizationIds) {
                    if (!(OrganizationTypeEnum.DEPARTMENT.getOrganizationType().equals(finalEmployeeOrganizationMap.get(organizationId).getOrganizationType())
                            || OrganizationTypeEnum.BRANCH.getOrganizationType().equals(finalEmployeeOrganizationMap.get(organizationId).getOrganizationType()))
                    ) {
                        MapDetail4NeedConveneVo needConvene = new MapDetail4NeedConveneVo();
                        if (date.getDayOfWeek().equals(DayOfWeek.SATURDAY) || date.getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
                            LocalDate mondayOfThisWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                            needConvene.setCategory("周会");
                            setNeedConvene(organizationId, finalWeekMeetingScheduleMap.get(mondayOfThisWeek + "#" + organizationId), finalEmployeeOrganizationMap, needConvene, businessGroupMap, needConveneList);
                        } else {
                            needConvene.setCategory("日会");
                            setNeedConvene(organizationId, finalDayMeetingScheduleMap.get(date + "#" + organizationId), finalEmployeeOrganizationMap, needConvene, businessGroupMap, needConveneList);
                        }
                    }
                }

                detail4.setNeedConveneList(needConveneList);

                // 参与（直接上级召开的）

                // 参与（所有被邀请的）,按邀请人组织 分组
                Map<String, List<MapDetail4MeetingVo>> participateAllMap;
                List<MapDetail4MeetingVo> participateAllList = detail4.getParticipateAllList();
                if (!CollectionUtils.isEmpty(participateAllList)) {
                    participateAllMap = participateAllList.stream().collect(Collectors.groupingBy(MapDetail4MeetingVo::getConveneOrganizationId));
                } else {
                    participateAllMap = new HashMap<>();
                }

                for (String parentOrganizationId : parentOrganizationIds) {
                    if (participateAllMap.containsKey(parentOrganizationId)) {
                        List<MapDetail4MeetingVo> participateList = detail4.getParticipateList();
                        if (CollectionUtils.isEmpty(participateList)) {
                            participateList = new ArrayList<>();
                        }
                        participateList.addAll(participateAllMap.get(parentOrganizationId));
                        detail4.setParticipateList(participateList);
                    }

                    List<MapDetail4NeedParticipateVo> needParticipateList = detail4.getNeedParticipateList();
                    if (CollectionUtils.isEmpty(needParticipateList)) {
                        needParticipateList = new ArrayList<>();
                    }
                    MapDetail4NeedParticipateVo needParticipate = new MapDetail4NeedParticipateVo();
                    needParticipate.setBusinessGroup(parentMap.get(parentOrganizationId).getParentBusinessGroup());
                    needParticipate.setBusinessGroupName(businessGroupMap.get(parentMap.get(parentOrganizationId).getParentBusinessGroup()));
                    needParticipate.setOrganizationId(parentMap.get(parentOrganizationId).getParentOrganizationId());
                    needParticipate.setOrganizationName(parentMap.get(parentOrganizationId).getParentOrganizationName());
                    needParticipate.setIsHaving(participateAllMap.containsKey(parentOrganizationId));
                    needParticipate.setParticipateList(participateAllMap.get(parentOrganizationId));
                    needParticipateList.add(needParticipate);
                    detail4.setNeedParticipateList(needParticipateList.stream().distinct().collect(Collectors.toList()));
                }

                detail4Map.put(date, detail4);
            });

            allList.addAll(meetingList);
        }

        Map<Integer, String> attendanceTypeMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE);
        allList.forEach(vo -> vo.setIndexTag(allList.indexOf(vo)));
        // 设定每日下班标记 & 计算 当日距上班点最远距离
        log.info("设定每日下班标记 & 计算 当日距上班点最远距离");
        Map<LocalDate, List<MapDetailListVo>> all1234Map = allList.stream().collect(Collectors.groupingBy(MapDetailListVo::getAttendanceDate));
        all1234Map.forEach((date, list) -> {
            if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
                // 每日的全量打卡的有效定位的最后一条设为下班打卡
                list.sort(Comparator.comparing(MapDetailListVo::getAttendanceTimeSort).reversed());
                for (MapDetailListVo vo : list) {
                    if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                        if (nowTime.toLocalDate().equals(vo.getAttendanceDate())) {
                            if (vo.getTime().isAfter(LocalTime.of(17, 30))) {
                                vo.setIsOffWork(Boolean.TRUE);
                            }
                        } else {
                            vo.setIsOffWork(Boolean.TRUE);
                        }
                        MapDetail1Vo detail1 = detail1Map.get(date);
                        if (Objects.isNull(detail1)) {
                            detail1 = new MapDetail1Vo();
                        }
                        detail1.setAttendanceNameOffWork(ComonLanguageEnum.OFF_WORK.getDesc());
                        detail1.setAttendanceTimeOffWork(vo.getAttendanceTime());
                        detail1.setTimeOffWork(vo.getTime());
                        detail1Map.put(date, detail1);
                        break;
                    }
                }

                // 计算 当日距上班点最远距离
                list.sort(Comparator.comparing(MapDetailListVo::getAttendanceTimeSort));
                MapDetail1Vo detail1 = detail1Map.get(date);
                if (Objects.nonNull(detail1) && StringUtils.isNotBlank(detail1.getLatitude()) && StringUtils.isNotBlank(detail1.getLongitude())) {
                    for (MapDetailListVo vo : list) {
                        if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                            if (Objects.isNull(vo.getIsFirstOnWork()) || !vo.getIsFirstOnWork()) {
                                double distance = calculateDistance(Double.parseDouble(detail1.getLatitude()), Double.parseDouble(detail1.getLongitude()), Double.parseDouble(vo.getLatitude()), Double.parseDouble(vo.getLongitude()));
                                if (distance > detail1.getFarthestDistance()) {
                                    detail1.setFarthestDistance(distance);
                                    detail1.setFarthestDistanceName(attendanceTypeMap.get(vo.getAttendanceType()));
                                    detail1.setIndexTag(vo.getIndexTag());
                                }
                            }
                        }
                    }
                    detail1Map.put(date, detail1);
                }
            }
        });
        // 标记每日最远距离
        log.info("标记每日最远距离");
        detail1Map.forEach((date, detail1) -> {
            if (Objects.nonNull(detail1.getIndexTag())) {
                MapDetailListVo vo = allList.get(detail1.getIndexTag());
                vo.setIsFarthestDistance(Boolean.TRUE);
                vo.setFarthestDistance(detail1.getFarthestDistance());
                allList.stream().filter(v -> v.getIsFirstOnWork() != null && v.getIsFirstOnWork() && Objects.nonNull(v.getAttendanceDate()) && v.getAttendanceDate().isEqual(date))
                        .forEach(v -> {
                            v.setFarthestDistanceName(vo.getAttendanceName());
                            v.setFarthestDistance(vo.getFarthestDistance());
                        });
            }
        });

        // V、动态定位
        List<MapDetailListVo> realtimePositioningList = new ArrayList<>();
        if (isZB && CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(request.getTimezone())) {
            List<SfaMapRealtimePositioning> sfaMapRealtimePositioningList = sfaMapRealtimePositioningMapper.selectList(new LambdaQueryWrapper<SfaMapRealtimePositioning>()
                    .eq(SfaMapRealtimePositioning::getDeleteFlag, 0)
                    .eq(SfaMapRealtimePositioning::getEmployeeInfoId, request.getEmployeeInfoId())
                    .le(SfaMapRealtimePositioning::getPositioningTime, DateTimeUtility.changeTimezone(request.getAttendanceEndDateTime(), request.getTimezone(), CommonConstant.TIMEZONE_ASIA_SHANGHAI))
                    .ge(SfaMapRealtimePositioning::getPositioningTime, DateTimeUtility.changeTimezone(request.getAttendanceStartDateTime(), request.getTimezone(), CommonConstant.TIMEZONE_ASIA_SHANGHAI))
                    .apply("DATE_FORMAT(positioning_time,'%H:%i:%s') >= {0}", LocalTime.parse(settingService.getValue("map_realtime_positioning_start_time"), DateTimeFormatter.ofPattern("HH:mm:ss")))
                    .apply("DATE_FORMAT(positioning_time,'%H:%i:%s') <= {0}", LocalTime.parse(settingService.getValue("map_realtime_positioning_end_time"), DateTimeFormatter.ofPattern("HH:mm:ss")))
                    .orderByDesc(SfaMapRealtimePositioning::getPositioningTime)
            );
            if (!CollectionUtils.isEmpty(sfaMapRealtimePositioningList)) {
                sfaMapRealtimePositioningList.forEach(vo -> {
                    vo.setPositioningTime(DateTimeUtility.changeTimezone(vo.getPositioningTime(), null, request.getTimezone()));
                    vo.setPositioningDate(vo.getPositioningTime().toLocalDate());
                    MapDetailListVo map = new MapDetailListVo();
                    map.setAttendanceType(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_5);
                    map.setAttendanceName(ComonLanguageEnum.POSITIONING.getDesc());
                    map.setAttendanceTime(vo.getPositioningTime());
                    map.setAttendanceTimeSort(vo.getPositioningTime());
                    map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                    map.setTime(map.getAttendanceTimeSort().toLocalTime());
                    StringBuilder address = new StringBuilder();
                    Optional.ofNullable(vo.getProvince()).ifPresent(address::append);
                    Optional.ofNullable(vo.getCity()).ifPresent(address::append);
                    Optional.ofNullable(vo.getDistrict()).ifPresent(address::append);
                    Optional.ofNullable(vo.getAddress()).ifPresent(address::append);
                    map.setAddress(address.toString());
                    map.setLongitude(vo.getLongitude());
                    map.setLatitude(vo.getLatitude());
                    realtimePositioningList.add(map);
                    MapDetail5Vo detail5 = detail5Map.get(map.getAttendanceDate());
                    if (Objects.isNull(detail5)) {
                        map.setIsNewestRealtimePositioning(Boolean.TRUE);
                        detail5 = new MapDetail5Vo();
                        detail5.setPositioningTime(map.getAttendanceTime());
                        detail5.setAddress(map.getAddress());
                        detail5.setLongitude(map.getLongitude());
                        detail5.setLatitude(map.getLatitude());
                        detail5Map.put(map.getAttendanceDate(), detail5);
                    }
                });
            }
            List<SfaMapRealtimePositioning> sfaMapRealtimePositioningListSame = sfaMapRealtimePositioningMapper.selectList(new LambdaQueryWrapper<SfaMapRealtimePositioning>()
                    .eq(SfaMapRealtimePositioning::getDeleteFlag, 0)
                    .in(SfaMapRealtimePositioning::getEmployeeInfoId, request.getSameEmployeeInfoIdList())
                    .le(SfaMapRealtimePositioning::getPositioningTime, DateTimeUtility.changeTimezone(request.getAttendanceEndDateTime(), request.getTimezone(), CommonConstant.TIMEZONE_ASIA_SHANGHAI))
                    .ge(SfaMapRealtimePositioning::getPositioningTime, DateTimeUtility.changeTimezone(request.getAttendanceStartDateTime(), request.getTimezone(), CommonConstant.TIMEZONE_ASIA_SHANGHAI))
                    .apply("DATE_FORMAT(positioning_time,'%H:%i:%s') >= {0}", LocalTime.parse(settingService.getValue("map_realtime_positioning_start_time"), DateTimeFormatter.ofPattern("HH:mm:ss")))
                    .apply("DATE_FORMAT(positioning_time,'%H:%i:%s') <= {0}", LocalTime.parse(settingService.getValue("map_realtime_positioning_end_time"), DateTimeFormatter.ofPattern("HH:mm:ss")))
                    .orderByDesc(SfaMapRealtimePositioning::getPositioningTime)
            );
            if (!CollectionUtils.isEmpty(sfaMapRealtimePositioningListSame)) {
                sfaMapRealtimePositioningListSame.forEach(vo -> {
                    vo.setPositioningTime(DateTimeUtility.changeTimezone(vo.getPositioningTime(), null, request.getTimezone()));
                    vo.setPositioningDate(vo.getPositioningTime().toLocalDate());
                    MapDetailListVo map = new MapDetailListVo();
                    map.setEmployeeInfoId(vo.getEmployeeInfoId());
                    map.setAttendanceTimeSort(vo.getPositioningTime());
                    map.setAttendanceDate(map.getAttendanceTimeSort().toLocalDate());
                    map.setLongitude(vo.getLongitude());
                    map.setLatitude(vo.getLatitude());
                    allListSame.add(map);
                });
            }

        }
        allList.addAll(realtimePositioningList);

        // 计算 当日总距离(同组同岗)
        log.info("计算 当日总距离(同组同岗)");
        Map<String, List<MapDetailListVo>> allMapSame = allListSame.stream()
                .filter(e -> e.getEmployeeInfoId() != null && e.getAttendanceDate() != null)
                .collect(Collectors.groupingBy(x -> x.getEmployeeInfoId() + "#" + x.getAttendanceDate()));
        allMapSame.forEach((employeeInfoIdDate, list) -> {
            if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
                String date = employeeInfoIdDate.split("#")[1];
                Double farthestDistance = farthestDistanceMap.get(date);
                Double totalDistance = totalDistanceMap.get(date);
                if (Objects.isNull(farthestDistance)) {
                    farthestDistance = (double) 0;
                }
                if (Objects.isNull(totalDistance)) {
                    totalDistance = (double) 0;
                }

                double employeeInfoIdDateTotalDistance = 0;
                list.sort(Comparator.comparing(MapDetailListVo::getAttendanceTimeSort));
                for (MapDetailListVo vo : list) {
                    int index = list.indexOf(vo);
                    if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                        if (index > 0) {
                            MapDetailListVo preVo = findPrevious(list, index);
                            if (Objects.nonNull(preVo)) {
                                double distance = calculateDistance(Double.parseDouble(preVo.getLatitude()), Double.parseDouble(preVo.getLongitude()), Double.parseDouble(vo.getLatitude()), Double.parseDouble(vo.getLongitude()));
                                employeeInfoIdDateTotalDistance = employeeInfoIdDateTotalDistance + distance;
                            }
                        }
                    }
                }
                if (employeeInfoIdDateTotalDistance > farthestDistance) {
                    farthestDistance = employeeInfoIdDateTotalDistance;
                }
                farthestDistanceMap.put(date, farthestDistance);
                totalDistanceMap.put(date, totalDistance + employeeInfoIdDateTotalDistance);
            }
        });

        // 计算 当日总距离
        log.info("计算 当日总距离");
        Map<LocalDate, List<MapDetailListVo>> allMap = allList.stream().collect(Collectors.groupingBy(MapDetailListVo::getAttendanceDate));
        allMap.forEach((date, list) -> {
            MapDetail1Vo detail1 = detail1Map.get(date);
            if (Objects.isNull(detail1)) {
                detail1 = new MapDetail1Vo();
            }
            if (!CollectionUtils.isEmpty(list) && list.size() > 1) {
                list.sort(Comparator.comparing(MapDetailListVo::getAttendanceTimeSort));
                for (MapDetailListVo vo : list) {
                    int index = list.indexOf(vo);
                    if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                        if (index > 0) {
                            MapDetailListVo preVo = findPrevious(list, index);
                            if (Objects.nonNull(preVo)) {
                                double distance = calculateDistance(Double.parseDouble(preVo.getLatitude()), Double.parseDouble(preVo.getLongitude()), Double.parseDouble(vo.getLatitude()), Double.parseDouble(vo.getLongitude()));
                                detail1.setTotalDistance(detail1.getTotalDistance() + distance);
                            }
                        }
                    }
                }
            }
            detail1.setTotalDistanceMax(Optional.ofNullable(farthestDistanceMap.get(date.toString())).orElse((double) 0));
            Double totalDistance = totalDistanceMap.get(date.toString());
            if (totalDistance != null && !CollectionUtils.isEmpty(request.getSameEmployeeInfoIdList())) {
                int employeeCount = request.getSameEmployeeInfoIdList().size();
                detail1.setTotalDistanceAvg(totalDistance / employeeCount);
            }
            detail1Map.put(date, detail1);
        });

        // 获取配置-每个点nKM内
        double mapDistance = Double.parseDouble(settingService.getValue("map_distance")) * 1000;

        // 过滤出筛选的打卡类型
        allFilterList = allList.stream()
                .filter(vo -> finalAttendanceTypeList.contains(vo.getAttendanceType())
                        && !request.getAttendanceTypeExclude().contains(vo.getAttendanceType())
                        && !request.getAttendanceTypeExclude().contains(vo.getAttendanceChildType()))
                .collect(Collectors.toList());
        List<MapDetailListVo> allFilterLongitudeLatitudeList = allFilterList.stream().filter(vo -> StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())).collect(Collectors.toList());
        // 赋序号，计算筛选类型点位之间的距离
        log.info("赋序号，计算筛选类型点位之间的距离");
        allFilterList.sort(Comparator.comparing(MapDetailListVo::getAttendanceTimeSort));
        List<MapDetailListVo> finalAllFilterList = allFilterList;
        allFilterList.forEach(vo -> {
            int index = finalAllFilterList.indexOf(vo);
            vo.setSerialNumber(index + 1);
            if (StringUtils.isNotBlank(vo.getLatitude()) && StringUtils.isNotBlank(vo.getLongitude())) {
                double latitude = Double.parseDouble(vo.getLatitude());
                double longitude = Double.parseDouble(vo.getLongitude());
                if (index > 0) {
                    MapDetailListVo preVo = findPrevious(finalAllFilterList, index);
                    if (Objects.nonNull(preVo)) {
                        vo.setPrePointDistance(calculateDistance(Double.parseDouble(preVo.getLatitude()), Double.parseDouble(preVo.getLongitude()), latitude, longitude));
                    }
                }
                // 每个点nKM内有多少个点
                for (MapDetailListVo llVo : allFilterLongitudeLatitudeList) {
                    if (calculateDistance(Double.parseDouble(llVo.getLatitude()), Double.parseDouble(llVo.getLongitude()), latitude, longitude) <= mapDistance) {
                        vo.setTotalPoint(Optional.ofNullable(vo.getTotalPoint()).orElse(0) + 1);
                    }
                }
            }
        });

        Map<LocalDate, List<MapDetailListVo>> allFilterMap = allFilterList.stream().collect(Collectors.groupingBy(MapDetailListVo::getAttendanceDate));

        //获取开始时间和结束时间内的考勤日历
        log.info("获取开始时间和结束时间内的考勤日历");
        List<MemberCalendarVO> memberCalendarList = attendanceService.getMemberCalendar(request.getEmployeeInfoId(), request.getAttendanceStartDate().toString(), request.getAttendanceEndDate().toString());
        Map<String, MemberCalendarVO> memberCalendarMap;
        if (CollectionUtil.isNotEmpty(memberCalendarList)) {
            memberCalendarMap = memberCalendarList.stream().collect(Collectors.toMap(MemberCalendarVO::getCalendarDate, Function.identity(), (x1, x2) -> x1));
        } else {
            memberCalendarMap = MapUtil.newHashMap();
        }

        String organizationNames = employeeOrganizationList.stream().map(org -> businessGroupMap.get(org.getBusinessGroup()) + "-" + org.getOrganizationName()).collect(Collectors.joining(","));
        String positionTypeNames = employeePositionTypeList.stream().map(CeoBusinessPositionType::getPositionName).collect(Collectors.joining(","));
        EmployeeInfoVO employeeInfoVo = sfaEmployeeInfoMapper.queryEmployeeInfo(sfaEmployeeInfoEntity.getId());
        dateList.forEach(date -> {
            Boolean isWorkDay = Boolean.TRUE;
            Boolean isLeave = Boolean.FALSE;
            MapListVo mapListVo = new MapListVo();
            mapListVo.setDistrictCodeList(districtCodeList);
            mapListVo.setAttendanceDate(date);
            mapListVo.setEmployeeId(sfaEmployeeInfoEntity.getEmployeeId());
            mapListVo.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
            mapListVo.setEmployeeName(sfaEmployeeInfoEntity.getEmployeeName());
            mapListVo.setOrganizationNames(organizationNames);
            mapListVo.setPositionTypeName(positionTypeNames);
            if (Objects.nonNull(employeeInfoVo)) {
                mapListVo.setOnboardTime(employeeInfoVo.getOnboardTime());
                mapListVo.setOnboardDays(employeeInfoVo.getOnboardDays());
                mapListVo.setAvatar(employeeInfoVo.getAvatar());
                mapListVo.setAddress(employeeInfoVo.getAddress());
                mapListVo.setLongitude(employeeInfoVo.getLongitude());
                mapListVo.setLatitude(employeeInfoVo.getLatitude());
                mapListVo.setProbationStatus(sfaEmployeeInfoEntity.getEmployeeStatus() == 1);
            }

            //考勤日历
            MemberCalendarVO memberCalendarVO = memberCalendarMap.get(date.toString());
            if (Objects.nonNull(memberCalendarVO)) {
                isWorkDay = Objects.nonNull(memberCalendarVO.getIsWorkDay()) && memberCalendarVO.getIsWorkDay() == 0;
                mapListVo.setIsWorkDay(memberCalendarVO.getIsWorkDay());
                mapListVo.setDayOfWeekStr(LocalDateUtils.getWeekOfDate(date, RequestUtils.getRegion()));
            }

            //请假
            SfaLeave sfaLeave = sfaLeaveMapper.getLeaveInfoAtThatDay(sfaEmployeeInfoEntity.getId(), date.toString());
            if (Objects.nonNull(sfaLeave)) {
                isLeave = Boolean.TRUE;
                mapListVo.setBusinessNum(sfaLeave.getBusinessNum());
                mapListVo.setLeaveReason(sfaLeave.getLeaveReason());
            }

            //出差
            Integer tripId = businessTripMapper.selectTrip(sfaEmployeeInfoEntity.getId(), date);
            if (Objects.nonNull(tripId)) {
                mapListVo.setIsTrip(Boolean.TRUE);
            }

            MapDetail4Vo detail4Vo = detail4Map.get(date);
            if (isWorkDay && !isLeave) {
                mapListVo.setAttendanceIsNormal(Objects.nonNull(attendanceIsNormalMap.get(date)) ? attendanceIsNormalMap.get(date) : Boolean.FALSE);
                mapListVo.setCompleteIsNormal(Objects.nonNull(completeIsNormalMap.get(date)) ? completeIsNormalMap.get(date) : Boolean.FALSE);
                mapListVo.setVisitIsNormal(Objects.isNull(visitIsNormalMap.get(date)) ? Boolean.FALSE : visitIsNormalMap.get(date));

                if (Objects.nonNull(request.getPositionTypeId()) && request.getPositionTypeId() == 10) {
                    if (Objects.nonNull(detail4Vo) && !CollectionUtils.isEmpty(detail4Vo.getParticipateAllList())) {
                        mapListVo.setMeetingIsNormal(detail4Vo.getParticipateAllList().stream().anyMatch(vo -> Objects.nonNull(vo.getImage())));
                    } else {
                        mapListVo.setMeetingIsNormal(Boolean.FALSE);
                    }
                } else if (Objects.nonNull(request.getPositionTypeId()) && request.getPositionTypeId() == 3) {
                    if (Objects.nonNull(detail4Vo) && !CollectionUtils.isEmpty(detail4Vo.getParticipateAllList())) {
                        mapListVo.setMeetingIsNormal(detail4Vo.getParticipateAllList().stream().anyMatch(vo -> Objects.nonNull(vo.getImage())));
                    } else {
                        mapListVo.setMeetingIsNormal(null);
                    }
                } else {
                    if (Objects.nonNull(detail4Vo) && !CollectionUtils.isEmpty(detail4Vo.getNeedConveneList())) {
                        mapListVo.setMeetingIsNormal(detail4Vo.getNeedConveneList().stream().noneMatch(vo -> "未召开".equals(vo.getNeedConveneTypeName())));
                    } else {
                        mapListVo.setMeetingIsNormal(Boolean.FALSE);
                    }
                }
            } else {
                mapListVo.setAttendanceIsNormal(Objects.nonNull(attendanceIsNormalMap.get(date)) ? Boolean.TRUE : null);
                mapListVo.setCompleteIsNormal(Objects.nonNull(completeIsNormalMap.get(date)) ? Boolean.TRUE : null);
                mapListVo.setVisitIsNormal(Objects.nonNull(visitIsNormalMap.get(date)) ? Boolean.TRUE : null);

                if (Objects.nonNull(request.getPositionTypeId()) && (request.getPositionTypeId() == 10 || request.getPositionTypeId() == 3)) {
                    if (Objects.nonNull(detail4Vo) && !CollectionUtils.isEmpty(detail4Vo.getParticipateAllList())) {
                        mapListVo.setMeetingIsNormal(detail4Vo.getParticipateAllList().stream().anyMatch(vo -> Objects.nonNull(vo.getImage())) ? Boolean.TRUE : null);
                    } else {
                        mapListVo.setMeetingIsNormal(null);
                    }
                } else {
                    if (Objects.nonNull(detail4Vo) && !CollectionUtils.isEmpty(detail4Vo.getNeedConveneList())) {
                        mapListVo.setMeetingIsNormal(detail4Vo.getNeedConveneList().stream().anyMatch(vo -> "已召开".equals(vo.getNeedConveneTypeName())) ? Boolean.TRUE : null);
                    } else {
                        mapListVo.setMeetingIsNormal(null);
                    }
                }


            }
//            if (isBD) {
//                mapListVo.setCompleteIsNormal(null);
//                mapListVo.setMeetingIsNormal(null);
//            }
            mapListVo.setDetail1(detail1Map.get(date));
            mapListVo.setDetail2List(detail2ListMap.get(date));
            mapListVo.setDetail3List(detail3ListMap.get(date));
            mapListVo.setDetail4(detail4Map.get(date));
            mapListVo.setDetail5(detail5Map.get(date));

            List<MapDetailListVo> filterList = allFilterMap.get(date);
            if (!CollectionUtils.isEmpty(filterList)) {
                mapListVo.setDetailList(filterList);
            }

            returnList.add(mapListVo);
        });

        returnList.sort(Comparator.comparing(MapListVo::getAttendanceDate));
        return returnList;
    }

    @Override
    public List<MapListVo> attendanceListForPeriod(PeriodMapListRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("attendanceListForPeriod start request:{}", request);
        // 日纬度-- 使用原始方法

        if (request.getPeriod() == null || PERIOD_DAY == request.getPeriod()) {
            return mapService.attendanceList(request);
        } else {
            List<MapListVo> result = new ArrayList<>();
            List<LocalDate> localDateList = new ArrayList<>();
            int chunkSize = CHUNK_SIZE_DAY;
            if (PERIOD_MONTH == request.getPeriod()) {
                // 月纬度
                // 进行匹配
                if (!Pattern.compile(PERIOD_MONTH_REGEX).matcher(request.getYearMonth()).matches()) {
                    throw new ApplicationException(BizExceptionLanguageEnum.MONTH_MUST_NOT_NULL.getTextMsg());
                }
                chunkSize = CHUNK_SIZE_MONTH;
                localDateList.addAll(LocalDateUtils.getDaysInMonth(request.getYearMonth()));
            } else if (PERIOD_QUARTER == request.getPeriod()) {
                // 季纬度
                // 编译正则表达式
                if (!Pattern.compile(PERIOD_QUARTER_REGEX).matcher(request.getYearMonth()).matches()) {
                    throw new ApplicationException(BizExceptionLanguageEnum.QUARTER_MUST_NOT_NULL.getTextMsg());
                }
                chunkSize = CHUNK_SIZE_QUARTER;
                localDateList.addAll(LocalDateUtils.getDaysInQuarter(request.getYearMonth()));
            }
            // 根据入职时间排除时间--合伙人无入职时间
            SfaEmployeeInfoModel employeeInfoEntity = getEmployeeInfoEntity(request);
            LocalDate now = LocalDate.now();
            if (Objects.nonNull(employeeInfoEntity.getOnboardTime())) {
                localDateList.removeIf(localDate -> localDate.isBefore(employeeInfoEntity.getOnboardTime().toLocalDate()) || localDate.isAfter(now));
            }
            if (CollectionUtil.isEmpty(localDateList)) {
                throw new ApplicationException(BizExceptionLanguageEnum.NO_ATTENDANCE_RECORD.getTextMsg());
            }
            // 以时间拆分
            List<List<LocalDate>> chunks = splitList(localDateList, chunkSize);
            // 获取当前请求属性
            RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
            // 创建 CompletableFuture 列表
            List<CompletableFuture<List<MapListVo>>> futures = chunks.stream()
                    .map(chunk -> {

                        MapListRequest mapListRequest = new MapListRequest();
                        BeanUtil.copyProperties(request, mapListRequest);
                        mapListRequest.setAttendanceStartDate(chunk.get(0));
                        mapListRequest.setAttendanceEndDate(chunk.get(chunk.size() - 1));
                        return CompletableFuture.supplyAsync(() -> {
                            try {
                                RequestContextHolder.setRequestAttributes(attributes, true);
                                return mapService.attendanceList(mapListRequest);
                            } finally {
                                RequestContextHolder.resetRequestAttributes();
                            }


                        }, taskExecutor);
                    })
                    .collect(Collectors.toList());

            // 创建一个 CompletableFuture 来等待所有任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));

            // 当所有任务完成后，收集结果
            CompletableFuture<List<List<MapListVo>>> allResults = allFutures.thenApply(v ->
                    futures.stream().flatMap(future -> {
                                try {
                                    return Stream.of(future.get());
                                } catch (InterruptedException | ExecutionException e) {
                                    log.warn("任务执行失败，参数=[{}]", Jackson.toJsonString(request), e);
                                    Thread.currentThread().interrupt(); // 恢复中断状态
                                    return Stream.empty();
                                }
                            })
                            .filter(CollectionUtil::isNotEmpty)
                            .collect(Collectors.toList())
            );

            try {
                // 获取所有结果
                List<List<MapListVo>> allVoLists = allResults.get();
                allVoLists.forEach(result::addAll);
            } catch (InterruptedException | ExecutionException e) {
                log.warn("任务执行过程中出现异常，参数=[{}]", Jackson.toJsonString(request), e);
            }
            // 根据attendanceDate排序
            result.sort(Comparator.comparing(MapListVo::getAttendanceDate));
            long endTime = System.currentTimeMillis();
            log.info("attendanceListForPeriod end time:[{}]", endTime - startTime);
            return result;
        }
    }

    private List<List<LocalDate>> splitList(List<LocalDate> list, int chunkSize) {
        List<List<LocalDate>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            chunks.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return chunks;
    }

    private List<String> getDistrictCodeList(MapListRequest request, List<SfaPositionRelationEntity> sfaPositionRelationList) {
        List<String> returnList = new ArrayList<>();
        List<String> districtCodeList = new ArrayList<>();
        try {
            Map<Integer, String> mapQueryRegionTypeMap = dictCodeServiceImpl.getMapByClassCdIncludeEn(DictCodeConstants.CLASSCD_MAP_QUERY_REGION_TYPE);
            Map<Integer, List<SfaPositionRelationEntity>> positionTypeIdMap = sfaPositionRelationList.stream().collect(Collectors.groupingBy(SfaPositionRelationEntity::getPositionTypeId));
            positionTypeIdMap.forEach((positionTypeId, list) -> {
                request.setPositionTypeId(positionTypeId);
                request.setQueryRegionType(mapQueryRegionTypeMap.get(positionTypeId));
                List<String> organizationIds = list.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
                switch (positionTypeId) {
                    case 10:
                        request.setDepartmentIds(organizationIds);
                        break;
                    case 2:
                        request.setCompanyIds(organizationIds);
                        break;
                    case 11:
                        request.setCompanyIds(ceoBusinessOrganizationViewMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                                .in(CeoBusinessOrganizationViewEntity::getProvinceId, organizationIds)
                                .eq(CeoBusinessOrganizationViewEntity::getOrganizationType, "company")
                        ).stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList()));
                        break;
                    case 12:
                        request.setCompanyIds(ceoBusinessOrganizationViewMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                                .in(CeoBusinessOrganizationViewEntity::getVirtualAreaId, organizationIds)
                                .eq(CeoBusinessOrganizationViewEntity::getOrganizationType, "company")
                        ).stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList()));
                        break;
                    case 1:
                        request.setCompanyIds(ceoBusinessOrganizationViewMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>()
                                .in(CeoBusinessOrganizationViewEntity::getOrgId3, organizationIds)
                                .eq(CeoBusinessOrganizationViewEntity::getOrganizationType, "company")
                        ).stream().map(CeoBusinessOrganizationViewEntity::getOrganizationId).collect(Collectors.toList()));
                        break;
                    default:
                        break;
                }
                if (CommonConstant.REGION_INDONESIA.equals(RequestUtils.getRegion())) {
                    districtCodeList.addAll(wwOrganizationAreaMapper.selectIdDistrictCodeList(request));
                } else {
                    districtCodeList.addAll(wwOrganizationAreaMapper.selectDistrictCodeList(request));
                }

            });
            if (!CollectionUtils.isEmpty(districtCodeList)) {
                Map<String, String> specialRegionMap = dictCodeServiceImpl.getMapByClassCdString(DictCodeConstants.CLASSCD_SPEICAL_REGION);
                for (int i = 0; i < districtCodeList.size(); i++) {
                    String districtCode = districtCodeList.get(i);
                    if (specialRegionMap.containsKey(districtCode)) {
                        districtCodeList.set(i, specialRegionMap.get(districtCode));
                    }
                }
                returnList = districtCodeList.stream().distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取行政区划失败", e);
        }
        return returnList;
    }

    private SfaEmployeeInfoModel getEmployeeInfoEntity(MapListRequest request) {
        // 参数处理-获取并校验目标人员信息
        if (StringUtils.isBlank(request.getEmployeeId()) && Objects.isNull(request.getEmployeeInfoId())) {
            throw new ApplicationException(BizExceptionLanguageEnum.EMPLOYEE_ORGANIZATION_EMPLOYEEID_MUST_NOT_NULL.getTextMsg());
        }

        SfaEmployeeInfoModel sfaEmployeeInfoEntity = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(StringUtils.isNotBlank(request.getEmployeeId()), SfaEmployeeInfoModel::getEmployeeId, request.getEmployeeId())
                .eq(Objects.nonNull(request.getEmployeeInfoId()), SfaEmployeeInfoModel::getId, request.getEmployeeInfoId())
        );
        if (Objects.isNull(sfaEmployeeInfoEntity)) {
            throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_TARGET.getTextMsg());
        }
        request.setEmployeeInfoId(sfaEmployeeInfoEntity.getId());
        request.setEmployeeId(sfaEmployeeInfoEntity.getEmployeeId());
        request.setMemberKey(sfaEmployeeInfoEntity.getMemberKey());
        return sfaEmployeeInfoEntity;
    }

    private List<LocalDate> getDateList(MapListRequest request) {
        // 参数处理-处理请求的日期
        if (Objects.nonNull(request.getAttendanceDate())
                && (Objects.nonNull(request.getAttendanceStartDate()) || Objects.nonNull(request.getAttendanceEndDate()))) {
            throw new ApplicationException("指定日期与日期范围不能同时有");
        }
        if (Objects.nonNull(request.getAttendanceDate())) {
            request.setAttendanceStartDate(request.getAttendanceDate());
            request.setAttendanceEndDate(request.getAttendanceDate());
        }
        // 默认约束时间范围
        if (Objects.isNull(request.getAttendanceStartTime())) {
            request.setAttendanceStartTime(LocalTime.of(0, 0, 0));
        }
        if (Objects.isNull(request.getAttendanceEndTime())) {
            request.setAttendanceEndTime(LocalTime.of(23, 59, 59));
        }
        request.setAttendanceStartDateTime(LocalDateTime.of(request.getAttendanceStartDate(), request.getAttendanceStartTime()));
        request.setAttendanceEndDateTime(LocalDateTime.of(request.getAttendanceEndDate(), request.getAttendanceEndTime()));

        request.setAttendanceStartDateMonday(request.getAttendanceStartDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
        request.setAttendanceEndDateSunday(request.getAttendanceEndDate().with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)));

        // 获取请求日期范围中所有日期
        return Stream.iterate(request.getAttendanceStartDate(), date -> date.plusDays(1))
                .limit(ChronoUnit.DAYS.between(request.getAttendanceStartDate(), request.getAttendanceEndDate()) + 1)
                .collect(Collectors.toList());
    }

    private List<Integer> getAttendanceTypeList(MapListRequest request, boolean isBD) {
        // 参数处理-需排除的打卡类型
        if (CollectionUtils.isEmpty(request.getAttendanceTypeExclude())) {
            request.setAttendanceTypeExclude(new ArrayList<>());
        }
        // 参数处理-确定打卡类型范围(大类)
        List<Integer> attendanceTypeList;
        if (Objects.isNull(request.getAttendanceType()) && Objects.isNull(request.getAttendanceChildType())) {
            attendanceTypeList = Stream.of(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_1,
                            DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_2,
                            DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_3,
                            DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_4,
                            DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_5)
                    // 过滤掉request中指定需要排除的考勤类型
//                    .filter(type -> !isTypeExcluded(request, type))
                    .collect(Collectors.toList());
        } else if (Objects.nonNull(request.getAttendanceType())) {
            attendanceTypeList = Stream.of(request.getAttendanceType(),
                            Objects.equals(request.getAttendanceType(), DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_1) ? DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_2 : request.getAttendanceType())
                    // 过滤掉request中指定需要排除的考勤类型
//                    .filter(type -> !isTypeExcluded(request, type))
                    .collect(Collectors.toList());
        } else {
            Integer mapAttendanceType = MapAttendanceTypeEnum.findMapAttendanceType(request.getAttendanceChildType());
            attendanceTypeList = Stream.of(mapAttendanceType)
                    // 过滤掉request中指定需要排除的考勤类型
//                    .filter(type -> !isTypeExcluded(request, type))
                    .collect(Collectors.toList());

            // 特殊处理。子状态上班:包括上班点/迟到/出差   子状态通关:包括通关点和异常
            List<Integer> attendanceChildTypeList = specialHandlingAttendanceChildType(request);

            request.getAttendanceTypeExclude().addAll(
                    MapAttendanceTypeEnum.findMapAttendanceChildTypeList(mapAttendanceType).stream()
                            .filter(type -> !attendanceChildTypeList.contains(type))
                            .collect(Collectors.toList()));
        }

        return attendanceTypeList;
    }

    /**
     * 子状态特殊处理逻辑: 子状态上班:包括上班点/迟到/出差   子状态通关:包括通关点和异常
     */
    private static List<Integer> specialHandlingAttendanceChildType(MapListRequest request) {
        List<Integer> attendanceChildTypeList = new ArrayList<>();
        // 默认
        attendanceChildTypeList.add(request.getAttendanceChildType());
        if (MapAttendanceTypeEnum.CHILD10.getMapAttendanceChildType().equals(request.getAttendanceChildType())) {
            attendanceChildTypeList.add(MapAttendanceTypeEnum.CHILD11.getMapAttendanceChildType());
            attendanceChildTypeList.add(MapAttendanceTypeEnum.CHILD12.getMapAttendanceChildType());
        } else if (MapAttendanceTypeEnum.CHILD20.getMapAttendanceChildType().equals(request.getAttendanceChildType())) {
            attendanceChildTypeList.add(MapAttendanceTypeEnum.CHILD21.getMapAttendanceChildType());
        }
        return attendanceChildTypeList;
    }

    private static void setNeedConvene(String organizationId, List<MeetingInfoVO> meetingScheduleList, Map<String, CeoBusinessOrganizationEntity> employeeOrganizationMap, MapDetail4NeedConveneVo needConvene, Map<Integer, String> businessGroupMap, List<MapDetail4NeedConveneVo> needConveneList) {
        if (CollectionUtil.isEmpty(meetingScheduleList)) {
            // 数据库会议日程表没有应召开的，即为 无需召开
            CeoBusinessOrganizationEntity organization = employeeOrganizationMap.get(organizationId);
            needConvene.setNeedConveneTypeName("无需召开");
            needConvene.setBusinessGroup(organization.getBusinessGroup());
            needConvene.setBusinessGroupName(businessGroupMap.get(organization.getBusinessGroup()));
            needConvene.setOrganizationId(organizationId);
            needConvene.setOrganizationName(organization.getOrganizationName());
            needConveneList.add(needConvene);
        } else {
            for (MeetingInfoVO meeting : meetingScheduleList) {
                if (Objects.isNull(meeting.getInfoId()) || Objects.isNull(meeting.getActualStartTime())) {
                    needConvene.setNeedConveneTypeName("未召开");
                } else {
                    needConvene.setNeedConveneTypeName("已召开");
                    needConvene.setImage(meeting.getCheckPic());
                }
                needConvene.setBusinessGroup(meeting.getConveneBusinessGroup());
                needConvene.setBusinessGroupName(businessGroupMap.get(meeting.getConveneBusinessGroup()));
                needConvene.setOrganizationId(meeting.getConveneOrganizationId());
                needConvene.setOrganizationName(meeting.getConveneOrganizationName());
                needConveneList.add(needConvene);
            }
        }
    }


    private boolean isTypeExcluded(MapListRequest request, int type) {
        // 如果request中指定了需要排除的考勤类型，且包含当前检查的类型，则返回true，表示需要排除
        if (!CollectionUtils.isEmpty(request.getAttendanceTypeExclude())) {
            // 假设getAttendanceTypeExclude返回的是一个可变的、线程不安全的List，此处将其转换为HashSet以提高查找效率
            Set<Integer> exclusionSet = new HashSet<>(request.getAttendanceTypeExclude());
            if (exclusionSet.contains(DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_1) && type == DictCodeConstants.CLASSCD_MAP_ATTENDANCE_TYPE_ITEMVALUE_2) {
                return true;
            }
            return exclusionSet.contains(type);
        }
        return false;
    }

    private static double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int EARTH_RADIUS = 6371;

        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);

        lat1 = Math.toRadians(lat1);
        lat2 = Math.toRadians(lat2);

        double a = Math.pow(Math.sin(dLat / 2), 2) + Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);
        double c = 2 * Math.asin(Math.sqrt(a));

        return EARTH_RADIUS * c * 1000; // Distance in kilometers
    }

    private static MapDetailListVo findPrevious(List<MapDetailListVo> list, int index) {
        for (int i = index - 1; i >= 0; i--) {
            MapDetailListVo preVo = list.get(i);
            if (StringUtils.isNotBlank(preVo.getLatitude()) && StringUtils.isNotBlank(preVo.getLongitude())) {
                return preVo;
            }
        }
        return null; // 如果没有找到，则返回null
    }

    private static MapDetail1Vo findPrevious1(List<MapDetail1Vo> list, int index) {
        for (int i = index - 1; i >= 0; i--) {
            MapDetail1Vo preVo = list.get(i);
            if (StringUtils.isNotBlank(preVo.getLatitude()) && StringUtils.isNotBlank(preVo.getLongitude())) {
                return preVo;
            }
        }
        return null; // 如果没有找到，则返回null
    }

    private void setDetail3Vo(List<MapDetailListVo> visitCustomerTypeList, MapDetail3Vo detail3CustomerType, DateTimeFormatter formatter) {
        if (!CollectionUtils.isEmpty(visitCustomerTypeList)) {
            detail3CustomerType.setVisitNum(visitCustomerTypeList.size());
            long totalCustomerTypeSeconds = visitCustomerTypeList.stream()
                    .filter(vo -> StringUtils.isNotBlank(vo.getVisitTimeDuration()))
                    .map(vo -> LocalTime.parse(vo.getVisitTimeDuration(), formatter))
                    .map(LocalTime::toSecondOfDay)
                    .mapToLong(Long::valueOf)
                    .reduce(0L, Long::sum);
            BigDecimal avgCustomerType = new BigDecimal(totalCustomerTypeSeconds)
                    .divide(new BigDecimal(visitCustomerTypeList.size()), 2, RoundingMode.HALF_UP);
            String unitCustomerType = ComonLanguageEnum.SECONDS.getDesc();
            if (avgCustomerType.compareTo(new BigDecimal(60)) > 0) {
                avgCustomerType = avgCustomerType.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                unitCustomerType = ComonLanguageEnum.MINUTES.getDesc();
            }
            detail3CustomerType.setVisitTimeDurationAvg(avgCustomerType + unitCustomerType);
            List<MapDetailListVo> filterCustomerTypeList = visitCustomerTypeList.stream().filter(vo -> vo.getOpenType() == 1).collect(Collectors.toList());
            detail3CustomerType.setPotentialRate(new BigDecimal(filterCustomerTypeList.size()).divide(new BigDecimal(visitCustomerTypeList.size()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).toString());
        }
    }

    public static class CustomComparator implements Comparator<MapDetail3Vo> {
        @Override
        public int compare(MapDetail3Vo o1, MapDetail3Vo o2) {
            Integer ct1 = o1.getCustomerType();
            Integer ct2 = o2.getCustomerType();

            // 定义排序顺序：null, 2, 3, 0, 1
            int[] sortOrder = {99, 2, 3, 0, 1};
            int index1 = ct1 == null ? 99 : ct1;
            int index2 = ct2 == null ? 99 : ct2;

            // 找到对应的排序位置
            int order1 = -1;
            int order2 = -1;
            for (int i = 0; i < sortOrder.length; i++) {
                if (sortOrder[i] == index1) {
                    order1 = i;
                }
                if (sortOrder[i] == index2) {
                    order2 = i;
                }
            }

            // 比较排序位置
            return Integer.compare(order1, order2);
        }
    }


}
