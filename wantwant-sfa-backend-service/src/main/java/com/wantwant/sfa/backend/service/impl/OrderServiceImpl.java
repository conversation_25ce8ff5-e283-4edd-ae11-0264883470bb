package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.annotation.MethodTransName;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.dict.request.DictCodeRequest;
import com.wantwant.sfa.backend.dict.service.DictCodeService;
import com.wantwant.sfa.backend.dict.vo.DictCodeVo;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.order.AdsOrderItemDetailMapper;
import com.wantwant.sfa.backend.model.EmployeeVerifyPO;
import com.wantwant.sfa.backend.model.OrderGrantPO;
import com.wantwant.sfa.backend.model.OrderVerifyPO;
import com.wantwant.sfa.backend.order.request.*;
import com.wantwant.sfa.backend.order.vo.*;
import com.wantwant.sfa.backend.service.IOrderService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.common.base.CommonConstant;
import com.wantwant.sfa.common.base.DateTimeUtility;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {

    @Autowired
    private DictCodeService dictCodeService;

    @Autowired
    private SfaOrderMapper sfaOrderMapper;
    @Autowired
    private SettingServiceImpl settingServiceImpl;

    @Value("${want-want.orderListMemberDataBaseName}")
    private String orderListMemberDataBaseName;

    @Value("${want-want.onlineDate}")
    private String onlineDate;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OrderGrantMapper grantMapper;

    @Autowired
    private ItemProfitMonitoringMapper itemProfitMonitoringMapper;

    @Autowired
    private EmployeeVerifyMapper employeeVerifyMapper;

    @Autowired
    private OrderVerifyMapper orderVerifyMapper;

    @Autowired
    private AdsOrderItemDetailMapper adsOrderItemDetailMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Resource
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;

    private static final String FINISHED_ORDER_LIST_KEY = "sfa:zw:finished:orderSet";

    @Override
    public IPage<OrderListNewVo> orderList(OrderListNewRequest request) {
        log.info("orderList request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(loginInfo.getBusinessGroup());
        request.setTimeZone(loginInfo.getTimezone());
        if (CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(request.getTimeZone())) {
            request.setOrderChannel(settingServiceImpl.getValue(DictCodeConstants.BH_ORDER_CHANNEL));
        }
        // 如果登录的是总部，判断是否有稽核权限，如果是稽核则查询全组
        if(loginInfo.getOrganizationType().equals("zb")){
            Integer hasRole = roleEmployeeRelationMapper.checkHasRole(request.getPerson(), 63, RequestUtils.getBusinessGroup());
            if(hasRole > 0){
                request.setPersonBusinessGroup(99);
            }
        }


        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_OPERATOR.getTextMsg());
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }
        if(StringUtils.isNotBlank(request.getOrganizationId())){
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
//                    .eq(CeoBusinessOrganizationEntity::getBusinessGroup, request.getPersonBusinessGroup())
            );
            request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
        }

        Page<OrderListNewVo> page = new Page<>(request.getPage(), request.getRows());
        List<OrderListNewVo> list = adsOrderItemDetailMapper.queryOrderList(page, request);

        if (!CollectionUtils.isEmpty(list)) {
            List<String> organizationIdList = list.stream().map(OrderListNewVo::getOrganizationId).collect(Collectors.toList());
            Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
//                    .eq(CeoBusinessOrganizationEntity::getBusinessGroup, request.getPersonBusinessGroup())
            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));
            Map<Integer, String> userIdentityMap = dictCodeService.getCodeItem(DictCodeRequest.builder().classCd(
                            CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage()) ? DictCodeConstants.CLASSCD_USER_IDENTITY_EN : DictCodeConstants.CLASSCD_USER_IDENTITY
                    ).build())
                    .stream().collect(Collectors.toMap(model -> Integer.valueOf(model.getItemValue()), DictCodeVo::getItemContent));

            Map<Long, LocalDateTime> lastOrderMap = new HashMap<>();
            List<OrderListNewVo> lastOrderList = adsOrderItemDetailMapper.queryNotFirstOrderMemberKeyList(list, request.getTimeZone());
            if (!CollectionUtils.isEmpty(lastOrderList)) {
                lastOrderMap = lastOrderList.stream().collect(Collectors.toMap(OrderListNewVo::getMemberKey, OrderListNewVo::getPlacedAt));
            }
            Map<Long, LocalDateTime> finalLastOrderMap = lastOrderMap;
            LocalDate now = DateTimeUtility.changeTimezone(LocalDateTime.now(), null, request.getTimeZone()).toLocalDate();
            list.forEach(order -> {
                order.setTheYearMon(Objects.nonNull(order.getPlacedAt()) ? LocalDateTimeUtils.formatTime(order.getPlacedAt(), "yyyy-MM") : "-");
                order.setServiceFlag(Objects.nonNull(order.getReturnAmount()) && order.getReturnAmount().compareTo(BigDecimal.ZERO) > 0);
                order.setOrganizationName(organizationMap.get(order.getOrganizationId()));
                order.setUserIdentityName(userIdentityMap.get(order.getUserIdentity()));
                order.setMethod(getMethod(order.getSkuRetailSubTotal(), order.getFreeTotalAmount()));
                order.setTypeDesc(getTypeDesc(order.getType()));
                order.setStatus(ComonLanguageEnum.getDescByEnv(order.getStatus()));
                order.setCustomerTypeNew(ComonLanguageEnum.getDescByEnv(order.getCustomerTypeNew()));
                if (finalLastOrderMap.containsKey(order.getMemberKey())) {
                    order.setLastOrderDays(ChronoUnit.DAYS.between(finalLastOrderMap.get(order.getMemberKey()).toLocalDate(), now));
                } else {
                    order.setLastOrderDays(0L);
                }

                // 合伙人利润率=品项利润/盘价业绩
                if (Objects.nonNull(order.getItemSupplyPriceTotal()) && order.getItemSupplyPriceTotal().signum() > 0) {
                    order.setItemProfitTotalRate(order.getItemProfitTotal().multiply(new BigDecimal(100)).divide(order.getItemSupplyPriceTotal(), 2, RoundingMode.HALF_UP));
                }
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public OrderDetailNewVo orderDetail(OrderDetailNewRequest request) {
        log.info("orderDetail request:{}", request);
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        // 如果登录的是总部，判断是否有稽核权限，如果是稽核则查询全组
        if(RequestUtils.getLoginInfo().getOrganizationType().equals("zb")){
            Integer hasRole = roleEmployeeRelationMapper.checkHasRole(request.getPerson(), 63, RequestUtils.getBusinessGroup());
            if(hasRole > 0){
                request.setPersonBusinessGroup(99);
            }
        }


        OrderDetailNewVo detail = adsOrderItemDetailMapper.queryOrderDetail(request);
        if (Objects.nonNull(detail)) {
            Map<Integer, String> userIdentityMap = dictCodeService.getCodeItem(DictCodeRequest.builder().classCd(
                            CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage()) ? DictCodeConstants.CLASSCD_USER_IDENTITY_EN : DictCodeConstants.CLASSCD_USER_IDENTITY
                    ).build())
                    .stream().collect(Collectors.toMap(model -> Integer.valueOf(model.getItemValue()), DictCodeVo::getItemContent));
            detail.setUserIdentityName(userIdentityMap.get(detail.getUserIdentity()));

            // 合伙人利润率=品项利润/盘价业绩
            if (Objects.nonNull(detail.getItemSupplyPriceTotal()) && detail.getItemSupplyPriceTotal().signum() > 0) {
                detail.setItemProfitTotalRate(detail.getItemProfitTotal().multiply(new BigDecimal(100)).divide(detail.getItemSupplyPriceTotal(), 2, RoundingMode.HALF_UP));
            }

            OrderSkuListNewRequest orderSkuListNewRequest = new OrderSkuListNewRequest();
            orderSkuListNewRequest.setPersonBusinessGroup(  request.getPersonBusinessGroup());
            orderSkuListNewRequest.setCode(request.getCode());
            List<OrderSkuListNewVo> list = adsOrderItemDetailMapper.queryOrderSkuList(null, orderSkuListNewRequest);
            list.forEach(sku -> {
                // 实付单价=实付小计/sku数量
                if (Objects.nonNull(sku.getSkuQuantity()) && sku.getSkuQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    sku.setSkuRetailSub(sku.getSkuRetailSubTotal().divide(sku.getSkuQuantity(), 2, RoundingMode.HALF_UP));
                }

                // 合伙人利润率=品项利润/盘价业绩
                if (Objects.nonNull(sku.getItemSupplyPriceTotal()) && sku.getItemSupplyPriceTotal().signum() > 0) {
                    sku.setItemProfitTotalRate(sku.getItemProfitTotal().multiply(new BigDecimal(100)).divide(sku.getItemSupplyPriceTotal(), 2, RoundingMode.HALF_UP));
                }

                sku.setCommodityType(ComonLanguageEnum.getDescByEnv(sku.getCommodityType()));
            });
            detail.setSkuList(list);

            //快递信息
            List<DeliveryInformationVo> deliveryInformatVos = adsOrderItemDetailMapper.findDeliveryInformat(request.getCode());
            detail.setDeliveryInformation(deliveryInformatVos);

            detail.setStatus(ComonLanguageEnum.getDescByEnv(detail.getStatus()));
            detail.setCustomerTypeNew(ComonLanguageEnum.getDescByEnv(detail.getCustomerTypeNew()));
        }
        return detail;
    }

    @Override
    public IPage<OrderSkuListNewVo> orderSkuList(OrderSkuListNewRequest request) {
        log.info("orderSkuList request:{}", request);
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());

        Page<OrderSkuListNewVo> page = new Page<>(request.getPage(), request.getRows());
        List<OrderSkuListNewVo> list = adsOrderItemDetailMapper.queryOrderSkuList(page, request);

        list.forEach(sku -> {

            // 实付单价=实付小计/sku数量
            if (Objects.nonNull(sku.getSkuQuantity()) && sku.getSkuQuantity().compareTo(BigDecimal.ZERO) > 0) {
                sku.setSkuRetailSub(sku.getSkuRetailSubTotal().divide(sku.getSkuQuantity(), 2, RoundingMode.HALF_UP));
            }

            // 合伙人利润率=品项利润/盘价业绩
            if (Objects.nonNull(sku.getItemSupplyPriceTotal()) && sku.getItemSupplyPriceTotal().signum() > 0) {
                sku.setItemProfitTotalRate(sku.getItemProfitTotal().multiply(new BigDecimal(100)).divide(sku.getItemSupplyPriceTotal(), 2, RoundingMode.HALF_UP));
            }

            sku.setCommodityType(ComonLanguageEnum.getDescByEnv(sku.getCommodityType()));
        });
        page.setRecords(list);
        return page;
    }

    @Override
    public IPage<OrderSkuExportNewVo> orderSkuExport(OrderListNewRequest request) {
        log.info("orderSkuExport request:{}", request);
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setPersonBusinessGroup(loginInfo.getBusinessGroup());
        request.setTimeZone(loginInfo.getTimezone());
        if (CommonConstant.TIMEZONE_ASIA_SHANGHAI.equals(request.getTimeZone())) {
            request.setOrderChannel(settingServiceImpl.getValue(DictCodeConstants.BH_ORDER_CHANNEL));
        }
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException(BizExceptionLanguageEnum.ORGANIZATION_INFO_NOT_EXIST_OPERATOR.getTextMsg());
            // 非总部人员，限定范围，本人及以下组织
        } else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setPersonOrganizationIds(personOrganizationIds);
            request.setPersonOrganizationType(loginInfo.getOrganizationType());
        }
        if(StringUtils.isNotBlank(request.getOrganizationId())){
            CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                    .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
                    .eq(CeoBusinessOrganizationEntity::getBusinessGroup, request.getPersonBusinessGroup())
            );
            request.setOrganizationType(ceoBusinessOrganizationEntity.getOrganizationType());
        }

        // 如果登录的是总部，判断是否有稽核权限，如果是稽核则查询全组
        if(loginInfo.getOrganizationType().equals("zb")){
            Integer hasRole = roleEmployeeRelationMapper.checkHasRole(request.getPerson(), 63, RequestUtils.getBusinessGroup());
            if(hasRole > 0){
                request.setPersonBusinessGroup(99);
            }
        }


        Page<OrderSkuExportNewVo> page = new Page<>(request.getPage(), request.getRows());
        List<OrderSkuExportNewVo> list = adsOrderItemDetailMapper.exportOrderSkuList(page, request);

        list.forEach(sku -> {
            // 实付单价=实付小计/sku数量
            if (Objects.nonNull(sku.getSkuQuantity()) && sku.getSkuQuantity().compareTo(BigDecimal.ZERO) > 0) {
                sku.setSkuRetailSub(sku.getSkuRetailSubTotal().divide(sku.getSkuQuantity(), 2, RoundingMode.HALF_UP));
            }

            sku.setMethod(getMethod(sku.getSkuRetailSubTotal(), sku.getFreeTotalAmount()));
            sku.setTypeDesc(getTypeDesc(sku.getType()));
            sku.setStatus(ComonLanguageEnum.getDescByEnv(sku.getStatus()));
            sku.setCommodityType(ComonLanguageEnum.getDescByEnv(sku.getCommodityType()));
        });
        page.setRecords(list);
        return page;
    }

    private static String getMethod(BigDecimal skuRetailSubTotal, BigDecimal freeTotalAmount) {
        boolean flag = CommonConstant.REGION_INDONESIA.equals(RequestUtils.getRegion())
                || CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())
                || CommonConstant.TIMEZONE_ASIA_JAKARTA.equals(RequestUtils.getTimezone());
        if (flag) {
            // 默认现金支付
            return "Cash Payment";
        }
        String method;
        if (skuRetailSubTotal.signum() > 0 && freeTotalAmount.signum() > 0) {
            method = "组合支付";
        } else if (skuRetailSubTotal.signum() > 0 && freeTotalAmount.signum() == 0) {
            method = "现金支付";
        } else if (skuRetailSubTotal.signum() == 0 && freeTotalAmount.signum() > 0) {
            method = "旺金币支付";
        } else {
            method = "旺金币支付";
        }
        return method;
    }

    private static String getTypeDesc(Integer type) {
        boolean flag = CommonConstant.REGION_INDONESIA.equals(RequestUtils.getRegion())
                || CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())
                || CommonConstant.TIMEZONE_ASIA_JAKARTA.equals(RequestUtils.getTimezone());
        if (Objects.isNull(type)) {
            return flag ? "Other orders" : "其他订单";
        }
        String typeDesc;
        switch (type){
            case 3:
                typeDesc = flag ? "Regular Orders" : "普通订单";
                break;
            case 9:
                typeDesc = flag ? "Tasting Orders" : "试吃订单";
                break;
            case 999:
                typeDesc = flag ? "Pre-sale Orders" : "预售订单";
                break;
            default:
                typeDesc = flag ? "Other Orders" : "其他订单";
        }
        return typeDesc;
    }

    //订单导出数量判断
    @Override
    public Integer CountOrderListExport(OrderListRequest request) {
        log.info("start OrderServiceImpl orderListExportCount request:{}", request);
        return sfaOrderMapper.getOrderListExportCount(request, orderListMemberDataBaseName);
    }

    @Override
    public void notice(OrderNoticeRequest request) {
        // 造旺业务
        if (request.getBusinessCode() == 2) {
            redisUtil.sSet(FINISHED_ORDER_LIST_KEY, request.getOrderNo());
        }

    }

    //订单导出数据
    @Override
    public List<OrderExportVo> orderListExport(OrderListRequest request) {
        log.info("start OrderServiceImpl orderListExport request:{}", request);

        //首单限定金额
        String firstOrderAmount = settingServiceImpl.getValue("first_order_amount");

        List<String> firstOrderList = sfaOrderMapper.getFirstOrderList(firstOrderAmount);

        List<OrderExportVo> list = sfaOrderMapper.getOrderListExport(request, orderListMemberDataBaseName);

        for (int i = list.size() - 1; i >= 0; i--) {
            OrderExportVo order = list.get(i);
            order.setOnlineDate(onlineDate);

            //如果SKU为空则去掉记录
            if (StringUtils.isBlank(order.getSku())) {
                list.remove(i);
                continue;
            }

            //首单判断
            if (CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
                order.setFirstOrder(firstOrderList.contains(order.getOrderCode()) ? "True" : "False");
            } else {
                order.setFirstOrder(firstOrderList.contains(order.getOrderCode()) ? "是" : "否");
            }


        }

        return list;
    }


    //订单列表导出
    @Override
    @MethodTransName(value = "订单列表")
    public Workbook orderListExportExcel(OrderListRequest request) {
        log.info("start OrderServiceImpl orderListExportExcel request:{}", request);

        List<OrderExportVo> list = orderListExport(request);
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        return ExcelExportUtil.exportExcel(new ExportParams(null, sheetName),
                OrderExportVo.class, list);
    }

    @Override
    public IPage<OrderGrantVO> queryGrantByPage(OrderGrantRequest request) {
        Page<OrderGrantVO> page = new Page<>(request.getPage(), request.getRows());
        List<OrderGrantVO> list = sfaOrderMapper.selectPageBySql(page, request);
        page.setRecords(list);
        return page;
    }

    @Override
    public void exportGrant(OrderGrantRequest request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        List<OrderGrantVO> list = sfaOrderMapper.listGrant(request);
        String fileName = "品项利润发放";
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook wb = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), OrderGrantVO.class, list);
        try {
            if (wb instanceof HSSFWorkbook) {
                fileName = fileName + ".xls";
            } else {
                fileName = fileName + ".xlsx";
            }
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            wb.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    /**
     * 品相利润监控
     */
    @Override
    public IPage<OrderMonitorVO> queryMonitorByPage(OrderMonitorRequest request) {
        Page<OrderMonitorVO> page = new Page<>(request.getPage(), request.getRows());
        List<OrderMonitorVO> list = itemProfitMonitoringMapper.selectMonitorList(page, request);
        page.setRecords(list);
        return page;
    }

    /**
     * 品相利润监控导出
     */
    @Override
    public void exportMonitor(OrderMonitorRequest request,HttpServletResponse response) {
        List<OrderMonitorVO> list = itemProfitMonitoringMapper.selectMonitorList(null, request);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), OrderMonitorVO.class, list);
        EasyPoiUtil.downLoadExcel("品相利润监控.xls", response, workbook);
    }

    /**
     * 品相利润监控批量处理
     */
    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public void monitorBatch(MonitorBatchRequest batchRequest) {
        log.info("品相利润监控批量处理入参:{}",batchRequest);
        EmployeeVerifyPO employeeVerify = new EmployeeVerifyPO();
        employeeVerify.setTheYearMon(batchRequest.getTheYearMon());
        employeeVerify.setStatus(batchRequest.getStatus());
        employeeVerify.setCreateBy(batchRequest.getCreateBy());
        employeeVerify.setUpdateBy(batchRequest.getCreateBy());
        if (CommonUtil.ListUtils.isNotEmpty(batchRequest.getEmployeeInfoId())){
            batchRequest.getEmployeeInfoId().forEach(e -> {
                List<String> codes = sfaOrderMapper.selectOrder(batchRequest.getTheYearMon(),e);
                if (CommonUtil.ListUtils.isNotEmpty(codes)){
                    OrderVerifyPO orderVerify = new OrderVerifyPO();
                    orderVerify.setStatus(batchRequest.getStatus());
                    orderVerify.setCreateBy(batchRequest.getCreateBy());
                    orderVerify.setUpdateBy(batchRequest.getCreateBy());
                    codes.forEach(o -> {
                        orderVerify.setId(null);
                        orderVerify.setCode(o);
                        orderVerifyMapper.insert(orderVerify);
                    });
                }
                employeeVerify.setId(null);
                employeeVerify.setEmployeeInfoId(e);
                employeeVerifyMapper.insert(employeeVerify);
            });
        }

    }

    /** 
     * 利润趋势分析
     */
    @Override
    public List<OrderMonitorVO> listProfit(Integer employeeInfoId) {
        return itemProfitMonitoringMapper.selectListProfit(employeeInfoId);
    }

    /**
     * 品相利润监控订单批量处理
     */
    @Override
    public void monitorOrderBatch(MonitorOrderBatchRequest batchRequest) {
        log.info("品相利润监控订单批量处理入参:{}",batchRequest);
        OrderVerifyPO orderVerify = new OrderVerifyPO();
        orderVerify.setStatus(batchRequest.getStatus());
        orderVerify.setCreateBy(batchRequest.getCreateBy());
        orderVerify.setUpdateBy(batchRequest.getCreateBy());
        if (CommonUtil.ListUtils.isNotEmpty(batchRequest.getCode())){
            batchRequest.getCode().forEach(o -> {
                orderVerify.setId(null);
                orderVerify.setCode(o);
                orderVerifyMapper.insert(orderVerify);
            });
        }
    }

    @Override
    public OrderBatchUploadVo orderBatchGrantUpload(List<OrderBatchExcelVo> orderList, String person) {
        List<OrderBatchUploadListVO> abnormalList = new ArrayList<>();

        for (OrderBatchExcelVo order : orderList) {

            String orderNumber = order.getOrderNumber();
            float orderprice = 0;
            if (StringUtils.isNotBlank(order.getOrderPrice())
            && NumberUtil.isNumber(order.getOrderPrice())){
                orderprice = Float.parseFloat(order.getOrderPrice());
            }

            log.info("订单号:{}，发放金额{}", orderNumber, orderprice);

            String reason = null;
            if (StringUtils.isBlank(orderNumber)) {
                reason = "订单号为空！";
                continue;
            } else if (orderprice <= 0) {
                reason = "订单发放金额异常！";

                OrderBatchUploadListVO orderBatchUploadListVO = new OrderBatchUploadListVO();
                orderBatchUploadListVO.setOrderNumber(orderNumber);
                orderBatchUploadListVO.setReason(reason);
                abnormalList.add(orderBatchUploadListVO);
                continue;
            }


            OrderGrantVO orderGrantVO = sfaOrderMapper.selectOneOrder(orderNumber);
            log.info("详情：{}", orderGrantVO);

            if (orderGrantVO == null) {
                reason = "未找到该订单";
            } else {

                //0，1可发放
                int state = 0;
                if (orderGrantVO.getState() != null) {
                    state = orderGrantVO.getState().intValue();
                }
                //品项利润
                float productCommissionAmount = 0;
                if (orderGrantVO.getProductCommissionAmount() != null) {
                    productCommissionAmount = orderGrantVO.getProductCommissionAmount().floatValue();
                }
                float grantAmountTotal = 0;
                if (orderGrantVO.getGrantAmountTotal() != null) {
                    grantAmountTotal = orderGrantVO.getGrantAmountTotal().floatValue();
                }

                if (state != 0 && state != 1) {
                    reason = "品项利润已发放";
                } else if (productCommissionAmount <= 0) {
                    reason = "品项利润为0";
                } else if (productCommissionAmount - grantAmountTotal < orderprice) {
                    reason = "发放额度超过实际额度";
                }
            }


            if (StringUtils.isNotBlank(reason)) {
                OrderBatchUploadListVO orderBatchUploadListVO = new OrderBatchUploadListVO();
                orderBatchUploadListVO.setOrderNumber(orderNumber);
                orderBatchUploadListVO.setReason(reason);
                abnormalList.add(orderBatchUploadListVO);
            } else {
                float orderPrice = Float.parseFloat(order.getOrderPrice());

                GrantRequest grantRequest = new GrantRequest();
                grantRequest.setEmployeeId(person);
                grantRequest.setCode(orderNumber);
                grantRequest.setAmount(BigDecimal.valueOf(orderPrice));
                grantRequest.setProductCommissionAmount(orderGrantVO.getProductCommissionAmount());

//                this.orderGrant(grantRequest);
            }

        }

        log.info("\n总数：{}失败：{}list：{}", orderList.size(), abnormalList.size(), abnormalList);

        OrderBatchUploadVo orderBatchUploadVo = new OrderBatchUploadVo();
        orderBatchUploadVo.setAllCount(orderList.size());
        orderBatchUploadVo.setSucceedCount(orderList.size() - abnormalList.size());
        orderBatchUploadVo.setList(abnormalList);

        return orderBatchUploadVo;
    }

    @Override
    public List<OrderTypeVO> queryOrderType() {
        List<OrderTypeVO> list = sfaOrderMapper.queryOrderType();
        return list;
    }

    @Override
    public Response<List<String>> selectOrderNo(SearchOrderNoRequest searchOrderNoRequest) {
        String organizationId = searchOrderNoRequest.getOrganizationId();
        if(StringUtils.isNotBlank(organizationId)){
            String organizationType = organizationMapper.getOrganizationType(organizationId);
            searchOrderNoRequest.setOrganizationType(organizationType);
        }
        searchOrderNoRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<String> list = adsOrderItemDetailMapper.selectOrderNo(searchOrderNoRequest);
        return Response.success(list);
    }


}
