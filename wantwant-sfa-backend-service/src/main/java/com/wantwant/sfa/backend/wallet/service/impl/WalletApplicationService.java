package com.wantwant.sfa.backend.wallet.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.*;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.application.WalletQuotaApplication;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.AuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.domain.wallet.DO.ExpensesAdditionalDO;
import com.wantwant.sfa.backend.domain.wallet.DO.WalletQuotaApplicationDO;
import com.wantwant.sfa.backend.gold.enums.TransactionBusinessTypeEnum;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.wallet.*;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.GoldCoinConnectorUtil;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.wallet.dto.*;
import com.wantwant.sfa.backend.wallet.dto.AuditDecisionDTO;
import com.wantwant.sfa.backend.wallet.entity.*;
import com.wantwant.sfa.backend.wallet.request.*;
import com.wantwant.sfa.backend.wallet.service.IWalletApplicationService;
import com.wantwant.sfa.backend.wallet.service.IWalletBlackListService;
import com.wantwant.sfa.backend.wallet.service.IWalletSearchService;
import com.wantwant.sfa.backend.wallet.service.IWalletService;
import com.wantwant.sfa.common.base.JacksonHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/11/27/上午9:11
 */
@Service
@Slf4j
public class WalletApplicationService implements IWalletApplicationService {
    private static final String QUICK_SEND_LOCK = "sfa:wallet:quickSend";
    private static final String REWARDS_SEND_LOCK = "sfa:rewards:org";
    @Autowired
    private IWalletService walletService;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Autowired
    private WantWalletMapper wantWalletMapper;
    @Autowired
    private WantWalletTypeMapper wantWalletTypeMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Autowired
    private WantWalletPersonalLogMapper wantWalletPersonalLogMapper;
    @Autowired
    private IPenaltyService penaltyService;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private WalletQuotaApplication walletQuotaApplication;
    @Resource
    private IWalletSearchService walletSearchService;
    @Resource
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Resource
    private IWalletBlackListService walletBlackListService;
    @Resource
    private WantWalletLogMapper wantWalletLogMapper;
    @Resource
    private ICheckCustomerService checkCustomerService;
    @Resource
    private WalletRewardsLogMapper walletRewardsLogMapper;
    @Resource
    private WalletSendLogMapper walletSendLogMapper;
    @Resource
    private IEmpService empService;
    @Resource
    private GoldCoinConnectorUtil goldCoinConnectorUtil;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private AuditService auditService;

    private static final String revenueMsg = "{0}-{1}-{2}";

    private static final String REVENUE_KEY = "revenue";

    private static final String EXPENDITURE_KEY = "expenditure";

    // 钱包发放相关常量
    private static final int RECEIVER_TYPE_PARTNER = 2;
    private static final int RECEIVER_TYPE_ORGANIZATION = 1;
    private static final int POSITION_TYPE_PARTNER = 3;
    private static final int POSITION_TYPE_HEADQUARTERS = 7;
    private static final int AUDIT_CHANNEL_WALLET = 3;
    private static final int EXPENSE_RATE_CHECK_TYPE = 1;
    private static final String HR_EMPLOYEE_CONFIG_KEY = "zw_hr_employee_id";

    @Value("${aes.key:f199f8e51a811e98}")
    public String AES_KEY;
    @Autowired
    private RedisUtil redisUtil;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void quickSend(WalletSendDTO walletSendDTO) {
        log.info("【wallet quick send】dto:{}", walletSendDTO);
        
        // 参数校验
        validateWalletSendDTO(walletSendDTO);
        
        // 初始化支出方/收入方
        Map<String, String> recordMap = getRecord(walletSendDTO);

        // 判断是否需要检查审核逻辑
        if (shouldSkipAuditFlow(walletSendDTO)) {
            doSend(walletSendDTO, recordMap);
            return;
        }

        // 检查审核条件并决定处理方式
        AuditDecisionDTO auditDecision = determineAuditDecision(walletSendDTO);
        
        if (auditDecision.needsAuditFlow()) {
            startAuditFlow(walletSendDTO, recordMap, auditDecision.isBypassHierarchy());
        } else {
            doSend(walletSendDTO, recordMap);
        }
    }
    
    /**
     * 校验钱包发送DTO参数
     */
    private void validateWalletSendDTO(WalletSendDTO walletSendDTO) {
        if (Objects.isNull(walletSendDTO)) {
            throw new IllegalArgumentException("钱包发送参数不能为空");
        }
        if (Objects.isNull(walletSendDTO.getReceiverType())) {
            throw new IllegalArgumentException("接收方类型不能为空");
        }
        if (StringUtils.isBlank(walletSendDTO.getReceiverKey())) {
            throw new IllegalArgumentException("接收方标识不能为空");
        }
    }
    
    /**
     * 判断是否应该跳过审核流程
     */
    private boolean shouldSkipAuditFlow(WalletSendDTO walletSendDTO) {
        return !walletSendDTO.isVerifyAuditFlowCheck() || StringUtils.isNotBlank(walletSendDTO.getWpCode());
    }
    
    /**
     * 确定审核决策
     */
    private AuditDecisionDTO determineAuditDecision(WalletSendDTO walletSendDTO) {
        Integer receiverType = walletSendDTO.getReceiverType();
        
        if (RECEIVER_TYPE_PARTNER == receiverType) {
            return handlePartnerAuditDecision(walletSendDTO);
        } else {
            return handleOrganizationAuditDecision(walletSendDTO);
        }
    }
    
    /**
     * 处理合伙人审核决策
     */
    private AuditDecisionDTO handlePartnerAuditDecision(WalletSendDTO walletSendDTO) {
        String receiverKey = walletSendDTO.getReceiverKey();
        
        // 获取员工信息
        SfaEmployeeInfoModel employeeInfo = getEmployeeInfoByMemberKey(receiverKey);
        SfaPositionRelationEntity positionRelation = getLatestPositionRelation(employeeInfo.getId().longValue());

        boolean needsFlow = false;
        boolean isBypassHierarchy = false;
        if(POSITION_TYPE_PARTNER == positionRelation.getPositionTypeId()){
            needsFlow = true;
            isBypassHierarchy = checkBypassHierarchy(positionRelation, walletSendDTO.getProcessUserId());
        }

        return new AuditDecisionDTO(needsFlow, isBypassHierarchy);
    }
    
    /**
     * 处理组织审核决策
     */
    private AuditDecisionDTO handleOrganizationAuditDecision(WalletSendDTO walletSendDTO) {
        String receiverKey = walletSendDTO.getReceiverKey();
        BigDecimal quota = walletSendDTO.getQuota();
        
        // 检查费用率是否超标
        boolean ratioOver = walletSearchService.checkRatioOver(EXPENSE_RATE_CHECK_TYPE, quota, receiverKey, null);
        boolean isBypassHierarchy = checkBypassHierarchy(receiverKey, walletSendDTO.getProcessUserId());
        
        return new AuditDecisionDTO(ratioOver, isBypassHierarchy);
    }
    
    /**
     * 检查是否跨级操作
     */
    private boolean checkBypassHierarchy(SfaPositionRelationEntity positionRelation, String processUserId) {
        String organizationCode = positionRelation.getOrganizationCode();
        String parentOrganizationId = organizationMapper.getOrganizationParentId(organizationCode);
        
        CeoBusinessOrganizationPositionRelation parentRelation = getAuditPerson(parentOrganizationId);
        
        return isNotHeadquarters(parentRelation) && isNotSameUser(parentRelation, processUserId);
    }
    
    /**
     * 检查是否跨级操作（组织版本）
     */
    private boolean checkBypassHierarchy(String organizationId, String processUserId) {
        String parentOrganizationId = organizationMapper.getOrganizationParentId(organizationId);
        CeoBusinessOrganizationPositionRelation parentRelation = getAuditPerson(parentOrganizationId);
        
        return isNotHeadquarters(parentRelation) && isNotSameUser(parentRelation, processUserId);
    }
    
    /**
     * 获取员工信息
     */
    private SfaEmployeeInfoModel getEmployeeInfoByMemberKey(String memberKey) {
        SfaEmployeeInfoModel employeeInfo = sfaEmployeeInfoMapper.selectOne(
            new LambdaQueryWrapper<SfaEmployeeInfoModel>()
                .eq(SfaEmployeeInfoModel::getMemberKey, memberKey)
        );
        if (Objects.isNull(employeeInfo)) {
            throw new ApplicationException("员工信息获取失败");
        }
        return employeeInfo;
    }
    
    /**
     * 获取最新岗位关系
     */
    private SfaPositionRelationEntity getLatestPositionRelation(Long employeeInfoId) {
        SfaPositionRelationEntity positionRelation = sfaPositionRelationMapper.selectOne(
            new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmployeeInfoId, employeeInfoId)
                .eq(SfaPositionRelationEntity::getBusinessGroup,RequestUtils.getBusinessGroup())
                .eq(SfaPositionRelationEntity::getDeleteFlag,0)
                .orderByDesc(SfaPositionRelationEntity::getId)
                .last("limit 1")
        );
        if (Objects.isNull(positionRelation)) {
            throw new ApplicationException("岗位表获取信息失败");
        }
        return positionRelation;
    }
    
    /**
     * 获取审核人员信息
     */
    private CeoBusinessOrganizationPositionRelation getAuditPerson(String organizationId) {
        SelectAuditDto selectAuditDto = new SelectAuditDto();
        selectAuditDto.setCurrentOrganizationId(organizationId);
        selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode(HR_EMPLOYEE_CONFIG_KEY));
        selectAuditDto.setChannel(AUDIT_CHANNEL_WALLET);
        selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
        
        return auditService.chooseAuditPerson(selectAuditDto);
    }
    
    /**
     * 判断是否非总部
     */
    private boolean isNotHeadquarters(CeoBusinessOrganizationPositionRelation relation) {
        return Objects.nonNull(relation) && POSITION_TYPE_HEADQUARTERS != relation.getPositionTypeId();
    }
    
    /**
     * 判断是否非同一用户
     */
    private boolean isNotSameUser(CeoBusinessOrganizationPositionRelation relation, String userId) {
        return Objects.nonNull(relation) && !relation.getEmployeeId().equals(userId);
    }
    
    /**
     * 启动审核流程
     */
    private void startAuditFlow(WalletSendDTO walletSendDTO, Map<String, String> recordMap, boolean bypassHierarchy) {
        walletQuotaApplication.startQuotaApplyFlow(
            assemble(recordMap, walletSendDTO, bypassHierarchy), 
            walletSendDTO.getProcessUserId()
        );
    }
    


    private WalletQuotaApplicationDO assemble(Map<String, String> recordMap, WalletSendDTO walletSendDTO, boolean bypassHierarchy) {
        WalletQuotaApplicationDO walletQuotaApplicationDO = new WalletQuotaApplicationDO();
        walletQuotaApplicationDO.setApplyType(walletSendDTO.getReceiverType());
        walletQuotaApplicationDO.setExpenditure(recordMap.get(EXPENDITURE_KEY));
        walletQuotaApplicationDO.setExpenditureOrganizationId(walletSendDTO.getSenderOrgCode());
        walletQuotaApplicationDO.setRevenue(recordMap.get(REVENUE_KEY));
        walletQuotaApplicationDO.setBypassHierarchy(bypassHierarchy);
        walletQuotaApplicationDO.setQuota(walletSendDTO.getQuota());
        walletQuotaApplicationDO.setPaymentWalletType(walletSendDTO.getSendWalletType());
        walletQuotaApplicationDO.setPaymentSpuId(walletSendDTO.getSubTypeId());
        walletQuotaApplicationDO.setAcceptedWalletType(walletSendDTO.getReceiverWalletType());
        walletQuotaApplicationDO.setAcceptedSpuId(walletSendDTO.getSubTypeId());
        walletQuotaApplicationDO.setCostPurpose(walletSendDTO.getCostPurpose());
        ExpensesAdditionalDTO expensesAdditionalDTO = walletSendDTO.getExpensesAdditionalDTO();
        if (Objects.nonNull(expensesAdditionalDTO)) {
            ExpensesAdditionalDO expensesAdditionalDO = new ExpensesAdditionalDO();
            BeanUtils.copyProperties(expensesAdditionalDTO, expensesAdditionalDO);
            walletQuotaApplicationDO.setExpensesAdditionalDO(expensesAdditionalDO);
        }

        Integer receiverType = walletSendDTO.getReceiverType();
        if (receiverType == 2) {
            walletQuotaApplicationDO.setAcceptedMemberKey(Long.valueOf(walletSendDTO.getReceiverKey()));
        } else {
            walletQuotaApplicationDO.setAcceptedOrganizationId(walletSendDTO.getReceiverKey());
        }
        walletQuotaApplicationDO.setRemark(walletSendDTO.getRemark());
        walletQuotaApplicationDO.setAnnexRequestList(walletSendDTO.getAnnexRequestList());
        return walletQuotaApplicationDO;
    }

    public void doSend(WalletSendDTO walletSendDTO, Map<String, String> recordMap) {
        // 正常发放逻辑
        String senderOrgCode = walletSendDTO.getSenderOrgCode();
        List<WantWalletLogEntity> usedLogList = new ArrayList<>();
        if (StringUtils.isNotBlank(senderOrgCode)) {
            // 减去额度，调用额度使用方法
            usedLogList = walletService.used(convertToUsedDTO(walletSendDTO, recordMap));
        }

        Integer receiverType = walletSendDTO.getReceiverType();

        if (receiverType == 1) {
            // 币种转换
            convertCoins(walletSendDTO, usedLogList);

            // 发放给组织，调用额度增加方法
            walletService.add(convertToAddDTO(walletSendDTO, recordMap, usedLogList));

            // 如果组织是分公司，并且是临期售后或临期售后补发，还需要调用旺铺的售后接口
            saleAmountProcess(walletSendDTO, usedLogList);

            // 组织发放的旺金币需要执行扣罚追缴,组别币追缴扣罚
            penaltyService.pressPenalty(walletSendDTO.getReceiverKey(), null, true);


        } else {
            if (walletSendDTO.isCallWp()) {
                // 构建旺铺发放接口所需参数
                GoldCoinTransactionGrantRequest goldCoinTransactionGrantRequest = buildGoldCoinTransactionGrantRequest(walletSendDTO, usedLogList);
                // 调用发放给个人的接口,调用旺铺接口
                this.personalBatch(goldCoinTransactionGrantRequest);
            }

        }
    }

    private void convertCoins(WalletSendDTO walletSendDTO, List<WantWalletLogEntity> usedLogList) {
        Integer receiverWalletType = walletSendDTO.getReceiverWalletType();
        if (receiverWalletType == 2) {
            usedLogList.forEach(e -> {
                e.setSubTypeId(walletSendDTO.getSubTypeId());
            });
        }
    }

    /**
     * 构建旺金币发放请求
     *
     * @param walletSendDTO 钱包发送DTO
     * @param usedLogList   使用记录列表
     * @return 旺金币发放请求
     * @throws ApplicationException 当参数为空或业务枚举缺失时抛出异常
     */
    private GoldCoinTransactionGrantRequest buildGoldCoinTransactionGrantRequest(WalletSendDTO walletSendDTO, List<WantWalletLogEntity> usedLogList) {
        // 参数校验
        if (Objects.isNull(walletSendDTO)) {
            throw new ApplicationException("钱包发送参数不能为空");
        }

        if (StringUtils.isBlank(walletSendDTO.getReceiverKey())) {
            throw new ApplicationException("接收者ID不能为空");
        }

        // 获取钱包类型信息
        WantWalletTypeEntity walletTypeEntity = getWalletTypeEntity(walletSendDTO.getReceiverWalletType());

        // 如果使用记录为空，创建默认记录
        List<WantWalletLogEntity> logList = ensureUsedLogList(walletSendDTO, usedLogList);

        // 构建请求对象
        GoldCoinTransactionGrantRequest request = new GoldCoinTransactionGrantRequest();
        GoldCoinTransactionGrantMemberRequest memberRequest = buildMemberRequest(walletSendDTO, logList, walletTypeEntity);

        request.setGrantMemberList(Collections.singletonList(memberRequest));
        request.setRemark(walletSendDTO.getRemark());
        return request;
    }

    /**
     * 确保使用记录列表不为空
     */
    private List<WantWalletLogEntity> ensureUsedLogList(WalletSendDTO walletSendDTO, List<WantWalletLogEntity> usedLogList) {
        if (!CollectionUtils.isEmpty(usedLogList)) {
            return usedLogList;
        }

        // 创建默认记录
        WantWalletLogEntity defaultLogEntity = new WantWalletLogEntity();
        defaultLogEntity.setDeptCode(walletSendDTO.getDeptCode());
        defaultLogEntity.setDeptName(walletSendDTO.getDeptName());
        defaultLogEntity.setSubTypeId(walletSendDTO.getSubTypeId());
        defaultLogEntity.setQuota(walletSendDTO.getQuota());
        defaultLogEntity.setApplyType(walletSendDTO.getApplyType());

        try {
            defaultLogEntity.setMemberKey(Long.parseLong(walletSendDTO.getReceiverKey()));
        } catch (NumberFormatException e) {
            throw new ApplicationException("接收者ID格式错误: " + walletSendDTO.getReceiverKey());
        }

        defaultLogEntity.setRemark(walletSendDTO.getRemark());
        defaultLogEntity.setBoundary(walletSendDTO.getBoundary());

        return Collections.singletonList(defaultLogEntity);
    }

    /**
     * 获取钱包类型实体
     */
    private WantWalletTypeEntity getWalletTypeEntity(Integer receiverWalletType) {
        if (Objects.isNull(receiverWalletType)) {
            throw new ApplicationException("接收者钱包类型不能为空");
        }

        WantWalletTypeEntity walletTypeEntity = wantWalletTypeMapper.selectById(receiverWalletType);
        if (Objects.isNull(walletTypeEntity)) {
            throw new ApplicationException("钱包类型不存在: " + receiverWalletType);
        }

        return walletTypeEntity;
    }

    /**
     * 构建发放用户请求
     */
    private GoldCoinTransactionGrantMemberRequest buildMemberRequest(WalletSendDTO walletSendDTO,
                                                                    List<WantWalletLogEntity> logList,
                                                                    WantWalletTypeEntity walletTypeEntity) {
        GoldCoinTransactionGrantMemberRequest memberRequest = new GoldCoinTransactionGrantMemberRequest();

        // 设置基本信息
        memberRequest.setActIdOrApplyNo(walletSendDTO.getWpCode());
        memberRequest.setCause(walletSendDTO.getCostPurpose());
        memberRequest.setMemberKey(walletSendDTO.getReceiverKey());

        // 设置操作人信息
        setOperatorInfo(memberRequest, walletSendDTO);

        // 设置业务场景
        setTransactionBusinessType(memberRequest, walletSendDTO);

        // 构建发放明细列表
        List<GoldCoinTransactionGrantDetailRequest> detailList = buildGrantDetailList(
            walletSendDTO, logList, walletTypeEntity);
        memberRequest.setGrantDetailList(detailList);

        return memberRequest;
    }

    /**
     * 设置操作人信息
     */
    private void setOperatorInfo(GoldCoinTransactionGrantMemberRequest memberRequest, WalletSendDTO walletSendDTO) {
        memberRequest.setOpUserId(walletSendDTO.getProcessUserId());
        memberRequest.setOpUserName(walletSendDTO.getProcessUserName());
        memberRequest.setOpUserOrganization(walletSendDTO.getProcessUserOrgName());
        memberRequest.setOpUserRole(walletSendDTO.getProcessUserRole());
    }

    /**
     * 设置业务场景类型
     */
    private void setTransactionBusinessType(GoldCoinTransactionGrantMemberRequest memberRequest, WalletSendDTO walletSendDTO) {
        TransactionBusinessTypeEnum transactionBusinessTypeEnum = walletSendDTO.getTransactionBusinessTypeEnum();
        if (Objects.isNull(transactionBusinessTypeEnum)) {
            throw new ApplicationException("业务场景类型不能为空");
        }
        memberRequest.setTransactionBusinessType(transactionBusinessTypeEnum.getCode());
    }

    /**
     * 构建发放明细列表
     */
    private List<GoldCoinTransactionGrantDetailRequest> buildGrantDetailList(WalletSendDTO walletSendDTO,
                                                                           List<WantWalletLogEntity> logList,
                                                                           WantWalletTypeEntity walletTypeEntity) {
        Integer receiverWalletType = walletSendDTO.getReceiverWalletType();

        return logList.stream()
                .map(logEntity -> buildGrantDetailRequest(walletSendDTO, logEntity, walletTypeEntity, receiverWalletType))
                .collect(Collectors.toList());
    }

    /**
     * 构建单个发放明细请求
     */
    private GoldCoinTransactionGrantDetailRequest buildGrantDetailRequest(WalletSendDTO walletSendDTO,
                                                                         WantWalletLogEntity logEntity,
                                                                         WantWalletTypeEntity walletTypeEntity,
                                                                         Integer receiverWalletType) {


        GoldCoinTransactionGrantDetailRequest detailRequest = new GoldCoinTransactionGrantDetailRequest();

        // 旺金币类型
        detailRequest.setAmountSubType(walletTypeEntity.getWpId());

        // 金币子类型唯一编码
        String subTypeId = StringUtils.EMPTY;
        // 产品组币subTypeId为spuId
        if(Objects.equals(receiverWalletType, 2)){
            subTypeId = walletSendDTO.getSubTypeId();
        }else{
            subTypeId = logEntity.getBusinessGroupCode();
        }

        detailRequest.setAmountSubTypeId(subTypeId);



        // 费用类型
        detailRequest.setApplyType(logEntity.getApplyType());

        // 关联流水号
        detailRequest.setAssociatedCode(String.valueOf(logEntity.getLogId()));

        // 部门编码
        String deptCode = StringUtils.isNotBlank(logEntity.getDeptCode())
            ? logEntity.getDeptCode()
            : walletSendDTO.getDeptCode();
        detailRequest.setDepartmentCode(deptCode);

        // 支出相关信息
        setExpenditureInfo(detailRequest, logEntity);

        // 本次发放金额
        detailRequest.setGrantAmount(logEntity.getQuota());

        // 边际费用
        Integer boundary = Objects.nonNull(logEntity.getBoundary())
            ? logEntity.getBoundary()
            : walletSendDTO.getBoundary();
        detailRequest.setMarginalCost(boundary);

        return detailRequest;
    }

    /**
     * 设置支出相关信息
     */
    private void setExpenditureInfo(GoldCoinTransactionGrantDetailRequest detailRequest, WantWalletLogEntity logEntity) {
        final int SPU_COIN_TYPE = 2;

        Integer walletTypeId = logEntity.getWalletTypeId();
        detailRequest.setExpenditureAmountSubType(walletTypeId);

        // 支出币种子类
        String expenditureSubTypeId = Objects.equals(walletTypeId, SPU_COIN_TYPE)
            ? logEntity.getSubTypeId()
            : logEntity.getBusinessGroupCode();
        detailRequest.setExpenditureAmountSubTypeId(expenditureSubTypeId);

        // 支出方产品组ID
        detailRequest.setExpenditureProductGroupId(logEntity.getBusinessGroupCode());
    }

    @Override
    public void personalBatch(GoldCoinTransactionGrantRequest goldCoinTransactionGrantRequest) {
        log.info("【personal want coins send】list:{}", goldCoinTransactionGrantRequest);
        List<ActivityQuotaValidModel> list = new ArrayList<>();

        List<GoldCoinTransactionGrantMemberRequest> grantMemberList = goldCoinTransactionGrantRequest.getGrantMemberList();
        if(CollectionUtils.isEmpty(grantMemberList)) {
            throw new ApplicationException("无发放记录");
        }

        grantMemberList.forEach(e -> {
            String memberKey = e.getMemberKey();

            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, e.getMemberKey()).last("limit 1"));
            if (Objects.nonNull(sfaEmployeeInfoModel)) {

                List<GoldCoinTransactionGrantDetailRequest> grantDetailList = e.getGrantDetailList();

                if(CollectionUtils.isEmpty(grantDetailList)){
                    throw new ApplicationException("发放数据异常");
                }

                grantDetailList.forEach(g -> {
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = Optional.ofNullable(ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, sfaEmployeeInfoModel.getPositionId()))).orElse(new CeoBusinessOrganizationPositionRelation());

                    // 保存发放记录
                    WantWalletPersonalLogEntity wantWalletPersonalLogEntity = new WantWalletPersonalLogEntity();
                    wantWalletPersonalLogEntity.setPositionId(sfaEmployeeInfoModel.getPositionId());
                    WantWalletTypeEntity wantWalletTypeEntity = wantWalletTypeMapper.selectOne(new LambdaQueryWrapper<WantWalletTypeEntity>()
                            .eq(WantWalletTypeEntity::getWpId, g.getAmountSubType()).eq(WantWalletTypeEntity::getDeleteFlag, 0));
                    wantWalletPersonalLogEntity.setWalletTypeId(wantWalletTypeEntity.getId());
                    wantWalletPersonalLogEntity.setSubTypeId(g.getAmountSubTypeId());
                    wantWalletPersonalLogEntity.setQuota(g.getGrantAmount());
                    wantWalletPersonalLogEntity.setMemberKey(Long.valueOf(memberKey));
                    wantWalletPersonalLogEntity.setCreateTime(LocalDateTime.now());
                    wantWalletPersonalLogEntity.setDeleteFlag(0);
                    String associatedCode = g.getAssociatedCode();
                    if(StringUtils.isNotBlank(associatedCode)){
                        wantWalletPersonalLogEntity.setWalletLogId(Long.valueOf(associatedCode));
                    }
                    wantWalletPersonalLogEntity.setRemark(goldCoinTransactionGrantRequest.getRemark());
                    wantWalletPersonalLogEntity.setApplyType(g.getApplyType());
                    wantWalletPersonalLogEntity.setCreateUserId(e.getOpUserId());
                    wantWalletPersonalLogEntity.setReceivePositionTypeId(ceoBusinessOrganizationPositionRelation.getPositionTypeId());
                    wantWalletPersonalLogEntity.setCreateUserName(e.getOpUserName());
                    wantWalletPersonalLogMapper.insert(wantWalletPersonalLogEntity);
                    g.setAssociatedCode(wantWalletPersonalLogEntity.getLogId().toString());
                });

            }else{
                log.error("personal want coins send memberKey:{} not found", memberKey);
            }

        });

        goldCoinConnectorUtil.grantGoldCoin(goldCoinTransactionGrantRequest);
    }

    @Override
    @Transactional
    public void ceoQuickSend(QuotaCeoSendRequest quotaCeoSendRequest, String organizationId, SfaBusinessGroupEntity sfaBusinessGroupEntity, String wpCode) {
        WalletSendDTO walletSendDTO = new WalletSendDTO();
        walletSendDTO.setProcessUserId("CEO");
        walletSendDTO.setProcessUserName("旺铺操作");
        walletSendDTO.setQuota(quotaCeoSendRequest.getQuota());
        walletSendDTO.setBusinessGroup(sfaBusinessGroupEntity.getBusinessGroupCode());
        walletSendDTO.setReceiverType(1);
        walletSendDTO.setReceiverWalletType(quotaCeoSendRequest.getAmountSubType());
        walletSendDTO.setReceiverKey(organizationId);
        walletSendDTO.setSubTypeId(quotaCeoSendRequest.getAmountSubTypeId());
        walletSendDTO.setRemark(quotaCeoSendRequest.getRemark());
        walletSendDTO.setApplyType(quotaCeoSendRequest.getApplyType());
        walletSendDTO.setDeptCode(quotaCeoSendRequest.getDeptCode());
        walletSendDTO.setDeptName(quotaCeoSendRequest.getDeptName());
        walletSendDTO.setWpOperation(quotaCeoSendRequest.getWpOperation());
        walletSendDTO.setBoundary(quotaCeoSendRequest.getBoundary());
        walletSendDTO.setRemark(quotaCeoSendRequest.getRemark());
        walletSendDTO.setWpCode(wpCode);
        this.quickSend(walletSendDTO);
    }

    @Override
    @Transactional
    public void freezeAccount(FreezeAccountRequest freezeAccountRequest) {
        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, freezeAccountRequest.getOrganizationId()).last("limit 1"));
        if (Objects.isNull(ceoBusinessOrganizationPositionRelation) || StringUtils.isBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())) {
            throw new ApplicationException("当前组织无可操作的人员");
        }

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(freezeAccountRequest.getPerson(), RequestUtils.getLoginInfo());

        String employeeId = ceoBusinessOrganizationPositionRelation.getEmployeeId();
        Integer freeze = freezeAccountRequest.getFreeze();
        if (freeze == 1) {
            walletBlackListService.addBlackList(employeeId, 0, null, freezeAccountRequest.getAgentId(), false);
        } else {
            walletBlackListService.deleteBlackList(employeeId, null, 0, personInfo.getEmployeeName());
        }
    }

    @Override
    @Transactional
    public void retrieve(RetrieveRequest retrieveRequest) {
        // 发送发账号信息
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, retrieveRequest.getSourceOrganizationId()));

        List<WantWalletTypeEntity> wantWalletTypeEntities = wantWalletTypeMapper.selectList(new LambdaQueryWrapper<WantWalletTypeEntity>().eq(WantWalletTypeEntity::getDeleteFlag, 0));

        CeoBusinessOrganizationPositionRelation personInfo = null;
        if (StringUtils.isNotBlank(retrieveRequest.getPerson())) {
            personInfo = checkCustomerService.getPersonInfo(retrieveRequest.getPerson(), RequestUtils.getLoginInfo());
        } else {
            personInfo = new CeoBusinessOrganizationPositionRelation();
            personInfo.setEmployeeId("ROOT");
            personInfo.setEmployeeName("system");
        }


        CeoBusinessOrganizationPositionRelation finalPersonInfo = personInfo;
        wantWalletTypeEntities.forEach(e -> {

            Integer walletType = e.getId();

            BigDecimal surplusQuota = Optional.ofNullable(wantWalletLogMapper.selectSurplusQuota(retrieveRequest.getSourceOrganizationId(), walletType, null, null)).orElse(BigDecimal.ZERO);

            if (surplusQuota.compareTo(BigDecimal.ZERO) > 0) {
                if (walletType != 2) {
                    WalletSendDTO walletSendDTO = new WalletSendDTO();
                    walletSendDTO.setSenderOrgCode(retrieveRequest.getSourceOrganizationId());
                    walletSendDTO.setSendWalletType(walletType);
                    walletSendDTO.setQuota(surplusQuota);
                    walletSendDTO.setReceiverWalletType(walletType);
                    walletSendDTO.setReceiverKey(retrieveRequest.getTargetOrganizationId());
                    walletSendDTO.setReceiverType(1);
                    walletSendDTO.setRemark("回收");
                    walletSendDTO.setProcessUserId(finalPersonInfo.getEmployeeId());
                    walletSendDTO.setProcessUserName(finalPersonInfo.getEmployeeName());
                    this.quickSend(walletSendDTO);

                } else {
                    // spu币
                    List<WantWalletLogEntity> logEntities = wantWalletLogMapper.selectList(new LambdaQueryWrapper<WantWalletLogEntity>()
                            .eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId())
                            .eq(WantWalletLogEntity::getWalletTypeId, walletType)
                            .gt(WantWalletLogEntity::getSurplus, 0)
                            .eq(WantWalletLogEntity::getType, 1)
                            .eq(WantWalletLogEntity::getDeleteFlag, 0)
                    );
                    if (!CollectionUtils.isEmpty(logEntities)) {
                        logEntities.forEach(l -> {
                            WalletSendDTO walletSendDTO = new WalletSendDTO();
                            walletSendDTO.setSenderOrgCode(retrieveRequest.getSourceOrganizationId());
                            walletSendDTO.setSendWalletType(walletType);
                            walletSendDTO.setQuota(l.getSurplus());
                            walletSendDTO.setReceiverWalletType(walletType);
                            walletSendDTO.setReceiverKey(retrieveRequest.getTargetOrganizationId());
                            walletSendDTO.setReceiverType(1);
                            walletSendDTO.setSubTypeId(l.getSubTypeId());
                            walletSendDTO.setRemark("回收");
                            walletSendDTO.setProcessUserId(finalPersonInfo.getEmployeeId());
                            walletSendDTO.setProcessUserName(finalPersonInfo.getEmployeeName());
                            this.quickSend(walletSendDTO);
                        });
                    }
                }
            }
        });


    }

    @Override
    @Transactional
    public String ceoRevert(CeoQuotaRevertRequest ceoQuotaRevertRequest) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(ceoQuotaRevertRequest.getPerson(), RequestUtils.getLoginInfo());

        WantWalletPersonalLogEntity wantWalletPersonalLogEntity = wantWalletPersonalLogMapper.selectById(ceoQuotaRevertRequest.getLogId());
        if (Objects.isNull(wantWalletPersonalLogEntity)) {
            throw new ApplicationException("发放记录不存在");
        }

        // 调用旺铺回收接口
        CeoRevertModel ceoRevertModel = new CeoRevertModel();
        ceoRevertModel.setGrantId(String.valueOf(wantWalletPersonalLogEntity.getLogId()));
        ceoRevertModel.setMemberKey(ceoQuotaRevertRequest.getMemberKey());
        ceoRevertModel.setOrganizationName(ceoQuotaRevertRequest.getExpenditure());
        ceoRevertModel.setSource(0);
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
        ceoRevertModel.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
        BigDecimal quota = activityQuotaConnectorUtil.ceoRevert(ceoRevertModel);


        wantWalletPersonalLogEntity.setIsRevert(1);
        wantWalletPersonalLogEntity.setRevertUserId(personInfo.getEmployeeId());
        wantWalletPersonalLogEntity.setRevertUserName(personInfo.getEmployeeName());
        wantWalletPersonalLogEntity.setRevertQuota(quota);
        wantWalletPersonalLogMapper.updateById(wantWalletPersonalLogEntity);

        return "回收额度 " + quota.toString();
    }

    @Override
    public void freezeAccountBatch(FreezeBatchRequest freezeBatchRequest) {
        List<String> organizationIds = freezeBatchRequest.getOrganizationIds();
        organizationIds.forEach(e -> {
            FreezeAccountRequest freezeAccountRequest = new FreezeAccountRequest();
            freezeAccountRequest.setOrganizationId(e);
            freezeAccountRequest.setAgentId(freezeBatchRequest.getAgentId());
            freezeAccountRequest.setFreeze(freezeBatchRequest.getFreeze());
            freezeAccountRequest.setPerson(freezeBatchRequest.getPerson());
            freezeAccount(freezeAccountRequest);
        });
    }

    @Override
    public void retrieveByMemberKey(PersonRetrieveRequest personRetrieveRequest) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(personRetrieveRequest.getPerson(), RequestUtils.getLoginInfo());

        List<WantWalletPersonalLogEntity> wantWalletPersonalLogEntities = wantWalletPersonalLogMapper.selectList(new LambdaQueryWrapper<WantWalletPersonalLogEntity>()
                .eq(WantWalletPersonalLogEntity::getMemberKey, personRetrieveRequest.getMemberKey())
                .eq(WantWalletPersonalLogEntity::getDeleteFlag, 0).isNotNull(WantWalletPersonalLogEntity::getLogId)
                .eq(WantWalletPersonalLogEntity::getIsRevert, 0)
        );

        if (CollectionUtils.isEmpty(wantWalletPersonalLogEntities)) {
            return;
        }

        String organizationName = organizationMapper.getOrganizationName(personRetrieveRequest.getOrganizationId());

        wantWalletPersonalLogEntities.forEach(e -> {
            // 调用旺铺回收接口
            CeoRevertModel ceoRevertModel = new CeoRevertModel();
            ceoRevertModel.setGrantId(String.valueOf(e.getLogId()));
            ceoRevertModel.setMemberKey(personRetrieveRequest.getMemberKey());
            ceoRevertModel.setOrganizationName(organizationName);
            ceoRevertModel.setSource(0);
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
            ceoRevertModel.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
            BigDecimal quota = null;
            try {
                quota = activityQuotaConnectorUtil.ceoRevert(ceoRevertModel);
            } catch (Exception ex) {
                log.info("【旺铺回收失败】ex:{}", ex.getMessage());
            }


            e.setIsRevert(1);
            e.setRevertUserId(personInfo.getEmployeeId());
            e.setRevertUserName(personInfo.getEmployeeName());
            e.setRevertQuota(quota);
            wantWalletPersonalLogMapper.updateById(e);
        });


    }

    @Override
    @Transactional
    public void rewardsBatch(RewardsBatchRequest rewardsBatchRequest) {
        // 查看审核人
        List<WalletRewardsLog> logs = new ArrayList<>();

        List<RewardsRequest> rewardsRequests = rewardsBatchRequest.getRewardsRequests();
        for (RewardsRequest rewardsRequest : rewardsRequests) {
            WalletRewardsLog walletRewardsLog = WalletRewardsLog.builder().batchId(rewardsBatchRequest.getBatchId())
                    .organizationId(rewardsRequest.getOrganizationId()).quota(rewardsRequest.getQuota()).build();
            walletRewardsLog.init(rewardsBatchRequest.getEmpId(), rewardsBatchRequest.getEmpName());

            try {

                if(!redisUtil.setLockWithWait(REWARDS_SEND_LOCK, rewardsBatchRequest.getBatchId().toString(), 10, TimeUnit.SECONDS, 10000, 1000)){
                    walletRewardsLog.setStatus(0);
                    walletRewardsLog.setErrLog("发放奖励，抢占锁失败");
                    logs.add(walletRewardsLog);
                    continue;
                }

                WalletSendDTO walletSendDTO = new WalletSendDTO();
                walletSendDTO.setProcessUserId(rewardsBatchRequest.getEmpId());
                walletSendDTO.setProcessUserName(rewardsBatchRequest.getEmpName());
                walletSendDTO.setQuota(rewardsRequest.getQuota());
                walletSendDTO.setBusinessGroup(rewardsRequest.getBusinessGroupCode());
                walletSendDTO.setReceiverType(1);
                walletSendDTO.setReceiverWalletType(rewardsRequest.getAmountSubType());
                walletSendDTO.setReceiverKey(rewardsRequest.getOrganizationId());
                walletSendDTO.setSubTypeId(rewardsRequest.getAmountSubTypeId());
                walletSendDTO.setRemark(rewardsRequest.getRemark());
                walletSendDTO.setApplyType(rewardsRequest.getApplyType());
                walletSendDTO.setDeptCode(rewardsRequest.getDeptCode());
                walletSendDTO.setDeptName(rewardsRequest.getDeptName());
                walletSendDTO.setBoundary(rewardsRequest.getBoundary());
                walletSendDTO.setRemark(rewardsRequest.getRemark());
                this.quickSend(walletSendDTO);
                walletRewardsLog.setStatus(1);
            } catch (Exception e) {
                walletRewardsLog.setStatus(0);
                walletRewardsLog.setErrLog(e.getMessage());
            } finally {
                redisUtil.unLock(REWARDS_SEND_LOCK, rewardsBatchRequest.getBatchId().toString());
                logs.add(walletRewardsLog);
            }
        }

        if (!CollectionUtils.isEmpty(logs)) {
            walletRewardsLogMapper.batchInsert(logs);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String send(String encryption) {
        // AES解密
        AES aes = new AES(Mode.ECB, Padding.PKCS5Padding,AES_KEY.getBytes());
        String jsonStr = aes.decryptStr(encryption);
        log.info("【coins send】decryptStr:{}", jsonStr);

        CeoQuotaSendRequest ceoQuotaSendRequest = JacksonHelper.toObj(jsonStr, CeoQuotaSendRequest.class, true);
        if(Objects.isNull(ceoQuotaSendRequest)){
            throw new ApplicationException("数据异常");
        }
        // 参数校验
        validateAndProcessRequest(ceoQuotaSendRequest);

        // 创建发放记录
        SfaWalletSendLog sfaWalletSendLog = initLog(ceoQuotaSendRequest);

//        if(!redisUtil.setLockIfAbsent(QUICK_SEND_LOCK, ceoQuotaSendRequest.getReceiveOrganizationId(), 5, TimeUnit.SECONDS)){
//           throw new ApplicationException("请求正在处理中");
//        }

        try {
            WalletSendDTO build = WalletSendDTO.builder().receiverKey(ceoQuotaSendRequest.getReceiveOrganizationId())
                    .receiverWalletType(ceoQuotaSendRequest.getReceiveWalletType())
                    .receiverType(1)
                    .sendWalletType(ceoQuotaSendRequest.getSendWalletType())
                    .subTypeId(ceoQuotaSendRequest.getSubTypeId())
                    .senderOrgCode(ceoQuotaSendRequest.getSendOrganizationId())
                    .quota(ceoQuotaSendRequest.getQuota())
                    .businessGroup(ceoQuotaSendRequest.getBusinessGroup().toString())
                    .processUserId("CEO")
                    .processUserName("旺铺操作")
                    .verifyAuditFlowCheck(false)
                    .wpCode(ceoQuotaSendRequest.getWpCode())
                    .remark(ceoQuotaSendRequest.getRemark())
                    .deptCode(ceoQuotaSendRequest.getDeptCode())
                    .deptName(ceoQuotaSendRequest.getDeptName())
                    .applyType(ceoQuotaSendRequest.getApplyType())
                    .boundary(ceoQuotaSendRequest.getBoundary()).build();
            this.quickSend(build);

            // 设置成功状态
            sfaWalletSendLog.setStatus(1);

            // 在同一事务中插入日志
            walletSendLogMapper.insert(sfaWalletSendLog);

            return sfaWalletSendLog.getCode();
        } catch(Exception e){
            // 设置错误信息
            sfaWalletSendLog.setStatus(2);  // 设置失败状态
            sfaWalletSendLog.setErrMsg(e.getMessage());

            // 在当前事务中插入失败日志（这个操作会在单独的事务中执行）
            try {
                // 使用新事务插入日志
                walletService.saveFailLog(sfaWalletSendLog);
            } catch (Exception logEx) {
                log.error("保存失败日志异常: {}", logEx.getMessage(), logEx);
            }

            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            return sfaWalletSendLog.getCode();
        }

    }

    @Override
    public void saveFailLog(SfaWalletSendLog sfaWalletSendLog) {
        walletSendLogMapper.insert(sfaWalletSendLog);

    }

    private void validateAndProcessRequest(CeoQuotaSendRequest ceoQuotaSendRequest) {
        Integer type = ceoQuotaSendRequest.getType();

        switch (type) {
            case 1:
                validateAndSetReceiveOrganization(ceoQuotaSendRequest);
                validateAndSetSendOrganization(ceoQuotaSendRequest);
                break;
            case 2:
                validateAndSetReceiveOrganization(ceoQuotaSendRequest);
                break;
            default:
                throw new ApplicationException("未知类型：" + type);
        }
    }

    private void validateAndSetReceiveOrganization(CeoQuotaSendRequest ceoQuotaSendRequest) {
        if (StringUtils.isBlank(ceoQuotaSendRequest.getReceiveOrganizationName())) {
            throw new ApplicationException("请选择接受组织");
        }
        if (StringUtils.isBlank(ceoQuotaSendRequest.getReceiverBusinessGroupCode())) {
            throw new ApplicationException("请选择接受组织产品组");
        }

        SfaBusinessGroupEntity receiveBusinessGroup = sfaBusinessGroupMapper.selectBusinessGroupByCode(ceoQuotaSendRequest.getReceiverBusinessGroupCode());
        if (receiveBusinessGroup == null) {
            throw new ApplicationException("接受组织产品组不存在");
        }

        String receiveOrganizationId = organizationMapper.getOrganizationIdByName(
                ceoQuotaSendRequest.getReceiveOrganizationName(),
                3,
                receiveBusinessGroup.getId()
        );
        if (StringUtils.isBlank(receiveOrganizationId)) {
            throw new ApplicationException("接受组织不存在");
        }
        ceoQuotaSendRequest.setReceiveOrganizationId(receiveOrganizationId);
        ceoQuotaSendRequest.setBusinessGroup(receiveBusinessGroup.getId());
    }

    private void validateAndSetSendOrganization(CeoQuotaSendRequest ceoQuotaSendRequest) {
        if (StringUtils.isBlank(ceoQuotaSendRequest.getSendOrganizationName())) {
            throw new ApplicationException("请选择发放组织");
        }
        if (StringUtils.isBlank(ceoQuotaSendRequest.getSendBusinessGroupCode())) {
            throw new ApplicationException("请选择发放组织产品组");
        }

        SfaBusinessGroupEntity sendBusinessGroup = sfaBusinessGroupMapper.selectBusinessGroupByCode(ceoQuotaSendRequest.getSendBusinessGroupCode());
        if (sendBusinessGroup == null) {
            throw new ApplicationException("发放组织产品组不存在");
        }

        String sendOrganizationId = organizationMapper.getOrganizationIdByName(
                ceoQuotaSendRequest.getSendOrganizationName(),
                3,
                sendBusinessGroup.getId()
        );
        if (StringUtils.isBlank(sendOrganizationId)) {
            throw new ApplicationException("发放组织不存在");
        }
        ceoQuotaSendRequest.setSendOrganizationId(sendOrganizationId);
    }

    private SfaWalletSendLog initLog(CeoQuotaSendRequest ceoQuotaSendRequest) {
        long id = IdUtil.getSnowflake().nextId();
        SfaWalletSendLog sfaWalletSendLog = new SfaWalletSendLog();
        BeanUtils.copyProperties(ceoQuotaSendRequest, sfaWalletSendLog);
        sfaWalletSendLog.setCode("sfa" + id);
        return sfaWalletSendLog;
    }


    private void saleAmountProcess(WalletSendDTO walletSendDTO, List<WantWalletLogEntity> usedLogList) {
        String receiverKey = walletSendDTO.getReceiverKey();
        String organizationType = organizationMapper.getOrganizationType(receiverKey);
        if (!organizationType.equals("company")) {
            return;
        }
        if (CollectionUtils.isEmpty(usedLogList)) {
            usedLogList = new ArrayList<>();
            WantWalletLogEntity wantWalletLogEntity = new WantWalletLogEntity();
            wantWalletLogEntity.setDeptCode(walletSendDTO.getDeptCode());
            wantWalletLogEntity.setDeptName(walletSendDTO.getDeptName());
            wantWalletLogEntity.setSubTypeId(walletSendDTO.getSubTypeId());
            wantWalletLogEntity.setApplyType(walletSendDTO.getApplyType());
            wantWalletLogEntity.setQuota(walletSendDTO.getQuota());
            wantWalletLogEntity.setRemark(walletSendDTO.getRemark());
            wantWalletLogEntity.setBoundary(walletSendDTO.getBoundary());
            usedLogList.add(wantWalletLogEntity);
        }

        List<WantWalletLogEntity> collect = usedLogList.stream().filter(f -> f.getApplyType() == 14 || f.getApplyType() == 27).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            collect.forEach(e -> {
                UpdateBatchSaleAmountModel model = new UpdateBatchSaleAmountModel();
                model.setAmount(e.getQuota());
                model.setApplyType(Integer.parseInt(e.getSubTypeId()));
                model.setFlag(1);
                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(walletSendDTO.getBusinessGroup());
                model.setProductGroupId(sfaBusinessGroupEntity.getBusinessGroupCode());
                model.setCompanyName(organizationMapper.getOrganizationName(walletSendDTO.getReceiverKey()));

                model.setYearMonthStart(walletSendDTO.getStartYearMonth());
                model.setYearMonthEnd(walletSendDTO.getEndYearMonth());
                activityQuotaConnectorUtil.updateBatchSaleAmountForSfa(model);
            });
        }
    }

    private Map<String, String> getRecord(WalletSendDTO walletSendDTO) {
        Map<String, String> map = new HashMap<>();
        String senderOrgCode = walletSendDTO.getSenderOrgCode();
        if (StringUtils.isBlank(senderOrgCode)) {
            map.put(EXPENDITURE_KEY, "总部");
        } else {
            String organizationName = organizationMapper.getOrganizationName(walletSendDTO.getSenderOrgCode());
            map.put(EXPENDITURE_KEY, organizationName);
        }

        // 组织增加
        if (walletSendDTO.getReceiverType() == 1) {
            String receiverOrg = organizationMapper.getOrganizationName(walletSendDTO.getReceiverKey());
            map.put(REVENUE_KEY, receiverOrg);
        } else {
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, walletSendDTO.getReceiverKey()));
            if (Objects.isNull(sfaEmployeeInfoModel)) {
                throw new ApplicationException("发放人不存在");
            }

            SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getPositionId, walletSendDTO.getPositionId())
                    .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).last("limit 1"));
            if (Objects.isNull(sfaPositionRelationEntity)) {
                throw new ApplicationException("岗位信息错误");
            }

            String orgName = sfaPositionRelationEntity.getAreaName();
            if (StringUtils.isNotBlank(sfaPositionRelationEntity.getVareaName())) {
                orgName = sfaPositionRelationEntity.getVareaName();
            }
            if (StringUtils.isNotBlank(sfaPositionRelationEntity.getProvinceName())) {
                orgName = sfaPositionRelationEntity.getProvinceName();
            }
            if (StringUtils.isNotBlank(sfaPositionRelationEntity.getCompanyName())) {
                orgName = sfaPositionRelationEntity.getCompanyName();
            }
            if (StringUtils.isNotBlank(sfaPositionRelationEntity.getDepartmentName())) {
                orgName = sfaPositionRelationEntity.getDepartmentName();
            }

            map.put(REVENUE_KEY, MessageFormat.format(revenueMsg, orgName, sfaEmployeeInfoModel.getEmployeeName(), sfaEmployeeInfoModel.getMobile()));
        }

        return map;
    }

    private WalletAddDTO convertToAddDTO(WalletSendDTO walletSendDTO, Map<String, String> recordMap, List<WantWalletLogEntity> usedLogList) {
        // 如果账号不存在，先创建账号
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, walletSendDTO.getReceiverKey()).eq(WantWalletAccountEntity::getDeleteFlag, 0));

        if (Objects.isNull(wantWalletAccountEntity)) {
            CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
            createWalletAccountDTO.setWalletType(walletSendDTO.getReceiverWalletType());
            createWalletAccountDTO.setOrganizationId(walletSendDTO.getReceiverKey());
            createWalletAccountDTO.setProcessUserName(walletSendDTO.getProcessUserName());
            createWalletAccountDTO.setProcessUserId(walletSendDTO.getProcessUserId());
            walletService.createAccount(createWalletAccountDTO);

            wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, walletSendDTO.getReceiverKey()).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        } else {
            // 检查账号是否开启对应的账号类型,如没有则开启
            WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletEntity::getWalletTypeId, walletSendDTO.getReceiverWalletType()).eq(WantWalletEntity::getDeleteFlag, 0));
            if (Objects.isNull(wantWalletEntity)) {
                CreateWalletAccountDTO createWalletAccountDTO = new CreateWalletAccountDTO();
                createWalletAccountDTO.setOrganizationId(walletSendDTO.getReceiverKey());
                createWalletAccountDTO.setWalletType(walletSendDTO.getReceiverWalletType());
                createWalletAccountDTO.setProcessUserName(walletSendDTO.getProcessUserName());
                createWalletAccountDTO.setProcessUserId(walletSendDTO.getProcessUserId());
                walletService.createAccount(createWalletAccountDTO);
            }
        }


        WalletAddDTO walletAddDTO = new WalletAddDTO();
        walletAddDTO.setProcessUserName(walletSendDTO.getProcessUserName());
        walletAddDTO.setProcessUserId(walletSendDTO.getProcessUserId());
        walletAddDTO.setWpCode(walletSendDTO.getWpCode());
        walletAddDTO.setRevenue(recordMap.get(REVENUE_KEY));
        walletAddDTO.setExpenditure(recordMap.get(EXPENDITURE_KEY));
        walletAddDTO.setWalletTypeId(walletSendDTO.getReceiverWalletType());
        walletAddDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletAddDTO.setRemark(walletSendDTO.getRemark());

        List<WalletAddDetailDTO> detailDTOS = new ArrayList<>();
        // 通过组织发放而来
        if (!CollectionUtils.isEmpty(usedLogList)) {
            usedLogList.forEach(e -> {
                WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
                walletAddDetailDTO.setSubTypeId(e.getSubTypeId());
                walletAddDetailDTO.setApplyType(e.getApplyType());
                walletAddDetailDTO.setCostPurpose(walletSendDTO.getCostPurpose());
                walletAddDetailDTO.setBoundary(e.getBoundary());
                walletAddDetailDTO.setWpOperation(e.getWpOperation());
                walletAddDetailDTO.setQuota(e.getQuota());
                walletAddDetailDTO.setDeptName(e.getDeptName());
                walletAddDetailDTO.setDeptCode(e.getDeptCode());
                detailDTOS.add(walletAddDetailDTO);
            });
        } else {
            WalletAddDetailDTO walletAddDetailDTO = new WalletAddDetailDTO();
            BeanUtils.copyProperties(walletSendDTO, walletAddDetailDTO);
            detailDTOS.add(walletAddDetailDTO);
        }
        walletAddDTO.setDetailDTOList(detailDTOS);
        return walletAddDTO;
    }

    private WalletUsedDTO convertToUsedDTO(WalletSendDTO walletSendDTO, Map<String, String> recordMap) {

        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, walletSendDTO.getSenderOrgCode()).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        if (Objects.isNull(wantWalletAccountEntity)) {
            throw new ApplicationException("组织账号未创建");
        }

        Integer businessGroupById = organizationMapper.getBusinessGroupById(walletSendDTO.getSenderOrgCode());
        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroupById);

        WalletUsedDTO walletUsedDTO = new WalletUsedDTO();
        BeanUtils.copyProperties(walletSendDTO, walletUsedDTO);
        walletUsedDTO.setWalletTypeId(walletSendDTO.getSendWalletType());
        walletUsedDTO.setWalletAccountId(wantWalletAccountEntity.getAccountId());
        walletUsedDTO.setExpenditure(recordMap.get(EXPENDITURE_KEY));
        walletUsedDTO.setRevenue(recordMap.get(REVENUE_KEY));
        walletUsedDTO.setSubTypeId(walletSendDTO.getSubTypeId());
        walletUsedDTO.setCostPurpose(walletSendDTO.getCostPurpose());
        walletUsedDTO.setBusinessGroupCode(sfaBusinessGroupEntity.getBusinessGroupCode());
        Integer receiverType = walletSendDTO.getReceiverType();
        if (receiverType == 2) {
            walletUsedDTO.setMemberKey(Long.parseLong(walletSendDTO.getReceiverKey()));
        }


        return walletUsedDTO;
    }
}
