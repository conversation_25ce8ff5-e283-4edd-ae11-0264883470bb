package com.wantwant.sfa.backend.service.meeting.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.google.common.collect.Lists;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.activityQuota.model.QuotaInfo;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.entity.SfaPositionEmp;
import com.wantwant.sfa.backend.arch.vo.ArchEmpVo;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.BusinessGroupEnum;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.display.enums.ProcessType;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationTreeEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationViewEntity;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.leave.entity.SfaLeave;
import com.wantwant.sfa.backend.map.request.RealtimePositioningListRequest;
import com.wantwant.sfa.backend.map.service.MapService;
import com.wantwant.sfa.backend.map.vo.RealtimePositioningVo;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.arch.RoleEmployeeRelationMapper;
import com.wantwant.sfa.backend.mapper.arch.SfaPositionEmpMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.businessTrip.BusinessTripMapper;
import com.wantwant.sfa.backend.mapper.leave.SfaLeaveMapper;
import com.wantwant.sfa.backend.mapper.meeting.*;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.review.SfaReviewReportMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskAssignMapper;
import com.wantwant.sfa.backend.mapper.task.SfaTaskMapper;
import com.wantwant.sfa.backend.mapper.task.TaskMeetingMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportCalendarMapper;
import com.wantwant.sfa.backend.mapper.workReport.WorkReportMapper;
import com.wantwant.sfa.backend.meeting.request.*;
import com.wantwant.sfa.backend.meeting.vo.*;
import com.wantwant.sfa.backend.model.*;
import com.wantwant.sfa.backend.model.businessTrip.BusinessTripPO;
import com.wantwant.sfa.backend.model.meeting.*;
import com.wantwant.sfa.backend.model.organization.bind.OrganizationRelationModel;
import com.wantwant.sfa.backend.notify.enums.NotifyTypeEnum;
import com.wantwant.sfa.backend.organization.vo.OrgVo;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.review.dto.PromiseDTO;
import com.wantwant.sfa.backend.review.entity.SfaReviewReportEntity;
import com.wantwant.sfa.backend.review.service.IReviewReportSearchService;
import com.wantwant.sfa.backend.review.vo.ReviewReportVo;
import com.wantwant.sfa.backend.saleManage.enums.SaleActionEnum;
import com.wantwant.sfa.backend.saleManage.model.SaleBehaviorUpdateModel;
import com.wantwant.sfa.backend.saleManage.service.ISaleManageService;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.service.NotifyService;
import com.wantwant.sfa.backend.service.meeting.*;
import com.wantwant.sfa.backend.service.meeting.dto.MeetingModifyModelDTO;
import com.wantwant.sfa.backend.service.meeting.dto.MeetingSignInfoDTO;
import com.wantwant.sfa.backend.service.meeting.dto.OrgEmpDTO;
import com.wantwant.sfa.backend.service.meeting.dto.SaleDateDTO;
import com.wantwant.sfa.backend.service.meeting.entity.MeetingDinnerFileEntity;
import com.wantwant.sfa.backend.service.meeting.entity.MeetingDinnerInfoEntity;
import com.wantwant.sfa.backend.service.meeting.entity.MeetingIssueEntity;
import com.wantwant.sfa.backend.task.dto.TaskLogDTO;
import com.wantwant.sfa.backend.task.entity.SfaTaskAssignEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskEntity;
import com.wantwant.sfa.backend.task.entity.SfaTaskMeetingEntity;
import com.wantwant.sfa.backend.task.enums.TaskLogTypeEnum;
import com.wantwant.sfa.backend.task.service.ITaskLogService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.workReport.PO.WorkReportCalendarPO;
import com.wantwant.sfa.backend.workReport.entity.SfaWorkReportEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* 线下会议信息 服务实现类
*
* @since 2024-02-21
*/
@Slf4j
@Service

public class MeetingInfoServiceImpl extends ServiceImpl<MeetingInfoMapper, MeetingInfoPO> implements MeetingInfoService {

	private static final String MEETING_VERSION = "V3.0.0";
	@Resource
	private MeetingFileService meetingFileService;
	@Resource
	private EmployeeMapper employeeMapper;

	@Resource
	private MeetingRecordService meetingRecordService;

	@Resource
	private MeetingSummaryService meetingSummaryService;

	@Autowired
	private GeTuiUtil geTuiUtil;

	@Autowired
	private MeetingIssueMapper meetingIssueMapper;

	@Autowired
	private IReviewReportSearchService reviewReportSearchService;
	@Autowired
	private OrganizationAddressMapper organizationAddressMapper;
	@Autowired
	private ICheckCustomerService checkCustomerService;
	@Autowired
	private SfaPositionRelationMapper sfaPositionRelationMapper;
	@Autowired
	private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
	@Autowired
	private ApplyMemberMapper applyMemberMapper;
	@Autowired
	private CeoBusinessOrganizationTreeMapper ceoBusinessOrganizationTreeMapper;

	@Autowired
	private OrganizationMapper organizationMapper;
	@Resource
	private SfaPositionEmpMapper sfaPositionEmpMapper;
	@Resource
	private RedisUtil redisUtil;
	@Resource
	private WorkReportMapper workReportMapper;
	@Resource
	private MeetingConnectUtil meetingConnectUtil;
	@Resource
	private IMeetingPromiseService meetingPromiseService;
	@Resource
	private WorkReportCalendarMapper workReportCalendarMapper;
	@Resource
	private ConfigMapper configMapper;

	private static final String NEXT_ORG_KEY = "sfa:next:org:";
	@Resource
	private IAuditService auditService;

	@Resource
	private SfaLeaveMapper sfaLeaveMapper;

	@Resource
	private NotifyService notifyService;
	@Resource
	private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
	@Resource
	private MeetingRecordMapper meetingRecordMapper;
	@Resource
	private MeetingSummaryMapper meetingSummaryMapper;
	@Resource
	private MeetingDinnerInfoMapper meetingDinnerInfoMapper;
	@Resource
	private MeetingDinnerFileMapper meetingDinnerFileMapper;
	@Resource
	private SfaTaskAssignMapper sfaTaskAssignMapper;
	@Resource
	private TaskMeetingMapper taskMeetingMapper;
	@Resource
	private SfaTaskMapper sfaTaskMapper;
	@Resource
	private ITaskLogService taskLogService;
	@Resource
	private MeetingBigDataMapper meetingBigDataMapper;
	@Resource
	private BusinessTripMapper businessTripMapper;
	@Resource
	private SfaBusinessGroupMapper sfaBusinessGroupMapper;

	@Resource
	private AuthService authService;
	@Resource
	private CeoBusinessOrganizationViewMapper ceoBusinessOrganizationViewMapper;
	@Resource
	private MapService mapService;
	@Resource
	private WwOrganizationAreaMapper wwOrganizationAreaMapper;

	@Resource
	private OrganizationBindRelationMapper organizationBindRelationMapper;
	@Resource
	private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
	@Resource
	private SfaReviewReportMapper reviewReportMapper;
	@Resource
	private MeetingAuditMapper meetingAuditMapper;
	@Resource
	private ISaleManageService saleManageService;
	@Resource
	private RabbitMQSender rabbitMQSender;
	/**
	 * 会议列表
	 */
	@Override
	public IPage<MeetingInfoVO> queryByPage(MeetingQueryRequest request) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		request.setBusinessGroup(loginInfo.getBusinessGroup());
		request.setOrganizationType(loginInfo.getOrganizationType());
		String organizationCode = request.getOrganizationCode();


		if(StringUtils.isNotBlank(organizationCode)){
			request.setSearchOrganizationType(organizationMapper.getOrganizationType(organizationCode));
		}



		Page<MeetingInfoVO> page = new Page<>(request.getPage(), request.getRows());
		List<MeetingInfoVO> list = baseMapper.selectPageBySql(page, request);

		list.forEach(e -> {

			Integer businessGroup = organizationMapper.getBusinessGroupById(e.getOrganizationId());
			if(businessGroup == 99){
				e.setAllGroup(true);
			}

			List<MeetingUserVO> meetingUserVOS = baseMapper.selectMeetingUsers(e.getInfoId());
			meetingUserVOS.forEach(m -> {
				String organizationType = m.getOrganizationType();

				String organizationId = m.getOrganizationId();
				Integer businessGroupById = organizationMapper.getBusinessGroupById(organizationId);

				SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroupById);
				m.setBusinessGroupName(sfaBusinessGroupEntity.getBusinessGroupName());


				Integer ceoType = m.getCeoType();
				Integer jobsType = m.getJobsType();
				Integer position = m.getPosition();



				if(Objects.nonNull(ceoType) && Objects.nonNull(jobsType) && Objects.nonNull(position)){
					m.setPositionName(PositionEnum.getPositionName(ceoType,jobsType,position));
				}else{
					m.setPositionName(OrganizationTypeEnum.getPositionName(m.getOrganizationType()));
				}
			});
			e.setMeetingUserVOS(meetingUserVOS);


			String category = e.getCategory();
			if(category.equals("月会/季会") || category.equals("聚餐")){
				// 创建会议后主会人/负责人即可点击开始
				if(Objects.isNull(e.getActualStartTime()) && (e.getLeaderId().equals(request.getEmployeeId())|| e.getCreateBy().equals(request.getEmployeeId()))){
					e.setStartFlag(1);
				}
			}
			String organizationType = organizationMapper.getOrganizationType(e.getOrganizationId());
			e.setOrganizationType(organizationType);

			if(category.equals("日会")){
				List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, e.getInfoId()).eq(MeetingRecordPO::getRoleFlag, 0).eq(MeetingRecordPO::getIsDelete, 0));
				if(!CollectionUtils.isEmpty(meetingRecordPOS)){
					for (MeetingRecordPO meetingRecordPO : meetingRecordPOS) {
						String curOrgType = organizationMapper.getOrganizationType(meetingRecordPO.getOrganizationId());
						if(StringUtils.isNotBlank(curOrgType) && !curOrgType.equals("zb") && !curOrgType.equals("branch") ){
							e.setFileOrgCode(e.getOrganizationId());
							break;
						}
					}
				}
			}

			if(category.equals("聚餐") && e.getStatus() == 2){
				// 获取聚餐信息
				e.setCommitTime(e.getUpdateTime());

			}


			if(category.equals("日会") || category.equals("其他")){
				Integer count = meetingRecordMapper.selectCount(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, e.getInfoId()).isNotNull(MeetingRecordPO::getQuestion).eq(MeetingRecordPO::getIsDelete, 0));
				if(Objects.nonNull(count) && count > 0){
					e.setProblemFeedback("true");
				}
			}else{
				Integer count = meetingIssueMapper.selectCount(new LambdaQueryWrapper<MeetingIssueEntity>().eq(MeetingIssueEntity::getInfoId, e.getInfoId()).eq(MeetingIssueEntity::getDeleteFlag, 0));
				if(Objects.nonNull(count) && count > 0){
					e.setProblemFeedback("true");
				}
			}


			String avatar = baseMapper.getAvatar(e.getCreateBy());
			e.setAvatar(avatar);
		});

		page.setRecords(list);
		return page;
	}

	@Override
	public void exportList(MeetingQueryRequest request, HttpServletResponse response) {
		LoginModel loginInfo = RequestUtils.getLoginInfo();
		request.setBusinessGroup(loginInfo.getBusinessGroup());
		request.setOrganizationType(loginInfo.getOrganizationType());
		String organizationCode = request.getOrganizationCode();


		if(StringUtils.isNotBlank(organizationCode)){
			request.setSearchOrganizationType(organizationMapper.getOrganizationType(organizationCode));
		}

		List<MeetingExportVO> list = baseMapper.selectListBySql(request);
		if(!CollectionUtils.isEmpty(list)){
			list.forEach(e -> {
				Integer ceoType = e.getCeoType();
				Integer jobsType = e.getJobsType();
				Integer position = e.getPosition();
				if(Objects.isNull(ceoType) || Objects.isNull(jobsType) || Objects.isNull(position)){
					e.setRecordPositionName("总部");
				}else{
					e.setRecordPositionName(PositionEnum.getPositionName(ceoType,jobsType,position));
				}

			});
		}

		Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "sheet1"), MeetingExportVO.class, list);
		EasyPoiUtil.downLoadExcel("线下会议列表.xls", response, workbook);
	}

	/**
	 * 保存会议
	 */
	@Override
	@Transactional(rollbackFor = ApplicationException.class)
	public Integer saveInfo(MeetingSaveRequest request) {
		log.info("保存会议入参:{}", JSON.toJSONString(request));

		// 检查有哪些修改
		MeetingModifyModelDTO meetingModifyModelDTO = checkModifyModel(request);

		String employeeName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getCreateBy(), RequestUtils.getChannel());
		//参会人
		Map<String,Employee> employeeMap = new LinkedHashMap<>();
		Map<String,Employee> parentEmployeeMap = new HashMap<>();
		MeetingInfoPO infoPo = new MeetingInfoPO();
		BeanUtils.copyProperties(request,infoPo);
		infoPo.setCreateName(employeeName);
		//会议纪要负责人
		Employee leaderEmployee = employeeMapper.selectEmployeeByPositonId(request.getLeaderPositionId());
		infoPo.setLeaderId(leaderEmployee.getEmployeeId());
		infoPo.setLeaderName(leaderEmployee.getEmployeeName());

		LoginModel loginInfo = RequestUtils.getLoginInfo();
		Integer positionTypeId = loginInfo.getPositionTypeId();
		if(positionTypeId == 7){
			request.setOrganizationId(organizationMapper.getZbOrganizationIdByBusinessGroup(RequestUtils.getBusinessGroup()));
		}



		List<String> addressCodes = request.getAddressCodes();
		if(!CollectionUtils.isEmpty(addressCodes)){
			infoPo.setAddressCodes(String.join(",",addressCodes));

			for(int i=0; i < addressCodes.size(); i++){
				String name = baseMapper.searchRegionName(addressCodes.get(i),i+1);
				if(i == 0){
					infoPo.setProvince(name);
				}else if(i == 1){
					infoPo.setCity(name);
				}else{
					infoPo.setDistrict(name);
				}
			}
		}

		// 检查聚餐创建次数
		if((positionTypeId == 1 || positionTypeId == 12) && request.getCategory().equals("聚餐")){
			String orgType = "area";
			if(positionTypeId == 12){
				orgType = "varea";
			}
			// 根据组织查询分公司数量
			List<String> companyCodes = Optional.ofNullable(organizationMapper.selectCompanyCodesByOrgCode(orgType, request.getOrganizationId())).orElse(new ArrayList<>());
			// 可创建会议次数
			int times = companyCodes.size();
			// 已创建会议次数
			Integer meetingCount = baseMapper.selectCount(new LambdaQueryWrapper<MeetingInfoPO>().eq(MeetingInfoPO::getOrganizationId, request.getOrganizationId())
					.eq(MeetingInfoPO::getCategory, "聚餐").eq(MeetingInfoPO::getIsDelete, 0)
					.likeRight(MeetingInfoPO::getStartTime,LocalDate.now().toString().substring(0,7))
					.in(MeetingInfoPO::getStatus, 0, 2));
			if(meetingCount >= times){
				throw new ApplicationException("本月创建聚餐数已满");
			}
		}


		List<Employee> employeeList = Lists.newArrayList();

		List<MeetingEmpVo> meetingEmpVos = request.getMeetingEmpVos();
		if(!CollectionUtils.isEmpty(meetingEmpVos)){
			// 非合伙人
			meetingEmpVos.stream().filter(f -> (StringUtils.isNotBlank(f.getEmployeeId())
					&& Objects.isNull(f.getMemberKey())) || f.getPositionTypeId() != 3 ).forEach(e -> {

				Employee createEmployee = employeeMapper.selectEmployeeByOrgId(e.getEmployeeId(), e.getOrganizationId(),null);

				employeeList.add(createEmployee);
			});

			// 合伙人
			List<Long> memberKeyList = meetingEmpVos.stream().filter(f -> f.getPositionTypeId() == 3 && Objects.nonNull(f.getMemberKey())).map(MeetingEmpVo::getMemberKey).collect(Collectors.toList());
			if(!CollectionUtils.isEmpty(memberKeyList)){
				List<Employee> ceoList = employeeMapper.selectCeoByMemberKey(memberKeyList,RequestUtils.getBusinessGroup());
				if(!CollectionUtils.isEmpty(ceoList)){
					employeeList.addAll(ceoList);
				}
			}

		}


        //添加发起人
        Employee createEmployee = employeeMapper.selectEmployeeByOrgId(request.getCreateBy(), request.getOrganizationId(),null);
		if (Objects.isNull(createEmployee)){
            createEmployee = employeeMapper.selectEmployeeByOrgId(request.getCreateBy(), null,RequestUtils.getBusinessGroup());
            infoPo.setOrganizationId(createEmployee.getOrganizationId());
        }

        employeeList.add(createEmployee);
        //会议纪要负责人
        employeeList.add(leaderEmployee);
        //上级岗位
        if (createEmployee.getPositionTypeId() != 7) {
            List<Employee> parentEmpList = employeeMapper.selectParentOrgList(request.getOrganizationId());

            parentEmpList.forEach(e -> {
                if(!employeeMap.containsKey(e.getPositionId())){
                    parentEmployeeMap.put(e.getPositionId(),e);
                }
            });
        }

        if(!CollectionUtils.isEmpty(employeeList)){
        	// 非合伙人
            employeeList.stream()
                .filter(f -> Objects.nonNull(f) && StringUtils.isNotBlank(f.getEmployeeId()) && Objects.isNull(f.getMemberKey()))
                .sorted(Comparator.comparing(Employee::getPartTime, Comparator.nullsLast(Comparator.naturalOrder())))
                .forEach(e -> { if(!employeeMap.containsKey(e.getPositionId())){
                    employeeMap.put(e.getPositionId(),e);
                }
            });

            // 合伙人
			employeeList.stream().filter(f ->Objects.nonNull(f) && Objects.nonNull(f.getMemberKey())).forEach(e -> {
				if(!employeeMap.containsKey(e.getPositionId())){
					employeeMap.put(e.getPositionId(),e);
				}
			});
        }

		String mode = request.getMode();
        if("线上".equals(mode)){
			infoPo.setLatitude(null);
			infoPo.setLongitude(null);
			infoPo.setProvince(null);
			infoPo.setCity(null);
			infoPo.setDistrict(null);
			infoPo.setStreet(null);
			infoPo.setAddress(null);
			infoPo.setAddressCodes(null);
		}else{
			infoPo.setLink(null);
			String address = request.getAddress();
			if(StringUtils.isBlank(address)){
				throw new ApplicationException("线下会议请填写会议地址");
			}
		}

		Integer infoId;
		if (request.getInfoId() != null && request.getInfoId() > 0){
			infoId = request.getInfoId();
			infoPo.setStatus(0);
			baseMapper.updateById(infoPo);
			//删除附件
			meetingFileService.remove(new QueryWrapper<MeetingFilePO>().eq("w_id", infoId).eq("type", 1));
		}else{
			// 设置版本
			infoPo.setVersion(MEETING_VERSION);

			baseMapper.insert(infoPo);
			infoId = infoPo.getInfoId();
		}

		// 保存会议ID
		if(request.getCategory().equals("其他") && Objects.nonNull(request.getTaskId())){
			SfaTaskMeetingEntity sfaTaskMeetingEntity = taskMeetingMapper.selectOne(new LambdaQueryWrapper<SfaTaskMeetingEntity>().eq(SfaTaskMeetingEntity::getTaskId, request.getTaskId()).eq(SfaTaskMeetingEntity::getDeleteFlag, 0).isNull(SfaTaskMeetingEntity::getInfoId).last("limit 1"));
			if(Objects.nonNull(sfaTaskMeetingEntity)){
				sfaTaskMeetingEntity.setInfoId(infoId.longValue());
				sfaTaskMeetingEntity.setUpdateTime(LocalDateTime.now());
				taskMeetingMapper.updateById(sfaTaskMeetingEntity);

				SfaTaskEntity sfaTaskEntity = sfaTaskMapper.selectById(request.getTaskId());
				if(Objects.nonNull(sfaTaskEntity)){
					sfaTaskEntity.setTaskTag(StringUtils.EMPTY);
					sfaTaskMapper.updateById(sfaTaskEntity);
				}

				// 保存任务会议记录
				TaskLogDTO taskLogDTO = new TaskLogDTO();
				taskLogDTO.setTaskId(request.getTaskId());
				taskLogDTO.setType(TaskLogTypeEnum.CREATE_MEETING.getType());
				taskLogDTO.setRemark("创建会议，会议ID:"+infoId);
				taskLogDTO.setProcessUserName(createEmployee.getEmployeeName());
				taskLogDTO.setProcessUserId(createEmployee.getEmployeeId());
				taskLogService.saveLog(taskLogDTO);

			}
		}



		//附件
		List<MeetingFileVO> files = request.getFiles();
		if (CommonUtil.ListUtils.isNotEmpty(files)){
			List<MeetingFilePO> filePOS = Lists.newArrayList();
			files.stream().filter(f -> f.getUrl() != null).forEach(f -> {
				MeetingFilePO filePO = new MeetingFilePO();
				BeanUtils.copyProperties(f, filePO);
				filePO.setWId(infoId);
				filePO.setType(1);
				filePO.setCreateBy(request.getCreateBy());
				filePO.setCreateName(employeeName);
				filePOS.add(filePO);
			});
			meetingFileService.saveBatch(filePOS);
		}


		// 获取当前会议参会人
		List<MeetingRecordPO> list = Optional.ofNullable(meetingRecordService.list(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, infoPo.getInfoId()).eq(MeetingRecordPO::getIsDelete, 0))).orElse(new ArrayList<>());

		List<MeetingRecordPO> recordPOList = Lists.newArrayList();
		Set<String> empIds = new TreeSet<>();
		employeeMap.forEach((k,v) -> {
			MeetingRecordPO recordPO = new MeetingRecordPO();
			recordPO.setInfoId(infoId);
			recordPO.setEmployeeId(v.getEmployeeId());
			recordPO.setRoleFlag(0);
			recordPO.setBusinessGroup(organizationMapper.getBusinessGroupById(v.getOrganizationId()));
			recordPO.setEmployeeName(v.getEmployeeName());
			recordPO.setOrganizationId(v.getOrganizationId());

			Integer businessGroupById = organizationMapper.getBusinessGroupById(v.getOrganizationId());
			recordPO.setBusinessGroup(businessGroupById);
			recordPO.setReceiveStatus(0);
			recordPO.setCheckStatus(0);
			recordPO.setGetuiStatus(0);
			recordPO.setMemberKey(v.getMemberKey());
			recordPO.setGetuiStatus(1);
			Integer partTime = v.getPartTime();
			// 设置为主岗
			if(Objects.nonNull(partTime) && partTime == 0){
				recordPO.setPrimaryPosition(1);
			}else{
				recordPO.setPrimaryPosition(0);
			}
			if (v.getPositionTypeId() == 7) {
				recordPO.setOrganizationName("总部");
				recordPO.setPrimaryPosition(1);
			}else{
				recordPO.setOrganizationName(organizationMapper.getOrganizationName(v.getOrganizationId()));
			}

			recordPOList.add(recordPO);
			if(StringUtils.isNotBlank(v.getEmployeeId())){
				empIds.add(v.getEmployeeId());
			}

		});

		empIds.forEach(e -> {
			String organizationId = infoPo.getOrganizationId();
			String organizationName = organizationMapper.getOrganizationName(organizationId);
			String position = OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(organizationId));
			String createName = infoPo.getCreateName();
			String topic = infoPo.getSubclass();
			String title = organizationName+"-"+position+"-"+createName;
			if("总部".equals(position)){
				title = organizationName+"-"+createName;
			}

			if(meetingModifyModelDTO.isModifyBaseInfo() && list.stream().filter(f -> StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e)).findFirst().isPresent()){
				String message = title+"的【"+topic+"】会议信息已更新";
				NotifyPO po = new NotifyPO();
				po.setTitle(message);
				po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
				po.setContent(message);
				po.setCode("meetingsDetail?infoId="+infoPo.getInfoId()+"&mode=detail&category="+infoPo.getCategory());
				po.setEmployeeId(e);
				po.setCreateBy("-1");
				po.setUpdateBy("-1");
				List<NotifyPO> notifyPOS = Arrays.asList(po);
				notifyService.saveBatch(notifyPOS);

			}else{
				Optional<MeetingRecordPO> first = recordPOList.stream().filter(f -> StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e)).findFirst();
				if(first.isPresent()){
					MeetingRecordPO meetingRecordPO = first.get();
					Integer businessGroup = meetingRecordPO.getBusinessGroup();

					if(!e.equals(infoPo.getCreateBy())){
						String payload = "{\"title\":\"系统推送\",\"businessGroup\":\""+businessGroup+"\",\"type\":601}";
						geTuiUtil.AppPushToSingleSync(e, "系统推送", title+"邀请您参加【"+topic+"】", payload, 1);
					}
				}

			}

		});

		// 检查是否有删除的
		if(Objects.nonNull(request.getInfoId())){
			Set<String> deleteEmpIds = new TreeSet<>();
			Collection<Employee> values = Optional.ofNullable(employeeMap.values()).orElse(new ArrayList<>());
			list.forEach(e -> {
				Optional<Employee> first = values.stream().filter(f -> (
						((StringUtils.isNotBlank(e.getEmployeeId()) && StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e.getEmployeeId())) ||
						(Objects.nonNull(e.getMemberKey()) && e.getMemberKey().equals(f.getMemberKey()))))&& e.getIsDelete() == 0).findFirst();

				if(!first.isPresent()){
					if(StringUtils.isNotBlank(e.getEmployeeId())){
						deleteEmpIds.add(e.getEmployeeId());
					}

					// 删除记录
					e.setIsDelete(1);
					meetingRecordService.removeById(e.getRecordId());
				}
			});

			if(!CollectionUtils.isEmpty(deleteEmpIds)){
				String organizationId = infoPo.getOrganizationId();
				String organizationName = organizationMapper.getOrganizationName(organizationId);
				String position = OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(organizationId));
				String createName = infoPo.getCreateName();
				String topic = infoPo.getSubclass();
				String title = organizationName+"-"+position+"-"+createName;

				deleteEmpIds.stream().forEach(e -> {
					String message = "您好,"+title+"的【"+topic+"】会议已将您移除";
					NotifyPO po = new NotifyPO();
					po.setTitle(message);
					po.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
					po.setContent(message);
					po.setCode("/offlineMeetings");
					po.setEmployeeId(e);
					po.setCreateBy("-1");
					po.setUpdateBy("-1");
					List<NotifyPO> notifyPOS = Arrays.asList(po);
					notifyService.saveBatch(notifyPOS);
				});
			}
		}




		parentEmployeeMap.forEach((k,v) -> {
			Optional<MeetingRecordPO> first = list.stream().filter(f -> f.getIsDelete() == 0 && StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(v.getEmployeeId())).findFirst();
			if(!first.isPresent()){
				MeetingRecordPO pRecordPO = new MeetingRecordPO();
				pRecordPO.setInfoId(infoId);
				pRecordPO.setEmployeeId(v.getEmployeeId());
				pRecordPO.setRoleFlag(1);
				pRecordPO.setEmployeeName(v.getEmployeeName());
				pRecordPO.setOrganizationId(v.getOrganizationId());
				pRecordPO.setBusinessGroup(organizationMapper.getBusinessGroupById(v.getOrganizationId()));
				if (v.getPositionTypeId() == 7) {
					pRecordPO.setOrganizationName("总部");
				}else{
					pRecordPO.setOrganizationName(v.getOrganizationName());
				}
				pRecordPO.setReceiveStatus(0);
				if(v.getEmployeeId().equals(infoPo.getCreateBy())){
					pRecordPO.setReceiveStatus(1);
				}
				Integer partTime = v.getPartTime();
				// 设置为主岗
				if(Objects.nonNull(partTime) && partTime == 0){
					pRecordPO.setPrimaryPosition(1);
				}else{
					pRecordPO.setPrimaryPosition(0);
				}
				pRecordPO.setCheckStatus(0);
				pRecordPO.setGetuiStatus(0);
				recordPOList.add(pRecordPO);
			}

		});
		if (CommonUtil.ListUtils.isNotEmpty(recordPOList)) {
			List<MeetingRecordPO> insertRecordList = Lists.newArrayList();
			recordPOList.forEach(e -> {

				if(StringUtils.isNotBlank(e.getEmployeeId()) && (e.getEmployeeId().equals(infoPo.getCreateBy())  || e.getEmployeeId().equals(infoPo.getLeaderId()))){
					e.setReceiveStatus(1);
				}

				Optional<MeetingRecordPO> first = list.stream().filter(f -> f.getIsDelete() == 0 &&
						((StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e.getEmployeeId())) ||
								(Objects.nonNull(f.getMemberKey()) && f.getMemberKey().equals(e.getMemberKey())))
				).findFirst();
				if(!first.isPresent()){

					Optional<MeetingRecordPO> existsOptional = insertRecordList.stream().filter(f -> (StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e.getEmployeeId())) ||
							(Objects.nonNull(f.getMemberKey()) && f.getMemberKey().equals(e.getMemberKey()))

					).findFirst();
					if(!existsOptional.isPresent()){
						insertRecordList.add(e);
					}else{
						meetingRecordService.updateById(e);
					}

				}else{
					MeetingRecordPO meetingRecordPO = first.get();
					meetingRecordPO.setOrganizationId(e.getOrganizationId());
					meetingRecordPO.setOrganizationName(e.getOrganizationName());
					meetingRecordPO.setPrimaryPosition(e.getPrimaryPosition());
					meetingRecordMapper.updateById(meetingRecordPO);
				}
			});

			if(!CollectionUtils.isEmpty(insertRecordList)){
				meetingRecordService.saveBatch(insertRecordList);
			}

		}
		return infoId;
	}

	private MeetingModifyModelDTO checkModifyModel(MeetingSaveRequest request) {
		MeetingModifyModelDTO meetingModifyModelDTO = new MeetingModifyModelDTO();

		Integer infoId = request.getInfoId();
		if(Objects.isNull(infoId)){
			return meetingModifyModelDTO;
		}

		MeetingInfoPO meetingInfoPO = baseMapper.selectById(request.getInfoId());
		if(Objects.isNull(meetingInfoPO)){
			throw new ApplicationException("会议不存在");
		}
		MeetingInfoPO newMeetingInfoPO = new MeetingInfoPO();
		String leaderPositionId = request.getLeaderPositionId();
		BeanUtils.copyProperties(request,newMeetingInfoPO);
		CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getPositionId, leaderPositionId));

		int compare = Objects.compare(meetingInfoPO, newMeetingInfoPO, new Comparator<MeetingInfoPO>() {
			@Override
			public int compare(MeetingInfoPO o1, MeetingInfoPO o2) {
				if (o1.getCategory().equals(o2.getCategory()) &&
						o1.getSubclass().equals(o2.getSubclass()) &&
						( (StringUtils.isBlank(o1.getTopic()) && StringUtils.isBlank(o2.getTopic())) || (StringUtils.isNotBlank(o1.getTopic()) && o1.getTopic().equals(o2.getTopic()))) &&
						o1.getStartTime().isEqual(o2.getStartTime()) &&
						o1.getEndTime().isEqual(o2.getEndTime()) &&
						o1.getMode().equals(o2.getMode()) &&
						( (StringUtils.isBlank(o1.getRemark()) && StringUtils.isBlank(o2.getRemark())) || (StringUtils.isNotBlank(o1.getRemark()) && o1.getRemark().equals(o2.getRemark())))&&
						(  (StringUtils.isBlank (o1.getAddress()) && StringUtils.isBlank (o2.getAddress())) || (StringUtils.isBlank (o1.getLink()) && StringUtils.isBlank (o2.getLink())) || StringUtils.isNotBlank(o1.getLink()) && o1.getLink().equals(o2.getLink()))  ||
								(StringUtils.isNotBlank(o1.getAddress()) && o1.getAddress().equals(o2.getAddress()))) {
					return 0;
				} else {
					return 1;
				}
			}
		});
		if(compare == 1){
			meetingModifyModelDTO.setModifyBaseInfo(true);
			return meetingModifyModelDTO;
		}

		// 比较会议纪要负责人是否一致
		if(!ceoBusinessOrganizationPositionRelation.getPositionId().equals(newMeetingInfoPO.getLeaderPositionId())){
			meetingModifyModelDTO.setModifyLeader(true);
		}

		return meetingModifyModelDTO;
	}

	@Override
	@Transactional(rollbackFor = ApplicationException.class)
	public Integer deleteMeeting(Integer infoId) {
		meetingRecordService.removeById(infoId);
		meetingFileService.remove(new QueryWrapper<MeetingFilePO>().eq("w_id", infoId).eq("type", 1));
		meetingRecordService.remove(new QueryWrapper<MeetingRecordPO>().eq("info_id",infoId));
		return infoId;
	}

	@Override
	public Integer cancel(Integer infoId, Integer status, String employeeId) {
		MeetingInfoPO po = baseMapper.selectById(infoId);
		po.setStatus(status);

		List<MeetingRecordPO> list = meetingRecordService.list(new QueryWrapper<MeetingRecordPO>().eq("info_id", infoId).eq("role_flag",0));

//		list.forEach(r -> {
//			Integer businessGroupById = organizationMapper.getBusinessGroupById(r.getOrganizationId());
//			String payload = "{\"title\":\"系统推送\",\"businessGroup\":\""+businessGroupById+"\"}";
//			geTuiUtil.AppPushToSingleSync(r.getEmployeeId(), "系统推送", po.getCreateName()+"发起的会议已取消，请知晓！", payload, 1);
//		});


		String organizationId = po.getOrganizationId();
		String organizationName = organizationMapper.getOrganizationName(organizationId);
		String position = OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(organizationId));
		String createName = po.getCreateName();
		String topic = po.getSubclass();
		String title = organizationName+"-"+position+"-"+createName;
		if(position.equals("总部")){
			title = organizationName+createName;
		}

		if(!CollectionUtils.isEmpty(list)){
			List<String> empList = list.stream().map(MeetingRecordPO::getEmployeeId).distinct().collect(Collectors.toList());
			List<NotifyPO> notifyPOS = new ArrayList<>();
			String finalTitle = title;
			empList.forEach(e -> {
				Optional<MeetingRecordPO> first = list.stream().filter(f -> StringUtils.isNotBlank(f.getEmployeeId()) && f.getEmployeeId().equals(e)).findFirst();
				if(first.isPresent()){
					MeetingRecordPO meetingRecordPO = first.get();
					Integer businessGroupById = organizationMapper.getBusinessGroupById(meetingRecordPO.getOrganizationId());
					String payload = "{\"title\":\"系统推送\",\"businessGroup\":\""+businessGroupById+"\",\"type\":601}";
					geTuiUtil.AppPushToSingleSync(e, "系统推送", "您好,"+ finalTitle +"的【"+topic+"】会议已取消", payload, 1);


					String message = "您好,"+ finalTitle +"的【"+topic+"】会议已取消";
					NotifyPO notify = new NotifyPO();
					notify.setTitle(message);
					notify.setType(NotifyTypeEnum.SYSTEM_ALERTS.getType());
					notify.setContent(message);
					notify.setCode("meetingsDetail?infoId="+po.getInfoId()+"&mode=detail&category="+po.getCategory());
					notify.setEmployeeId(e);
					notify.setCreateBy("-1");
					notify.setUpdateBy("-1");
					notifyPOS.add(notify);
				}
			});






			notifyService.saveBatch(notifyPOS);

		}


		return baseMapper.updateById(po);
	}

	@Override
	public MeetingDetailVO getDetailByInfoId(Integer infoId, String employeeId) {
		MeetingDetailVO meetingDetailVO = baseMapper.selectDetailByInfoId(infoId, employeeId);
		String organizationPath = meetingDetailVO.getOrganizationPath();
		if(organizationPath.endsWith("-")){
			meetingDetailVO.setCreateName(meetingDetailVO.getOrganizationPath()+meetingDetailVO.getCreateName());
		}else{
			meetingDetailVO.setCreateName(meetingDetailVO.getOrganizationPath()+"-"+meetingDetailVO.getCreateName());
		}

		// 检查登录人是否有稽核权限
		if(Objects.nonNull(meetingDetailVO.getStatus()) && meetingDetailVO.getStatus() == 2 && meetingDetailVO.getAuditStatus() == 0){
			Integer hasRole = roleEmployeeRelationMapper.selectCount(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().eq(RoleEmployeeRelationEntity::getEmployeeId, employeeId).eq(RoleEmployeeRelationEntity::getRoleId, 63)
					.eq(RoleEmployeeRelationEntity::getDeleteFlag, 0));
			if(hasRole > 0){
				meetingDetailVO.setCanAudit(true);
			}
		}

		// 设置会议状态
        setMeetingStatus(meetingDetailVO);

		String addressCodesValue = meetingDetailVO.getAddressCodesValue();
		if(StringUtils.isNotBlank(addressCodesValue)){
			meetingDetailVO.setAddressCodes(Arrays.asList(addressCodesValue.split(",")));
		}

		String category = meetingDetailVO.getCategory();
		if(category.equals("月会/季会") || category.equals("聚餐")){
			// 创建会议后主会人/负责人即可点击开始
			if(meetingDetailVO.getStatus() != 1 && meetingDetailVO.getStatus() != 3 && Objects.isNull(meetingDetailVO.getActualStartTime()) && (meetingDetailVO.getLeaderId().equals(employeeId)|| meetingDetailVO.getCreateBy().equals(employeeId))){
				meetingDetailVO.setStartFlag(1);
			}
		}


		if((category.equals("月会/季会") || category.equals("聚餐")) && meetingDetailVO.getStatus() == 0 && Objects.nonNull(meetingDetailVO.getActualStartTime())){
			meetingDetailVO.setJoinFlag(1);
		}

		// 获取会议稽核信息
		List<MeetingAuditPO> meetingAuditPOS = Optional.ofNullable(meetingAuditMapper.selectList(new LambdaQueryWrapper<MeetingAuditPO>().eq(MeetingAuditPO::getInfoId, infoId)
				.eq(MeetingAuditPO::getDeleteFlag, 0))).orElse(new ArrayList<>());

		Optional<MeetingAuditPO> meetingAuditOptional = meetingAuditPOS.stream().filter(f -> f.getType() == 2).findFirst();
		if(meetingAuditOptional.isPresent()){
			MeetingAuditPO meetingAuditPO = meetingAuditOptional.get();
			meetingDetailVO.setMeetingPhotoAuditResult(meetingAuditPO.getAuditResult());
			meetingDetailVO.setMeetingPhotoExceptionReason(meetingAuditPO.getReason());
		}

		List<RecordEmpVO> recordList = baseMapper.getRecordList(meetingDetailVO.getInfoId());
		if(!CollectionUtils.isEmpty(recordList)){
			recordList = recordList.stream().filter(f -> f.getRoleFlag() == 0 ).sorted(Comparator.comparing(RecordEmpVO::getOrganizationType,
					(x, y) -> {return new BigDecimal(OrganizationTypeEnum.getOrder(x)).compareTo(new BigDecimal(OrganizationTypeEnum.getOrder(y)));}).reversed()).collect(Collectors.toList());
			recordList.forEach(e -> {
				e.setShowData(true);

				Optional<MeetingAuditPO> auditOptional = meetingAuditPOS.stream().filter(f -> Objects.nonNull(f.getRecordId()) && f.getRecordId().equals(e.getRecordId())).findFirst();
				if(auditOptional.isPresent()){
					MeetingAuditPO meetingAuditPO = auditOptional.get();
					e.setAuditResult(meetingAuditPO.getAuditResult());
					e.setReason(meetingAuditPO.getReason());
				}

				String orgType = organizationMapper.getOrganizationType(e.getOrganizationId());
				e.setOrganizationType(orgType);

				Integer businessGroupById = organizationMapper.getBusinessGroupById(e.getOrganizationId());
				if(Objects.nonNull(businessGroupById)){
					e.setBusinessGroupName(sfaBusinessGroupMapper.selectById(businessGroupById).getBusinessGroupName());
				}


				if(StringUtils.isNotBlank(e.getEmployeeId()) && e.getEmployeeId().equals(employeeId) && e.getReceiveStatus() != 2 && e.getCheckStatus()  == 0){

					if(category.equals("月会/季会") || category.equals("聚餐")){
						LocalDateTime actualEndTime = meetingDetailVO.getActualEndTime();

							LocalDateTime actualStartTime = meetingDetailVO.getActualStartTime();
							if(Objects.nonNull(actualStartTime) && Objects.isNull(actualEndTime)){
								meetingDetailVO.setCheckFlag(1);
							}
					}else{
						LocalDateTime startDate = meetingDetailVO.getStartTime().minusMinutes(15L);

						LocalDateTime currentTime = LocalDateTime.now();

						LocalDateTime actualEndTime = meetingDetailVO.getActualEndTime();
						if(Objects.isNull(actualEndTime) && (currentTime.isEqual(startDate) || currentTime.isAfter(startDate))){
							meetingDetailVO.setCheckFlag(1);
						}
					}
				}


				SfaPositionRelationEntity positionRelationEntity = null;
				if(!orgType.equals("branch")){
					positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
							.eq(SfaPositionRelationEntity::getEmpId, e.getEmployeeId()).last("limit 1"));
				}else{
					positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
							.eq(SfaPositionRelationEntity::getOrganizationCode, e.getOrganizationId()).last("limit 1"));
				}


				if(Objects.nonNull(positionRelationEntity)){
					if(orgType.equals("branch")){
						e.setOrganizationName(positionRelationEntity.getDepartmentName());
					}
					e.setPositionTypeId(positionRelationEntity.getPositionTypeId());
					Integer employeeInfoId = positionRelationEntity.getEmployeeInfoId();
					SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
					if(Objects.nonNull(sfaEmployeeInfoModel)){
						ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
						if(Objects.nonNull(applyMemberPo)){
							e.setAvatar(applyMemberPo.getPicUrl());

							if(Objects.nonNull(applyMemberPo.getCeoType()) && Objects.nonNull(applyMemberPo.getJobsType()) && Objects.nonNull(applyMemberPo.getPosition())){
								e.setPositionName(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));
							}else{
								log.warn("sfa_apply_member parameter error applyId:{}",applyMemberPo.getId());
							}

							e.setOnBoardDate(LocalDateTimeUtils.formatTime(sfaEmployeeInfoModel.getOnboardTime(),LocalDateTimeUtils.yyyy_MM_dd));

						}else{
							e.setPositionName(OrganizationTypeEnum.getPositionName(e.getOrganizationType()));
						}

					}

					int curOrder = OrganizationTypeEnum.getOrder(RequestUtils.getLoginInfo().getOrganizationType());

					int order = OrganizationTypeEnum.getOrder(organizationMapper.getOrganizationType(e.getOrganizationId()));

					SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(organizationMapper.getBusinessGroupById(e.getOrganizationId()));

					if(curOrder <= order && sfaBusinessGroupEntity.getId() == RequestUtils.getBusinessGroup()){
						e.setRealDataReview(true);
					}


					// 会议类型为日会、周会、月/季会时总部人员不用带出
					if(category.equals("月会/季会") || category.equals("日会") || category.equals("周会")){
						String organizationType = organizationMapper.getOrganizationType(e.getOrganizationId());
						if(organizationType.equals("zb")){
							e.setShowData(false);
						}
					}



					if(StringUtils.isNotBlank(e.getEmployeeId())){
						if(e.getEmployeeId().equals(meetingDetailVO.getCreateBy()) || e.getEmployeeId().equals(meetingDetailVO.getLeaderId())){
							e.setShowData(false);
						}
					}


					if(category.equals("月会/季会")){
						// 检查月报是否填写
						PromiseDTO promiseDTO = reviewReportSearchService.selectSalePromise(LocalDateTimeUtils.formatTime(meetingDetailVO.getStartTime(),LocalDateTimeUtils.yyyy_MM_dd).substring(0,7),e.getOrganizationId());
						e.setSalesCommitmentPromise(promiseDTO.getSalesCommitmentPromise());
						e.setCustomerOrderPricePromise(promiseDTO.getCustomerOrderPricePromise());
						e.setTransactionCustomerCountPromise(promiseDTO.getTransactionCustomerCountPromise());
					}

					LocalDateTime startTime = meetingDetailVO.getStartTime().minusMinutes(15L);
					if(e.getReceiveStatus() == 0 && StringUtils.isNotBlank(e.getEmployeeId())
							&& e.getEmployeeId().equals(employeeId) && Objects.isNull(meetingDetailVO.getActualStartTime())
							&& (LocalDateTime.now().isBefore(startTime) || LocalDateTime.now().isEqual(startTime)) && meetingDetailVO.getStatus() != 3){
						meetingDetailVO.setCanConfirm(true);
					}

					e.setAreaName(positionRelationEntity.getAreaName());
					e.setVareaName(positionRelationEntity.getVareaName());
					e.setProvinceName(positionRelationEntity.getProvinceName());
					e.setCompanyName(positionRelationEntity.getCompanyName());
					e.setDepartmentName(positionRelationEntity.getDepartmentName());
				}

				// 主会人确认后可开始会议
				if(meetingDetailVO.getStatus() != 1 && meetingDetailVO.getStatus() != 3 && StringUtils.isNotBlank(e.getEmployeeId()) && e.getEmployeeId().equals(employeeId) && (meetingDetailVO.getCreateBy().equals(employeeId) || meetingDetailVO.getLeaderId().equals(employeeId) ) && e.getCheckStatus() == 1 && Objects.isNull(meetingDetailVO.getActualStartTime())){
					meetingDetailVO.setStartFlag(1);
				}

			});


			// 设置总部头像
			List<RecordEmpVO> zbList = recordList.stream().filter(f -> f.getOrganizationId().contains("ZB")).collect(Collectors.toList());
			if(Objects.nonNull(zbList)){
				List<String> empList = zbList.stream().map(RecordEmpVO::getEmployeeId).collect(Collectors.toList());
				List<SfaPositionEmp> sfaPositionEmps = sfaPositionEmpMapper.selectList(new LambdaQueryWrapper<SfaPositionEmp>().in(SfaPositionEmp::getEmpId, empList).eq(SfaPositionEmp::getDeleteFlag, 0));
				if(!CollectionUtils.isEmpty(sfaPositionEmps)){
					recordList.forEach(e -> {
						Optional<SfaPositionEmp> first = sfaPositionEmps.stream().filter(f -> f.getEmpId().equals(e.getEmployeeId())).findFirst();
						if(first.isPresent()){
							SfaPositionEmp sfaPositionEmp = first.get();
							// 总部都设制为主岗
							e.setPrimaryPosition(1);
							e.setAvatar(sfaPositionEmp.getAvator());
						}
					});
				}
			}
		}

		String organizationId = meetingDetailVO.getOrganizationId();

		Integer businessGroupById = organizationMapper.getBusinessGroupById(organizationId);
		SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroupById);
		if(businessGroupById == 99){
			meetingDetailVO.setAllGroup(true);
		}
		meetingDetailVO.setBusinessGroupName(sfaBusinessGroupEntity.getBusinessGroupName());

		settingPath(organizationId,meetingDetailVO);

		meetingDetailVO.setRecordList(recordList);
		meetingDetailVO.setFiles(meetingFileService.getFiles(meetingDetailVO.getInfoId(),1));
		meetingDetailVO.setPicFiles(meetingFileService.getFiles(meetingDetailVO.getInfoId(),2));
		meetingDetailVO.setSummaryFiles(meetingFileService.getFiles(meetingDetailVO.getSummaryId(),3));
		MeetingRecordPO recordPO = meetingRecordService.getOne(new QueryWrapper<MeetingRecordPO>().eq("info_id", infoId).eq("employee_id", employeeId).last("limit 1"));

		// 问题反馈
		List<MeetingIssueEntity> meetingIssueEntities = meetingIssueMapper.selectList(new LambdaQueryWrapper<MeetingIssueEntity>().eq(MeetingIssueEntity::getInfoId, infoId).eq(MeetingIssueEntity::getDeleteFlag, 0));
		if(!CollectionUtils.isEmpty(meetingIssueEntities)){
			List<MeetingIssueRequest> issueRequestList = new ArrayList<>();
			meetingIssueEntities.forEach(e -> {
				MeetingIssueRequest meetingIssueRequest = new MeetingIssueRequest();
				BeanUtils.copyProperties(e,meetingIssueRequest);
				issueRequestList.add(meetingIssueRequest);
			});
			meetingDetailVO.setIssueList(issueRequestList);
		}


		if(category.equals("月会/季会")){
			settingMonthReport(meetingDetailVO,recordList,employeeId);
		}else if(category.equals("周会")){
			settingWeekReport(meetingDetailVO,recordList,employeeId);
		}else if(category.equals("日会")){

			String organizationType = organizationMapper.getOrganizationType(organizationId);

			settingDailyReport(meetingDetailVO,recordList,employeeId);

		}

		if(category.equals("聚餐")){
			MeetingDinnerVO meetingDinnerVO = new MeetingDinnerVO();
			int managerCount = Optional.ofNullable(recordList.stream().filter(f -> f.getRoleFlag() == 0 && StringUtils.isNotBlank(f.getEmployeeId())  && f.getCheckStatus() == 1).collect(Collectors.toList())).orElse(new ArrayList()).size();
			int ceoCount = Optional.ofNullable(recordList.stream().filter(f -> f.getRoleFlag() == 0 && Objects.nonNull(f.getMemberKey())).collect(Collectors.toList())).orElse(new ArrayList()).size();
			int totalCount =  managerCount + ceoCount;
			meetingDinnerVO.setParticipantCount(totalCount);


			String dinnerPerPrice = configMapper.getValueByCode("dinner_per_price");
			if(StringUtils.isNotBlank(dinnerPerPrice)){
				meetingDinnerVO.setStandardFee(new BigDecimal(dinnerPerPrice));
				BigDecimal estimatePrice = new BigDecimal(dinnerPerPrice).multiply(new BigDecimal(totalCount));
				meetingDinnerVO.setEstimatePrice(estimatePrice);
			}

			MeetingDinnerInfoEntity meetingDinnerInfoEntity = meetingDinnerInfoMapper.selectOne(new LambdaQueryWrapper<MeetingDinnerInfoEntity>().eq(MeetingDinnerInfoEntity::getInfoId, infoId).eq(MeetingDinnerInfoEntity::getDeleteFlag, 0).last("limit 1"));
			if(Objects.nonNull(meetingDinnerInfoEntity)){
				meetingDinnerVO.setDinnerId(meetingDinnerInfoEntity.getDinnerId());
				meetingDinnerVO.setActualPrice(meetingDinnerInfoEntity.getActualPrice());

				String invoiceImg = meetingDinnerInfoEntity.getInvoiceImg();
				if(StringUtils.isNotBlank(invoiceImg)){
					List invoiceList = Arrays.asList(invoiceImg.split(","));
					meetingDinnerVO.setInvoiceImg(invoiceList);
				}

				List<MeetingDinnerFileEntity> meetingDinnerFileEntities = meetingDinnerFileMapper.selectList(new LambdaQueryWrapper<MeetingDinnerFileEntity>().eq(MeetingDinnerFileEntity::getDinnerId, meetingDinnerInfoEntity.getDinnerId()).eq(MeetingDinnerFileEntity::getDeleteFlag, 0));
				if(!CollectionUtils.isEmpty(meetingDinnerFileEntities)){
					List<MeetingDinnerFileRequest> annexList = new ArrayList<>();
					meetingDinnerFileEntities.forEach(e -> {
						MeetingDinnerFileRequest fileRequest = new MeetingDinnerFileRequest();
						BeanUtils.copyProperties(e,fileRequest);
						fileRequest.setName(e.getFileName());
						fileRequest.setFileType(e.getFileType());
						annexList.add(fileRequest);
					});
					meetingDinnerVO.setAnnexList(annexList);
				}
			}else{
				if(meetingDetailVO.getLeaderId().equals(employeeId) || meetingDetailVO.getCreateBy().equals(employeeId)){

					if(Objects.nonNull(meetingDetailVO.getActualEndTime())){
						meetingDetailVO.setCanModifyDinner(true);
					}


				}
			}

			meetingDetailVO.setMeetingDinnerVO(meetingDinnerVO);
		}



		if (null != recordPO) {
			meetingDetailVO.setNotes(recordPO.getNotes());
		}



		return meetingDetailVO;
	}

    private void setMeetingStatus(MeetingDetailVO meetingDetailVO) {
		LocalDateTime startTime = meetingDetailVO.getStartTime();
		LocalDateTime currentTime = LocalDateTime.now();
		LocalDateTime endTime = meetingDetailVO.getStartTime().plusDays(1).toLocalDate().atStartOfDay();

        MeetingSummaryPO meetingSummaryPO = meetingSummaryMapper.selectOne(new LambdaQueryWrapper<MeetingSummaryPO>().eq(MeetingSummaryPO::getInfoId, meetingDetailVO.getInfoId()).eq(MeetingSummaryPO::getIsDelete, 0).last("limit 1"));


        // 当前时间大于等于会议预约时间，且未提交会议记录
        if(startTime.isBefore(currentTime) || startTime.isEqual(currentTime)){
            if(Objects.isNull(meetingSummaryPO) || meetingSummaryPO.getCommitStatus() == 0){
                meetingDetailVO.setSummaryStatus("待提交");
            }
        }
        // 超过会议日期次日0:00 为未提交
        if(endTime.isBefore(currentTime)){
            if(Objects.isNull(meetingSummaryPO) || meetingSummaryPO.getCommitStatus() == 0){
                meetingDetailVO.setSummaryStatus("未提交");
            }
        }

        // 提交时间为会议日期当日23:59分之前提交
        if(Objects.nonNull(meetingSummaryPO) && meetingSummaryPO.getCommitStatus() == 1 && meetingSummaryPO.getUpdateTime().isBefore(endTime)){
            meetingDetailVO.setSummaryStatus("已提交");
        }
        // 超过次日0:00上传的会议纪要
        if(Objects.nonNull(meetingSummaryPO) && meetingSummaryPO.getCommitStatus() == 1 && (meetingSummaryPO.getUpdateTime().isAfter(endTime) || meetingSummaryPO.getUpdateTime().isEqual(endTime))){
            meetingDetailVO.setSummaryStatus("逾期提交");
        }

    }


    private void settingPath(String organizationId, MeetingDetailVO meetingDetailVO) {

		List<CeoBusinessOrganizationTreeEntity> ceoBusinessOrganizationTreeEntities = ceoBusinessOrganizationTreeMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationTreeEntity>().eq(CeoBusinessOrganizationTreeEntity::getOrganizationId,organizationId).gt(CeoBusinessOrganizationTreeEntity::getLevel, 0).orderByDesc(CeoBusinessOrganizationTreeEntity::getLevel));
		if(!CollectionUtils.isEmpty(ceoBusinessOrganizationTreeEntities)){
			List<String> collect = ceoBusinessOrganizationTreeEntities.stream().map(CeoBusinessOrganizationTreeEntity::getOrganizationParentId).collect(Collectors.toList());
			collect.add(organizationId);
			meetingDetailVO.setOrgPath(collect);
		}
	}

	private void settingWeekReport(MeetingDetailVO meetingDetailVO, List<RecordEmpVO> recordList, String employeeId) {

		String leaderName = meetingDetailVO.getLeaderName();
		String organizationId = meetingDetailVO.getOrganizationId();
		String organizationName = organizationMapper.getOrganizationName(organizationId);

		String createBy = meetingDetailVO.getCreateBy();

		LocalDateTime startTime = meetingDetailVO.getStartTime();

		List<ReportVO>  list = new ArrayList<>();
		ReportVO reportVO = new ReportVO();
		reportVO.setTitle("销售周会");

		WorkReportCalendarPO workReportCalendarPO = workReportCalendarMapper.selectOne(new LambdaQueryWrapper<WorkReportCalendarPO>().le(WorkReportCalendarPO::getStartDate, LocalDateTimeUtils.formatTime(startTime,LocalDateTimeUtils.yyyy_MM_dd)).ge(WorkReportCalendarPO::getEndDate, LocalDateTimeUtils.formatTime(startTime,LocalDateTimeUtils.yyyy_MM_dd)).eq(WorkReportCalendarPO::getDeleteFlag, 0).last("limit 1"));
		reportVO.setPosition(OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(organizationId)));
		reportVO.setEmployeeName(leaderName);
		reportVO.setOrganizationId(organizationId);
		reportVO.setYearMonth(startTime.toLocalDate().toString().substring(0,7));
		reportVO.setPublishUser(createBy.equals(employeeId));

		if(Objects.nonNull(workReportCalendarPO)){
			String subtitle = organizationName + Integer.parseInt(workReportCalendarPO.getMonth())+"月第"+ NumberChineseFormatter.format(workReportCalendarPO.getWeeks(),false,false) +"周";
			reportVO.setSubTitle(subtitle);
			reportVO.setPeriod(workReportCalendarPO.getStartDate().toString().substring(5,10) + "~" + workReportCalendarPO.getEndDate().toString().substring(5,10));
		}

		list.add(reportVO);
		meetingDetailVO.setReportVOList(list);
	}



	private void settingDailyReport(MeetingDetailVO meetingDetailVO, List<RecordEmpVO> recordList, String employeeId) {
		List<ReportVO>  list = new ArrayList<>();
		String organizationId = meetingDetailVO.getOrganizationId();
		String organizationType = organizationMapper.getOrganizationType(organizationId);

		LocalDateTime startTime = meetingDetailVO.getStartTime();
		if(!organizationType.equals("zb")){
			String leaderName = meetingDetailVO.getLeaderName();

			String organizationName = organizationMapper.getOrganizationName(organizationId);


			ReportVO reportVO = new ReportVO();
			reportVO.setTitle("日会");
			reportVO.setSubTitle(organizationName);
			reportVO.setPosition(OrganizationTypeEnum.getPositionName(organizationMapper.getOrganizationType(organizationId)));
			reportVO.setOrganizationType(organizationMapper.getOrganizationType(organizationId));
			reportVO.setEmployeeName(leaderName);
			reportVO.setOrganizationId(organizationId);
			reportVO.setYearMonth(LocalDateTimeUtils.formatTime(startTime,LocalDateTimeUtils.yyyy_MM_dd));
			list.add(reportVO);
		}else{
			recordList.forEach(e -> {
				String childOrgType = organizationMapper.getOrganizationType(e.getOrganizationId());
				if(!childOrgType.equals("zb") && !childOrgType.equals("branch")){

					ReportVO reportVO = new ReportVO();
					reportVO.setTitle("日会");
					reportVO.setSubTitle(organizationMapper.getOrganizationName(e.getOrganizationId()));
					reportVO.setPosition(OrganizationTypeEnum.getPositionName(childOrgType));
					reportVO.setOrganizationType(childOrgType);
					reportVO.setEmployeeName(e.getEmployeeName());
					reportVO.setOrganizationId(e.getOrganizationId());
					reportVO.setYearMonth(LocalDateTimeUtils.formatTime(startTime,LocalDateTimeUtils.yyyy_MM_dd));
					list.add(reportVO);
				}

			});
		}


		meetingDetailVO.setReportVOList(list);
	}

	private void settingMonthReport(MeetingDetailVO meetingDetailVO, List<RecordEmpVO> recordList,String employeeId) {

		String yearMonth = meetingDetailVO.getStartTime().toLocalDate().toString().substring(0, 7);

		List<String> organizationIds = recordList.stream().filter(f -> Objects.nonNull(f.getReceiveStatus())
				 && f.getRoleFlag() == 0).map(RecordEmpVO::getOrganizationId).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(organizationIds)){
			organizationIds =  recordList.stream().filter(f -> Objects.nonNull(f.getReceiveStatus()) && f.getReceiveStatus() != 2).map(RecordEmpVO::getOrganizationId).collect(Collectors.toList());
		}


		if(CollectionUtils.isEmpty(organizationIds)){
			return;
		}

		String organizationId = meetingDetailVO.getOrganizationId();
		String organizationType1 = organizationMapper.getOrganizationType(organizationId);
		if(organizationType1.equals("company") || organizationType1.equals("province")){
			organizationIds = Arrays.asList(organizationId);
		}
		if(organizationType1.equals("area") || organizationType1.equals("varea")){
			organizationIds = organizationIds.stream().filter(f -> !f.equals(organizationId)).collect(Collectors.toList());
		}


		if(CollectionUtils.isEmpty(organizationIds)){
			return;
		}

		List<ReviewReportVo> list = reviewReportSearchService.selectReportWithOrganizationId(yearMonth,organizationIds,employeeId);
		if(!CollectionUtils.isEmpty(list)){
			String organizationType = RequestUtils.getLoginInfo().getOrganizationType();
			int currentOrder = OrganizationTypeEnum.getOrder(organizationType);
			List<ReportVO> reportVOList = new ArrayList<>();
			list.stream().filter(f -> !f.getOrganizationId().contains("ZB")).forEach( e -> {
				ReportVO reportVO = new ReportVO();
				BeanUtils.copyProperties(e,reportVO);
				int order = OrganizationTypeEnum.getOrder(organizationMapper.getOrganizationType(e.getOrganizationId()));
				if(organizationType.equals("department") && order == OrganizationTypeEnum.COMPANY.getOrder()){
					reportVO.setIsCanView(1);
				}

				if(order == currentOrder || currentOrder < order){
					reportVO.setIsCanView(1);
				}
				reportVO.setEmployeeId(e.getEmployeeId());
				reportVOList.add(reportVO);
			});
			meetingDetailVO.setReportVOList(reportVOList);
		}
	}

	/**
	 * 保存会议纪要
	 *
	 * @param request
	 * @return: java.lang.Integer
	 * @date: 2/27/24 10:28 AM
	 */
	@Override
	@Transactional(rollbackFor = ApplicationException.class)
	public Integer saveSummary(MeetingSummarySaveRequest request) {
		log.info("保存会议纪要入参:{}", JSON.toJSONString(request));
		Integer infoId = request.getInfoId();
		MeetingInfoPO infoPO = baseMapper.selectById(infoId);
		if(Objects.isNull(infoPO)){
			throw new ApplicationException("会议不存在");
		}

		Integer commitStatus = Optional.ofNullable(request.getCommitStatus()).orElse(0);

		String createName = employeeMapper.getEmployeeNameByEmployeeIdFor123(request.getCreateBy(), RequestUtils.getChannel());
		MeetingSummaryPO summaryPO = meetingSummaryService.getOne(new QueryWrapper<MeetingSummaryPO>().eq("info_id", request.getInfoId()));
		if (Objects.nonNull(summaryPO)){
			BeanUtils.copyProperties(request,summaryPO);
			if(commitStatus == 1){
				summaryPO.setCommitTime(LocalDateTime.now());
			}
			meetingSummaryService.updateById(summaryPO);
			meetingFileService.remove(new QueryWrapper<MeetingFilePO>().eq("w_id", summaryPO.getSummaryId()).in("type", Arrays.asList(3)));
		}else{
			summaryPO = new MeetingSummaryPO();
			BeanUtils.copyProperties(request,summaryPO);
			if(commitStatus == 1){
				summaryPO.setCommitTime(LocalDateTime.now());
			}
			meetingSummaryService.save(summaryPO);
		}





		//附件
		Integer summaryId = summaryPO.getSummaryId();
		List<MeetingFilePO> filePOS = Lists.newArrayList();
		List<MeetingFileVO> picFiles = request.getPicFiles();
		if (CommonUtil.ListUtils.isNotEmpty(picFiles)){
			meetingFileService.remove(new QueryWrapper<MeetingFilePO>().eq("w_id", request.getInfoId()).in("type", Arrays.asList(2)));
			picFiles.stream().filter(f -> f.getUrl() != null).forEach(f -> {
				MeetingFilePO picFilePO = new MeetingFilePO();
				BeanUtils.copyProperties(f, picFilePO);
				picFilePO.setWId(request.getInfoId());
				picFilePO.setType(2);
				picFilePO.setCreateBy(request.getCreateBy());
				picFilePO.setCreateName(createName);
				filePOS.add(picFilePO);
			});
		}


		List<MeetingFileVO> summaryFiles = request.getSummaryFiles();
		if (CommonUtil.ListUtils.isNotEmpty(summaryFiles)){
			summaryFiles.stream().filter(f -> f.getUrl() != null).forEach(f -> {
				MeetingFilePO filePO = new MeetingFilePO();
				BeanUtils.copyProperties(f, filePO);
				filePO.setWId(summaryId);
				filePO.setType(3);
				filePO.setCreateBy(request.getCreateBy());
				filePO.setCreateName(createName);
				filePOS.add(filePO);
			});
		}


		meetingFileService.saveBatch(filePOS);

		List<MeetingFilePO> list = meetingFileService.list(new LambdaQueryWrapper<MeetingFilePO>().eq(MeetingFilePO::getWId, request.getInfoId()).eq(MeetingFilePO::getType, 2).eq(MeetingFilePO::getIsDelete, 0));
		Integer tag = Optional.ofNullable(infoPO.getTag()).orElse(0);
		if(commitStatus == 1 && CollectionUtils.isEmpty(list) && tag == 0){
			throw new ApplicationException("请使用app上传会议照片");
		}

		List<RecordEmpVO> recordList = request.getRecordList();
		if (CommonUtil.ListUtils.isNotEmpty(recordList)) {
			recordList.forEach(r -> {
				if (r.getRecordId() != null){
					MeetingRecordPO recordPO = new MeetingRecordPO();
					recordPO.setRecordId(r.getRecordId());
					recordPO.setProblem(r.getProblem());
					recordPO.setSuggestions(r.getSuggestions());
					recordPO.setRequirements(r.getRequirements());
					recordPO.setCommitment(r.getCommitment());
					recordPO.setOthers(r.getOthers());
					recordPO.setQuestion(r.getQuestion());
					recordPO.setSolution(r.getSolution());
					recordPO.setSalesCommitmentPromise(r.getSalesCommitmentPromise());
					recordPO.setTransactionCustomerCountPromise(r.getTransactionCustomerCountPromise());
					recordPO.setCustomerOrderPricePromise(r.getCustomerOrderPricePromise());
					meetingRecordService.updateById(recordPO);
				}
			});
		}

		List<MeetingIssueRequest> issueList = request.getIssueList();
		// 先删除所有问题
		MeetingIssueEntity deleteIssue = new MeetingIssueEntity();
		deleteIssue.setDeleteFlag(1);
		meetingIssueMapper.update(deleteIssue, new LambdaQueryWrapper<MeetingIssueEntity>().eq(MeetingIssueEntity::getInfoId,request.getInfoId()).eq(MeetingIssueEntity::getDeleteFlag,0));


		if(!CollectionUtils.isEmpty(issueList)){
			issueList.forEach(e -> {
				MeetingIssueEntity meetingIssueEntity = new MeetingIssueEntity();
				meetingIssueEntity.setInfoId(request.getInfoId());
				BeanUtils.copyProperties(e,meetingIssueEntity);
				meetingIssueEntity.setCreateTime(LocalDateTime.now());
				meetingIssueEntity.setDeleteFlag(0);
				meetingIssueMapper.insert(meetingIssueEntity);
			});
		}



		if (request.getCommitStatus() != null && request.getCommitStatus() == 1){
			MeetingInfoPO meetingInfoPO = new MeetingInfoPO();
			meetingInfoPO.setInfoId(request.getInfoId());
			meetingInfoPO.setStatus(2);
			baseMapper.updateById(meetingInfoPO);


			if(Objects.nonNull(request.getWorkFlowId())){
				String category = infoPO.getCategory();
				SaleActionEnum saleActionEnum = null;
				if(category.equals("日会")){
					saleActionEnum = SaleActionEnum.DAILY_MEETING_SUMMARY;
				}else if(category.equals("周会")){
					saleActionEnum = SaleActionEnum.WEEK_MEETING_SUMMARY;
				}else if(category.equals("月会/季会")){
					saleActionEnum = SaleActionEnum.MONTH_MEETING_SUMMARY;
				}


				if(Objects.nonNull(saleActionEnum)){
					SaleBehaviorUpdateModel saleBehaviorUpdateModel = SaleBehaviorUpdateModel.builder().empId(request.getCreateBy())
							.businessGroup(RequestUtils.getBusinessGroup())
							.positionTypeId(RequestUtils.getLoginInfo().getPositionTypeId())
							.workFlowId(request.getWorkFlowId())
							.actionId(saleActionEnum.getId())
							.build();
					saleManageService.updateSaleBehavior(saleBehaviorUpdateModel);
				}
			}

		}

		if(commitStatus == 1) {
			try {
				String nodeCode = "";
				switch (infoPO.getCategory()) {
					case "周会":
						nodeCode = "UPLOAD_WEEKLY_SUMMARY";
						break;
					case "月会/季会":
						nodeCode = "UPLOAD_MONTHLY_SUMMARY";
						break;
				}
				if (StringUtils.isNotBlank(nodeCode)) {
					JSONObject obj = new JSONObject();
					obj.put("nodeCode", nodeCode);
					obj.put("person", infoPO.getCreateBy());
					obj.put("organizationCode", infoPO.getOrganizationId());
					rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
				}
			} catch (Exception ex) {
				log.error("meetingSaveSummary 发送MQ失败", ex);
			}

			try {
				String nodeCode = "";
				switch (infoPO.getCategory()) {
                    case "周会":
                        nodeCode = "CONDUCT_WEEKLY_MEETING";
                        break;
                    case "月会/季会":
                        nodeCode = "CONDUCT_MONTHLY_MEETING";
                        break;
				}
				if(StringUtils.isNotBlank(nodeCode)) {
					JSONObject obj = new JSONObject();
					obj.put("nodeCode", nodeCode);
					obj.put("person", infoPO.getCreateBy());
					obj.put("organizationCode", infoPO.getOrganizationId());
					rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
				}
			} catch (Exception ex) {
				log.error("meetingSaveSummary 发送MQ失败", ex);
			}
		}
		return summaryId;
	}

	/**
	 * 弹窗
	 *
	 * @param employeeId
	 * @return: java.util.List<com.wantwant.sfa.backend.meeting.vo.MeetingInfoVO>
	 * @date: 2/27/24 3:32 PM
	 */
	@Override
	public List<MeetingInfoVO> listByEmpId(String employeeId) {
		// 20240613杨志慧需求改为15分钟弹窗
		List<MeetingInfoVO> list = baseMapper.selectListByEmpId(employeeId,RequestUtils.getBusinessGroup());


		list.forEach(m -> {
			if(m.getPositionType().equals("总部")){
				m.setMsg(m.getOrganizationName() + "-" + m.getCreateName() + " 邀请您参加【" + m.getSubclass() +"】");
			}else{
				m.setMsg(m.getOrganizationName() + "-" + m.getPositionType() + "-" + m.getCreateName() + " 邀请您参加【" + m.getSubclass() +"】");
			}

		});


		return list;
	}

	/**
	 * 参加会议
	 */
	@Override
	public Integer attend(AttendMeetingRequest request) {
		log.info("参加会议入参:{}", JSON.toJSONString(request));
		MeetingRecordPO recordPO = new MeetingRecordPO();
		BeanUtils.copyProperties(request,recordPO);
		meetingRecordService.update(recordPO,new UpdateWrapper<MeetingRecordPO>().eq("info_id",request.getInfoId()).eq("employee_id",request.getEmployeeId()));



		return request.getInfoId();
	}

	@Override
	public List<MeetingExportVO> getGeTuiList() {
		return baseMapper.getGeTuiList();
	}

	/**
	 * 批注
	 */
	@Override
	public Integer setNotes(NotesRequest request) {
		log.info("批注入参:{}", JSON.toJSONString(request));
		List<MeetingRecordPO> list = meetingRecordService.list(new QueryWrapper<MeetingRecordPO>().eq("info_id", request.getInfoId()).eq("employee_id",request.getEmployeeId()));
		if (CommonUtil.ListUtils.isNotEmpty(list)) {
			list.forEach(r -> r.setNotes(request.getNotes()));
			meetingRecordService.updateBatchById(list);
		}
		return request.getInfoId();
	}

	/**
	 * 签到
	 */
	@Override
	@Transactional
	public Integer checkIn(CheckInRequest request) {
		log.info("签到入参:{}", JSON.toJSONString(request));
		List<MeetingRecordPO> list = meetingRecordService.list(new QueryWrapper<MeetingRecordPO>().eq("info_id", request.getInfoId()).eq("employee_id",request.getEmployeeId()));
		MeetingSignInfoDTO meetingSignInfoDTO = new MeetingSignInfoDTO();
		MeetingInfoPO meetingInfoPO = baseMapper.selectById(request.getInfoId());
		meetingSignInfoDTO.setMeetingType(meetingInfoPO.getCategory());
		meetingSignInfoDTO.setMonthLyMeetingSignTime(LocalDate.now().toString());
		SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, request.getEmployeeId()).last("limit 1"));
		if(Objects.nonNull(sfaEmployeeInfoModel) && Objects.nonNull(sfaEmployeeInfoModel.getMemberKey())){
			meetingSignInfoDTO.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
		}

		String picUrl = null;
		if(Objects.nonNull(sfaEmployeeInfoModel)) {
			ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
			picUrl = applyMemberPo.getPicUrl();
		}else{
			SfaPositionEmp sfaPositionEmp = sfaPositionEmpMapper.selectOne(new LambdaQueryWrapper<SfaPositionEmp>().eq(SfaPositionEmp::getEmpId, request.getEmployeeId()).eq(SfaPositionEmp::getDeleteFlag, 0).last("limit 1"));
			if(Objects.nonNull(sfaPositionEmp)){
				picUrl = sfaPositionEmp.getAvator();
			}
		}
		if (CommonUtil.ListUtils.isNotEmpty(list)) {
			String finalPicUrl = picUrl;
			list.forEach(r -> {
				r.setCheckStatus(1);
				r.setProvince(request.getProvinceName());
				r.setCity(request.getCityName());
				r.setDistrict(request.getDistrictName());
				r.setStreet(request.getStreet());
				r.setLatitude(request.getLatitude());
				r.setLongitude(request.getLongitude());
				r.setReceiveStatus(1);
				r.setRejectReasons(null);
				r.setCheckPic(request.getCheckPic());
				r.setCheckTime(LocalDateTime.now());
				r.setSignUpPicUrl(finalPicUrl);

				// 检查是否迟到
				checkLateFlag(meetingInfoPO.getCategory(),meetingInfoPO.getActualStartTime(),r);

				try {
					String nodeCode = "";
					switch (meetingInfoPO.getCategory()) {
						case "日会":
							nodeCode = "ATTEND_DAILY_MEETING";
							break;
						case "周会":
							nodeCode = "ATTEND_WEEKLY_MEETING";
							break;
						case "月会/季会":
							nodeCode = "ATTEND_MONTHLY_MEETING";
							break;
					}
					if (!meetingInfoPO.getOrganizationId().startsWith("ZB") && StringUtils.isNotBlank(nodeCode) && !meetingInfoPO.getCreateBy().equals(request.getEmployeeId())) {
						JSONObject obj = new JSONObject();
						obj.put("nodeCode", nodeCode);
						obj.put("person", request.getEmployeeId());
						obj.put("organizationCode", r.getOrganizationId());
						obj.put("businessId", request.getInfoId());
						rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
					}
				} catch (Exception ex) {
					log.error("meetingCheckIn 发送MQ失败", ex);
				}
			});
			meetingRecordService.updateBatchById(list);
		}

		Long workFlowId = request.getWorkFlowId();
		if(Objects.nonNull(workFlowId)){
			SaleActionEnum saleActionEnum = null;
			if(meetingInfoPO.getCategory().equals("日会")){
				saleActionEnum = SaleActionEnum.DAILY_MEETING_LAUNCH;
			}else if(meetingInfoPO.getCategory().equals("周会")){
				saleActionEnum = SaleActionEnum.WEEK_MEETING_JOIN;
			}else if(meetingInfoPO.getCategory().equals("月会/季会")){
				saleActionEnum = SaleActionEnum.MONTH_MEETING_JOIN;
			}

			Integer positionTypeByEmpId = checkCustomerService.getPositionTypeByEmpId(request.getEmployeeId());

			SaleBehaviorUpdateModel saleBehaviorUpdateModel = SaleBehaviorUpdateModel.builder().empId(request.getEmployeeId())
					.businessGroup(RequestUtils.getBusinessGroup())
					.positionTypeId(positionTypeByEmpId)
					.workFlowId(workFlowId)
					.actionId(saleActionEnum.getId())
					.build();
			saleManageService.updateSaleBehavior(saleBehaviorUpdateModel);
		}


		if(Objects.nonNull(meetingSignInfoDTO.getMemberKey())){
			meetingConnectUtil.UpdateCityManagerMonthLySignInfo(meetingSignInfoDTO);
		}

		return request.getInfoId();
	}

	private void checkLateFlag(String category, LocalDateTime actualStartTime, MeetingRecordPO r) {
		if(category.equals("聚餐") || category.equals("月会/季会") || Objects.isNull(actualStartTime)){
			return;
		}

		// 标记为迟到
		if(LocalDateTime.now().isAfter(actualStartTime)){
			r.setLateFlag(1);
		}

	}

	@Override
	public  List<OrganizationAddressVO> getAddress(String organizationId) {
		return organizationAddressMapper.getAddress(organizationId);
	}

	@Override
	@Transactional
	public void uploadMeetingPhoto(MeetingPhotoRequest meetingPhotoRequest) {
		MeetingInfoPO meetingInfoPO = baseMapper.selectById(meetingPhotoRequest.getInfoId());
		if(Objects.isNull(meetingInfoPO)){
			throw new ApplicationException("获取会议信息失败");
		}

		meetingFileService.remove(new QueryWrapper<MeetingFilePO>().eq("w_id", meetingInfoPO.getInfoId()).eq("type", 2));

		CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(meetingPhotoRequest.getPerson(), RequestUtils.getLoginInfo());


		List<MeetingFileVO> picFiles = meetingPhotoRequest.getPicFiles();
		if (CommonUtil.ListUtils.isNotEmpty(picFiles)){
			List<MeetingFilePO> filePOS = Lists.newArrayList();
			picFiles.stream().filter(f -> f.getUrl() != null).forEach(f -> {
				MeetingFilePO filePO = new MeetingFilePO();
				BeanUtils.copyProperties(f, filePO);
				filePO.setWId(meetingInfoPO.getInfoId());
				filePO.setType(2);
				filePO.setCreateBy(personInfo.getEmployeeId());
				filePO.setCreateName(personInfo.getEmployeeName());
				filePOS.add(filePO);
			});

			meetingFileService.saveBatch(filePOS);
		}

	}

	@Override
	public MeetingSelectEmpVo selectEmpVos(String person, Integer searchType, String key, String day, boolean filterCeo, Integer businessGroup) {
		MeetingSelectEmpVo meetingSelectEmpVo = new MeetingSelectEmpVo();
		
		// 获取直属下级
		List<MeetingEmpVo> subordinateList = getSubordinateList(person, businessGroup, day);
		
		// 获取直属上级
		List<MeetingEmpVo> parentList = getSuperiorList(person, businessGroup, day);
		
		// 处理关键字搜索
		handleKeywordSearch(meetingSelectEmpVo, searchType, key, day, filterCeo, businessGroup);
		
		// 获取当前组织信息
		List<MeetingOrgEmpVo> orgList = getCurrentOrgInfo(day, filterCeo, businessGroup);
		
		// 组装结果
		meetingSelectEmpVo.setMeetingOrgEmpVos(orgList);
		meetingSelectEmpVo.setSubordinateList(subordinateList);
		meetingSelectEmpVo.setSuperiorList(parentList);
		
		return meetingSelectEmpVo;
	}

	/**
	 * 获取直属下级列表
	 */
	private List<MeetingEmpVo> getSubordinateList(String person, Integer businessGroup, String day) {
		List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(
			new LambdaQueryWrapper<SfaPositionRelationEntity>()
				.eq(SfaPositionRelationEntity::getEmpId, person)
				.eq(SfaPositionRelationEntity::getStatus, 1)
				.eq(SfaPositionRelationEntity::getBusinessGroup, businessGroup)
				.eq(SfaPositionRelationEntity::getDeleteFlag, 0)
		);
		
		if (CollectionUtils.isEmpty(positionRelationEntityList)) {
			return Collections.emptyList();
		}

		List<String> orgCodes = new ArrayList<>();
		positionRelationEntityList.forEach(e -> {
			List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationCode());
			if (!CollectionUtils.isEmpty(nextOrgCode)) {
				orgCodes.addAll(nextOrgCode);
			}
		});

		if (CollectionUtils.isEmpty(orgCodes)) {
			return Collections.emptyList();
		}

		List<MeetingEmpVo> subordinateList = Optional.ofNullable(
			sfaPositionRelationMapper.selectEmpByOrgCode(orgCodes)
		).orElse(Collections.emptyList());
		
		setDescription(subordinateList, day);
		return subordinateList;
	}

	/**
	 * 获取直属上级列表
	 */
	private List<MeetingEmpVo> getSuperiorList(String person, Integer businessGroup, String day) {
		Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
		
		// 省区总监/区域总监 上级为 总督导/大区总监
		if (positionTypeId == 2 || positionTypeId == 11) {
			return getSuperiorForRegionalDirector(person, businessGroup, day);
		}
		// 总督导/大区总监 上级为 总部（00272473）
		else if (positionTypeId == 1 || positionTypeId == 12) {
			return getSuperiorForAreaSupervisor(day);
		}
		
		return Collections.emptyList();
	}

	/**
	 * 获取省区总监/区域总监的上级
	 */
	private List<MeetingEmpVo> getSuperiorForRegionalDirector(String person, Integer businessGroup, String day) {
		List<SfaPositionRelationEntity> positionList = sfaPositionRelationMapper.selectList(
			new LambdaQueryWrapper<SfaPositionRelationEntity>()
				.eq(SfaPositionRelationEntity::getEmpId, person)
				.eq(SfaPositionRelationEntity::getBusinessGroup, businessGroup)
				.eq(SfaPositionRelationEntity::getPositionTypeId, RequestUtils.getLoginInfo().getPositionTypeId())
				.eq(SfaPositionRelationEntity::getStatus, 1)
				.eq(SfaPositionRelationEntity::getDeleteFlag, 0)
		);
		
		if (CollectionUtils.isEmpty(positionList)) {
			return Collections.emptyList();
		}

		List<String> parentOrgCodes = new ArrayList<>();
		positionList.forEach(e -> {
			SelectAuditDto selectAuditDto = new SelectAuditDto();
			selectAuditDto.setBusinessGroup(businessGroup);
			selectAuditDto.setChannel(RequestUtils.getChannel());
			selectAuditDto.setCurrentOrganizationId(e.getParentOrganizationCode());
			selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_senior_hr_employee_id"));
			
			CeoBusinessOrganizationPositionRelation relation = auditService.chooseAuditPerson(selectAuditDto);
			if (relation.getPositionTypeId() != 7) {
				parentOrgCodes.add(relation.getOrganizationId());
			}
		});

		if (CollectionUtils.isEmpty(parentOrgCodes)) {
			return Collections.emptyList();
		}

		List<MeetingEmpVo> parentList = Optional.ofNullable(
			sfaPositionRelationMapper.selectEmpByOrgCode(parentOrgCodes)
		).orElse(Collections.emptyList());
		
		setDescription(parentList, day);
		return parentList;
	}

	/**
	 * 获取总督导/大区总监的上级
	 */
	private List<MeetingEmpVo> getSuperiorForAreaSupervisor(String day) {
		String seniorHrEmployeeId = configMapper.getValueByCode("zw_senior_hr_employee_id");
		List<MeetingEmpVo> parentList = Optional.ofNullable(
			sfaPositionRelationMapper.selectByKey(
				seniorHrEmployeeId, 
				false, 
				Collections.singletonList(RequestUtils.getBusinessGroup()), 
				false
			)
		).orElse(Collections.emptyList());
		
		setDescription(parentList, day);
		return parentList;
	}

	/**
	 * 处理关键字搜索
	 */
	private void handleKeywordSearch(MeetingSelectEmpVo meetingSelectEmpVo, Integer searchType, String key, 
									String day, boolean filterCeo, Integer businessGroup) {
		if (StringUtils.isBlank(key)) {
			return;
		}

		boolean allBusinessGroup = RequestUtils.getBusinessGroup() == 99;
		MeetingOrgEmpVo empVO = new MeetingOrgEmpVo();

		if (searchType == 2) {
			// 人员搜索
			handlePersonSearch(empVO, key, day, filterCeo, businessGroup, allBusinessGroup);
			meetingSelectEmpVo.setSearchResult(Collections.singletonList(empVO));
		} else if (searchType == 3) {
			// 岗位搜索
			handlePositionSearch(empVO, key, day, filterCeo, businessGroup);
			meetingSelectEmpVo.setSearchResult(Collections.singletonList(empVO));
		} else {
			// 组织搜索
			handleOrgSearch(meetingSelectEmpVo, key, day, filterCeo, businessGroup);
		}
	}

	/**
	 * 处理人员搜索
	 */
	private void handlePersonSearch(MeetingOrgEmpVo empVO, String key, String day, boolean filterCeo, 
								   Integer businessGroup, boolean allBusinessGroup) {
		List<Integer> businessGroupCodes = Collections.singletonList(businessGroup);
		List<MeetingEmpVo> meetingEmpVos = sfaPositionRelationMapper.selectByKey(key, filterCeo, businessGroupCodes, allBusinessGroup);
		setDescription(meetingEmpVos, day);
		empVO.setMeetingEmpVoList(meetingEmpVos);
	}

	/**
	 * 处理岗位搜索
	 */
	private void handlePositionSearch(MeetingOrgEmpVo empVO, String key, String day, boolean filterCeo, Integer businessGroup) {
		List<MeetingOrgEmpVo> list = Optional.ofNullable(
			searchOrgEmp(day, key, 3, filterCeo, businessGroup)
		).orElse(new ArrayList<>());
		
		List<MeetingEmpVo> collect = list.stream()
			.map(MeetingOrgEmpVo::getMeetingEmpVoList)
			.flatMap(Collection::stream)
			.collect(Collectors.toList());
		empVO.setMeetingEmpVoList(collect);
	}

	/**
	 * 处理组织搜索
	 */
	private void handleOrgSearch(MeetingSelectEmpVo meetingSelectEmpVo, String key, String day, 
								boolean filterCeo, Integer businessGroup) {
		List<MeetingOrgEmpVo> list = Optional.ofNullable(
			searchOrgEmp(day, key, 1, filterCeo, businessGroup)
		).orElse(new ArrayList<>());
		meetingSelectEmpVo.setSearchResult(list);
	}

	/**
	 * 获取当前组织信息
	 */
	private List<MeetingOrgEmpVo> getCurrentOrgInfo(String day, boolean filterCeo, Integer businessGroup) {
		return searchOrgEmp(day, null, 1, filterCeo, businessGroup);
	}

	private List<MeetingOrgEmpVo> searchOrgEmp(String day, String key, Integer searchType, boolean filterCeo, Integer businessGroup) {
		List<MeetingOrgEmpVo> result = new ArrayList<>();

		List<OrgEmpDTO> orgEmpDTOS = null;
		if (Objects.nonNull(searchType) && searchType == 2 && StringUtils.isNotBlank(key)) {
			orgEmpDTOS = sfaPositionRelationMapper.selectOrgEmp(Arrays.asList(businessGroup), key, null, filterCeo);
		} else if (Objects.nonNull(searchType) && searchType == 3 && StringUtils.isNotBlank(key)) {
			List<PositionEnum> positionEnumList = Arrays.asList(PositionEnum.values()).stream()
				.filter(f -> f.getPositionName().contains(key))
				.collect(Collectors.toList());
			if (CollectionUtils.isEmpty(positionEnumList)) {
				orgEmpDTOS = Collections.emptyList();
			} else {
				orgEmpDTOS = sfaPositionRelationMapper.selectOrgEmp(Collections.singletonList(businessGroup), null, positionEnumList, filterCeo);
			}
		} else {
			orgEmpDTOS = sfaPositionRelationMapper.selectOrgEmp(Collections.singletonList(businessGroup), null, null, filterCeo);
		}

		if (!CollectionUtils.isEmpty(orgEmpDTOS)) {
			orgEmpDTOS.stream()
				.filter(f -> Objects.nonNull(f.getTreeOrgCode()))
				.collect(Collectors.groupingBy(OrgEmpDTO::getTreeOrgCode))
				.forEach((k, value) -> {
					OrgEmpDTO orgEmpDTO = value.stream().findFirst().get();
					MeetingOrgEmpVo vo = new MeetingOrgEmpVo();

					vo.setOrganizationId(orgEmpDTO.getOrganizationId());
					vo.setTreeOrgCode(orgEmpDTO.getTreeOrgCode());
					vo.setOrganizationParentId(orgEmpDTO.getParentOrganizationId());
					vo.setOrganizationName(orgEmpDTO.getOrganizationName());

					List<MeetingEmpVo> empList = new ArrayList<>();
					value.stream()
						.filter(f -> StringUtils.isNotBlank(f.getEmployeeName()))
						.forEach(e -> {
							MeetingEmpVo empVo = new MeetingEmpVo();
							empVo.setAvatar(e.getAvatar());
							empVo.setBusinessGroup(e.getBusinessGroup());
							empVo.setBusinessGroupName(e.getBusinessGroupName());
							empVo.setPositionTypeId(e.getPositionTypeId());
							empVo.setOrganizationId(e.getOrganizationId());
							empVo.setMemberKey(e.getMemberKey());
							empVo.setOrganizationName(e.getOrganizationName());
							empVo.setEmployeeName(e.getEmployeeName());
							empVo.setEmployeeId(e.getEmployeeId());
							empVo.setPrimaryPosition(Optional.ofNullable(orgEmpDTO.getPrimaryPosition()).orElse(0));


							String positionName = StringUtils.EMPTY;
							if (Objects.nonNull(e.getCeoType()) && Objects.nonNull(e.getJobsType()) && Objects.nonNull(e.getPosition())) {
								positionName = PositionEnum.getPositionName(e.getCeoType(), e.getJobsType(), e.getPosition());
							} else {
								log.warn("sfa_apply_member parameter error memberKey:{}", e.getMemberKey());
							}

							if (StringUtils.isNotBlank(positionName) && StringUtils.isNotBlank(e.getDepartmentName())) {
								String primaryPositionName = "";
								Integer primaryPosition = e.getPrimaryPosition();
								if(Objects.nonNull(primaryPosition) && primaryPosition == 1){
									primaryPositionName = "(主岗)";
								}
								if(Objects.nonNull(primaryPosition) && primaryPosition == 0){
									primaryPositionName = "(兼岗)";
								}
								empVo.setDescription(e.getBusinessGroupName() + "-" + e.getDepartmentName() + "-" + positionName + "-" + e.getEmployeeName() + "-" + e.getMobile() + primaryPositionName);
							}

							empList.add(empVo);
						});

					vo.setMeetingEmpVoList(empList);
					setDescription(empList, null);
					result.add(vo);
				});

			// 查询人员或岗位不需要转成树
			if (Objects.isNull(searchType) || searchType == 1) {
				TreeBuilder treeBuilder = new TreeBuilder();
				treeBuilder.asId(e -> BeanUtil.getProperty(e, "treeOrgCode"));
				treeBuilder.asPid(e -> BeanUtil.getProperty(e, "organizationParentId"));
				List<MeetingOrgEmpVo> orgVos = TreeUtil.list2Tree(result, treeBuilder);
				result.clear();
				result.addAll(orgVos);

				cleanTree(result);

				if (Objects.nonNull(searchType) && StringUtils.isNotBlank(key)) {
					List<MeetingOrgEmpVo> meetingOrgEmpVos = filterOrg(new ArrayList<>(), result, key);
					searchUUID(meetingOrgEmpVos);
					return meetingOrgEmpVos;
				}
			}
		}

		searchUUID(result);
		return result;
	}

	private void searchUUID(List<MeetingOrgEmpVo> result) {
		if (CollectionUtils.isEmpty(result)) {
			return;
		}
		result.forEach(e -> {
			e.setUuId(UUID.randomUUID().toString());
			List<MeetingEmpVo> meetingEmpVoList = e.getMeetingEmpVoList();
			if (!CollectionUtils.isEmpty(meetingEmpVoList)) {
				meetingEmpVoList.forEach(emp -> {
					emp.setUuId(UUID.randomUUID().toString());
				});
			}
			List<MeetingOrgEmpVo> children = e.getChildren();
			if (!CollectionUtils.isEmpty(children)) {
				searchUUID(children);
			}
		});
	}

	private List<MeetingOrgEmpVo> filterOrg(List<MeetingOrgEmpVo> newResult, List<MeetingOrgEmpVo> result, String key) {
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyList();
		}

		result.forEach(e -> {
			if (e.getOrganizationName().contains(key)) {
				// 去除重复
				Optional<MeetingOrgEmpVo> first = newResult.stream()
					.filter(f -> f.getOrganizationId().equals(e.getOrganizationId()))
					.findFirst();
				if (!first.isPresent()) {
					newResult.add(e);
				}
				filterOrg(newResult, e.getChildren(), key);
			} else {
				filterOrg(newResult, e.getChildren(), key);
			}
		});
		return newResult;
	}

	private void cleanTree(List<MeetingOrgEmpVo> result) {
		if (CollectionUtils.isEmpty(result)) {
			return;
		}

		result.forEach(e -> {
			if (CollectionUtils.isEmpty(e.getChildren())) {
				e.setChildren(null);
			} else {
				cleanTree(e.getChildren());
			}
		});
	}

	@Override
	public List<MeetingEmpVo> getSelectedEmp(Integer infoId) {
		MeetingInfoPO meetingInfoPO = baseMapper.selectById(infoId);
		List<MeetingEmpVo> list = Optional.ofNullable(baseMapper.getSelectedEmp(infoId)).orElse(ListUtils.EMPTY_LIST);
		setDescription(list,meetingInfoPO.getStartTime().toLocalDate().toString());
		return list;
	}

	@Override
	public MeetingInfoRouteVO checkMeetingRoute(String person, String date,String category,String organizationId,Long taskId) {
		MeetingInfoRouteVO meetingInfoRouteVO = new MeetingInfoRouteVO();

		LocalDate startDate = null;
		LocalDate endDate = null;



		// 检查本周有没有创建过周会
		MeetingInfoPO meetingInfoPO = null;
		if(category.equals("周会")){
			meetingInfoPO = baseMapper.selectOne(new LambdaQueryWrapper<MeetingInfoPO>().eq(MeetingInfoPO::getCreateBy, person).ge(MeetingInfoPO::getStartTime, startDate).le(MeetingInfoPO::getEndTime, endDate).eq(MeetingInfoPO::getCategory,"周会").eq(MeetingInfoPO::getIsDelete, 0).ne(MeetingInfoPO::getStatus, 1).last("limit 1"));
			startDate = LocalDate.parse(date).with(DayOfWeek.MONDAY);
			endDate = LocalDate.parse(date).with(DayOfWeek.SUNDAY);
		}else if(category.equals("日会")){
			meetingInfoPO = baseMapper.selectOne(new LambdaQueryWrapper<MeetingInfoPO>().eq(MeetingInfoPO::getCreateBy, person).likeRight(MeetingInfoPO::getStartTime, date).ne(MeetingInfoPO::getCategory,"其他").eq(MeetingInfoPO::getIsDelete, 0).ne(MeetingInfoPO::getStatus, 1).last("limit 1"));
			startDate = LocalDate.parse(date);
			endDate = LocalDate.parse(date);
		}else if(category.equals("月会/季会")){
			startDate = LocalDate.parse(date).with(TemporalAdjusters.firstDayOfMonth());
			endDate = startDate.plusMonths(1L).minusDays(1L);
			meetingInfoPO = baseMapper.selectOne(new LambdaQueryWrapper<MeetingInfoPO>().eq(MeetingInfoPO::getCreateBy, person).ge(MeetingInfoPO::getStartTime, startDate).le(MeetingInfoPO::getEndTime, endDate).eq(MeetingInfoPO::getCategory,"月会/季会").eq(MeetingInfoPO::getIsDelete, 0).ne(MeetingInfoPO::getStatus, 1).last("limit 1"));
		}

		if(Objects.nonNull(meetingInfoPO) || endDate.isBefore(LocalDate.now())){
			meetingInfoRouteVO.setUrl("/offlineMeetings");
		}else{
			meetingInfoRouteVO.setUrl("/releaseMeetings?mode=push");
			List<MeetingEmpVo> subordinateList = ListUtils.EMPTY_LIST;
			List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmpId, person).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
			List<String> orgCodes = new ArrayList<>();

			if(category.equals("月会/季会")) {

				SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, organizationId).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
				if (Objects.nonNull(sfaPositionRelationEntity)) {
					subordinateList = Optional.ofNullable(sfaPositionRelationMapper.selectEmpByOrgCode(Arrays.asList(sfaPositionRelationEntity.getOrganizationCode()))).orElse(ListUtils.EMPTY_LIST);
					meetingInfoRouteVO.setSubordinateList(subordinateList);
				}

				String organizationName = organizationMapper.getOrganizationName(organizationId);
				meetingInfoRouteVO.setSubclass(organizationName + startDate.getMonthValue() + "月月会");
			}else if(category.equals("其他") && Objects.nonNull(taskId)){
				meetingInfoRouteVO.setTaskId(taskId);

				SfaTaskMeetingEntity sfaTaskMeetingEntity = taskMeetingMapper.selectOne(new LambdaQueryWrapper<SfaTaskMeetingEntity>().eq(SfaTaskMeetingEntity::getTaskId, taskId).isNull(SfaTaskMeetingEntity::getInfoId).eq(SfaTaskMeetingEntity::getDeleteFlag, 0).last("limit 1"));
				if(Objects.nonNull(sfaTaskMeetingEntity)){
					// 查询任务相关人员
					List<SfaTaskAssignEntity> assignEntities = sfaTaskAssignMapper.selectList(new LambdaQueryWrapper<SfaTaskAssignEntity>()
							.eq(SfaTaskAssignEntity::getTaskId, taskId)
							.eq(SfaTaskAssignEntity::getDeleteFlag, 0)
					);
					date = LocalDate.now().toString();
			

					if(!CollectionUtils.isEmpty(assignEntities)){
						List<String> empIds = assignEntities.stream().map(SfaTaskAssignEntity::getAssignUserId).collect(Collectors.toList());
						subordinateList = Optional.ofNullable(sfaPositionRelationMapper.selectEmpByEmpIds(empIds,RequestUtils.getBusinessGroup())).orElse(new ArrayList<>());
						setDescription(subordinateList.stream().collect(Collectors.toList()),date);
						meetingInfoRouteVO.setSubordinateList(subordinateList);
					}
				}


			}else{
				if(StringUtils.isNotBlank(organizationId)){
					List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + organizationId);
					if(!CollectionUtils.isEmpty(nextOrgCode)){
						orgCodes.addAll(nextOrgCode);
					}
				}
				else if(!CollectionUtils.isEmpty(positionRelationEntityList)) {
					positionRelationEntityList.forEach(e -> {
						List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationCode());
						if (!CollectionUtils.isEmpty(nextOrgCode)) {
							orgCodes.addAll(nextOrgCode);
						}
					});
				}

				if(!CollectionUtils.isEmpty(orgCodes)){
					String organizationType = organizationMapper.getOrganizationType(organizationId);
					subordinateList = Optional.ofNullable(sfaPositionRelationMapper.selectEmpByOrgCode(orgCodes)).orElse(ListUtils.EMPTY_LIST);
					if(!category.equals("日会")){
						subordinateList = subordinateList.stream().filter(f -> f.getPositionTypeId() != 10).collect(Collectors.toList());
						// 设置描述
						setDescription(subordinateList.stream().filter(f -> f.getPositionTypeId() != 10).collect(Collectors.toList()),date);
					}else{
						setDescription(subordinateList.stream().collect(Collectors.toList()),date);
					}


				}


				if(category.equals("周会")){
					SfaWorkReportEntity sfaWorkReportEntity = workReportMapper.selectOne(new LambdaQueryWrapper<SfaWorkReportEntity>().eq(SfaWorkReportEntity::getDeleteFlag,0).orderByDesc(SfaWorkReportEntity::getWorkId).last("limit 1"));
					if(Objects.nonNull(sfaWorkReportEntity)){
						String msg = "{0}月第{1}周周会";
						meetingInfoRouteVO.setSubclass(MessageFormat.format(msg,sfaWorkReportEntity.getMonth(),sfaWorkReportEntity.getWeeks()));
					}
				}else if(category.equals("日会")){
					String organizationName = organizationMapper.getOrganizationName(organizationId);
					meetingInfoRouteVO.setSubclass(date.toString()+"-日会");
				}


				meetingInfoRouteVO.setSubordinateList(subordinateList.stream().filter(f -> !f.isLeave()).collect(Collectors.toList()));
			}



		}


		return meetingInfoRouteVO;
	}

	@Override
	public void operate(MeetingOperateRequest meetingOperateRequest) {
		Integer operateType = meetingOperateRequest.getOperateType();

		MeetingInfoPO meetingInfoPO = baseMapper.selectById(meetingOperateRequest.getInfoId());

		if(operateType == 1){
			meetingInfoPO.setStartEarlyReason(meetingOperateRequest.getStartEarlyReason());
			meetingInfoPO.setActualStartTime(meetingOperateRequest.getTime());

			String category = meetingInfoPO.getCategory();
			if(category.equals("聚餐") || category.equals("月会/季会")){
				// 获取参会人
				List<MeetingRecordPO> meetingRecordPOS = Optional.ofNullable(meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, meetingOperateRequest.getInfoId()).eq(MeetingRecordPO::getIsDelete, 0).ne(MeetingRecordPO::getReceiveStatus, 2))).orElse(new ArrayList<>());

				meetingRecordPOS.stream().filter(f -> StringUtils.isNotBlank(f.getEmployeeId()) && (!f.getEmployeeId().equals(meetingInfoPO.getCreateBy()) || !f.getEmployeeId().equals(meetingInfoPO.getLeaderId()))).forEach(e -> {


					String payload = "{\"title\":\"系统推送\",\"businessGroup\":\""+e.getBusinessGroup()+"\",\"title\":\""
							+meetingInfoPO.getSubclass()+"\",\"createUserName\":\""+meetingInfoPO.getCreateName()+"\",\"infoId\":"+meetingInfoPO.getInfoId()+",\"type\":604}";
					geTuiUtil.AppPushToSingleSync(e.getEmployeeId(), "系统推送", meetingInfoPO.getCreateName()+"邀请的会议【"+meetingInfoPO.getSubclass()+"】已开始，请进入签到！", payload, 1);

				});

			}

			// 调用接口同步流程状态
			if(Objects.nonNull(meetingOperateRequest.getWorkFlowId())){
				SaleActionEnum saleActionEnum = null;

				if(category.equals("日会")){
					saleActionEnum = SaleActionEnum.DAILY_MEETING_LAUNCH;
				}else if(category.equals("周会")){
					saleActionEnum = SaleActionEnum.WEEK_MEETING_LAUNCH;
				}else if(category.equals("月会/季会")){
					saleActionEnum = SaleActionEnum.MONTH_MEETING_LAUNCH;
				}


				if(Objects.nonNull(saleActionEnum)){
					SaleBehaviorUpdateModel saleBehaviorUpdateModel = SaleBehaviorUpdateModel.builder().empId(meetingOperateRequest.getPerson())
							.businessGroup(RequestUtils.getBusinessGroup())
							.positionTypeId(RequestUtils.getLoginInfo().getPositionTypeId())
							.workFlowId(meetingOperateRequest.getWorkFlowId())
							.actionId(saleActionEnum.getId())
							.build();
					saleManageService.updateSaleBehavior(saleBehaviorUpdateModel);
				}
			}

		}else{
			meetingInfoPO.setActualEndTime(meetingOperateRequest.getTime());
			String category = meetingInfoPO.getCategory();
			if(category.equals("日会")){
				meetingInfoPO.setStatus(2);
			}

			try {
				String nodeCode = "";
				// 周、月会 在提交会议纪要之后触发
                switch (category) {
                    case "日会":
                        nodeCode = "CONDUCT_DAILY_MEETING";
                        break;
//                    case "周会":
//                        nodeCode = "CONDUCT_WEEKLY_MEETING";
//                        break;
//                    case "月会/季会":
//                        nodeCode = "CONDUCT_MONTHLY_MEETING";
//                        break;
                }
				if(StringUtils.isNotBlank(nodeCode)) {
					JSONObject obj = new JSONObject();
					obj.put("nodeCode", nodeCode);
					obj.put("person", meetingInfoPO.getCreateBy());
					obj.put("organizationCode", meetingInfoPO.getOrganizationId());
					rabbitMQSender.sendMessage(DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_EXCHANGE, DictCodeConstants.WORKFLOW_REWARD_PUNISHMENT_QUEUE, null,obj,null);
				}
			} catch (Exception ex) {
				log.error("meetingOperate 发送MQ失败", ex);
			}
		}

		baseMapper.updateById(meetingInfoPO);
	}

	@Override
	public List<MeetingPromiseVO> selectMeetingPromise(Integer infoId, String person) {

		MeetingInfoPO meetingInfoPO = baseMapper.selectById(infoId);
		if(Objects.isNull(meetingInfoPO)){
			throw new ApplicationException("会议获取失败");
		}

		List<MeetingPromiseVO> list = meetingPromiseService.selectPromise(meetingInfoPO,person);

		return list;
	}

	@Override
	public MeetingRedPointVO getRedPoint(String person) {
		MeetingRedPointVO meetingRedPointVO = new MeetingRedPointVO();

		Integer unStartCount = Optional.ofNullable(baseMapper.selectUnStartedCount(person,RequestUtils.getBusinessGroup())).orElse(0);
		meetingRedPointVO.setUnStartedCount(unStartCount);

		Integer uploadSummaryCount = Optional.ofNullable(baseMapper.selectUploadSummaryCount(person,RequestUtils.getBusinessGroup())).orElse(0);
		meetingRedPointVO.setUploadSummaryCount(uploadSummaryCount);

		Integer processingMeetingCount = Optional.ofNullable(baseMapper.selectProcessingCount(person,RequestUtils.getBusinessGroup())).orElse(0);
		meetingRedPointVO.setProcessingCount(processingMeetingCount);


		return meetingRedPointVO;
	}

	@Override
	@Transactional
	public void saveDinnerInfo(MeetingDinnerRequest meetingDinnerRequest) {
		// 找到会议信息
		MeetingDinnerInfoEntity meetingDinnerInfoEntity = meetingDinnerInfoMapper.selectOne(new LambdaQueryWrapper<MeetingDinnerInfoEntity>().eq(MeetingDinnerInfoEntity::getInfoId, meetingDinnerRequest.getInfoId()).eq(MeetingDinnerInfoEntity::getDeleteFlag, 0));
		if(Objects.nonNull(meetingDinnerInfoEntity)){
			meetingDinnerInfoEntity.setDeleteFlag(1);
			meetingDinnerInfoMapper.updateById(meetingDinnerInfoEntity);

			// 所有附件删除
			MeetingDinnerFileEntity deleteOption = new MeetingDinnerFileEntity();
			deleteOption.setDeleteFlag(1);
			meetingDinnerFileMapper.update(deleteOption,new LambdaQueryWrapper<MeetingDinnerFileEntity>().eq(MeetingDinnerFileEntity::getDinnerId,meetingDinnerInfoEntity.getDinnerId()).eq(MeetingDinnerFileEntity::getDeleteFlag,0));
		}

		meetingDinnerInfoEntity = new MeetingDinnerInfoEntity();
		BeanUtils.copyProperties(meetingDinnerRequest,meetingDinnerInfoEntity);
		List<String> invoiceImg = meetingDinnerRequest.getInvoiceImg();
		if(!CollectionUtils.isEmpty(invoiceImg)){
			meetingDinnerInfoEntity.setInvoiceImg(String.join(",",invoiceImg));
		}
		meetingDinnerInfoMapper.insert(meetingDinnerInfoEntity);


		List<MeetingDinnerFileRequest> annexList = meetingDinnerRequest.getAnnexList();
		if(!CollectionUtils.isEmpty(annexList)){
			MeetingDinnerInfoEntity finalMeetingDinnerInfoEntity = meetingDinnerInfoEntity;
			annexList.forEach(e -> {
				MeetingDinnerFileEntity fileEntity  = new MeetingDinnerFileEntity();
				fileEntity.setDinnerId(finalMeetingDinnerInfoEntity.getDinnerId());
				fileEntity.setFileName(e.getName());
				fileEntity.setFileType(e.getFileType());
				fileEntity.setUrl(e.getUrl());
				meetingDinnerFileMapper.insert(fileEntity);
			});
		}


		// 找到会议详情
		MeetingInfoPO infoPO = baseMapper.selectById(meetingDinnerRequest.getInfoId());
		if(Objects.isNull(infoPO)){
			throw new ApplicationException("获取会议详情失败");
		}
		infoPO.setStatus(2);
		infoPO.setUpdateTime(LocalDateTime.now());
		baseMapper.updateById(infoPO);

		// 记录是否可报销信息
        List<DinnerEmpVO> empVOList = Optional.ofNullable(meetingDinnerRequest.getEmpVOList().stream().filter(f -> f.isReimbursed()).collect(Collectors.toList())).orElse(new ArrayList<>());
        List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, meetingDinnerRequest.getInfoId()).eq(MeetingRecordPO::getIsDelete, 0));
        if(!CollectionUtils.isEmpty(meetingRecordPOS)){
            meetingRecordPOS.forEach(e -> {
                String employeeId = e.getEmployeeId();
                Long memberKey = e.getMemberKey();
                if(StringUtils.isNotBlank(employeeId) && empVOList.stream().filter(f -> employeeId.equals(f.getEmpId())).findFirst().isPresent()){
                    e.setReimbursed(1);
                }
                if(Objects.nonNull(memberKey) && empVOList.stream().filter(f -> Objects.nonNull(f.getMemberKey()) && f.getMemberKey().equals(memberKey)).findFirst().isPresent()){
                    e.setReimbursed(1);
                }
                meetingRecordMapper.updateById(e);
            });
        }

    }

	@Override
	public List<SaleDateVO> getSaleDate(Integer infoId, DailyMeetingFileVO dailyMeetingFileVO) {
		MeetingInfoPO infoPO = baseMapper.selectById(infoId);
		if(Objects.isNull(infoPO)){
			throw new ApplicationException("会议信息不存在");
		}
		String organizationId = infoPO.getOrganizationId();
		String organizationType = organizationMapper.getOrganizationType(organizationId);
		List<String> nextOrgCode = null;
		if(organizationType.equals("zb")){
			// 获取邀请人的直属下级
			List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, infoId).eq(MeetingRecordPO::getRoleFlag, 0).eq(MeetingRecordPO::getIsDelete, 0));
			if(!CollectionUtils.isEmpty(meetingRecordPOS)){
				nextOrgCode = new ArrayList<>();
				List<String> finalNextOrgCode = nextOrgCode;
				meetingRecordPOS.forEach(e -> {
					String currentOrg = e.getOrganizationId();
					String currentType = organizationMapper.getOrganizationType(currentOrg);
					List<String> nextCodes = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationId());
					finalNextOrgCode.add(e.getOrganizationId());
					if(!CollectionUtils.isEmpty(nextCodes)){
						finalNextOrgCode.addAll(nextCodes);
					}

				});
				nextOrgCode.addAll(finalNextOrgCode);
			}

		}else{
			// 根据会议ID获取直属下级
			nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + infoPO.getOrganizationId());
			if(CollectionUtils.isEmpty(nextOrgCode)){
				return ListUtils.EMPTY_LIST;
			}
		}


		nextOrgCode = nextOrgCode.stream().distinct().collect(Collectors.toList());

		// 根据会议ID获取3个日期：昨日，本月，本季
		// 本月和本季已昨日为准
		LocalDate yesterday = LocalDate.parse(dailyMeetingFileVO.getDate());

		int quarter = (yesterday.getMonthValue() - 1) /3 + 1;
		String season = yesterday.getYear() + "-Q"+quarter;

		String theYearMonth = yesterday.toString().substring(0,7);


		// 查询销售数据
		List<SaleDateDTO> list = Optional.ofNullable(meetingBigDataMapper.getSaleDate(nextOrgCode,yesterday,theYearMonth,season)).orElse(new ArrayList<>());

		return list.stream().map(SaleDateDTO::convert).collect(Collectors.toList());

	}

	@Override
	public List<SubordinateUserVo> getSubordinateUser(Integer infoId) {
		MeetingInfoPO infoPO = baseMapper.selectById(infoId);

		String date = infoPO.getStartTime().toLocalDate().toString();

		if(Objects.isNull(infoPO)){
			throw new ApplicationException("会议信息不存在");
		}

		String organizationId = infoPO.getOrganizationId();
		List<String> nextOrgCode = null;
		String organizationType = organizationMapper.getOrganizationType(organizationId);
		if(organizationType.equals("zb")){
			// 获取邀请人的直属下级
			List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, infoId).eq(MeetingRecordPO::getRoleFlag, 0).eq(MeetingRecordPO::getIsDelete, 0));
			if(!CollectionUtils.isEmpty(meetingRecordPOS)){
				nextOrgCode = new ArrayList<>();
				List<String> finalNextOrgCode = nextOrgCode;
				meetingRecordPOS.forEach(e -> {
					String currentOrg = e.getOrganizationId();
					String currentType = organizationMapper.getOrganizationType(currentOrg);
					List<String> nextCodes = (List<String>) redisUtil.get(NEXT_ORG_KEY + e.getOrganizationId());
					finalNextOrgCode.add(e.getOrganizationId());
					if(!CollectionUtils.isEmpty(nextCodes)){
						finalNextOrgCode.addAll(nextCodes);
					}

				});
				nextOrgCode.addAll(finalNextOrgCode);
			}

		}else{
			// 根据会议ID获取直属下级
			nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + infoPO.getOrganizationId());
			if(CollectionUtils.isEmpty(nextOrgCode)){
				return ListUtils.EMPTY_LIST;
			}
		}


		nextOrgCode = nextOrgCode.stream().distinct().collect(Collectors.toList());


		List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().in(SfaPositionRelationEntity::getOrganizationCode, nextOrgCode)
				.eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
		if(CollectionUtils.isEmpty(positionRelationEntityList)){
			return ListUtils.EMPTY_LIST;
		}

		List<Integer> employeeInfoIds = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getEmployeeInfoId).collect(Collectors.toList());
		List<SfaEmployeeInfoModel> employeeInfoModels = sfaEmployeeInfoMapper.selectList(new LambdaQueryWrapper<SfaEmployeeInfoModel>().in(SfaEmployeeInfoModel::getId, employeeInfoIds));
		if(CollectionUtils.isEmpty(employeeInfoModels)){
			return ListUtils.EMPTY_LIST;
		}

		Integer businessGroup = organizationMapper.getBusinessGroupById(infoPO.getOrganizationId());

		List<SubordinateUserVo> userVos = new ArrayList<>();
		employeeInfoModels.forEach(e -> {
			SubordinateUserVo subordinateUserVo = new SubordinateUserVo();
			subordinateUserVo.setEmployeeId(e.getEmployeeId());
			subordinateUserVo.setEmployeeInfoId(e.getId());
			subordinateUserVo.setUserName(e.getEmployeeName());
			List<SfaPositionRelationEntity> pList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, e.getId())
					.eq(SfaPositionRelationEntity::getBusinessGroup, businessGroup)
					.eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
			if(!CollectionUtils.isEmpty(pList)){
				List<String> collect = pList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
				subordinateUserVo.setOrganizationName(organizationMapper.getOrganizationNames(collect));

				Integer positionTypeId = pList.stream().findFirst().get().getPositionTypeId();
				subordinateUserVo.setOrganizationId(pList.stream().findFirst().get().getOrganizationCode());
				subordinateUserVo.setPositionName(PositionTypeEnum.findPositionNameById(positionTypeId));
				subordinateUserVo.setPositionTypeId(positionTypeId);
				subordinateUserVo.setOrder(PositionTypeEnum.findPositionOrderById(positionTypeId));
			}

			subordinateUserVo.setStatus("正常");
			List<SfaLeave> sfaLeaves = sfaLeaveMapper.selectList(new QueryWrapper<SfaLeave>().eq("apply_employee_info_id", e.getId())
					.eq("leave_status", 1).le("date_format(leave_start_time,'%Y-%m-%d')", date)
					.ge("date_format(leave_end_time,'%Y-%m-%d')", date)
					.eq("delete_flag", 0)
			);
			if(!CollectionUtils.isEmpty(sfaLeaves)){
				subordinateUserVo.setStatus("请假");
			}

			List<BusinessTripPO> businessTripPOS = businessTripMapper.selectList(new LambdaQueryWrapper<BusinessTripPO>().eq(BusinessTripPO::getCreatedBy, e.getId()).eq(BusinessTripPO::getStatus, 2)
					.le(BusinessTripPO::getStartDate, date).ge(BusinessTripPO::getEndDate, date)
					.eq(BusinessTripPO::getIsDelete, 0)
			);
			if(!CollectionUtils.isEmpty(businessTripPOS)){
				subordinateUserVo.setStatus("出差");
			}

			userVos.add(subordinateUserVo);
		});

		return userVos.stream().sorted(Comparator.comparing(SubordinateUserVo::getOrder).thenComparing(SubordinateUserVo::getEmployeeInfoId,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());

	}

	@Transactional
    @Override
    public BigDecimal faceSimilar(String url) {
        BigDecimal score = null;
        List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getCheckPic, url).eq(MeetingRecordPO::getIsDelete, 0));
        if(!CollectionUtils.isEmpty(meetingRecordPOS)) {
            MeetingRecordPO meetingRecordPO = meetingRecordPOS.get(0);
            if(Objects.nonNull(meetingRecordPO) && Objects.nonNull(meetingRecordPO.getCheckPic()) && Objects.nonNull(meetingRecordPO.getSignUpPicUrl())) {
                String faceScore = authService.compareFace(meetingRecordPO.getSignUpPicUrl(), meetingRecordPO.getCheckPic());
                if(Objects.nonNull(faceScore)) {
                    score = new BigDecimal(faceScore);
                }
            }
			BigDecimal finalScore = score;
			meetingRecordPOS.forEach(e -> {
            	e.setFaceSimilarScore(finalScore);
            	meetingRecordMapper.updateById(e);
			});
		}
        return score;
    }

	@Override
	public MeetingDinnerVO getDinnerInfo(Integer infoId) {
		MeetingDinnerVO meetingDinnerVO = new MeetingDinnerVO();

        MeetingInfoPO infoPO = baseMapper.selectById(infoId);

        // 找到会议信息
		MeetingDinnerInfoEntity meetingDinnerInfoEntity = meetingDinnerInfoMapper.selectOne(new LambdaQueryWrapper<MeetingDinnerInfoEntity>().eq(MeetingDinnerInfoEntity::getInfoId, infoId).eq(MeetingDinnerInfoEntity::getDeleteFlag, 0));
		if(Objects.nonNull(meetingDinnerInfoEntity)) {
			String invoiceImg = meetingDinnerInfoEntity.getInvoiceImg();
			if(StringUtils.isNotBlank(invoiceImg)){
				List invoiceList = Arrays.asList(invoiceImg.split(","));
				meetingDinnerVO.setInvoiceImg(invoiceList);
			}

			meetingDinnerVO.setActualPrice(meetingDinnerInfoEntity.getActualPrice());

			// 设置附件信息
			List<MeetingDinnerFileEntity> meetingDinnerFileEntities = meetingDinnerFileMapper.selectList(new LambdaQueryWrapper<MeetingDinnerFileEntity>().eq(MeetingDinnerFileEntity::getDinnerId, meetingDinnerInfoEntity.getDinnerId()).eq(MeetingDinnerFileEntity::getDeleteFlag, 0));
			if(!CollectionUtils.isEmpty(meetingDinnerFileEntities)){
				List<MeetingDinnerFileRequest> annexList = new ArrayList<>();
				meetingDinnerFileEntities.forEach(e -> {
					MeetingDinnerFileRequest fileRequest = new MeetingDinnerFileRequest();
					BeanUtils.copyProperties(e,fileRequest);
					fileRequest.setName(e.getFileName());
					fileRequest.setFileType(e.getFileType());
					annexList.add(fileRequest);
				});
				meetingDinnerVO.setAnnexList(annexList);
			}

		}


		String dinnerPerPrice = configMapper.getValueByCode("dinner_per_price");
		if(StringUtils.isNotBlank(dinnerPerPrice)){
			meetingDinnerVO.setStandardFee(new BigDecimal(dinnerPerPrice));
		}else{
			meetingDinnerVO.setStandardFee(BigDecimal.ZERO);
		}

		List<MeetingRecordPO> meetingRecordPOS = meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, infoId).eq(MeetingRecordPO::getRoleFlag, 0));

		if(CollectionUtils.isEmpty(meetingRecordPOS)){
			return meetingDinnerVO;
		}

		int participantCount = meetingRecordPOS.size();
		meetingDinnerVO.setParticipantCount(participantCount);

		List<DinnerEmpVO> empVOList = new ArrayList<>();

		meetingRecordPOS.forEach(e -> {
			DinnerEmpVO dinnerEmpVO = new DinnerEmpVO();
			Integer businessGroup = e.getBusinessGroup();
			SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);
			dinnerEmpVO.setBusinessGroupName(sfaBusinessGroupEntity.getBusinessGroupName());
			String organizationType = organizationMapper.getOrganizationType(e.getOrganizationId());
            dinnerEmpVO.setEmpId(e.getEmployeeId());
            dinnerEmpVO.setMemberKey(e.getMemberKey());
			// 获取当月报销次数
			int joinCount = meetingRecordMapper.selectMonthDinnerCount(infoPO.getStartTime().toLocalDate().toString().substring(0,7),e.getEmployeeId(),e.getOrganizationId(),infoId);

            // 总参加的会议
			int totalJoinCount = meetingRecordMapper.selectMonthDinnerCount(infoPO.getStartTime().toLocalDate().toString().substring(0,7),e.getEmployeeId(),e.getOrganizationId(),null);

			dinnerEmpVO.setJoinCount(totalJoinCount);

            Integer checkStatus = e.getCheckStatus();

            if(Objects.nonNull(e.getMemberKey())){
				checkStatus = 1;
			}
			dinnerEmpVO.setOrganizationType(organizationType);

            if(organizationType.equals("zb")){
				dinnerEmpVO.setOrganizationName("总部");


				SfaPositionEmp sfaPositionEmp = sfaPositionEmpMapper.selectOne(new LambdaQueryWrapper<SfaPositionEmp>().eq(SfaPositionEmp::getEmpId, e.getEmployeeId()).eq(SfaPositionEmp::getDeleteFlag,0).last("limit 1"));

				if(Objects.nonNull(sfaPositionEmp)){
					dinnerEmpVO.setAvatar(sfaPositionEmp.getAvator());
				}

				CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, e.getOrganizationId()).eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, e.getEmployeeId()).last("limit 1"));
				if(Objects.nonNull(ceoBusinessOrganizationPositionRelation)){
					dinnerEmpVO.setEmployeeName(ceoBusinessOrganizationPositionRelation.getEmployeeName()+"（在职）");
				}else{
					dinnerEmpVO.setEmployeeName(e.getEmployeeName()+"（离职）");
				}

				if(checkStatus == 1){
                    dinnerEmpVO.setReimbursed(true);
					dinnerEmpVO.setEstimatePrice(meetingDinnerVO.getStandardFee());
                }else{
					dinnerEmpVO.setEstimatePrice(BigDecimal.ZERO);
				}

			}else{
				CeoBusinessOrganizationViewEntity viewEntity = ceoBusinessOrganizationViewMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationViewEntity>().eq(CeoBusinessOrganizationViewEntity::getOrganizationId, e.getOrganizationId()));
				List<String>orgNames = new ArrayList<>();
				if(StringUtils.isNotBlank(viewEntity.getOrgName3())){
					orgNames.add(viewEntity.getOrgName3());
				}
				if(StringUtils.isNotBlank(viewEntity.getVirtualAreaName())){
					orgNames.add(viewEntity.getVirtualAreaName());
				}
				if(StringUtils.isNotBlank(viewEntity.getProvinceName())){
					orgNames.add(viewEntity.getProvinceName());
				}
				if(StringUtils.isNotBlank(viewEntity.getOrgName2())){
					orgNames.add(viewEntity.getOrgName2());
				}
				if(StringUtils.isNotBlank(viewEntity.getDepartmentName())){
					orgNames.add(viewEntity.getDepartmentName());
				}
				dinnerEmpVO.setOrganizationName(String.join("/",orgNames));


				SfaEmployeeInfoModel sfaEmployeeInfoModel = null;
				Long memberKey = e.getMemberKey();
				if(Objects.nonNull(memberKey)){
					sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, memberKey).last("limit 1"));
				}else{
					sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, e.getEmployeeId()).last("limit 1"));
				}


				if(Objects.nonNull(sfaEmployeeInfoModel)){
					Integer status = sfaEmployeeInfoModel.getStatus();
					String statusStr = "（在职）";
					if(status > 2){
						statusStr = "（离职）";
					}
					dinnerEmpVO.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName() + statusStr);

					ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
					if(Objects.nonNull(applyMemberPo)){
						String positionName = PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());
						dinnerEmpVO.setPositionName(positionName);
						dinnerEmpVO.setAvatar(applyMemberPo.getPicUrl());
					}
				}
				dinnerEmpVO.setJoinCount(totalJoinCount);
				// 大区/战区获取分公司数量
				if(organizationType.equals("area") || organizationType.equals("varea")){
					List<String> companyCodes = Optional.ofNullable(organizationMapper.getCompanyCodes(organizationType, e.getOrganizationId())).orElse(new ArrayList<>());
					dinnerEmpVO.setLimitCount(companyCodes.stream().filter(f -> StringUtils.isNotBlank(f)).collect(Collectors.toList()).size());
				}else{
					dinnerEmpVO.setLimitCount(1);
				}

				dinnerEmpVO.setEstimatePrice(BigDecimal.ZERO);
                int reimbursed = e.getReimbursed();
				if(reimbursed == 0) {
                    if(joinCount <= dinnerEmpVO.getLimitCount() && checkStatus == 1){
                        dinnerEmpVO.setReimbursed(true);
						dinnerEmpVO.setEstimatePrice(meetingDinnerVO.getStandardFee());
                    }
				}else{
					dinnerEmpVO.setReimbursed(true);
					dinnerEmpVO.setEstimatePrice(meetingDinnerVO.getStandardFee());
				}

			}



			empVOList.add(dinnerEmpVO);
		});

		int size = empVOList.stream().filter(f -> f.isReimbursed()).collect(Collectors.toList()).size();
		meetingDinnerVO.setReimbursableCount(size);
		meetingDinnerVO.setEstimatePrice(meetingDinnerVO.getStandardFee().multiply(new BigDecimal(size)));
		meetingDinnerVO.setEmpVOList(empVOList);
		return meetingDinnerVO;
	}

	@Override
	public CheckInPositionVO getCheckInPosition(Integer infoId,String empId) {
		MeetingInfoPO infoPO = baseMapper.selectById(infoId);
		if(Objects.isNull(infoPO)){
			throw new ApplicationException("会议不存在");
		}
		CheckInPositionVO checkInPositionVO = new CheckInPositionVO();

		SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, empId).orderByDesc(SfaEmployeeInfoModel::getId).last("limit 1"));
		if(Objects.isNull(sfaEmployeeInfoModel)){
			return checkInPositionVO;
		}


		RealtimePositioningListRequest realtimePositioningListRequest = new RealtimePositioningListRequest();
		realtimePositioningListRequest.setEmployeeInfoId(sfaEmployeeInfoModel.getId());
		realtimePositioningListRequest.setPositioningStartDate(infoPO.getEndTime().toLocalDate().minusMonths(1L));
		realtimePositioningListRequest.setPositioningEndDate(infoPO.getEndTime().toLocalDate());
		List<RealtimePositioningVo> realtimePositioningVos = Optional.ofNullable(mapService.realtimePositioningList(realtimePositioningListRequest)).orElse(new ArrayList<>());
		Optional<RealtimePositioningVo> realtimePositionOptional = realtimePositioningVos.stream().sorted(Comparator.comparing(RealtimePositioningVo::getPositioningTime).reversed()).filter(f -> StringUtils.isNotBlank(f.getProvince())).findFirst();
		if(!realtimePositionOptional.isPresent()){
			return checkInPositionVO;
		}

		RealtimePositioningVo realtimePositioningVo = realtimePositionOptional.get();
		StringBuffer sb = new StringBuffer();
		if(StringUtils.isNotBlank(realtimePositioningVo.getProvince())){
			sb.append(realtimePositioningVo.getProvince());
		}
		if(StringUtils.isNotBlank(realtimePositioningVo.getCity())){
			sb.append(realtimePositioningVo.getCity());
		}
		if(StringUtils.isNotBlank(realtimePositioningVo.getDistrict())){
			sb.append(realtimePositioningVo.getDistrict());
		}
		if(StringUtils.isNotBlank(realtimePositioningVo.getAddress())){
			sb.append(realtimePositioningVo.getAddress());
		}
		checkInPositionVO.setLastCheckInPosition(sb.toString());

		String orgName = getOrganization(realtimePositioningVo.getProvince(),realtimePositioningVo.getCity(),realtimePositioningVo.getDistrict(), RequestUtils.getBusinessGroup());
		checkInPositionVO.setOrganizationName(orgName);
		return checkInPositionVO;
	}



    @Override
    public MeetingSelectEmpVo selectEmpVosForWeb(String person, Integer searchType, String key, String day, boolean filterCeo,Integer businessGroup) {

        MeetingSelectEmpVo meetingSelectEmpVo = this.selectEmpVos(person, searchType, key, day, filterCeo,businessGroup);
        if(Objects.isNull(meetingSelectEmpVo)){
            return meetingSelectEmpVo;
        }


        List<MeetingOrgEmpVo> searchResult = meetingSelectEmpVo.getSearchResult();
        meetingSelectEmpVo.setSearchResult(fillList(searchResult, searchType));

        meetingSelectEmpVo.setMeetingOrgEmpVos(fillList(meetingSelectEmpVo.getMeetingOrgEmpVos(), searchType));


        return meetingSelectEmpVo;
    }

	@Override
	public MeetingStartCheckVO checkMeetingStart(Integer infoId) {
		MeetingStartCheckVO meetingStartCheckVO = new MeetingStartCheckVO();
		List<MeetingEmpCheckVO> readList = new ArrayList<>();
		List<MeetingEmpCheckVO> unreadList = new ArrayList<>();


		MeetingInfoPO infoPO = baseMapper.selectById(infoId);
		if(Objects.isNull(infoPO)){
			throw new ApplicationException("会议ID不存在");
		}

		String yearMonth = infoPO.getStartTime().toLocalDate().toString().substring(0, 7);

		int monthValue = infoPO.getStartTime().getMonthValue();
		int year = infoPO.getStartTime().getYear();

		String organizationType = organizationMapper.getOrganizationType(infoPO.getOrganizationId());
		if(!infoPO.getCategory().equals("月会/季会") || (!organizationType.equals("area") && !organizationType.equals("varea"))){
			meetingStartCheckVO.setCanStart(true);
			meetingStartCheckVO.setReadList(readList);
			meetingStartCheckVO.setUnreadList(unreadList);
			return meetingStartCheckVO;
		}
		
		List<MeetingRecordPO> meetingRecordPOS = Optional.ofNullable(meetingRecordMapper.selectList(new LambdaQueryWrapper<MeetingRecordPO>().eq(MeetingRecordPO::getInfoId, infoId).eq(MeetingRecordPO::getRoleFlag, 0).eq(MeetingRecordPO::getIsDelete, 0))).orElse(new ArrayList<>());



		meetingRecordPOS.stream().filter(f -> Objects.nonNull(f.getEmployeeId()) && !f.getEmployeeId().equals(infoPO.getCreateBy())).forEach(e -> {
			String empOrgType = organizationMapper.getOrganizationType(e.getOrganizationId());
			if(!empOrgType.equals("zb") && !empOrgType.equals("branch")){
				MeetingEmpCheckVO meetingEmpCheckVO = new MeetingEmpCheckVO();

				SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, e.getEmployeeId()).last("limit 1"));
				if(Objects.nonNull(sfaEmployeeInfoModel)){
					ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
					if(Objects.nonNull(applyMemberPo)){
						meetingEmpCheckVO.setAvatar(applyMemberPo.getPicUrl());
					}
					String organizationName = organizationMapper.getOrganizationName(e.getOrganizationId());
					meetingEmpCheckVO.setEmpName(organizationName +"-"+ OrganizationTypeEnum.getPositionName(empOrgType) +"-"+ sfaEmployeeInfoModel.getEmployeeName());
				}


				if (monthValue == 1) {
					meetingEmpCheckVO.setReportName("第四季度复盘报告");
				}
				else if (monthValue == 4) {
					meetingEmpCheckVO.setReportName("第一季度复盘报告");
				}
				else if (monthValue == 7) {
					meetingEmpCheckVO.setReportName("第二季度复盘报告");
				}
				else if (monthValue == 10) {
					meetingEmpCheckVO.setReportName("第三季度复盘报告");
				}else{
					meetingEmpCheckVO.setReportName(year+"年"+monthValue+"月复盘报告");
				}

				SfaReviewReportEntity sfaReviewReportEntity = reviewReportMapper.selectOne(new LambdaQueryWrapper<SfaReviewReportEntity>()
						.eq(SfaReviewReportEntity::getOrganizationId, e.getOrganizationId())
						.eq(SfaReviewReportEntity::getYearMonth, yearMonth).eq(SfaReviewReportEntity::getDeleteFlag, 0).last("limit 1")
				);


				if(Objects.isNull(sfaReviewReportEntity) || sfaReviewReportEntity.getSendStatus() == 0){
					meetingEmpCheckVO.setStatus("待提交");
//					2024-10-17杨志慧：未提交的月报不需要查看
//                    unreadList.add(meetingEmpCheckVO);
				}else{

					String curOrgType = organizationMapper.getOrganizationType(e.getOrganizationId());

					if(!curOrgType.equals("area") && !curOrgType.equals("varea") ){
						// 获取直属上级
						SelectAuditDto selectAuditDto = new SelectAuditDto();
						selectAuditDto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(e.getOrganizationId()));
						selectAuditDto.setBusinessGroup(organizationMapper.getBusinessGroupById(e.getOrganizationId()));
						selectAuditDto.setChannel(3);
						selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_senior_hr_employee_id"));
						CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);
						// 跳过代理督导
						if(!auditPerson.getEmployeeName().equals("代理督导") && !auditPerson.getEmployeeId().equals("00272473")){
							Integer replyId = reviewReportMapper.queryReplyId(sfaReviewReportEntity.getReviewId(), auditPerson.getEmployeeId());
							if(Objects.isNull(replyId)){
								meetingEmpCheckVO.setStatus("待查看");
								unreadList.add(meetingEmpCheckVO);
							}else{
								meetingEmpCheckVO.setStatus("已查看");
								readList.add(meetingEmpCheckVO);
							}
						}
					}
                }
			}
		});
        meetingStartCheckVO.setShowList(true);
        meetingStartCheckVO.setReadList(readList);
        meetingStartCheckVO.setUnreadList(unreadList);
        if(unreadList.size() == 0){
            meetingStartCheckVO.setCanStart(true);
        }

		return meetingStartCheckVO;
	}

	@Override
	@Transactional
	public void audit(MeetingAuditRequest meetingAuditRequest) {
		String key = "sfa:estimate:audit";
		if (!redisUtil.setLockIfAbsent(key, meetingAuditRequest.getInfoId().toString(), 1, TimeUnit.SECONDS)) {
			throw new ApplicationException("请勿重复操作");
		}

		try{
			CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(meetingAuditRequest.getPerson(), RequestUtils.getLoginInfo());

			MeetingInfoPO infoPO = baseMapper.selectById(meetingAuditRequest.getInfoId());

			if(Objects.isNull(infoPO)){
				throw new ApplicationException("会议不存在");
			}


			// 设置会议表稽核状态，全部稽核正常显示"正常"
			List<AuditDetailRequest> auditDetailRequestList = meetingAuditRequest.getAuditDetailRequestList();
			Optional<AuditDetailRequest> hasException = auditDetailRequestList.stream().filter(f -> f.getAuditResult() == 2).findFirst();
			if(hasException.isPresent()){
				infoPO.setAuditStatus(2);
			}else{
				infoPO.setAuditStatus(1);
			}
			baseMapper.updateById(infoPO);

			// 记录稽核操作
			auditDetailRequestList.forEach(e -> {
				MeetingAuditPO meetingAuditPO = new MeetingAuditPO();
				BeanUtils.copyProperties(e,meetingAuditPO);
				meetingAuditPO.setInfoId(meetingAuditRequest.getInfoId());
				meetingAuditPO.setAuditUserId(personInfo.getEmployeeId());
				meetingAuditPO.setAuditUserName(personInfo.getEmployeeName());
				meetingAuditPO.setAuditTime(LocalDateTime.now());
				meetingAuditMapper.insert(meetingAuditPO);
			});
		}finally {
			redisUtil.unLock(key, meetingAuditRequest.getInfoId().toString());
		}

	}

	@Override
	public List<MeetingNoticeVO> notice(String empId) {
		return baseMapper.selectMeetingNotice(empId);
	}

	private List<MeetingOrgEmpVo> fillList(List<MeetingOrgEmpVo> searchResult,Integer searchType) {
        if(CollectionUtils.isEmpty(searchResult)){
            return ListUtils.EMPTY_LIST;
        }

        if(Objects.nonNull(searchType) && (searchType == 2 || searchType == 3)){
        	return searchResult;
		}

        List<MeetingOrgEmpVo> list = new ArrayList<>();




        searchResult.forEach(e -> {
            List<MeetingEmpVo> meetingEmpVoList = e.getMeetingEmpVoList();

			List<MeetingOrgEmpVo> tempChildren = new ArrayList<>();

			List<MeetingOrgEmpVo> children = e.getChildren();

            if(!CollectionUtils.isEmpty(meetingEmpVoList)){
                meetingEmpVoList.forEach(emp -> {
					Integer positionTypeId = emp.getPositionTypeId();
					MeetingOrgEmpVo meetingEmpVo = new MeetingOrgEmpVo();
					BeanUtils.copyProperties(emp,meetingEmpVo);
					meetingEmpVo.setKey(String.valueOf(emp.getMemberKey()));

					if(positionTypeId == 3){
						tempChildren.add(meetingEmpVo);
					}else{
						if(CollectionUtils.isEmpty(children)){
							e.setChildren(Arrays.asList(meetingEmpVo));
						}else{
							children.add(meetingEmpVo);
						}

					}
                });
            }
            e.setMeetingEmpVoList(null);




            if(!CollectionUtils.isEmpty(children)){
                e.setChildren(fillList(children,searchType));
            }



			MeetingOrgEmpVo meetingOrgEmpVo = new MeetingOrgEmpVo();
            BeanUtils.copyProperties(e,meetingOrgEmpVo);
			if(Objects.nonNull(e.getBusinessGroup())){
				meetingOrgEmpVo.setKey(String.valueOf(e.getBusinessGroup()));
			}
            if(StringUtils.isNotBlank(e.getOrganizationId())){
				meetingOrgEmpVo.setKey(e.getOrganizationId());
			}

			if(!CollectionUtils.isEmpty(e.getChildren())){
				meetingOrgEmpVo.setChildren(e.getChildren().stream().filter(Objects::nonNull)
						.sorted(Comparator.comparing(MeetingOrgEmpVo::getMemberKey, Comparator.nullsLast(Comparator.naturalOrder())))
						.collect(Collectors.toList()));
			}


			if(!CollectionUtils.isEmpty(tempChildren)){
				if(CollectionUtils.isEmpty(meetingOrgEmpVo.getChildren())){
					meetingOrgEmpVo.setChildren(tempChildren);
				}else{
					meetingOrgEmpVo.getChildren().addAll(tempChildren);
				}

			}

			if(StringUtils.isNotBlank(meetingOrgEmpVo.getKey())){
				list.add(meetingOrgEmpVo);
			}

            
        });
        return list;

    }

    private List<EmpOrgVO> searchEmpByOrgCode(String organizationId,Integer businessGroup, String day,boolean filterCeo) {
		List<EmpOrgVO> list = new ArrayList<>();
		String organizationType = organizationMapper.getOrganizationType(organizationId);
		List<SfaPositionRelationEntity> positionRelationEntityList = ListUtils.EMPTY_LIST;

		if(organizationType.equals("department") && !filterCeo){
			positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
					.eq(SfaPositionRelationEntity::getDepartmentCode,organizationId)
					.eq(SfaPositionRelationEntity::getStatus,1).eq(SfaPositionRelationEntity::getDeleteFlag,0));
		}else{
			positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
					.eq(SfaPositionRelationEntity::getOrganizationCode,organizationId)
					.eq(SfaPositionRelationEntity::getStatus,1).eq(SfaPositionRelationEntity::getDeleteFlag,0));
		}

		if(CollectionUtils.isEmpty(positionRelationEntityList)){
			return list;
		}

		SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(businessGroup);

		positionRelationEntityList.forEach(e -> {
			EmpOrgVO empOrgVO = new EmpOrgVO();
			empOrgVO.setType(3);
			empOrgVO.setPositionTypeId(e.getPositionTypeId());
			empOrgVO.setBusinessGroup(businessGroup);
			empOrgVO.setOrganizationId(e.getOrganizationCode());
			empOrgVO.setOrganizationName(organizationMapper.getOrganizationName(e.getOrganizationCode()));

			SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(e.getEmployeeInfoId());
			empOrgVO.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
			if(Objects.nonNull(sfaEmployeeInfoModel)){
				// 设置请假信息
				if(StringUtils.isNotBlank(day)){
					SfaLeave sfaLeave = sfaLeaveMapper.selectOne(new QueryWrapper<SfaLeave>().eq("apply_employee_info_id", sfaEmployeeInfoModel.getId())
							.eq("leave_status", 1).le("date_format(leave_start_time,'%Y-%m-%d')", day)
							.ge("date_format(leave_end_time,'%Y-%m-%d')", day)
							.eq("delete_flag", 0).last("limit 1")
					);

					if(Objects.nonNull(sfaLeave)){
						empOrgVO.setLeave(true);
						String startDate = LocalDateTimeUtils.formatTime(sfaLeave.getLeaveStartTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
						String endDate = LocalDateTimeUtils.formatTime(sfaLeave.getLeaveEndTime(),LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
						empOrgVO.setLeaveDate(startDate +"~"+endDate);
						empOrgVO.setLeaveReason(sfaLeave.getLeaveReason());
					}
				}

				ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
				if(Objects.nonNull(applyMemberPo)){
					empOrgVO.setAvatar(applyMemberPo.getPicUrl());
					String positionName = PositionEnum.getPositionName(applyMemberPo.getCeoType(), applyMemberPo.getJobsType(), applyMemberPo.getPosition());

					String employeeId = sfaEmployeeInfoModel.getEmployeeId();
					if(StringUtils.isBlank(employeeId)){
						employeeId = sfaEmployeeInfoModel.getMobile();
					}

					empOrgVO.setDescription(sfaBusinessGroupEntity.getBusinessGroupName()+"-"+empOrgVO.getOrganizationName() +"-"+positionName+"-" +applyMemberPo.getUserName()+"-"+employeeId);
				}
			}
			list.add(empOrgVO);
		});
		return list;
	}

	private boolean checkHasChildren(String organizationId,boolean filterCeo) {
		String organizationType = organizationMapper.getOrganizationType(organizationId);

		if(!organizationType.equals("department")){
			List<String> nextOrganizationByCode = organizationMapper.getNextOrganizationByCode(organizationId);
			if(!CollectionUtils.isEmpty(nextOrganizationByCode)){
				return true;
			}
		}




		if(organizationType.equals("department") && !filterCeo){
			Integer ceoCount = sfaPositionRelationMapper.selectCount(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getDepartmentCode, organizationId).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
			if(ceoCount > 0){
				return true;
			}
		}else{
			Integer ceoCount = sfaPositionRelationMapper.selectCount(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getOrganizationCode, organizationId).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0));
			if(ceoCount > 0){
				return true;
			}
		}
		return false;
	}

	private void searchSuperiorOrSubordinate(List<EmpOrgVO> result, String day, List<String> parentOrgCodes) {
		List<MeetingEmpVo> searchResult;
		searchResult = Optional.ofNullable(sfaPositionRelationMapper.selectEmpByOrgCode(parentOrgCodes)).orElse(new ArrayList<>());
		// 设置描述
		setDescription(searchResult, day);

		searchResult.forEach(e -> {
			EmpOrgVO empOrgVO = new EmpOrgVO();
			BeanUtils.copyProperties(e, empOrgVO);
			empOrgVO.setType(3);
			result.add(empOrgVO);
		});
	}

	private String getOrganization(String province, String city, String district, int businessGroup) {
		if(StringUtils.isBlank(province)){
			return StringUtils.EMPTY;
		}
		LambdaQueryWrapper<WwOrganizationAreaPo> query = new LambdaQueryWrapper<WwOrganizationAreaPo>();
		if(StringUtils.isNotBlank(district)){
			query.eq(WwOrganizationAreaPo::getProvinceName,province).eq(WwOrganizationAreaPo::getCityName,city).eq(WwOrganizationAreaPo::getDistrictName,district)
					.last("limit 1");
		}else if(StringUtils.isNotBlank(city)){
			query.eq(WwOrganizationAreaPo::getProvinceName,province).eq(WwOrganizationAreaPo::getCityName,city)
					.last("limit 1");
		}else{
			query.eq(WwOrganizationAreaPo::getProvinceName,province).last("limit 1");
		}


		WwOrganizationAreaPo areaPo = wwOrganizationAreaMapper.selectOne(query);
		if(Objects.isNull(areaPo)){
			if(StringUtils.isNotBlank(district)){
				return getOrganization(province,city,null, businessGroup);
			}
			else if(StringUtils.isNotBlank(city)){
				return getOrganization(province,null,null, businessGroup);
			}else{
				return StringUtils.EMPTY;
			}
		}

		String companyOrganizationId = areaPo.getCompanyOrganizationId();
		if(BusinessGroupEnum.D.getBusinessGroupId() == businessGroup
		|| BusinessGroupEnum.I.getBusinessGroupId() == businessGroup
		|| BusinessGroupEnum.J.getBusinessGroupId() == businessGroup){
			companyOrganizationId = areaPo.getExCompanyOrganizationId();
		}
		OrganizationRelationModel organizationRelationModel = organizationBindRelationMapper.selectOne(new LambdaQueryWrapper<OrganizationRelationModel>()
				.eq(OrganizationRelationModel::getOrgCode, companyOrganizationId).eq(OrganizationRelationModel::getBusinessGroup, RequestUtils.getBusinessGroup()).last("limit 1"));
		if(Objects.nonNull(organizationRelationModel)){
			return organizationMapper.getOrganizationName(organizationRelationModel.getSfaOrgCode());
		}
		return StringUtils.EMPTY;
	}


	private void setDescription(List<MeetingEmpVo> list, String day) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}

		// 设置请假信息
		setLeaveInfo(list, day);

		// 设置员工描述信息
		setEmployeeDescription(list);
	}



	/**
	 * 设置员工请假信息
	 */
	private void setLeaveInfo(List<MeetingEmpVo> list, String day) {
		if (StringUtils.isBlank(day) || CollectionUtils.isEmpty(list)) {
			return;
		}

		List<String> empIds = list.stream()
			.filter(f -> StringUtils.isNotBlank(f.getEmployeeId()))
			.map(MeetingEmpVo::getEmployeeId)
			.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(empIds)) {
			return;
		}

		List<SfaEmployeeInfoModel> employeeInfoList = sfaEmployeeInfoMapper.selectList(
			new LambdaQueryWrapper<SfaEmployeeInfoModel>().in(SfaEmployeeInfoModel::getEmployeeId, empIds)
		);

		if (CollectionUtils.isEmpty(employeeInfoList)) {
			return;
		}

		List<Integer> employeeInfoIds = employeeInfoList.stream()
			.map(SfaEmployeeInfoModel::getId)
			.collect(Collectors.toList());

		// 查询请假信息
		List<SfaLeave> sfaLeaves = sfaLeaveMapper.selectList(
			new QueryWrapper<SfaLeave>()
				.in("apply_employee_info_id", employeeInfoIds)
				.eq("leave_status", 1)
				.le("date_format(leave_start_time,'%Y-%m-%d')", day)
				.ge("date_format(leave_end_time,'%Y-%m-%d')", day)
				.eq("delete_flag", 0)
		);

		if (CollectionUtils.isEmpty(sfaLeaves)) {
			return;
		}

		// 构建员工ID到员工信息的映射
		Map<String, SfaEmployeeInfoModel> empIdToInfoMap = employeeInfoList.stream()
			.collect(Collectors.toMap(SfaEmployeeInfoModel::getEmployeeId, Function.identity(), (existing, replacement) -> existing));

		// 构建员工信息ID到请假信息的映射
		Map<Integer, SfaLeave> infoIdToLeaveMap = sfaLeaves.stream()
			.collect(Collectors.toMap(SfaLeave::getApplyEmployeeInfoId, Function.identity(), (existing, replacement) -> existing));

		// 设置请假信息
		list.stream()
			.filter(emp -> StringUtils.isNotBlank(emp.getEmployeeId()))
			.forEach(emp -> {
				SfaEmployeeInfoModel employeeInfo = empIdToInfoMap.get(emp.getEmployeeId());
				if (Objects.nonNull(employeeInfo)) {
					SfaLeave leave = infoIdToLeaveMap.get(employeeInfo.getId());
					if (Objects.nonNull(leave)) {
						emp.setLeave(true);
						String startDate = LocalDateTimeUtils.formatTime(leave.getLeaveStartTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
						String endDate = LocalDateTimeUtils.formatTime(leave.getLeaveEndTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss);
						emp.setLeaveDate(startDate + "~" + endDate);
						emp.setLeaveReason(leave.getLeaveReason());
					}
				}
			});
	}

	/**
	 * 设置员工描述信息
	 */
	private void setEmployeeDescription(List<MeetingEmpVo> list) {
		list.stream()
			.filter(emp -> Objects.nonNull(emp.getPositionTypeId()))
			.forEach(emp -> {
				String description = buildEmployeeDescription(emp);
				if (StringUtils.isNotBlank(description)) {
					emp.setDescription(description);
				}
			});
	}

	/**
	 * 根据职位类型构建员工描述信息
	 */
	private String buildEmployeeDescription(MeetingEmpVo emp) {
		Integer positionTypeId = emp.getPositionTypeId();
		String baseInfo = emp.getBusinessGroupName() + "-" + emp.getOrganizationName() + "-";
		String suffix = "-" + emp.getEmployeeName() + "-" + emp.getEmployeeId();

		String primaryPositionStr = StringUtils.EMPTY;

		Integer primaryPosition = emp.getPrimaryPosition();
		if(Objects.nonNull(primaryPosition) && primaryPosition == 1){
			primaryPositionStr = ComonLanguageEnum.PRIMARY_ROLE.getDesc();
		}else if(Objects.nonNull(primaryPosition) && primaryPosition == 0){
			primaryPositionStr = ComonLanguageEnum.SECONDARY_ROLE.getDesc();
		}



		switch (positionTypeId) {
			case 7:  // 总部员工
				return emp.getBusinessGroupName() + "-" + emp.getEmployeeName() + "-" + emp.getEmployeeId() + primaryPositionStr;
			case 3:  // 合伙人
				if (Objects.nonNull(emp.getCeoType()) && Objects.nonNull(emp.getJobsType()) && Objects.nonNull(emp.getPosition())) {
					return baseInfo + PositionEnum.getPositionName(emp.getCeoType(), emp.getJobsType(), emp.getPosition()) + suffix + primaryPositionStr;
				}
				return null;
			case 1:  // 战区督导
			case 2:  // 区域总监
			case 10: // 区域经理
			case 11: // 省区总监
			case 12: // 大区总监
				return baseInfo + OrganizationPositionRelationEnums.getPositionName(positionTypeId, RequestUtils.getLoginInfo().getLanguage()) + suffix + primaryPositionStr;
			default:
				return null;
		}
	}
}
