package com.wantwant.sfa.backend.arch.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.arch.dto.MenuFrequencyDTO;
import com.wantwant.sfa.backend.arch.entity.CustomResourcesEntity;
import com.wantwant.sfa.backend.arch.entity.ResourceEntity;
import com.wantwant.sfa.backend.arch.entity.RoleEmployeeRelationEntity;
import com.wantwant.sfa.backend.arch.entity.RoleResourcesRelationEntity;
import com.wantwant.sfa.backend.arch.request.MenuModuleRequest;
import com.wantwant.sfa.backend.arch.service.IRoleResourcesRelationService;
import com.wantwant.sfa.backend.arch.vo.MenuVo;
import com.wantwant.sfa.backend.arch.vo.ModuleVo;
import com.wantwant.sfa.backend.arch.vo.ResourcesVo;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.arch.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.market.vo.BusinessOrganizationVo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.TreeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/20/下午2:20
 */
@Service
@Slf4j
public class RoleResourcesRelationService implements IRoleResourcesRelationService {

    @Autowired
    private RoleResourcesRelationMapper roleResourcesRelationMapper;
    @Autowired
    private ResourceMapper resourceMapper;
    @Autowired
    private RoleEmployeeRelationMapper roleEmployeeRelationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private CustomResourcesMapper customResourcesMapper;
    @Autowired
    private ResourcesBTMapper resourcesBTMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;

    @Override
    public void bindResources(Integer roleId, List<Integer> resources, CeoBusinessOrganizationPositionRelation personInfo) {

        String language = RequestUtils.getLoginInfo().getLanguage();

        if(StringUtils.isBlank(language)){
            throw new ApplicationException(BizExceptionLanguageEnum.LANGUAGE_MUST_NOT_NULL.getTextMsg());
        }


        List<RoleResourcesRelationEntity> resourcesRelationEntities = roleResourcesRelationMapper.selectList(new QueryWrapper<RoleResourcesRelationEntity>()
                .eq("role_id", roleId)
        );




        if (CollectionUtils.isEmpty(resourcesRelationEntities) && CollectionUtils.isEmpty(resources)) {
            return;
        }

        // 未分配角色,直接更新
        if (CollectionUtils.isEmpty(resourcesRelationEntities) && !CollectionUtils.isEmpty(resources)) {
            resources.forEach(e -> {
                createNewResourcesRelation(roleId, personInfo, e);
            });

            return;
        }

        // 已分配的资源删除
        if (!CollectionUtils.isEmpty(resourcesRelationEntities) && CollectionUtils.isEmpty(resources)) {
            resourcesRelationEntities.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {
                e.setDeleteFlag(1);
                e.setUpdateUserId(personInfo.getEmployeeId());
                e.setUpdateUserName(personInfo.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                roleResourcesRelationMapper.updateById(e);
            });
            return;
        }


        // 检查是否有新增的角色
        resources.forEach(e -> {
            Optional<RoleResourcesRelationEntity> first = resourcesRelationEntities.stream().filter(f -> f.getResourceId().equals(e)).findFirst();
            if (first.isPresent()) {
                RoleResourcesRelationEntity roleResourcesRelationEntity = first.get();
                if (roleResourcesRelationEntity.getDeleteFlag() == 1) {
                    roleResourcesRelationEntity.setDeleteFlag(0);
                    roleResourcesRelationEntity.setUpdateUserId(personInfo.getEmployeeId());
                    roleResourcesRelationEntity.setUpdateUserName(personInfo.getEmployeeName());
                    roleResourcesRelationEntity.setUpdateTime(LocalDateTime.now());
                    roleResourcesRelationMapper.updateById(roleResourcesRelationEntity);
                }
            } else {
                // 创建新的角色
                createNewResourcesRelation(roleId, personInfo, e);
            }
        });

        // 检查是否有剔除的角色
        resourcesRelationEntities.stream().filter(f -> f.getDeleteFlag() == 0).forEach(e -> {

            Integer tempResourceId = e.getResourceId();

            Optional<Integer> first = resources.stream().filter(f -> f.equals(tempResourceId)).findFirst();

            if (!first.isPresent()) {
                e.setDeleteFlag(1);
                e.setUpdateUserId(personInfo.getEmployeeId());
                e.setUpdateUserName(personInfo.getEmployeeName());
                e.setUpdateTime(LocalDateTime.now());
                roleResourcesRelationMapper.updateById(e);
            }
        });
    }

    @Override
    public List<ResourcesVo> getResources(Integer terminal, Integer roleId) {
        String language = RequestUtils.getLoginInfo().getLanguage();
        log.info("get Resources language:{}",language);
        if(StringUtils.isBlank(language)){
            throw new ApplicationException(BizExceptionLanguageEnum.LANGUAGE_MUST_NOT_NULL.getTextMsg());
        }
        // 根据language获取资源
        List<ResourceEntity> resources = resourceMapper.selectByLanguage(language,terminal);

        List<Integer> checkedList = ListUtils.EMPTY_LIST;
        if (Objects.nonNull(roleId)) {
            // 获取所有选中的资源
            checkedList = roleResourcesRelationMapper.selectCheckedResource(roleId);
        }

        if (CollectionUtils.isEmpty(resources)) {
            return ListUtils.EMPTY_LIST;
        }

        List<ResourcesVo> voList = new ArrayList<>();
        List<Integer> finalCheckedList = checkedList;
        resources.forEach(e -> {
            ResourcesVo resourcesVo = new ResourcesVo();
            resourcesVo.setResourcesId(e.getId());
            resourcesVo.setResourceName(e.getTitle());
            resourcesVo.setTerminal(e.getTerminal());
            resourcesVo.setHidden(e.getHidden());
            resourcesVo.setParentId(e.getParentId());
            resourcesVo.setId(e.getId());
            if(StringUtils.isNotBlank(e.getContextMenu())){
                resourcesVo.setContextMenu(Arrays.asList(e.getContextMenu().split(",")).stream().map(Integer::valueOf).collect(Collectors.toList()));
            }

            // 判断是否选中
            Optional<Integer> first = finalCheckedList.stream().filter(f -> f.equals(e.getId())).findFirst();
            if (first.isPresent()) {
                resourcesVo.setChecked(true);
            }


            voList.add(resourcesVo);
        });


        return TreeUtil.list2Tree(voList);
    }

    @Override
    public List<ModuleVo> getMenuModule(MenuModuleRequest request) {

        String language = RequestUtils.getLoginInfo().getLanguage();
        log.info("get Resources language:{}",language);
        if(StringUtils.isBlank(language)){
            throw new ApplicationException(BizExceptionLanguageEnum.LANGUAGE_MUST_NOT_NULL.getTextMsg());
        }

        // 获取登陆人岗位
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, request.getPerson())
                .eq(SfaPositionRelationEntity::getPositionTypeId, loginInfo.getPositionTypeId())
                .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .orderByAsc(SfaPositionRelationEntity::getPartTime)
        );

        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException(BizExceptionLanguageEnum.POSITION_NOT_EXIST.getTextMsg());
        }
        // 所有岗位
        List<String> positionList = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getPositionId).collect(Collectors.toList());

        // 获取登陆人角色信息
        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().in(RoleEmployeeRelationEntity::getPositionId, positionList).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            throw new ApplicationException(BizExceptionLanguageEnum.ROLE_NOT_EXIST.getTextMsg());
        }
        List<Integer> roleIds = roleEmployeeRelationEntities.stream().map(RoleEmployeeRelationEntity::getRoleId).collect(Collectors.toList());
        // 根据角色获取菜单
        List<RoleResourcesRelationEntity> resourcesRelationEntities = roleResourcesRelationMapper.selectList(new LambdaQueryWrapper<RoleResourcesRelationEntity>().in(RoleResourcesRelationEntity::getRoleId, roleIds).eq(RoleResourcesRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(resourcesRelationEntities)){
            throw new ApplicationException(BizExceptionLanguageEnum.RESOURCE_NOT_EXIST.getTextMsg());
        }



        List<Integer> menusIds = resourcesRelationEntities.stream().map(RoleResourcesRelationEntity::getResourceId).collect(Collectors.toList());
        List<ResourceEntity> resourceEntities = resourceMapper.selectResourcesByIds(menusIds, language);

        List<MenuVo> menuList = new ArrayList<>();

        // 获取用户设置的快捷菜单
        List<CustomResourcesEntity> convenientMenuList = Optional.ofNullable(customResourcesMapper.selectList(new LambdaQueryWrapper<CustomResourcesEntity>().eq(CustomResourcesEntity::getEmpId, request.getPerson()).eq(CustomResourcesEntity::getType, 1).eq(CustomResourcesEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());
        // 更新提示菜单
        List<CustomResourcesEntity> promptMenuEntities = Optional.ofNullable(customResourcesMapper.selectList(new LambdaQueryWrapper<CustomResourcesEntity>().eq(CustomResourcesEntity::getEmpId, request.getPerson()).eq(CustomResourcesEntity::getType, 2).eq(CustomResourcesEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());

        // 查询查看频次
        List<MenuFrequencyDTO> frequencyDTOList = Optional.ofNullable(selectFrequency(request)).orElse(new ArrayList<>());

        // 便捷菜单
        List<MenuVo> cMenuList = new ArrayList<>();



        resourceEntities.stream().filter(f -> f.getHidden() == 0).forEach(e -> {
            MenuVo menuVo = new MenuVo();
            menuVo.setMenuName(e.getTitle());
            menuVo.setOrderNum(e.getOrderNum());
            menuVo.setOriginalMenuName(e.getOriginalTitle());
            Integer appSupport = e.getAppSupport();
            if(appSupport == 1){
                menuVo.setAppSupport(true);
            }
            // 更新提示菜单
            if(promptMenuEntities.stream().filter(f -> f.getResourceId().equals(e.getId())).findFirst().isPresent()){
                menuVo.setUpdatePrompt(true);
            }

            menuVo.setPath(e.getPath());
            menuVo.setId(e.getId());
            menuVo.setParentId(e.getParentId());
            menuVo.setDescription(e.getDescription());
            menuList.add(menuVo);

            // 快捷菜单
            if(convenientMenuList.stream().filter(f -> f.getResourceId().equals(e.getId())).findFirst().isPresent()){
                cMenuList.add(menuVo);
            }
        });



        List<MenuVo> finalList =  menuList.stream().sorted(Comparator.comparing(MenuVo::getOrderNum)).collect(Collectors.toList());

        List<MenuVo> menuVos = TreeUtil.list2Tree(finalList);

        List<ModuleVo> list = new LinkedList<>();

        // 快捷菜单
        if(!CollectionUtils.isEmpty(cMenuList)){
            ModuleVo moduleVo = new ModuleVo();
            moduleVo.setModuleName(ComonLanguageEnum.CONVENIENT_MENU.getDesc());
            moduleVo.setMenuList(cMenuList);
            list.add(moduleVo);
        }


        // 转换成2层结构
        menuVos.forEach(e -> {
            ModuleVo moduleVo = new ModuleVo();
            moduleVo.setModuleName(e.getMenuName());
            moduleVo.setDescription(e.getDescription());
            moduleVo.setOriginalModuleName(e.getOriginalMenuName());
            Optional<RoleResourcesRelationEntity> parentOptional = resourcesRelationEntities.stream().filter(f -> f.getResourceId().equals(e.getId()) && f.getConcern() == 1).findFirst();
            if(parentOptional.isPresent()){
                moduleVo.setConcern(true);
            }


            List<MenuVo> children = e.getChildren();
            List<MenuVo> childrenMenu = TreeUtil.tree2list(children);
            List<MenuVo> temp = new ArrayList<>();
            childrenMenu.stream().filter(f -> StringUtils.isNotBlank(f.getPath())).forEach(m -> {
                MenuVo menuVo = new MenuVo();
                BeanUtils.copyProperties(m,menuVo);

                String path = m.getPath();
                if(StringUtils.isNotBlank(path) && path.equals("/newData")){
                    SfaPositionRelationEntity positionRelationEntity = positionRelationEntityList.stream().findFirst().get();
                    menuVo.setPath(path + "?orgId=" + positionRelationEntity.getOrganizationCode());
                }

                Optional<RoleResourcesRelationEntity> childOptional = resourcesRelationEntities.stream().filter(f -> f.getResourceId().equals(m.getId()) && f.getConcern() == 1).findFirst();
                if(childOptional.isPresent()){
                    menuVo.setConcern(true);
                }

                // 设置查看率和频次
                String menuName = m.getOriginalMenuName();
                Optional<MenuFrequencyDTO> first = frequencyDTOList.stream().filter(f -> f.getTitle().equals(menuName)).findFirst();
                if(first.isPresent()){
                    MenuFrequencyDTO menuFrequencyDTO = first.get();
                    menuVo.setViewRate(menuFrequencyDTO.getViewRate());
                    menuVo.setMonthFrequency(menuFrequencyDTO.getMonthFrequency());
                }else{
                    menuVo.setViewRate(BigDecimal.ZERO);
                    menuVo.setMonthFrequency(BigDecimal.ZERO);
                }

                temp.add(menuVo);
            });
            moduleVo.setMenuList(temp);
            list.add(moduleVo);
        });


        if(StringUtils.isNotBlank(request.getKey())){

            List<ModuleVo> moduleVoList = list.stream().filter(f -> f.getModuleName().contains(request.getKey())).collect(Collectors.toList());

            List<ModuleVo> menuVoList = list.stream().filter(f -> f.getMenuList().stream().filter(gf -> gf.getMenuName().contains(request.getKey())).findFirst().isPresent()).collect(Collectors.toList());

            list.clear();

            if(!CollectionUtils.isEmpty(menuVoList)){
                menuVoList.forEach(e -> {
                    e.setMenuList(e.getMenuList().stream().filter(f -> f.getMenuName().contains(request.getKey())).collect(Collectors.toList()));
                });

                list.addAll(menuVoList);
            }

            if(!CollectionUtils.isEmpty(moduleVoList)){
                moduleVoList.forEach(e -> {
                    Optional<ModuleVo> first = list.stream().filter(f -> f.getOriginalModuleName().equals(e.getModuleName())).findFirst();
                    if(!first.isPresent()){
                        list.add(e);
                    }
                });
            }
        }




        return list;
    }

    private List<MenuFrequencyDTO> selectFrequency(MenuModuleRequest request) {
        String organizationId = request.getOrganizationId();

        if(StringUtils.isBlank(organizationId)){

            if( StringUtils.isBlank(request.getPositionTypeId())){
                // 总部
                return resourceMapper.selectFrequency(request.getPerson());
            }else{
                SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectById(RequestUtils.getBusinessGroup());
                request.setBusinessGroup(sfaBusinessGroupEntity.getBusinessGroupName());
                return resourcesBTMapper.selectFrequency(request);
            }

        }else{
            String organizationType = organizationMapper.getOrganizationType(request.getOrganizationId());
            request.setOrganizationType(organizationType);
            // 非总部查询大数据
            return resourcesBTMapper.selectFrequency(request);
        }
    }

    @Override
    public int getPromptCount(String person) {

        // 获取登陆人岗位
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmpId, person).eq(SfaPositionRelationEntity::getPositionTypeId, loginInfo.getPositionTypeId()).eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup()).eq(SfaPositionRelationEntity::getDeleteFlag, 0).eq(SfaPositionRelationEntity::getStatus, 1));

        if(CollectionUtils.isEmpty(positionRelationEntityList)){
            throw new ApplicationException("岗位信息获取失败");
        }
        // 所有岗位
        List<String> positionList = positionRelationEntityList.stream().map(SfaPositionRelationEntity::getPositionId).collect(Collectors.toList());

        // 获取登陆人角色信息
        List<RoleEmployeeRelationEntity> roleEmployeeRelationEntities = roleEmployeeRelationMapper.selectList(new LambdaQueryWrapper<RoleEmployeeRelationEntity>().in(RoleEmployeeRelationEntity::getPositionId, positionList).eq(RoleEmployeeRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(roleEmployeeRelationEntities)){
            throw new ApplicationException("登陆人角色获取失败");
        }
        List<Integer> roleIds = roleEmployeeRelationEntities.stream().map(RoleEmployeeRelationEntity::getRoleId).collect(Collectors.toList());
        // 根据角色获取菜单
        List<RoleResourcesRelationEntity> resourcesRelationEntities = roleResourcesRelationMapper.selectList(new LambdaQueryWrapper<RoleResourcesRelationEntity>().in(RoleResourcesRelationEntity::getRoleId, roleIds).eq(RoleResourcesRelationEntity::getDeleteFlag, 0));
        if(CollectionUtils.isEmpty(resourcesRelationEntities)){
            throw new ApplicationException("无可用菜单");
        }

        List<Integer> menusIds = resourcesRelationEntities.stream().map(RoleResourcesRelationEntity::getResourceId).collect(Collectors.toList());
        List<ResourceEntity> resourceEntities = resourceMapper.selectList(new LambdaQueryWrapper<ResourceEntity>().in(ResourceEntity::getId,menusIds).eq(ResourceEntity::getDeleteFlag,0).orderByAsc(ResourceEntity::getId));

        // 获取用户设置的快捷菜单
        List<CustomResourcesEntity> convenientMenuList = Optional.ofNullable(customResourcesMapper.selectList(new LambdaQueryWrapper<CustomResourcesEntity>().eq(CustomResourcesEntity::getEmpId, person).eq(CustomResourcesEntity::getType, 2).eq(CustomResourcesEntity::getDeleteFlag, 0))).orElse(new ArrayList<>());


        List<ResourceEntity> collect = resourceEntities.stream().filter(e -> convenientMenuList.stream().filter(cf -> cf.getResourceId().equals(e.getId())).findFirst().isPresent()).collect(Collectors.toList());
        return collect.size();
    }

    private String getParentPath(List<ResourceEntity> resourceEntities, Integer parentId) {
        if(Objects.nonNull(parentId)){
            Optional<ResourceEntity> first = resourceEntities.stream().filter(f -> f.getId().equals(parentId)).findFirst();
            if(first.isPresent()){
                ResourceEntity resourceEntity = first.get();
                String path = resourceEntity.getPath();
                if(path.startsWith("/")){
                    return path;
                }else{
                    return getParentPath(resourceEntities, resourceEntity.getParentId());
                }

            }
        }else{
            return "";
        }

        return "";
    }


    private void createNewResourcesRelation(Integer roleId, CeoBusinessOrganizationPositionRelation personInfo, Integer e) {
        RoleResourcesRelationEntity roleResourcesRelationEntity = new RoleResourcesRelationEntity();
        roleResourcesRelationEntity.setRoleId(roleId);
        roleResourcesRelationEntity.setResourceId(e);
        roleResourcesRelationEntity.setDeleteFlag(0);
        roleResourcesRelationEntity.setCreateUserId(personInfo.getEmployeeId());
        roleResourcesRelationEntity.setCreateUserName(personInfo.getEmployeeName());
        roleResourcesRelationEntity.setCreateTime(LocalDateTime.now());
        roleResourcesRelationEntity.setUpdateUserId(personInfo.getEmployeeId());
        roleResourcesRelationEntity.setUpdateUserName(personInfo.getEmployeeName());
        roleResourcesRelationEntity.setUpdateTime(LocalDateTime.now());
        roleResourcesRelationMapper.insert(roleResourcesRelationEntity);
    }

}
