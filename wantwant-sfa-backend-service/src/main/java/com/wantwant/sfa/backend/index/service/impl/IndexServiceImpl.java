package com.wantwant.sfa.backend.index.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wantwant.sfa.backend.BIndex.vo.BindexV2ConfigurationInfoVo;
import com.wantwant.sfa.backend.BIndex.vo.BindexV2ConfigurationVo;
import com.wantwant.sfa.backend.BIndex.vo.BindexV2OrgConfigurationInfoVo;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.index.dto.NextOrgDTO;
import com.wantwant.sfa.backend.index.service.IndexService;
import com.wantwant.sfa.backend.index.vo.*;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.mapper.ConfigMapper;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.RealtimeDataMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.review.SfaReviewReportMapper;
import com.wantwant.sfa.backend.mapper.review.SfaReviewReportPromisePerformanceMapper;
import com.wantwant.sfa.backend.metrics.service.IAlterService;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.review.entity.SfaReviewReportEntity;
import com.wantwant.sfa.backend.review.entity.SfaReviewReportPromisePerformanceEntity;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.util.CalculateUtils;
import com.wantwant.sfa.backend.util.RealTimeUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/01/26/上午9:27
 */
@Service
@Slf4j
public class IndexServiceImpl implements IndexService {
    @Resource
    private RealTimeUtils realTimeUtils;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    private SfaReviewReportMapper sfaReviewReportMapper;
    @Autowired
    private SfaReviewReportPromisePerformanceMapper sfaReviewReportPromisePerformanceMapper;
    @Autowired
    private RealtimeDataMapper realtimeDataMapper;
    @Autowired
    private IAlterService alterService;

    @Autowired
    private OrganizationMapper organizationMapper;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private RedisUtil redisUtil;
    private static final String NEXT_ORG_KEY = "sfa:next:org:";


    @Override
    public UserInfoVo getUserName(String person) {
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person, RequestUtils.getLoginInfo());
        UserInfoVo userInfoVo = new UserInfoVo();
        userInfoVo.setEmpName(personInfo.getEmployeeName());
        return userInfoVo;
    }

    @Override
    public PromiseVo getPromise(String person, String orgCode) {
        PromiseVo promiseVo = new PromiseVo();
        LoginModel loginInfo = RequestUtils.getLoginInfo();

        String zw_senior_hr_employee_id = configMapper.getValueByCode("zw_senior_hr_employee_id");
        // 如果是总部非老板直接返回
        if(loginInfo.getPositionTypeId() == 7 && !person.equals(zw_senior_hr_employee_id)){
            return promiseVo;
        }
        // 获取上月
        String lastMonth = LocalDate.now().minusMonths(1L).toString().substring(0, 7);

        // 获取上月月报数据
        SfaReviewReportEntity sfaReviewReportEntity = sfaReviewReportMapper.selectOne(new LambdaQueryWrapper<SfaReviewReportEntity>().eq(SfaReviewReportEntity::getYearMonth, lastMonth).eq(SfaReviewReportEntity::getOrganizationId, orgCode).eq(SfaReviewReportEntity::getDeleteFlag, 0));
        if(Objects.isNull(sfaReviewReportEntity)){
            return promiseVo;
        }

        // 获取月报承诺金额
        List<SfaReviewReportPromisePerformanceEntity> sfaReviewReportPromisePerformanceEntities = Optional.ofNullable(sfaReviewReportPromisePerformanceMapper.selectList(new LambdaQueryWrapper<SfaReviewReportPromisePerformanceEntity>()
                .eq(SfaReviewReportPromisePerformanceEntity::getOrganizationId, orgCode)
                .eq(SfaReviewReportPromisePerformanceEntity::getTheYearMonth, LocalDate.now().toString().substring(0, 7))
                .eq(SfaReviewReportPromisePerformanceEntity::getDeleteFlag, 0)
        )).orElse(new ArrayList<>());

        Optional<SfaReviewReportPromisePerformanceEntity> achievementPromiseOptional = sfaReviewReportPromisePerformanceEntities.stream().filter(f -> f.getMetricsName().contains("承诺完成业绩")).findFirst();

        if(!achievementPromiseOptional.isPresent()){
            return promiseVo;
        }

        SfaReviewReportPromisePerformanceEntity sfaReviewReportPromisePerformanceEntity = achievementPromiseOptional.get();

        promiseVo.setPromiseAmount(sfaReviewReportPromisePerformanceEntity.getPromiseAmount());
        promiseVo.setConfirmAmount(sfaReviewReportPromisePerformanceEntity.getConfirmAmount());


        DataOverviewVo dataOverviewVo = realtimeDataMapper.getDataOverview(orgCode,LocalDate.now().toString().substring(0,7),RequestUtils.getBusinessGroup());


        if(Objects.nonNull(dataOverviewVo) && Objects.nonNull(dataOverviewVo.getItemSupplyTotalCm()) && dataOverviewVo.getItemSupplyTotalCm().compareTo(BigDecimal.ZERO) != 0){

            BigDecimal promiseAmount = sfaReviewReportPromisePerformanceEntity.getPromiseAmount();

            BigDecimal confirmAmount = sfaReviewReportPromisePerformanceEntity.getConfirmAmount();

            BigDecimal amount = promiseAmount;
            if(Objects.isNull(amount) || Objects.nonNull(confirmAmount)){
                amount = confirmAmount;
            }




            BigDecimal rate = CalculateUtils.ratioPercent(dataOverviewVo.getItemSupplyTotalCm(), amount, 2);
            promiseVo.setAchievementRate(rate);

        }


        return promiseVo;
    }

    @Override
    public DataOverviewVo getDataOverview(String person, String orgCode) {
        String currentDate = LocalDate.now().toString().substring(0, 7);
        DataOverviewVo dataOverviewVo = realtimeDataMapper.getDataOverview(orgCode,currentDate,RequestUtils.getBusinessGroup());

        if(Objects.isNull(dataOverviewVo)){
            dataOverviewVo = new DataOverviewVo();
        }

        settingColor((IndicateColorVO)dataOverviewVo,orgCode,currentDate);
        SfaPositionRelationEntity positionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getOrganizationCode, orgCode)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .last("limit 1")
        );
        if(Objects.nonNull(positionRelationEntity)){
            dataOverviewVo.setEmployeeInfoId(positionRelationEntity.getEmployeeInfoId());
        }
        return dataOverviewVo;
    }



    private void settingColor(IndicateColorVO indicateColorVO, String orgCode, String currentDate) {
        if(Objects.isNull(indicateColorVO)){
            return;
        }
        // 盘价业绩目标达成率颜色设置
//        indicateColorVO.setSaleGoalColor(alterService.getAlterColor(32L,indicateColorVO.getSaleGoalAchievementRate(),currentDate,orgCode));
        // 盘价业绩环比增长率颜色
        indicateColorVO.setMonthRingRatioColor(alterService.getAlterColor(108L,indicateColorVO.getMonthRingRatio(),currentDate,orgCode));
        // 盘价业绩同步增长率颜色
        indicateColorVO.setMonthOnMonthRatioColor(alterService.getAlterColor(31L,indicateColorVO.getMonthOnMonthRatio(),currentDate,orgCode));
    }




    @Override
    public List<NextAchievementVo> getNextAchievement(String orgCode,String person,String orderField,String orderType) {

        List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + orgCode);
        log.info("【next org code】codes:{}",nextOrgCode);
        if(CollectionUtils.isEmpty(nextOrgCode)){
            return null;
        }

        List<NextOrgDTO> orgCodes = new ArrayList<>();

        List<NextOrgDTO> agentOrgCodes = new ArrayList<>();

        List<String> orgCodeList = new ArrayList<>();
        // 获取直属下级
        nextOrgCode.forEach(e -> {
            String organizationType = organizationMapper.getOrganizationType(e);
            if(StringUtils.isNotBlank(organizationType)){
                NextOrgDTO nextOrgDTO = new NextOrgDTO();
                nextOrgDTO.setOrgCode(e);
                nextOrgDTO.setOrgType(organizationType);
                orgCodeList.add(e);
                // 战区大区有代理组织
                if(organizationType.equals("province") || organizationType.equals("area")){
                    List<Map<String, String>> nextOrganization = organizationMapper.getNextOrganization(e);
                    if(!CollectionUtils.isEmpty(nextOrganization)){
                        nextOrganization.forEach(v -> {
                            NextOrgDTO agentDTO = new NextOrgDTO();
                            agentDTO.setOrgCode(v.get("organizationId"));
                            orgCodeList.add(v.get("organizationId"));
                            agentDTO.setOrgType(organizationType);
                            agentDTO.setParentOrgCode(e);
                            agentOrgCodes.add(agentDTO);
                        });
                    }
                }
                orgCodes.add(nextOrgDTO);
            }
        });

        // 获取本月承诺额度
        List<SfaReviewReportPromisePerformanceEntity> promisePerformanceList = Optional.ofNullable(sfaReviewReportPromisePerformanceMapper.selectList(new LambdaQueryWrapper<SfaReviewReportPromisePerformanceEntity>().eq(SfaReviewReportPromisePerformanceEntity::getTheYearMonth, LocalDate.now().toString().substring(0, 7))
                .in(SfaReviewReportPromisePerformanceEntity::getOrganizationId, orgCodeList).eq(SfaReviewReportPromisePerformanceEntity::getDeleteFlag, 0)
                .likeLeft(SfaReviewReportPromisePerformanceEntity::getMetricsName, "承诺完成业绩")
        )).orElse(new ArrayList<>());

        List<NextAchievementVo> parentList = realtimeDataMapper.getNextAchievement(orgCodes,orderField,orderType);
        if(CollectionUtils.isEmpty(parentList)){
            return ListUtils.EMPTY_LIST;
        }
        parentList.forEach(e -> {
            e.setPosition(OrganizationTypeEnum.getPositionName(e.getOrganizationType()));
            Optional<SfaReviewReportPromisePerformanceEntity>  promiseOptional = promisePerformanceList.stream().filter(f -> f.getOrganizationId().equals(e.getOrganizationId())).findFirst();
            if(promiseOptional.isPresent()){
                SfaReviewReportPromisePerformanceEntity sfaReviewReportPromisePerformanceEntity = promiseOptional.get();
                BigDecimal promiseAmount = sfaReviewReportPromisePerformanceEntity.getPromiseAmount();
                e.setPromisePerformance(promiseAmount);
                e.setPromiseAchievement(CalculateUtils.ratioPercent(e.getItemsSupplyTotalCm(),promiseAmount,3));
                e.setPromiseAchievementColor(realTimeUtils.saleGoalAchievementColor("10",LocalDate.now().toString().substring(0, 7),e.getPromiseAchievement(),true));
            }
            e.setSaleGoalColor(realTimeUtils.saleGoalAchievementColor("10",LocalDate.now().toString().substring(0, 7),e.getSaleGoalAchievementRate(),true));
        });

        if(CollectionUtils.isEmpty(agentOrgCodes)){
            return parentList;
        }


        List<NextAchievementVo> children = realtimeDataMapper.getNextAchievement(agentOrgCodes,orderField,orderType);
        if(CollectionUtils.isEmpty(children)){
            return parentList;
        }

        List<NextAchievementVo> list = new ArrayList<>();
        parentList.forEach(e -> {
            list.add(e);

            List<NextAchievementVo> collect = children.stream().filter(f -> f.getOrganizationParentId().equals(e.getOrganizationId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(collect)){
                collect.forEach(c -> {
                    c.setPosition(OrganizationTypeEnum.getPositionName(c.getOrganizationType()));
                    Optional<SfaReviewReportPromisePerformanceEntity>  promiseOptional = promisePerformanceList.stream().filter(f -> f.getOrganizationId().equals(c.getOrganizationId())).findFirst();
                    if(promiseOptional.isPresent()){
                        SfaReviewReportPromisePerformanceEntity sfaReviewReportPromisePerformanceEntity = promiseOptional.get();
                        BigDecimal promiseAmount = sfaReviewReportPromisePerformanceEntity.getPromiseAmount();
                        c.setPromisePerformance(promiseAmount);
                        c.setPromiseAchievement(CalculateUtils.ratioPercent(c.getItemsSupplyTotalCm(),promiseAmount,4));
                        c.setPromiseAchievementColor(realTimeUtils.saleGoalAchievementColor("10",LocalDate.now().toString().substring(0, 7),c.getPromiseAchievement(),true));
                    }
                    c.setEmployeeName(e.getEmployeeName());
                    c.setWorkingDays(e.getWorkingDays());
                    c.setSaleGoalColor(realTimeUtils.saleGoalAchievementColor("10",LocalDate.now().toString().substring(0, 7),c.getSaleGoalAchievementRate(),true));
                    list.add(c);
                });

            }

        });

        return list;
    }
}
