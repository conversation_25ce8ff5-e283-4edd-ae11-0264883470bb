package com.wantwant.sfa.backend.config;

import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.common.base.enums.BaseEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description 提前初始化需要校验的枚举
 * <AUTHOR>
 * @Date 2025/7/17 15:22
 **/
@Slf4j
@Component
public class EnumConfigService {

    @PostConstruct
    public void runOnStartup() {
        log.info("项目启动时执行校验枚举初始化");
        BaseEnum.enumClasses.add(BizExceptionLanguageEnum.class);

        // 添需要扫码的加包，这样以后新增枚举，就不用修改这里的代码
//        BaseEnum.packageNames.add("com.wantwant.sfa.sales.management.infrastructure.enums.language.scan");

    }
}
