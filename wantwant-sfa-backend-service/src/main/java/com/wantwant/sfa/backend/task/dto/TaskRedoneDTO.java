package com.wantwant.sfa.backend.task.dto;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 任务重办DTO
 * @Auther: zhengxu
 * @Date: 2023/08/10/下午4:00
 */
@Data
@Builder
@ToString
public class TaskRedoneDTO {

    private Long taskId;

    private String remark;

    private LocalDate deadline;

    private String taskTag;

    private String processUserId;

    private String processUserName;

    private Integer sendMessage;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
}


