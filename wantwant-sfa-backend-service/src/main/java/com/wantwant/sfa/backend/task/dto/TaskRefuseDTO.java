package com.wantwant.sfa.backend.task.dto;

import com.wantwant.sfa.backend.taskManagement.request.TaskAnnexRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description: 任务驳回DTO
 * @Auther: zhengxu
 * @Date: 2025/08/18
 */
@Data
@Builder
@ToString
public class TaskRefuseDTO {

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("任务标签")
    private String taskTag;

    @ApiModelProperty("处理人工号")
    private String processUserId;

    @ApiModelProperty("处理人姓名")
    private String processUserName;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
}
