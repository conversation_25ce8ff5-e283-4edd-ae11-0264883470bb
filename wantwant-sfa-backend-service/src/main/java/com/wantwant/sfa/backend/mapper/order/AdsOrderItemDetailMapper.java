package com.wantwant.sfa.backend.mapper.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.daily.request.DailyOrderPageRequest;
import com.wantwant.sfa.backend.daily.vo.DailyOrderVo;
import com.wantwant.sfa.backend.order.request.OrderDetailNewRequest;
import com.wantwant.sfa.backend.order.request.OrderListNewRequest;
import com.wantwant.sfa.backend.order.request.OrderSkuListNewRequest;
import com.wantwant.sfa.backend.order.request.SearchOrderNoRequest;
import com.wantwant.sfa.backend.order.vo.DeliveryInformationVo;
import com.wantwant.sfa.backend.order.vo.OrderDetailNewVo;
import com.wantwant.sfa.backend.order.vo.OrderListNewVo;
import com.wantwant.sfa.backend.order.vo.OrderSkuExportNewVo;
import com.wantwant.sfa.backend.order.vo.OrderSkuListNewVo;
import com.wantwant.sfa.backend.wallet.vo.AssociateOrderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("production_6")
public interface AdsOrderItemDetailMapper extends BaseMapper<Object> {
    List<OrderListNewVo> queryOrderList(Page<OrderListNewVo> page, @Param("params") OrderListNewRequest request);

    OrderDetailNewVo queryOrderDetail(@Param("params") OrderDetailNewRequest request);

    List<OrderSkuListNewVo> queryOrderSkuList(Page<OrderSkuListNewVo> page, @Param("params") OrderSkuListNewRequest request);

    List<OrderSkuExportNewVo> exportOrderSkuList(Page<OrderSkuExportNewVo> page, @Param("params") OrderListNewRequest request);

    List<OrderListNewVo> queryNotFirstOrderMemberKeyList(@Param("notFirstOrderMemberKeyList") List<OrderListNewVo> notFirstOrderMemberKeyList,
                                                         @Param("timeZone") String timeZone);

    List<DailyOrderVo> queryDailyOrderList(Page<DailyOrderVo> page, @Param("params") DailyOrderPageRequest request);

    List<String> selectOrderNo(SearchOrderNoRequest searchOrderNoRequest);

    List<AssociateOrderVO> selectByOrderList(@Param("orderList") List<String> orderList,@Param("businessGroup")int businessGroup);

    List<DeliveryInformationVo> findDeliveryInformat(@Param("code") String code);
}
