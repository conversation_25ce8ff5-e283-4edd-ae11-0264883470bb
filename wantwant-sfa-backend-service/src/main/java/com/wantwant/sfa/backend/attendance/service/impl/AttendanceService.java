package com.wantwant.sfa.backend.attendance.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.attendance.model.AttendanceTimeModel;
import com.wantwant.sfa.backend.attendance.request.*;
import com.wantwant.sfa.backend.attendance.service.IAttendanceService;
import com.wantwant.sfa.backend.attendance.vo.*;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.enums.BizExceptionLanguageEnum;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.attendanceTask.Attendance;
import com.wantwant.sfa.backend.util.AuthService;
import com.wantwant.sfa.backend.util.CommonUtil;
import com.wantwant.sfa.backend.util.EasyPoiUtil;
import com.wantwant.sfa.backend.util.ROOTConnectorUtil;
import com.wantwant.sfa.common.architecture.global.LocalizedTimezone;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AttendanceService extends ServiceImpl<AttendanceMapper, Attendance> implements IAttendanceService {

    @Autowired
    private MemberClockInMapper memberClockInMapper;

    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;

    @Autowired
    private SettingsMapper settingsMapper;

    @Resource
    private ROOTConnectorUtil connectorUtil;

    @Autowired
    private AttendanceMapper attendanceMapper;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Autowired
    private IAuditService  iAuditService;

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private AuthService authService;

    @Override
    public IPage<AttendanceListVo> getAttendanceInfoList(AttendanceListRequest request) {
        log.info("getAttendanceInfoList: {}", request);
        CeoBusinessOrganizationEntity entity = ceoBusinessOrganizationMapper.selectOne(
                new QueryWrapper<CeoBusinessOrganizationEntity>()
                        .eq("organization_id", request.getOrganizationId())
                        .eq("channel", RequestUtils.getChannel()));
        if(Objects.isNull(entity) || "branch".equals(entity.getOrganizationType())) {
            throw new ApplicationException("组织ID传入不正确");
        }
        request.setOrganizationType(entity.getOrganizationType());
        Page<AttendanceListVo> page = new Page<>(request.getPage(), request.getRows());
        String value = settingsMapper.getSfaSettingsByCode("zb_audit_personnel");
        int isZbAudit=0;
        if(value.contains(request.getPerson())){
            isZbAudit=1;
        }
        List<AttendanceListVo> list = memberClockInMapper.queryAttendanceList(page, request,isZbAudit);
        //null值标识全部状态 0 未稽核 1 正常 2 异常，目前sfa_attendance_audit只存1和2的数据
       /* list.stream().forEach(e -> {
            if(e.getAuditStatus() == null) {//未稽核数据sql查出来是null，需要转户为0
                e.setAuditStatus(0);
            }
        });
        if(request.getAuditStatus() != null) {
            list = list.stream().filter(e -> request.getAuditStatus().equals(e.getAuditStatus())).collect(Collectors.toList());
        }*/
        page.setRecords(list);
        return page;
    }

    @Override
    public AttendanceDetailVo getAttendanceDetail(AttendanceDetailRequest request) {
        log.info("getAttendanceDetail: {}", request);
        AttendanceDetailVo attendanceDetailVo = memberClockInMapper.getPartnerInfo(request.getMemberKey());
        if(attendanceDetailVo == null) {
            throw new ApplicationException("未找到合伙人信息");
        }

        //上班打卡
        Map<String, String> clockInfoMap = memberClockInMapper.getClockPicture(request.getMemberKey(), request.getAttendanceDate(), 0);
        if(clockInfoMap != null) {
            String clockPictureStr = clockInfoMap.get("clockPicture");
            if(clockPictureStr != null) {
                JSONObject jsonObject = JSONObject.parseObject(clockPictureStr);
                List<String> clockPicList = new ArrayList<>(2);
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {
                    clockPicList.add(entry.getValue().toString());
                }
                attendanceDetailVo.setClockPicList(clockPicList);
            }
            attendanceDetailVo.setLongitude(clockInfoMap.get("clockLongitude"));
            attendanceDetailVo.setLatitude(clockInfoMap.get("clockLatitude"));
        }
        //下班打卡
        clockInfoMap = memberClockInMapper.getClockPicture(request.getMemberKey(), request.getAttendanceDate(), 1);
        if(clockInfoMap != null) {
            String clockPictureStr = clockInfoMap.get("clockPicture");
            if(clockPictureStr != null) {
                JSONObject jsonObject = JSONObject.parseObject(clockPictureStr);
                List<String> clockPicList = new ArrayList<>(2);
                for (Map.Entry<String, Object> entry: jsonObject.entrySet()) {
                    clockPicList.add(entry.getValue().toString());
                }
                attendanceDetailVo.setClockOffPicList(clockPicList);
                attendanceDetailVo.setClockOffAddress(clockInfoMap.get("clockAddress"));
            }
        }

        LocalDateTime localDateTime = request.getAttendanceDate().atStartOfDay();
        LocalDateTime monthFirstDay =
                LocalDateTimeUtils.getDayStart(localDateTime.with(TemporalAdjusters.firstDayOfMonth())); // 本月的第一天
        LocalDateTime monthEndDay =
                LocalDateTimeUtils.getDayEnd(localDateTime.with(TemporalAdjusters.lastDayOfMonth())); // 本月的最后一天
        //获取异常次数
        int exceptionCount = memberClockInMapper.attendanceStatusCount(request.getMemberKey(), monthFirstDay, monthEndDay, 2);
        attendanceDetailVo.setCurrentMonthExceptionCount(exceptionCount);
        //获取合规次数
        int normalCount = memberClockInMapper.attendanceStatusCount(request.getMemberKey(), monthFirstDay, monthEndDay, 1);
        attendanceDetailVo.setCurrentMonthNormalCount(normalCount);
        //是否有审核权限
        String attendAudit =  settingsMapper.getSfaSettingsByCode("attendance_audit");
        if(attendAudit != null && request.getPerson() != null && attendAudit.contains(request.getPerson())) {
            Integer status = memberClockInMapper.getAttendanceAuditStatus(request.getMemberKey(), request.getAttendanceDate());
            if (status == null || status == 0) { //目前未稽核的数据是不在表里面的
                attendanceDetailVo.setCanAudit(true);
            }
        }
        return attendanceDetailVo;
    }

    @Transactional
    @Override
    public void attendanceAudit(AttendanceAuditRequest request) {
        String attendAudit =  settingsMapper.getSfaSettingsByCode("attendance_audit");
        if(attendAudit == null || request.getPerson() == null || !attendAudit.contains(request.getPerson())) {
            throw new ApplicationException("无审批权限");
        }
        Integer status = memberClockInMapper.getAttendanceAuditStatus(request.getMemberKey(), request.getAttendanceDate());
        if(status != null) {
            throw new ApplicationException("考勤已审批");
        }
        memberClockInMapper.insertAttendanceAuditRecord(request);
        //同步旺铺
        Map<String, String> map = new HashMap();
        map.put("auditReason","");
        map.put("auditResult",request.getStatus() == 1 ? "0": "1");
        map.put("date",request.getAttendanceDate().format(DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd)));
        map.put("memberKey",request.getMemberKey());
        map.put("operator", request.getPerson());
        connectorUtil.attendanceAudit(map);
    }

    @Transactional
    @Override
    public void attendanceAuditCheck(AttendanceAuditCheckRequest request) {
        log.info("attendanceAuditCheck request: {}", request);
        if(CommonUtil.StringUtils.isEmpty(request.getPerson())) {
            throw new ApplicationException("审核人工号为空");
        }
        //获取打卡人工号
        Attendance attendance = attendanceMapper.selectById(request.getAttendanceId());
        if(Objects.isNull(attendance)) {
            throw new ApplicationException("查找考勤明细失败");
        }
        String organizationName = ceoBusinessOrganizationPositionRelationMapper.getOrganizationNameByPerson(request.getPerson(), RequestUtils.getBusinessGroup());
        Attendance attendanceVo = new Attendance();
        if("zb".equals(RequestUtils.getLoginInfo().getOrganizationType())) {
            //找到总部人员工号
            String value = settingsMapper.getSfaSettingsByCode("zb_audit_personnel");
            if(!value.contains(request.getPerson())) {
                throw new ApplicationException("当前登录人无总部集合权限，请联系管理员配置");
            }
            attendanceVo.setZbAuditStatus(request.getAuditStatus());
            attendanceVo.setZbAuditReason(request.getAuditReason());
            attendanceVo.setZbAuditPerson(request.getPerson());
            attendanceVo.setZbAuditName(organizationName);
        }else {
            if(!request.getPerson().equals(attendance.getBusinessAuditPerson())) {
                throw new ApplicationException("当前审核人信息不正确");
            }
            attendanceVo.setBusinessAuditStatus(request.getAuditStatus());
            attendanceVo.setBusinessAuditReason(request.getAuditReason());
            attendanceVo.setBusinessAuditPerson(request.getPerson());
            attendanceVo.setBusinessAuditName(organizationName);
        }
        attendanceMapper.update(attendanceVo,new QueryWrapper<Attendance>().like("attendance_time", request.getAttendanceDate()).eq("employee_info_id",  attendance.getEmployeeInfoId()));
    }

    @Override
    public AttendanceAuditCheckVo attendanceAuditCheckJurisdiction(AttendanceCheckRequest request) {
        AttendanceAuditCheckVo attendanceAuditCheckVo = new AttendanceAuditCheckVo();
        //根据工号判断是不是总部
        String value = settingsMapper.getSfaSettingsByCode("zb_audit_personnel");
        //是总部 判断数据是否被稽核
        if(value.contains(request.getPerson())){
            Attendance attendance = attendanceMapper.selectById(request.getAttendanceId());
            if(attendance.getZbAuditStatus()!=0){//稽核过了，返回状态
                attendanceAuditCheckVo.setAuditStatus(attendance.getZbAuditStatus());
                attendanceAuditCheckVo.setAuditReason(attendance.getZbAuditReason());
            }else{//没有稽核过，可以稽核
                attendanceAuditCheckVo.setIsAudit(1);
            }
        }else{
            //不是总部，先判断是否被稽核，如果没有，在判断工号
            Attendance attendance = attendanceMapper.selectById(request.getAttendanceId());
            if(attendance.getBusinessAuditStatus()!=0){//稽核过了，返回状态
                attendanceAuditCheckVo.setAuditStatus(attendance.getBusinessAuditStatus());
                attendanceAuditCheckVo.setAuditReason(attendance.getBusinessAuditReason());
            }else if(Objects.nonNull(request.getPerson()) && request.getPerson().equals(attendance.getBusinessAuditPerson())){
                    attendanceAuditCheckVo.setIsAudit(1);
            }
            /*else{//没有稽核过，判断是否可以稽核  找到打卡人的上级组织id是否等于登录人的组织id
                //登录人的组织id
                CeoBusinessOrganizationPositionRelation organizationLog = ceoBusinessOrganizationPositionRelationMapper.getParentOrganizationIdByPerson(request.getPerson(), RequestUtils.getBusinessGroup());
                //打卡人的上级组织id
                CeoBusinessOrganizationPositionRelation organizationClock = ceoBusinessOrganizationPositionRelationMapper.getParentOrganizationIdByPerson(attendance.getMemberId(), RequestUtils.getBusinessGroup());
                //如果登录人组织id等于打卡人的上级组织id
                if(organizationLog.getOrganizationId().equals(organizationClock.getOrganizationParentId())){
                    attendanceAuditCheckVo.setIsAudit(1);
                }else{//看登录人组织id 找打卡人上级组织id，如果缺岗找上上级。如果等于，可以稽核
                    SelectAuditDto selectAuditDto = new SelectAuditDto();
                    selectAuditDto.setCurrentOrganizationId(organizationClock.getOrganizationParentId());
                    selectAuditDto.setStandbyEmployeeId(organizationClock.getEmployeeId());
                    selectAuditDto.setChannel(RequestUtils.getChannel());
                    selectAuditDto.setBusinessGroup(RequestUtils.getBusinessGroup());
                    CeoBusinessOrganizationPositionRelation ceoBusinessOrganization = iAuditService.chooseAuditPerson(selectAuditDto);
                    if(organizationLog.getOrganizationId().equals(ceoBusinessOrganization.getOrganizationId())){
                        attendanceAuditCheckVo.setIsAudit(1);
                    }
                }
            }*/
        }
        return attendanceAuditCheckVo;
    }

    @Override
    public List<AttendanceWeeklyDetailVo> attendanceWeeklyList(String startDate, String endDate, Integer employeeInfoId) {
        log.info("startDate: {}, endDate:{}, employeeInfoId:{}", startDate, endDate, employeeInfoId);
        return attendanceMapper.getAttendanceList(startDate, endDate, employeeInfoId);
    }

    @Override
    public AttendanceVisitInfoVo getLatestVisitInfo(Long attendanceId) {
        log.info("getLatestVisitInfo attendanceId:{}", attendanceId);
        // 查询考勤日期
        Attendance attendance = attendanceMapper.selectById(attendanceId);
        if (Objects.isNull(attendance)) {
            throw new ApplicationException("考勤信息不存在");
        }
        LocalDateTime attendanceTime = attendance.getAttendanceTime();
        String attendanceDate = attendanceTime.format(DateTimeFormatter.ofPattern(LocalDateTimeUtils.yyyy_MM_dd));
        // 查询当天最后一次拜访信息
        return attendanceMapper.getLatestVisitInfoByParams(attendanceDate, attendance.getEmployeeInfoId());
    }

    @Override
    public Attendance getAttendanceInfoByParams(Integer employeeInfoId, String attendanceTime) {
        log.info("employeeInfoId:{} attendanceTime:{}", employeeInfoId, attendanceTime);
        return attendanceMapper.getAttendanceInfoByParams(employeeInfoId, attendanceTime);
    }

    @Override
    public IPage<AttendanceListV2Vo> getAttendanceV2List(AttendanceListV2Request request) {
        log.info("getAttendanceV2List:{}", request);
        if(Objects.isNull(request.getBeginTime()) || Objects.isNull(request.getEndTime())) {
            throw new ApplicationException(BizExceptionLanguageEnum.ATTENDANCE_DATE_MUST_NOT_NULL.getTextMsg());
        }
        Page<AttendanceListV2Vo> page = new Page<>(request.getPage(), request.getRows());
        List<AttendanceListV2Vo> list = null;

        LocalDateTime searchStartTime =  request.getBeginTime().atStartOfDay();
        LocalDateTime searchEndTime = LocalDateTimeUtils.getDayEnd(request.getEndTime().atStartOfDay());;

        AttendanceTimeModel attendanceTimeModel = AttendanceTimeModel.builder().startTime(searchStartTime).endTime(searchEndTime).build();
        LocalizedTimezone.handleResult(attendanceTimeModel,RequestUtils.getLoginInfo().getTimezone());
        request.setSearchEndTime(attendanceTimeModel.getEndTime());
        request.setSearchStartTime(attendanceTimeModel.getStartTime());

        String region = RequestUtils.getLoginInfo().getRegion();
        Integer i18nFlag;

        if(region.equals(CommonConstant.REGION_INDONESIA)){
            i18nFlag = 1;
        } else {
            i18nFlag = 0;
        }

        // 时间处理





        if(request.getZbRole() == 0) {//非总部角色
            //检查入参的organizationIds类型
            String organizationType = null;
            if(!CollectionUtils.isEmpty(request.getOrganizationIds())) {
                organizationType = organizationMapper.getOrganizationType(request.getOrganizationIds().get(0));
            }
            list = attendanceMapper.getAttendanceV2List(page, request, organizationType, RequestUtils.getLoginInfo().getOrganizationType());
        }else {
            String specialList = StringUtils.EMPTY;
            if(i18nFlag == 0){
                specialList = settingsMapper.getSfaSettingsByCode("zb_promotion_specialist");
            }


            if(StringUtils.isNotBlank(specialList)){
                List<String> employeeIds = Arrays.asList(specialList.split(","));
                if(!CollectionUtils.isEmpty(employeeIds)) {
                    list = attendanceMapper.getAttendanceV2ListForZBRole(page, request,employeeIds);
                }
            }
        }

        if(!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> {
                Integer positionTypeId = e.getPositionTypeId();
                e.setPositionName(OrganizationPositionRelationEnums.getPositionName(positionTypeId, i18nFlag));
            });
        }

        page.setRecords(list);
        return page;
    }

    @Override
    public AttendanceListV2Vo getAttendanceV2Detail(Long attendanceId) {
        log.info("getAttendanceV2Detail");
        AttendanceListV2Vo attendanceV2Detail = attendanceMapper.getAttendanceV2Detail(attendanceId, RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getOrganizationType());
        if(Objects.isNull(attendanceV2Detail)) {
            return null;
        }
        Integer employeeInfoId = attendanceV2Detail.getEmployeeInfoId();
        if(Objects.isNull(employeeInfoId)){
            return attendanceV2Detail;
        }

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
        if(Objects.isNull(sfaEmployeeInfoModel)){
            return attendanceV2Detail;
        }

        Integer type = sfaEmployeeInfoModel.getType();
        if(Objects.nonNull(type) && (type == 6 || type == 7)) {
            attendanceV2Detail.setBusinessBD(true);

            Integer exceedsRange = attendanceV2Detail.getExceedsRange();
            if (Objects.nonNull(exceedsRange)) {
                attendanceV2Detail.setExceedsRange(exceedsRange);
            } else {
                attendanceV2Detail.setExceedsRange(0);
            }

            BigDecimal distanceExceeded = attendanceV2Detail.getDistanceExceeded();
            if (Objects.nonNull(distanceExceeded)) {
                attendanceV2Detail.setDistanceExceeded(distanceExceeded);
            }

            // 根据打卡点位获取最近点位合伙人信息
            NearestPartnerVo nearestPartnerVo = attendanceMapper.selectNearestPartner(attendanceV2Detail.getId());
            attendanceV2Detail.setNearestPartnerVo(nearestPartnerVo);

        }
        return attendanceV2Detail;
    }

    @Override
    public void exportAttendanceV2List(AttendanceListV2Request request, HttpServletResponse response) {
        log.info("exportAttendanceV2List:{}", request);
        if(Objects.isNull(request.getBeginTime()) || Objects.isNull(request.getEndTime())) {
            throw new ApplicationException("请选择查询的起始日期");
        }

        LocalDateTime searchStartTime =  request.getBeginTime().atStartOfDay();
        LocalDateTime searchEndTime = LocalDateTimeUtils.getDayEnd(request.getEndTime().atStartOfDay());;

        AttendanceTimeModel attendanceTimeModel = AttendanceTimeModel.builder().startTime(searchStartTime).endTime(searchEndTime).build();
        LocalizedTimezone.handleResult(attendanceTimeModel,RequestUtils.getLoginInfo().getTimezone());
        request.setSearchEndTime(attendanceTimeModel.getEndTime());
        request.setSearchStartTime(attendanceTimeModel.getStartTime());

        long daysBetween = ChronoUnit.DAYS.between(request.getBeginTime(), request.getEndTime());
        if(daysBetween > 60) {
            throw new ApplicationException("间隔超过60天，请联系管理员");
        }
        request.setPage(1);
        request.setRows(Integer.MAX_VALUE);
        Page<AttendanceListV2Vo> page = new Page<>(request.getPage(), request.getRows());
        List<AttendanceListV2Vo> list = null;
        if(request.getZbRole() == 0) {//非总部角色
            //检查入参的organizationIds类型
            String organizationType = null;
            if(!CollectionUtils.isEmpty(request.getOrganizationIds())) {
                organizationType = organizationMapper.getOrganizationType(request.getOrganizationIds().get(0));
            }
            list = attendanceMapper.getAttendanceV2List(page, request, organizationType, RequestUtils.getLoginInfo().getOrganizationType());
        }else {
            String specialList =  settingsMapper.getSfaSettingsByCode("zb_promotion_specialist");
            if(Objects.nonNull(specialList)){
                List<String> employeeIds = Arrays.asList(specialList.split(","));
                if(!CollectionUtils.isEmpty(employeeIds)) {
                    list = attendanceMapper.getAttendanceV2ListForZBRole(page, request,employeeIds);
                }
            }
        }

        Integer i18nFlag;
        String region = RequestUtils.getLoginInfo().getRegion();
        if(region.equals(CommonConstant.REGION_INDONESIA)){
            i18nFlag = 1;
        } else {
            i18nFlag = 0;
        }

        if(!CollectionUtils.isEmpty(list)){
            list.forEach(e -> {
                Integer positionTypeId = e.getPositionTypeId();
                e.setPositionName(OrganizationPositionRelationEnums.getPositionName(positionTypeId, i18nFlag));
            });
        }

        EasyPoiUtil.exportExcel(list,null,"sheet1",AttendanceListV2Vo.class,"考勤数据导出.xls",response);
    }

    @Override
    public void attendanceInitTask(String dateStr) {
        log.info("attendanceInitTask:{}", dateStr);
        LocalDate date = null;
        if (CommonUtil.StringUtils.isNotEmpty(dateStr)) {
            date = LocalDate.parse(dateStr);
        }else {
            date = LocalDate.now();
        }
        //从旺铺表获取考勤日历
        List<Map<String, Object>> calendarList = attendanceMapper.getAttendanceInitCalendar(date);
        if(CollectionUtils.isEmpty(calendarList)) {
            log.info("初始化考勤失败");
            return;
        }
        List<Integer> alreadyAttendanceEmpInfoList = attendanceMapper.selectList(new LambdaQueryWrapper<Attendance>().eq(Attendance::getCalendarDate, date).eq(Attendance::getDeleteFlag, 0)).stream().map(Attendance::getEmployeeInfoId).collect(Collectors.toList());
        //给所有当前在岗位的人，初始化
        List<Map<String, Object>> infoModelList = sfaEmployeeInfoMapper.queryBusinessCurrentEmployees(alreadyAttendanceEmpInfoList);
        log.info("考勤人员：{}", infoModelList);
        if(CollectionUtils.isEmpty(infoModelList)) {
            log.info("获取员工列表失败");
            return;
        }
        List<Attendance> attendanceList = new ArrayList<>();
        for (Map<String, Object> infoModel:infoModelList) {
            Attendance attendance = new Attendance();
            String companyName = String.valueOf(infoModel.get("companyName"));
            Optional<Map<String, Object>> optional = calendarList.stream().filter(e -> CommonUtil.StringUtils.isNotEmpty(companyName) && companyName.equals(String.valueOf(e.get("company")))).findFirst();
            //根据分公司获取对应地方考勤类型，没有对应分公司，则默认第一个
            Map<String, Object> currentCompanyCalendar = null;
            if(optional.isPresent()) {
                currentCompanyCalendar = optional.get();
            }else {
                currentCompanyCalendar = calendarList.get(0);
            }
            attendance.setCalendarDate(date);
            attendance.setCalendarYear(date.getYear());
            attendance.setCalendarMonth(date.getMonthValue());
            attendance.setCalendarDay(date.getDayOfMonth());
            attendance.setWorkDayType(Integer.valueOf(String.valueOf(currentCompanyCalendar.get("isWorkDay"))));
            attendance.setCompany(companyName);
            attendance.setEmployeeInfoId(Integer.valueOf(String.valueOf(infoModel.get("id"))));
            attendance.setEmployeeId(String.valueOf(infoModel.get("employeeId")));
            attendance.setEmployeeName(String.valueOf(infoModel.get("employeeName")));
            //默认上班未打卡状态,每个岗位的打卡类型都不一样
            attendance.setAttendanceType(1);
            attendance.setAttendanceStatus(3);
            attendance.setCreateTime(LocalDateTime.now());
            attendance.setCreatePerson("ROOT");
            attendance.setCreateType(1);//定时任务插入
            attendanceList.add(attendance);
        }
        if(!CollectionUtils.isEmpty(attendanceList)) {
            log.info("插入表的数据：{}", attendanceList);
            saveBatch(attendanceList);
        }

    }

    @Override
    public List<MemberCalendarVO> getMemberCalendar(Integer employeeInfoId,String startDate, String endDate) {
        log.info("AttendanceService getMemberCalendar employeeInfoId={},startDate ={},endDate={}",employeeInfoId,startDate,endDate);
        /**
         * 1.sfa_apply_member 查询company关联member_calendar 的 company字段
         * 2.没有值的话就work_place字段 转ceo_business_organization中文   关联member_calendar 的 company字段
         */
        List<MemberCalendarVO> calendarVOS = attendanceMapper.selectMemberCalendarByCompany(employeeInfoId, startDate, endDate);
        if(CollectionUtil.isEmpty(calendarVOS)){
            calendarVOS = attendanceMapper.selectMemberCalendarByWorkPlace(employeeInfoId, startDate, endDate);
        }
        return calendarVOS;
    }

    @Override
    public BigDecimal faceSimilar(String url) {
        BigDecimal score = null;
        Attendance attendance = attendanceMapper.selectOne(new LambdaQueryWrapper<Attendance>().eq(Attendance::getPicUrl, url).eq(Attendance::getDeleteFlag, 0));
        if(Objects.nonNull(attendance) && Objects.nonNull(attendance.getSignUpPicUrl()) && Objects.nonNull(attendance.getPicUrl())) {
            String faceScore = authService.compareFace(attendance.getSignUpPicUrl(), attendance.getPicUrl());
            if(Objects.nonNull(faceScore)) {
                score = new BigDecimal(faceScore);
                attendance.setFaceSimilarScore(score);
                attendanceMapper.updateById(attendance);
            }

        }
        return score;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAuditCheck(List<AttendanceAuditCheckRequest> list) {
        log.info("batchAuditCheck list request: {}", JSON.toJSONString(list));
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)||list.get(0)==null){
            throw new ApplicationException("数据不能为空");
        }
        String organizationName = ceoBusinessOrganizationPositionRelationMapper.getOrganizationNameByPerson(list.get(0).getPerson(), RequestUtils.getBusinessGroup());
        // list 根据attendanceId 去重复
        list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AttendanceAuditCheckRequest::getAttendanceId))), ArrayList::new));
        for (AttendanceAuditCheckRequest request : list) {
            if(CommonUtil.StringUtils.isEmpty(request.getPerson())) {
                throw new ApplicationException("审核人工号为空");
            }
            //获取打卡人工号
            Attendance attendance = attendanceMapper.selectById(request.getAttendanceId());
            if(Objects.isNull(attendance)) {
                throw new ApplicationException("查找考勤明细失败,数据ID："+request.getAttendanceId());
            }
            Attendance attendanceVo = new Attendance();
            if("zb".equals(RequestUtils.getLoginInfo().getOrganizationType())) {
                //找到总部人员工号
                String value = settingsMapper.getSfaSettingsByCode("zb_audit_personnel");
                if(!value.contains(request.getPerson())) {
                    throw new ApplicationException("当前登录人无总部集合权限，请联系管理员配置");
                }
                attendanceVo.setZbAuditStatus(request.getAuditStatus());
                attendanceVo.setZbAuditReason(request.getAuditReason());
                attendanceVo.setZbAuditPerson(request.getPerson());
                attendanceVo.setZbAuditName(organizationName);
            }else {
                if(!request.getPerson().equals(attendance.getBusinessAuditPerson())) {
                    throw new ApplicationException("当前审核人信息不正确,数据ID："+request.getAttendanceId());
                }
                attendanceVo.setBusinessAuditStatus(request.getAuditStatus());
                attendanceVo.setBusinessAuditReason(request.getAuditReason());
                attendanceVo.setBusinessAuditPerson(request.getPerson());
                attendanceVo.setBusinessAuditName(organizationName);
            }
            attendanceMapper.update(attendanceVo,new QueryWrapper<Attendance>().like("attendance_time", request.getAttendanceDate()).eq("employee_info_id",  attendance.getEmployeeInfoId()));
        }
    }

    @Override
    public List<AttendanceAuditCheckVo> batchJurisdiction(List<AttendanceCheckRequest> list) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)||list.get(0)==null){
            throw new ApplicationException("数据不能为空");
        }
        List<AttendanceAuditCheckVo> resList=new ArrayList<>();
        list = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AttendanceCheckRequest::getAttendanceId))), ArrayList::new));
        for (AttendanceCheckRequest request : list) {
            resList.add(this.attendanceAuditCheckJurisdiction(request));
        }
        return resList;
    }

}
