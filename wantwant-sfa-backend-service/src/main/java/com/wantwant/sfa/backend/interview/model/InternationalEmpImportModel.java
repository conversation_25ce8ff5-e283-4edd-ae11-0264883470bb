package com.wantwant.sfa.backend.interview.model;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class InternationalEmpImportModel {
    @Excel(name = "Name",fixedIndex = 0)
    private String employeeName;
    @Excel(name = "Gender",fixedIndex = 1)
    private String gender;
    @Excel(name = "ID Number",fixedIndex = 2)
    private String idCard;
    @Excel(name = "Mobile Number",fixedIndex = 3)
    private String mobile;
    @Excel(name = "Position",fixedIndex = 4)
    private String position;
    @Excel(name = "Channel",fixedIndex = 5)
    private String channel;


    @Excel(groupName = "* Province",fixedIndex = 6,name = "* Home Address")
    private String homeProvince;
    @Excel(groupName = "* City",fixedIndex = 7,name = "* Home Address")
    private String homeCity;
    @Excel(groupName = "* District",fixedIndex = 8,name = "* Home Address")
    private String homeDistrict;
    @Excel(groupName = "* Street",fixedIndex = 9,name = "* Home Address")
    private String homeStreet;
    @Excel(groupName = "* Province",fixedIndex = 10,name = "* Distribution Area")
    private String salesProvince;
    @Excel(groupName = "* City",fixedIndex = 11,name = "* Distribution Area")
    private String salesCity;
    @Excel(groupName = "* District",fixedIndex = 12,name = "* Distribution Area")
    private String salesDistrict;
    @Excel(name = "* Office Location",fixedIndex = 13)
    private String officeLocation;
    @Excel(name = "* Onboarding Date",fixedIndex = 14)
    private String hireDate;
}
