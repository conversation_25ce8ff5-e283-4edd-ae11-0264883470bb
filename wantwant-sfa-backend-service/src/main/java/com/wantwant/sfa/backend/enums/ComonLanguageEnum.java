package com.wantwant.sfa.backend.enums;

import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
@Getter
public enum ComonLanguageEnum {

    VISIT("拜访", "Visit"),
    COMPLETE("通关", "Complete"),
    ATTENDANCE("考勤", "Attendance"),
    ON_WORK("上班", "On Duty"),
    OFF_WORK("下班", "Off Duty"),
    POSITIONING("动态定位", "Kinematic Positioning"),


    ZB("总部", "Headquarters"),
    AREA("总督导", "National Sales Manager"),
    AREA_2("战区督导", "National Sales Manager"),
    V_AREA("大区总监", "Area Sales Manager"),
    PROVINCE("省区总监", "Provincial Director"),
    COMPANY("区域总监", "Area Sales Supervisor"),
    COMPANY_2("造旺总监", "Area Sales Supervisor"),
    DEPARTMENT("区域经理", "Regional Manager"),

    FULL_TIME_PARTNER("全职合伙人", "Full-time Partner"),
    PART_TIME_PARTNER("兼职合伙人", "Part-time Partner"),
    DISTRIBUTION_PARTNER("流通合伙人", "Distribution Partner"),
    CONTRACT_PARTNER("承揽合伙人", "Contracting Partner"),
    DIRECT_OPERATED_PARTNER("直营合伙人", "Direct-operated Partner"),


    BD_FULL_TIME("全职业务bd", "Full-time Business BD"),
    BD_PART_TIME("兼职业务bd", "Part-time Business BD"),
    BD_PART_TIME_CONTRACT("兼职承揽bd", "Part-time Contracting Business BD"),

    PARTNER("合伙人", "Partner"),
    WHOLESALE("批发", "Wholesale"),
    TERMINAL("终端", "Terminal"),
    FILE_CREATION("建档", "File Creation"),
    FULL_TIME("全职", "Full- ime"),
    PART_TIME("兼职", "Part-time"),
    DEALER("经销商", "Dealer"),
    PROSPECTIVE("意向", "Prospective"),
    potential("潜在", "Potential"),
    Contracting("承揽", "Contracting"),

    TOTAL("总计", "Total"),
    TOTAL2("合计", "Total"),

    MINUTES("分钟", "min"),
    SECONDS("秒", "s"),

    CONVENIENT_MENU("快捷菜单","Convenient Menu"),
    PRIMARY_ROLE("(主岗)","(Primary)"),
    SECONDARY_ROLE("(兼岗)", "(Secondary)"),


    /**
     * 本组客户：生命周期
     */
    CHURN("流失", "Churn"),
    FOCUS("关注", "Focus"),
    ABNORMALITY("异常", "Abnormality"),
    ACTIVE("活跃", "Active"),
    EARLY_WARNING("预警", "Early Warning"),

    Not_Ordered("未下单", "Not Ordered"),
    A1("A1", "A1"),
    A2("A2", "A2"),
    B1("B1", "B1"),
    B2("B2", "B2"),
    C1("C1", "C1"),
    C2("C2", "C2"),
    D1("D1", "D1"),


    /**
     * 订单：订单状态
     */

    PENDING_SHIPMENT("待发货", "Pending Shipment"),
    PENDING_RECEIPT("待收货", "Pending Receipt"),
    RECEIVED("已收货", "Received"),
    COMPLETED("已完成", "Completed"),
    UNKNOWN("未知", "Unknown"),

    /**
     * 订单：商品标签
     */
    PRODUCT_RETURN("货返", "Product return"),
    FREE_GIFT("赠品", "Free gift"),
    EXCLUSIVE_FOR_NEW_CUSTOMERS("新人专享", "Exclusive for new customers"),
    REGULAR("常态", "Regular"),
    TASTING("试吃", "Tasting"),
    PROMOTIONAL_MATERIALS("文宣品", "Promotional materials"),


    ;



    private final String zhDesc;

    private final String enDesc;

    public String getDesc() {
        String language = RequestUtils.getLoginInfo().getLanguage();
        if (CommonConstant.LANGUAGE_CHINESE.equals(language)) {
            return this.getZhDesc();
        } else {
            return this.getEnDesc();
        }
    }

    public static String getDescByEnv(String desc) {
        if (StringUtils.isBlank(desc)) {
            return null;
        }
        String language = RequestUtils.getLoginInfo().getLanguage();
        for (ComonLanguageEnum value : ComonLanguageEnum.values()) {
            if (value.getZhDesc().equals(desc)) {
                if (CommonConstant.LANGUAGE_ENGLISH.equals(language)) {
                    return value.getEnDesc();
                }
                return value.getZhDesc();
            }
        }
        return null;
    }
}
