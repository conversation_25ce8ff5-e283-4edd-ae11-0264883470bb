package com.wantwant.sfa.backend.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.sfa.backend.map.request.MapListRequest;
import com.wantwant.sfa.backend.market.request.OrganizationRegionRequest;
import com.wantwant.sfa.backend.market.vo.OrganizationRegiontVo;
import com.wantwant.sfa.backend.model.WwOrganizationAreaPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 爱旺旺大区分公司地区划分 Mapper 接口
 *
 * @since 2021-11-08
 */
public interface WwOrganizationAreaMapper extends BaseMapper<WwOrganizationAreaPo> {

    List<OrganizationRegiontVo> selectOrganizationRegionList(Page<OrganizationRegiontVo> page, @Param("params") OrganizationRegionRequest request);

    List<String> selectDistrictCodeList(@Param("params") MapListRequest request);

    List<String> selectIdDistrictCodeList(@Param("params") MapListRequest request);
}
