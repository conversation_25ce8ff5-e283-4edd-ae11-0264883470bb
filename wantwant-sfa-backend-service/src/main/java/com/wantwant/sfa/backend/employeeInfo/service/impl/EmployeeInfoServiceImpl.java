package com.wantwant.sfa.backend.employeeInfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.EmployeeInfoPostTypeEnum;
import com.wantwant.sfa.backend.common.EmployeeInfoTypeEnum;
import com.wantwant.sfa.backend.common.OrganizationPositionRelationEnums;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.employee.emums.OnboardEnum;
import com.wantwant.sfa.backend.employee.request.*;
import com.wantwant.sfa.backend.employee.vo.*;
import com.wantwant.sfa.backend.employeeInfo.model.EmployeeInfoModel;
import com.wantwant.sfa.backend.employeeInfo.model.RecommendModel;
import com.wantwant.sfa.backend.employeeInfo.service.IEmployeeInfoService;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessPositionType;
import com.wantwant.sfa.backend.interview.enums.CeoExEnum;
import com.wantwant.sfa.backend.interview.enums.PositionTypeEnum;
import com.wantwant.sfa.backend.interview.enums.ProcessResult;
import com.wantwant.sfa.backend.interview.enums.ProcessType;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.model.WeeklyEmployeeSnapshotPO;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.position.service.IPositionRelationService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.BeanUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/06/22/上午10:23
 */
@Service
public class EmployeeInfoServiceImpl implements IEmployeeInfoService {
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private WeeklyEmployeeSnapshotMapper weeklyEmployeeSnapshotMapper;
    @Autowired
    private IPositionRelationService positionRelationService;
    @Resource
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Resource
    private OrganizationMapper organizationMapper;
    @Resource
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Resource
    private CeoBusinessPositionTypeMapper ceoBusinessPositionTypeMapper;
    @Resource
    private IAuditService auditService;
    @Resource
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;



    @Override
    public List<RecommendVo> selectRecommendCeoList(RecommendCeoSearchRequest request) {

        List<RecommendModel>  list = sfaEmployeeInfoMapper.selectRecommendCeoList(request);

        List<RecommendVo> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return result;
        }

        list.forEach(e -> {
            RecommendVo vo = new RecommendVo();
            BeanUtils.copyProperties(e,vo);
            // 设置入职状态
            vo.setOnboardStatus(getOnboardStatus(e));
            result.add(vo);
        });


        return result;
    }

    @Override
    public RecommendDetailVo getRecommendDetailVo(Integer applyId) {
        RecommendModel model = sfaEmployeeInfoMapper.selectRecommendCeoDetail(applyId);
        if(Objects.isNull(model)){
            return null;
        }

        RecommendDetailVo vo = new RecommendDetailVo();
        BeanUtils.copyProperties(model,vo);

        Integer sex = model.getSex();
        if(1 == sex){
            vo.setSex("男");
        }else if(2 == sex){
            vo.setSex("女");
        }else{
            vo.setSex("未知");
        }
        vo.setOnboardStatus(getOnboardStatus(model));

        vo.setRecommendedPartnerStatus(getRecommendPartnerStatus(model.getPosition(),model.getJobsType(),model.getType()));

        return vo;
    }

    @Override
    public EmployeeInfoModel selectEmployeeInfoByMemberKey(Long memberKey) {
        return sfaEmployeeInfoMapper.selectEmployeeInfoByMemberKey(memberKey);
    }

    @Override
    public Page<CeoEmployeeInfoVo> selectCeoEmployeeInfo(CeoEmployeeRequest request) {

        List<CeoEmployeeInfoVo> list = sfaEmployeeInfoMapper.selectCeoEmployeeInfo(request);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        Page<CeoEmployeeInfoVo> page = new Page<>();
        page.setList(list);
        int count = sfaEmployeeInfoMapper.selectCeoEmployeeInfoCount(request);
        page.setTotalItem(count);
        page.setTotalPage(PageUtil.totalPage(count,request.getRows()));
        return page;
    }


    /**
     * 核心指标预警周报保存员工快照
     *
     * @param
     * @return: void
     * @date: 4/24/23 4:06 PM
     */
    @Override
    public void doWeeklySnapshot() {
        List<WeeklyEmployeeSnapshotPO> list = sfaEmployeeInfoMapper.selectSgEmployee();
        List<WeeklyEmployeeSnapshotPO> list1 = sfaEmployeeInfoMapper.selectToBeEmployee();
        list.addAll(list1);
        list.stream().filter(f -> StringUtils.isNotBlank(f.getCompanyCode())).forEach(w -> weeklyEmployeeSnapshotMapper.insert(w));
    }

    @Override
    public List<PartTimePositionVo> queryPartTime(Integer employeeInfoId) {
        // 主岗信息
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("主岗位信息获取失败");
        }

        // 获取兼岗信息
        List<PartTimePositionVo> list = positionRelationService.selectPositionByEmployeeInfoId(employeeInfoId);



        return list;
    }

    @Override
    public List<ConsultVo> selectConsult(Long memberKey) {

        List<ConsultVo> list = positionRelationService.selectConsult(memberKey);

        return list;
    }

    @Override
    public List<BusinessBDPersonnelVo> selectBusinessBDPersonnel(String departmentCode) {

        List<BusinessBDPersonnelVo> list = sfaEmployeeInfoMapper.selectBusinessBDPersonal(departmentCode);
        if(CollectionUtils.isEmpty(list)){
            return ListUtils.EMPTY_LIST;
        }

        list.forEach(e -> {
            e.setPositionName(PositionEnum.getPositionName(e.getCeoType(),e.getJobsType(),e.getPosition()));
        });

        return list;

    }

    @Override
    public AuditPersonVo selectAuditPerson(AuditPersonSearchRequest auditPersonSearchRequest) {

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new LambdaQueryWrapper<SfaBusinessGroupEntity>().eq(SfaBusinessGroupEntity::getBusinessGroupCode, auditPersonSearchRequest.getBusinessGroupCode()).eq(SfaBusinessGroupEntity::getDeleteFlag, 0));
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组不存在");
        }
        CeoExEnum exEnum = CeoExEnum.findByEx1(auditPersonSearchRequest.getEx1());
        CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = null;

        if(exEnum.getOrganizationType().equals("branch")){
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, auditPersonSearchRequest.getMemberKey()).last("limit 1"));

            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("员工信息不存在");
            }

            ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>().eq(CeoBusinessOrganizationEntity::getOrganizationId, sfaEmployeeInfoModel.getBranchCode()));
        }else{
           ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>().eq(CeoBusinessOrganizationEntity::getChannel, 3).eq(CeoBusinessOrganizationEntity::getBusinessGroup, sfaBusinessGroupEntity.getId()).eq(CeoBusinessOrganizationEntity::getOrganizationName, auditPersonSearchRequest.getOrganizationName()).eq(CeoBusinessOrganizationEntity::getOrganizationType, exEnum.getOrganizationType()));
        }
        if(Objects.isNull(ceoBusinessOrganizationEntity)){
            throw new ApplicationException("组织信息不存在");
        }

        SfaPositionRelationEntity sfaPositionRelationEntity = null;
        Integer searchType = auditPersonSearchRequest.getSearchType();

        if(searchType == 1){
            // 查询memberKey
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, auditPersonSearchRequest.getMemberKey()));
            if(Objects.isNull(sfaEmployeeInfoModel)){
                throw new ApplicationException("memberKey不存在");
            }
            sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId()).eq(SfaPositionRelationEntity::getOrganizationCode, ceoBusinessOrganizationEntity.getOrganizationId()).orderByDesc(SfaPositionRelationEntity::getId).last("limit 1"));

        }else{
            sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmpId, auditPersonSearchRequest.getEmployeeId()).eq(SfaPositionRelationEntity::getOrganizationCode, ceoBusinessOrganizationEntity.getOrganizationId()).orderByDesc(SfaPositionRelationEntity::getId).last("limit 1"));
        }

        if(Objects.isNull(sfaPositionRelationEntity)){
            throw new ApplicationException("员工岗位信息不存在");
        }
        SelectAuditDto selectAuditDto = new SelectAuditDto();
        selectAuditDto.setStandbyEmployeeId(auditPersonSearchRequest.getStandbyEmpId());
        selectAuditDto.setChannel(3);
        selectAuditDto.setBusinessGroup(sfaBusinessGroupEntity.getId());
        selectAuditDto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(ceoBusinessOrganizationEntity.getOrganizationId()));
        CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(selectAuditDto);

        if(Objects.isNull(auditPerson)){
            throw new ApplicationException("无可用审核人 ");
        }

        AuditPersonVo auditPersonVo = new AuditPersonVo();
        auditPersonVo.setEmployeeId(auditPerson.getEmployeeId());

        SfaPositionRelationEntity audit = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>().eq(SfaPositionRelationEntity::getEmpId, auditPerson.getEmployeeId()).eq(SfaPositionRelationEntity::getStatus, 1).eq(SfaPositionRelationEntity::getDeleteFlag, 0).last("limit 1"));
        if(Objects.nonNull(audit) && Objects.nonNull(audit.getEmployeeInfoId())){
            Integer employeeInfoId = audit.getEmployeeInfoId();
            SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
            if(Objects.nonNull(sfaEmployeeInfoModel)){
                auditPersonVo.setMemberKey(sfaEmployeeInfoModel.getMemberKey());
            }
        }

        auditPersonVo.setEmployeeName(auditPerson.getEmployeeName());
        auditPersonVo.setEx1(CeoExEnum.findEx1ByPositionTypeId(auditPerson.getPositionTypeId()));
        auditPersonVo.setBusinessGroupCode(sfaBusinessGroupMapper.selectById(auditPerson.getBusinessGroup()).getBusinessGroupCode());
        auditPersonVo.setOrganizationName(organizationMapper.getOrganizationName(auditPerson.getOrganizationId()));
        return auditPersonVo;
    }

    @Override
    public List<OrgVo> selectOrgByEmpId(CeoOrgSearchRequest ceoOrgSearchRequest) {

        String orgType = ceoOrgSearchRequest.getOrgType();
        List<OrgVo> list = new ArrayList<>();
        if(orgType.equals("zb")){
            SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new LambdaQueryWrapper<SfaBusinessGroupEntity>().eq(SfaBusinessGroupEntity::getBusinessGroupCode, ceoOrgSearchRequest.getBusinessGroupCode()).eq(SfaBusinessGroupEntity::getDeleteFlag, 0));
            if(Objects.nonNull(sfaBusinessGroupEntity)){
                // 获取所有战区
                List<String> orgCodes = organizationMapper.selectAreaOrganization(sfaBusinessGroupEntity.getId());
                orgCodes.forEach(e -> {
                    String organizationName = organizationMapper.getOrganizationName(e);
                    OrgVo orgVo = new OrgVo();
                    orgVo.setOrganizationId(e);
                    orgVo.setOrganizationName(organizationName);
                    list.add(orgVo);
                });

            }


        }else{
            List<OrgVo> orgVos = sfaPositionRelationMapper.selectOrgByEmpId(ceoOrgSearchRequest);
            list.addAll(orgVos);
        }

        return list;
    }

    @Override
    public IPage<ChildVo> queryChildListByParentOrg(ChildRequest request) {

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setBusinessGroup(loginInfo.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
        }else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setOrganizationIds(personOrganizationIds);
            request.setOrganizationType(loginInfo.getOrganizationType());
        }
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<ChildVo> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(request.getPage(), request.getRows());
        List<ChildVo> list = Optional.ofNullable(sfaEmployeeInfoMapper.queryChildListByParentOrg(page, request)).orElse(Collections.emptyList()).stream().peek(entity ->
                        entity.setPositionName(OrganizationPositionRelationEnums.getPositionName(entity.getPostTypeId(), RequestUtils.getRegion())))
                .collect(Collectors.toList());

        page.setRecords(list);
        return page;
    }

    @Override
    public List<BusinessGroupPositionTypeVo> queryBusinessGroupPositionType(BusinessGroupPositionTypeRequest request) {
        List<BusinessGroupPositionTypeVo> returnList = new ArrayList<>();
        Integer businessGroup = RequestUtils.getLoginInfo().getBusinessGroup();
        List<SfaPositionRelationEntity> personPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, request.getPerson())
                .eq(Objects.nonNull(request.getBusinessGroupId()), SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroupId())
                .ne(SfaPositionRelationEntity::getBusinessGroup, 99)
                .eq(SfaPositionRelationEntity::getPositionTypeId, 7)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(personPositionRelationList)) {
            return returnList;
        }
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntityList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .in(SfaBusinessGroupEntity::getId, personPositionRelationList.stream().map(SfaPositionRelationEntity::getBusinessGroup).distinct().collect(Collectors.toList()))
        );
        if (CollectionUtils.isEmpty(sfaBusinessGroupEntityList)) {
            return returnList;
        }
        // 判断当前产品组国际化属性--剔除不同的属性
        Integer i18nFlag = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getId().equals(businessGroup))
                .map(SfaBusinessGroupEntity::getI18nFlag).findFirst().orElse(0);
        sfaBusinessGroupEntityList = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getI18nFlag()
                        .equals(i18nFlag)).collect(Collectors.toList());
        List<PositionTypeVo> positionTypeList = new ArrayList<>();
        List<CeoBusinessPositionType> ceoBusinessPositionTypeList = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>()
                .in(CeoBusinessPositionType::getId, 1, 12, 11, 2, 10)
                .eq(Objects.nonNull(request.getPositionTypeId()), CeoBusinessPositionType::getId, request.getPositionTypeId())
        );
        if (!CollectionUtils.isEmpty(ceoBusinessPositionTypeList)) {
            ceoBusinessPositionTypeList.forEach(e -> {
                PositionTypeVo positionTypeVo = new PositionTypeVo();
                positionTypeVo.setPositionTypeId(e.getId().intValue());
                positionTypeVo.setPositionTypeName(e.getPositionName());
                positionTypeList.add(positionTypeVo);
            });
        }
        sfaBusinessGroupEntityList.forEach(e -> {
            BusinessGroupPositionTypeVo businessGroupPositionTypeVo = new BusinessGroupPositionTypeVo();
            businessGroupPositionTypeVo.setBusinessGroupId(e.getId());
            businessGroupPositionTypeVo.setBusinessGroupName(e.getBusinessGroupName());
            businessGroupPositionTypeVo.setPositionTypeList(disposePositionTypeList(positionTypeList, e.getI18nFlag()));
            returnList.add(businessGroupPositionTypeVo);
        });
        return returnList;
    }

    @Override
    public List<BusinessGroupPositionTypeVo> queryBusinessGroupPositionTypeIncludeBd(BusinessGroupPositionTypeRequest request) {
        List<BusinessGroupPositionTypeVo> returnList = new ArrayList<>();
        Integer businessGroup = RequestUtils.getLoginInfo().getBusinessGroup();
        // 登录人岗位信息
        List<SfaPositionRelationEntity> personPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, request.getPerson())
                .eq(Objects.nonNull(request.getBusinessGroupId()), SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroupId())
                .ne(SfaPositionRelationEntity::getBusinessGroup, 99)
                .eq(SfaPositionRelationEntity::getPositionTypeId, 7)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(personPositionRelationList)) {
            return returnList;
        }
        // 登录人产品组信息
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntityList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .in(SfaBusinessGroupEntity::getId, personPositionRelationList.stream().map(SfaPositionRelationEntity::getBusinessGroup).distinct().collect(Collectors.toList()))
        );
        if (CollectionUtils.isEmpty(sfaBusinessGroupEntityList)) {
            return returnList;
        }
        // 判断当前产品组国际化属性--剔除不同的属性
        Integer i18nFlag = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getId().equals(businessGroup))
                .map(SfaBusinessGroupEntity::getI18nFlag).findFirst().orElse(0);
        sfaBusinessGroupEntityList = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getI18nFlag()
                        .equals(i18nFlag)).collect(Collectors.toList());
        // 先查 大区-区域经理
        List<PositionTypeVo> positionTypeList = new ArrayList<>();
        List<CeoBusinessPositionType> ceoBusinessPositionTypeList = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>()
                .in(CeoBusinessPositionType::getId, 1, 12, 11, 2, 10)
                .eq(Objects.nonNull(request.getPositionTypeId()), CeoBusinessPositionType::getId, request.getPositionTypeId())
        );
        if (!CollectionUtils.isEmpty(ceoBusinessPositionTypeList)) {
            ceoBusinessPositionTypeList.forEach(e -> {
                PositionTypeVo positionTypeVo = new PositionTypeVo();
                positionTypeVo.setPositionTypeId(e.getId().intValue());
                positionTypeVo.setPositionTypeName(e.getPositionName());
                positionTypeList.add(positionTypeVo);
            });
        }
        positionTypeList.addAll(setBdPositionTypeInfo());

        sfaBusinessGroupEntityList.forEach(e -> {
            BusinessGroupPositionTypeVo businessGroupPositionTypeVo = new BusinessGroupPositionTypeVo();
            businessGroupPositionTypeVo.setBusinessGroupId(e.getId());
            businessGroupPositionTypeVo.setBusinessGroupName(e.getBusinessGroupName());
            businessGroupPositionTypeVo.setPositionTypeList(disposePositionTypeList(positionTypeList, e.getI18nFlag()));
            returnList.add(businessGroupPositionTypeVo);
        });
        return returnList;
    }

    /**
     * 额外处理职位类型 中转英
     *
     * @param positionTypeList
     * @param i18nFlag
     */
    public List<PositionTypeVo> disposePositionTypeList(List<PositionTypeVo> positionTypeList, Integer i18nFlag) {
        positionTypeList.forEach(positionTypeVo -> {
            positionTypeVo.setPositionTypeName(OrganizationPositionRelationEnums.getPositionName(positionTypeVo.getPositionTypeId(), i18nFlag));
        });
        return positionTypeList;
    }
    /**
     * 补充业务 bd 361 362 372
     * 全职 bd 361
     * 兼职 bd 362
     * 承揽 bd 372
     */
    private List<PositionTypeVo> setBdPositionTypeInfo() {
        List<PositionTypeVo> positionTypeList = new ArrayList<>();
        PositionTypeVo vo1 = new PositionTypeVo();
        vo1.setPositionTypeId(361);
        vo1.setPositionTypeName(EmployeeInfoPostTypeEnum.FULL_TIME.getName() + EmployeeInfoTypeEnum.BD_BUSINESS.getName());
        positionTypeList.add(vo1);

        PositionTypeVo vo2 = new PositionTypeVo();
        vo2.setPositionTypeId(362);
        vo2.setPositionTypeName(EmployeeInfoPostTypeEnum.PART_TIME.getName() + EmployeeInfoTypeEnum.BD_BUSINESS.getName());
        positionTypeList.add(vo2);

        PositionTypeVo vo3 = new PositionTypeVo();
        vo3.setPositionTypeId(372);
        vo3.setPositionTypeName(EmployeeInfoTypeEnum.BD_CONTRACT.getName());
        positionTypeList.add(vo3);

        return positionTypeList;
    }

    @Override
    public List<PositionTypeBusinessGroupDto> queryPositionTypeBusinessGroup(PositionTypeBusinessGroupRequest request) {
        List<PositionTypeBusinessGroupDto> returnList = new ArrayList<>();
        // 登陆人关联产品组信息
        List<SfaPositionRelationEntity> personPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, request.getPerson())
                .eq(Objects.nonNull(request.getBusinessGroupId()), SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroupId())
                .ne(SfaPositionRelationEntity::getBusinessGroup, 99)
                .eq(SfaPositionRelationEntity::getPositionTypeId, 7)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(personPositionRelationList)) {
            return returnList;
        }
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntityList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .in(SfaBusinessGroupEntity::getId, personPositionRelationList.stream().map(SfaPositionRelationEntity::getBusinessGroup).distinct().collect(Collectors.toList()))
        );
        // 判断当前产品组国际化属性--剔除不同的属性
        Integer i18nFlag = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getId().equals(RequestUtils.getLoginInfo().getBusinessGroup()))
                .map(SfaBusinessGroupEntity::getI18nFlag).findFirst().orElse(0);
        sfaBusinessGroupEntityList = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getI18nFlag()
                        .equals(i18nFlag)).collect(Collectors.toList());

        List<Long> positionTypeList = Arrays.asList(1L, 12L, 11L, 2L, 10L);
        List<CeoBusinessPositionType> ceoBusinessPositionTypeList = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>()
                .in(CeoBusinessPositionType::getId, positionTypeList)
                .eq(Objects.nonNull(request.getPositionTypeId()), CeoBusinessPositionType::getId, request.getPositionTypeId())
        );
        if(CollectionUtil.isNotEmpty(ceoBusinessPositionTypeList)&& CollectionUtil.isNotEmpty(sfaBusinessGroupEntityList)){
            List<BusinessGroupPositionRelationDto> groupDto = sfaBusinessGroupEntityList.stream().map(b -> {
                BusinessGroupPositionRelationDto dto = new BusinessGroupPositionRelationDto();
                dto.setBusinessGroupId(b.getId());
                dto.setBusinessGroupName(b.getBusinessGroupName());
                return dto;
            }).collect(Collectors.toList());
            // 排序
            ceoBusinessPositionTypeList.sort(Comparator.comparingInt(o -> positionTypeList.indexOf(o.getId())));
            returnList.addAll(ceoBusinessPositionTypeList.stream().map(p->{
                PositionTypeBusinessGroupDto dto = new PositionTypeBusinessGroupDto();
                dto.setPositionTypeId(p.getId().intValue());
                dto.setPositionTypeName(OrganizationPositionRelationEnums.getPositionName(dto.getPositionTypeId(), i18nFlag));
                dto.setBusinessGroupList(groupDto);
                return dto;
            }).collect(Collectors.toList()));
        }

        return returnList;
    }

    @Override
    public List<PositionTypeBusinessGroupDto> queryPositionTypeBusinessGroupIncludeBd(PositionTypeBusinessGroupRequest request) {
        List<PositionTypeBusinessGroupDto> returnList = new ArrayList<>();
        // 登陆人关联产品组信息
        List<SfaPositionRelationEntity> personPositionRelationList = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, request.getPerson())
                .eq(Objects.nonNull(request.getBusinessGroupId()), SfaPositionRelationEntity::getBusinessGroup, request.getBusinessGroupId())
                .ne(SfaPositionRelationEntity::getBusinessGroup, 99)
                .eq(SfaPositionRelationEntity::getPositionTypeId, 7)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0));
        if (CollectionUtils.isEmpty(personPositionRelationList)) {
            return returnList;
        }
        List<SfaBusinessGroupEntity> sfaBusinessGroupEntityList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
                .in(SfaBusinessGroupEntity::getId, personPositionRelationList.stream().map(SfaPositionRelationEntity::getBusinessGroup).distinct().collect(Collectors.toList()))
        );
        // 判断当前产品组国际化属性--剔除不同的属性
        Integer i18nFlag = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getId().equals(RequestUtils.getLoginInfo().getBusinessGroup()))
                .map(SfaBusinessGroupEntity::getI18nFlag).findFirst().orElse(0);
        sfaBusinessGroupEntityList = sfaBusinessGroupEntityList.stream()
                .filter(x -> x.getI18nFlag()
                        .equals(i18nFlag)).collect(Collectors.toList());
        List<Long> positionTypeList = Arrays.asList(1L, 12L, 11L, 2L, 10L);
        List<CeoBusinessPositionType> ceoBusinessPositionTypeList = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>()
                .in(CeoBusinessPositionType::getId, positionTypeList)
                .eq(Objects.nonNull(request.getPositionTypeId()), CeoBusinessPositionType::getId, request.getPositionTypeId())
        );

        if (CollectionUtil.isEmpty(ceoBusinessPositionTypeList)) {
            ceoBusinessPositionTypeList = new ArrayList<>();
        }
        /**
         * 补充业务 bd 361 362 372
         * 全职 bd 361
         * 兼职 bd 362
         * 承揽 bd 372
         */
        List<Long> finalPositionTypeList = Arrays.asList(1L, 12L, 11L, 2L, 10L, 361L, 362L, 372L);
        CeoBusinessPositionType t1 = new CeoBusinessPositionType();
        t1.setId(361L);
        t1.setPositionName(EmployeeInfoPostTypeEnum.FULL_TIME.getName() + EmployeeInfoTypeEnum.BD_BUSINESS.getName());
        ceoBusinessPositionTypeList.add(t1);
        CeoBusinessPositionType t2 = new CeoBusinessPositionType();
        t2.setId(362L);
        t2.setPositionName(EmployeeInfoPostTypeEnum.PART_TIME.getName() + EmployeeInfoTypeEnum.BD_BUSINESS.getName());
        ceoBusinessPositionTypeList.add(t2);
        CeoBusinessPositionType t3 = new CeoBusinessPositionType();
        t3.setId(372L);
        t3.setPositionName(EmployeeInfoPostTypeEnum.PART_TIME.getName() + EmployeeInfoTypeEnum.BD_CONTRACT.getName());
        ceoBusinessPositionTypeList.add(t3);


        List<BusinessGroupPositionRelationDto> groupDto = sfaBusinessGroupEntityList.stream().map(b -> {
            BusinessGroupPositionRelationDto dto = new BusinessGroupPositionRelationDto();
            dto.setBusinessGroupId(b.getId());
            dto.setBusinessGroupName(b.getBusinessGroupName());
            return dto;
        }).collect(Collectors.toList());
        // 排序
        ceoBusinessPositionTypeList.sort(Comparator.comparingInt(o -> finalPositionTypeList.indexOf(o.getId())));
        returnList.addAll(ceoBusinessPositionTypeList.stream().map(p -> {
            PositionTypeBusinessGroupDto dto = new PositionTypeBusinessGroupDto();
            dto.setPositionTypeId(p.getId().intValue());
            dto.setPositionTypeName(OrganizationPositionRelationEnums.getPositionName(dto.getPositionTypeId(), i18nFlag));
            dto.setBusinessGroupList(groupDto);
            return dto;
        }).collect(Collectors.toList()));

        return returnList;
    }

    @Override
    public List<ChildVo> queryChildListByParent(ChildRequest request) {

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        request.setBusinessGroup(loginInfo.getBusinessGroup());
        List<String> personOrganizationIds = organizationMapper.getEmployeeOrganizationId(request.getPerson(), loginInfo);
        if (CollectionUtils.isEmpty(personOrganizationIds)) {
            throw new ApplicationException("操作人组织信息获取失败");
        }else if (!OrganizationTypeEnum.ZB.getOrganizationType().equals(loginInfo.getOrganizationType())) {
            request.setOrganizationIds(personOrganizationIds);
            request.setOrganizationType(loginInfo.getOrganizationType());
        }
        return sfaEmployeeInfoMapper.queryChildListByParentOrg(null, request);
    }

    @Override
    public String selectParentOrgCode(Integer partnerMemberKey, Integer businessGroupId) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getMemberKey, partnerMemberKey).orderByDesc(SfaEmployeeInfoModel::getId).last("limit 1"));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("memberKey不存在");
        }

        SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmployeeInfoId, sfaEmployeeInfoModel.getId())
                .eq(SfaPositionRelationEntity::getBusinessGroup, businessGroupId).eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .orderByDesc(SfaPositionRelationEntity::getId).last("limit 1"));
        if(Objects.isNull(sfaPositionRelationEntity)){
            throw new ApplicationException("岗位表获取失败");
        }
        return sfaPositionRelationEntity.getParentOrganizationCode();
    }

    @Override
    public String getMobileByEmpId(String empId) {
        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new LambdaQueryWrapper<SfaEmployeeInfoModel>().eq(SfaEmployeeInfoModel::getEmployeeId, empId).last("limit 1"));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            return StringUtils.EMPTY;
        }
        return sfaEmployeeInfoModel.getMobile();
    }

    /**
     * 获取员工主要组织路径信息
     * 按照组织层级顺序返回：战区-大区-省区-分公司-部门
     *
     * @param employeeId 员工工号
     * @return 组织路径列表，按层级排序
     * @throws ApplicationException 当工号不存在或不支持查询时抛出异常
     */
    @Override
    public List<OrgVo> getPrimaryOrgPathByEmployeeId(String employeeId) {
        if (StringUtils.isBlank(employeeId)) {
            throw new ApplicationException("员工工号不能为空");
        }

        // 查询员工主岗位关系
        SfaPositionRelationEntity sfaPositionRelationEntity = sfaPositionRelationMapper.selectOne(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                .eq(SfaPositionRelationEntity::getEmpId, employeeId)
                .eq(SfaPositionRelationEntity::getStatus, 1)
                .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                .ne(SfaPositionRelationEntity::getPositionTypeId, PositionTypeEnum.ZB.getPositionTypeId())
                .last("limit 1"));
                
        if (Objects.isNull(sfaPositionRelationEntity)) {
            throw new ApplicationException("未找到员工[" + employeeId + "]的有效岗位信息");
        }
        
        // 按组织层级顺序构建路径
        List<OrgVo> orgPath = new ArrayList<>();
        
        // 使用工具方法添加组织节点，保证层级顺序
        addOrgNodeIfExists(orgPath, sfaPositionRelationEntity.getAreaCode(), sfaPositionRelationEntity.getAreaName(),OrganizationTypeEnum.AREA.getOrganizationType());
        addOrgNodeIfExists(orgPath, sfaPositionRelationEntity.getVareaCode(), sfaPositionRelationEntity.getVareaName(),OrganizationTypeEnum.VARE.getOrganizationType());
        addOrgNodeIfExists(orgPath, sfaPositionRelationEntity.getProvinceCode(), sfaPositionRelationEntity.getProvinceName(),OrganizationTypeEnum.PROVINCE.getOrganizationType());
        addOrgNodeIfExists(orgPath, sfaPositionRelationEntity.getCompanyCode(), sfaPositionRelationEntity.getCompanyName(),OrganizationTypeEnum.COMPANY.getOrganizationType());
        addOrgNodeIfExists(orgPath, sfaPositionRelationEntity.getDepartmentCode(), sfaPositionRelationEntity.getDepartmentName(),OrganizationTypeEnum.DEPARTMENT.getOrganizationType());

        return orgPath;
    }
    
    /**
     * 如果组织代码存在，则添加到组织路径中
     *
     * @param orgPath 组织路径列表
     * @param orgCode 组织代码
     * @param orgName 组织名称
     */
    private void addOrgNodeIfExists(List<OrgVo> orgPath, String orgCode, String orgName,String orgType) {
        if (StringUtils.isNotBlank(orgCode)) {
            orgPath.add(OrgVo.builder().organizationId(orgCode).organizationName(orgName).organizationType(orgType).build());
        }
    }

    public String getRecommendPartnerStatus(Integer position, Integer jobsType, Integer type) {
        if(Objects.nonNull(type) && type == 2){
            return "企业合伙人";
        }

        if(position == 1 && jobsType == 1){
            return "全职合伙人";
        }

        if(position == 1 && jobsType == 2){
            return "兼职合伙人";
        }

        if(position == 2){
            return "区域总监";
        }

        return StringUtils.EMPTY;
    }

    private String getOnboardStatus(RecommendModel e) {
        Integer processType = e.getProcessType();
        Integer processResult = e.getProcessResult();

        if(processType == ProcessType.INTERVIEW.getProcessCode()){
            if(processResult == ProcessResult.NOT_PROCESS.getResultCode() || processResult == ProcessResult.PASS.getResultCode() || processResult == ProcessResult.PROCESSING.getResultCode()){
                return OnboardEnum.INTERVIEWING.getName();
            }else{
                return OnboardEnum.INTERVIEW_FAIL.getName();
            }

        }else if(processType == ProcessType.REINTERVIEW.getProcessCode()){
            if(processResult == ProcessResult.NOT_PROCESS.getResultCode() || processResult == ProcessResult.PROCESSING.getResultCode()){
                return OnboardEnum.INTERVIEWING.getName();
            }else{
                return OnboardEnum.INTERVIEW_FAIL.getName();
            }
        }else if(processType == ProcessType.PROBATION.getProcessCode()){
            if(processResult == ProcessResult.PROCESSING.getResultCode()){
                return OnboardEnum.PROBATION.getName();
            }else{
                return OnboardEnum.PROBATION_FAIL.getName();
            }
        }else if(processType == ProcessType.APPLY_ONBOARD.getProcessCode() || processType == ProcessType.APPLY_ONBOARD_VAILD.getProcessCode()){
            if(processResult == ProcessResult.NOT_PROCESS.getResultCode() || processResult == ProcessResult.PASS.getResultCode() || processResult == ProcessResult.PROCESSING.getResultCode()){
                return OnboardEnum.ON_BOARD_WAIT.getName();
            }else{
                return OnboardEnum.ON_BOARD_FAIL.getName();
            }
        }else if(processType == ProcessType.ZB_VERIFY.getProcessCode()){
            return OnboardEnum.ON_BOARD_WAIT.getName();
        }else if(processType == ProcessType.DO_ONBOARD.getProcessCode()){
            if(processResult == ProcessResult.PASS.getResultCode()){
                return OnboardEnum.ON_BOARD.getName();
            }else if(processResult == ProcessResult.NOT_PROCESS.getResultCode() || processResult == ProcessResult.PROCESSING.getResultCode()){
                return OnboardEnum.ON_BOARD_WAIT.getName();
            }else{
                return OnboardEnum.ON_BOARD_FAIL.getName();
            }
        }else if(processType == ProcessType.APPLY_RESIGN.getProcessCode()){
            return OnboardEnum.ON_BOARD.getName();
        }else if(processType == ProcessType.DO_RESIGN.getProcessCode()){
            if(processResult == ProcessResult.PASS.getResultCode()){
                return OnboardEnum.OFF_BOARD.getName();
            }else{
                return OnboardEnum.ON_BOARD.getName();
            }
        }else{
            return StringUtils.EMPTY;
        }
    }
}
