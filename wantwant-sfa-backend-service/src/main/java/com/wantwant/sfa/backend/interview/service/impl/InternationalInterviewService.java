package com.wantwant.sfa.backend.interview.service.impl;



import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.alibaba.excel.util.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.domain.ceoModify.enums.GenderEnum;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.interview.api.InternationalEmpSearchRequest;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.interview.model.InternationalEmpImportModel;
import com.wantwant.sfa.backend.interview.model.InternationalEmpModel;
import com.wantwant.sfa.backend.interview.service.IInternationalEmpBuildService;
import com.wantwant.sfa.backend.interview.service.IInternationalInterviewService;
import com.wantwant.sfa.backend.interview.vo.InternationalEmpVo;
import com.wantwant.sfa.backend.mapper.OrganizationMapper;
import com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper;
import com.wantwant.sfa.backend.transaction.enums.InternationalPositionEnum;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.ExportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InternationalInterviewService implements IInternationalInterviewService {

    @Resource
    private IInternationalEmpBuildService internationalEmpBuildService;
    @Resource
    private IEmpService empService;
    @Resource
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;


    private static final String IMPORT_PERSONNEL_ERROR = "Import failed for user: {0}, error: {1}";
    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    @Transactional
    public List<String> importPersonnelInfo(MultipartFile file, String employeeId) {
        // 获取操作人信息
        ProcessUserDO processUserDO = empService.getUserById(employeeId);

        // 读区导入文件
        List<InternationalEmpImportModel> list = readFile(file);

        List<String> errorMessages = new ArrayList<>();
        // 批量插入数据
        for (InternationalEmpImportModel model : list) {
            try {
                // 创建用户
                internationalEmpBuildService.createPersonnelInfo(model,processUserDO);
            } catch (Exception e) {
                errorMessages.add(MessageFormat.format(IMPORT_PERSONNEL_ERROR, model.getEmployeeName(), e.getMessage()));
            }
        }


        return errorMessages;
    }

    private List<InternationalEmpImportModel> readFile(MultipartFile file) {
        List<InternationalEmpImportModel> list = new ArrayList<>();

        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), InternationalEmpImportModel.class, params);
        } catch (Exception e) {
            log.error("【gold upload】导入失败", e);
            throw new ApplicationException("File import failed");
        }
        // 过滤姓名为空的行
        list = list.stream().filter(f -> StringUtils.isNotBlank(f.getEmployeeName()))
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(list)){
            throw new ApplicationException("File empty or no valid data found");
        }

        return list;
    }

    @Override
    public IPage<InternationalEmpVo> selectInternationalEmpList(InternationalEmpSearchRequest internationalEmpSearchRequest) {
        IPage<InternationalEmpVo> page = new Page<>(internationalEmpSearchRequest.getPage(),internationalEmpSearchRequest.getRows());

        setRequestParams(internationalEmpSearchRequest);

        // 查询人员列表
        List<InternationalEmpVo> list = Optional.ofNullable(searchInternationalEmpList(internationalEmpSearchRequest,page)).orElse(Collections.emptyList());

        page.setRecords(list);
        return page;
    }

    private void setRequestParams(InternationalEmpSearchRequest internationalEmpSearchRequest) {
        internationalEmpSearchRequest.setBusinessGroup(RequestUtils.getBusinessGroup());


        String organizationId = internationalEmpSearchRequest.getOrganizationId();
        if(Objects.nonNull(organizationId)){
            internationalEmpSearchRequest.setOrganizationType(organizationMapper.getOrganizationType(organizationId));
        }
    }

    private List<InternationalEmpVo> searchInternationalEmpList(InternationalEmpSearchRequest internationalEmpSearchRequest, IPage<InternationalEmpVo> page) {
        List<InternationalEmpModel> internationalEmpModels = sfaEmployeeInfoMapper.searchInternationalEmpList(page, internationalEmpSearchRequest);
        if(CollectionUtils.isEmpty(internationalEmpModels)){
            return Collections.emptyList();
        }

        return internationalEmpModels.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    private InternationalEmpVo convertToVo(InternationalEmpModel model) {
        InternationalEmpVo vo = new InternationalEmpVo();
        vo.setEmployeeName(model.getEmployeeName());
        vo.setGender(GenderEnum.getGenderEnum(Optional.ofNullable(model.getGender()).orElse(0)).getEnDescription());
        vo.setMobile(model.getMobile());
        vo.setIdCard(model.getIdCard());
        vo.setChannel(model.getChannel());
        vo.setOfficeLocation(model.getOfficeLocation());
        vo.setAreaName(model.getAreaName());
        vo.setVareaName(model.getVareaName());
        vo.setCompanyName(model.getCompanyName());
        vo.setChannel(model.getChannel());
        EmployeeStatus employeeStatus = EmployeeStatus.findEnumByType(model.getEmployeeStatus());
        if(Objects.nonNull(employeeStatus)){
            vo.setEmployeeStatus(employeeStatus.getEnDescription());
        }

        PositionEnum positionEnum = PositionEnum.getEnum(model.getCeoType(), model.getJobsType(), model.getPosition());
        InternationalPositionEnum internationalPositionEnum = InternationalPositionEnum.getEnumByPosition(positionEnum);
        if(Objects.nonNull(internationalPositionEnum)){
            vo.setPosition(internationalPositionEnum.getPositionName());
        }

        // 拼接家庭住址
        StringBuilder homeAddress = new StringBuilder();
        if (StringUtils.isNotBlank(model.getHomeProvince())) homeAddress.append(model.getHomeProvince());
        if (StringUtils.isNotBlank(model.getHomeCity())) homeAddress.append(model.getHomeCity());
        if (StringUtils.isNotBlank(model.getHomeDistrict())) homeAddress.append(model.getHomeDistrict());
        if (StringUtils.isNotBlank(model.getHomeStreet())) homeAddress.append(model.getHomeStreet());
        vo.setHomeAddress(homeAddress.toString());
        
        // 拼接经销地区
        StringBuilder salesAddress = new StringBuilder();
        if (StringUtils.isNotBlank(model.getSalesProvince())) salesAddress.append(model.getSalesProvince());
        if (StringUtils.isNotBlank(model.getSalesCity())) salesAddress.append(model.getSalesCity());
        if (StringUtils.isNotBlank(model.getSalesDistrict())) salesAddress.append(model.getSalesDistrict());
        vo.setSalesAddress(salesAddress.toString());
        
        vo.setOfficeLocation(model.getOfficeLocation());

        LocalDate hireDate = model.getHireDate();
        if(Objects.nonNull(hireDate)){
            // 将hireDate转换为dd-MM-yyyy格式
            vo.setHireDate(hireDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            // 根据offBoardDate计算在职天数，如果offBoardDate为空，则计算当前日期与hireDate的差值
            LocalDate offBoardDate = model.getOffBoardDate();
            long onTheJobDays = 0;
            if (Objects.nonNull(offBoardDate)) {
                onTheJobDays = ChronoUnit.DAYS.between(hireDate, offBoardDate);
            } else {
                onTheJobDays = ChronoUnit.DAYS.between(hireDate, LocalDate.now());
            }
            vo.setOnTheJobDate(onTheJobDays);
        }

    
        return vo;
    }

    @Override
    public void exportInternationalEmpList(InternationalEmpSearchRequest internationalEmpSearchRequest, HttpServletRequest request, HttpServletResponse response) {
        setRequestParams(internationalEmpSearchRequest);
        // 查询人员列表
        List<InternationalEmpVo> list = Optional.ofNullable(searchInternationalEmpList(internationalEmpSearchRequest,null)).orElse(Collections.emptyList());


        ExportUtil.writeEasyExcelResponse(response, request, "在职人员列表", InternationalEmpVo.class, list);
    }
}
