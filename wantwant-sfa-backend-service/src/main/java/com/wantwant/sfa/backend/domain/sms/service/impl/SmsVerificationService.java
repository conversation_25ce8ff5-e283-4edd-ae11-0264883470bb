package com.wantwant.sfa.backend.domain.sms.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.wantwant.arch.notification.api.dto.MessageDto;
import com.wantwant.arch.notification.api.dto.NotifyJobMessageRequestDto;
import com.wantwant.arch.notification.api.dto.NotifyWeComMessageRequestDto;
import com.wantwant.arch.notification.api.dto.WcImSendTextContentDto;
import com.wantwant.arch.notification.api.service.NotificationApi;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.sfa.backend.domain.sms.DO.InitVerificationCodeDO;
import com.wantwant.sfa.backend.domain.sms.DO.VerificationCodeVerifyDO;
import com.wantwant.sfa.backend.domain.sms.repository.facade.ISmsVerificationRepository;
import com.wantwant.sfa.backend.domain.sms.repository.po.SmsVerificationsPO;
import com.wantwant.sfa.backend.domain.sms.service.ISmsVerificationService;
import com.wantwant.sfa.backend.domain.sms.service.factory.SmsVerificationFactory;
import com.wantwant.sfa.backend.util.GeTuiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/06/19/上午10:12
 */
@Service
@Slf4j
public class SmsVerificationService implements ISmsVerificationService {
    @Resource
    private NotificationApi notificationApi;

    @Resource
    private ISmsVerificationRepository smsVerificationRepository;
    @Resource
    private GeTuiService geTuiService;

    @Override
    @Transactional
    public void initVerificationCode(InitVerificationCodeDO initVerificationCodeDO) {
        // 当前有效的验证码标记无效
        smsVerificationRepository.invalid(initVerificationCodeDO.getMobile(),initVerificationCodeDO.getType());
        // 生成验证码
        String code = RandomUtil.randomNumbers(initVerificationCodeDO.getVerificationCodeSize());

        // 发送个推消息
        Map<String,String> smsParam = new HashMap<>();
        smsParam.put("code",code);

        if(initVerificationCodeDO.getMobile().startsWith("021")){

            NotifyWeComMessageRequestDto notifyWeComMessageRequestDto = new NotifyWeComMessageRequestDto();
            notifyWeComMessageRequestDto.setRobotKey("f91c0828-e7d0-4a0d-8789-739ebe0a7868");
            WcImSendTextContentDto wcImSendTextContentDto = new WcImSendTextContentDto();
            wcImSendTextContentDto.setContent("旺金币发放验证码: " + String.join(",",code));
            notifyWeComMessageRequestDto.setText(wcImSendTextContentDto);
            notificationApi.notifyWeComMessage(notifyWeComMessageRequestDto);
        }else{
            try {
                geTuiService.SmsPushList(initVerificationCodeDO.getMessageTemplateId(),smsParam, Arrays.asList(initVerificationCodeDO.getMobile()));
            } catch (Exception e) {
                log.info("【send verification code】error:{}",e.getMessage());
                throw new ApplicationException("验证码发放失败");
            }
        }


        SmsVerificationsPO smsVerificationsPO = SmsVerificationFactory.initVerificationPO(initVerificationCodeDO.getMobile(), code, initVerificationCodeDO.getType());
        // 保存验证码
        smsVerificationRepository.save(smsVerificationsPO);
    }

    @Override
    public boolean verify(VerificationCodeVerifyDO verificationCodeVerifyDO) {

        // 获取当前有效的验证码
        String code = smsVerificationRepository.getCode(verificationCodeVerifyDO.getType(),verificationCodeVerifyDO.getMobile());
        if(StringUtils.isBlank(code)){
            return false;
        }

        return code.equals(verificationCodeVerifyDO.getCode());

    }
}
