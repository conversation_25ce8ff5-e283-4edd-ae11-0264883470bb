package com.wantwant.sfa.backend.yearFestival.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.ex.BizException;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.daily.request.DailyEmployeeRequest;
import com.wantwant.sfa.backend.daily.vo.DailyEmployeeVo;
import com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO;
import com.wantwant.sfa.backend.entity.CeoBusinessOrganizationEntity;
import com.wantwant.sfa.backend.entity.CeoBusinessPositionType;
import com.wantwant.sfa.backend.mapper.ApplyMemberMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessOrganizationPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.CeoBusinessPositionTypeMapper;
import com.wantwant.sfa.backend.mapper.RealtimeMapper;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.daily.DimEmpPosRoleOrgDayMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.yearFestival.AnnualPerformanceCustomerDetailMapper;
import com.wantwant.sfa.backend.mapper.yearFestival.AnnualPerformanceCustomerDetailProductMapper;
import com.wantwant.sfa.backend.mapper.yearFestival.AnnualPerformanceSummaryOrganizationMapper;
import com.wantwant.sfa.backend.mapper.yearFestival.AnnualPerformanceSummaryOrganizationProductMapper;
import com.wantwant.sfa.backend.mapper.yearFestival.AnnualPerformanceSummaryTrendsMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.service.impl.SettingServiceImpl;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.yearFestival.request.YearFestivalRequest;
import com.wantwant.sfa.backend.yearFestival.service.YearFestivalService;
import com.wantwant.sfa.backend.yearFestival.vo.YearCustomerVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearFestivalDetailVo;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductOptions;
import com.wantwant.sfa.backend.yearFestival.vo.YearProductVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearCustomerOrg99Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearCustomerOrgVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearCustomerProductLineVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearCustomerProductSkuVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearCustomerProductSpuVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearFestivalDetail0Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearFestivalDetail1Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductLine0Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductLine1Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductPartnerLineVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductPartnerSkuVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductPartnerSpuVo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductSku0Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductSku1Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductSpu0Vo;
import com.wantwant.sfa.backend.yearFestival.vo.download.YearProductSpu1Vo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class YearFestivalServiceImpl implements YearFestivalService {

    private static final Object NEXT_ORG_KEY = "sfa:next:org:";

    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RealTimeUtils realTimeUtils;
    @Autowired
    private SettingServiceImpl settingServiceImpl;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private CeoBusinessOrganizationMapper ceoBusinessOrganizationMapper;
    @Autowired
    private CeoBusinessPositionTypeMapper ceoBusinessPositionTypeMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;


    @Autowired
    private RealtimeMapper realtimeMapper;
    @Autowired
    private DimEmpPosRoleOrgDayMapper dimEmpPosRoleOrgDayMapper;
    @Autowired
    private AnnualPerformanceSummaryOrganizationMapper annualPerformanceSummaryOrganizationMapper;
    @Autowired
    private AnnualPerformanceSummaryTrendsMapper annualPerformanceSummaryTrendsMapper;
    @Autowired
    private AnnualPerformanceSummaryOrganizationProductMapper annualPerformanceSummaryOrganizationProductMapper;
    @Autowired
    private AnnualPerformanceCustomerDetailMapper annualPerformanceCustomerDetailMapper;
    @Autowired
    private AnnualPerformanceCustomerDetailProductMapper annualPerformanceCustomerDetailProductMapper;


    private CeoBusinessOrganizationPositionRelation check(YearFestivalRequest request) {

        request.setYearFestivalScale(Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.YEAR_FESTIVAL_SCALE)));
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
        request.setLastYear(request.getTheYear() - 1);
        // 设定年月，用于获取人员信息。当前年节：当前自然年月；历史年节：最后一天所在年月
//        if (Integer.valueOf(settingServiceImpl.getValue(DictCodeConstants.YEAR_FESTIVAL_CURRENT)).compareTo(request.getTheYear()) == 0) {
//            request.setYearMonth(LocalDateTimeUtils.formatNow("yyyy-MM"));
//        } else {
//            switch (request.getTheYear()) {
//                case 2022:
//                    request.setYearMonth("2022-01");
//                    break;
//                case 2023:
//                    request.setYearMonth("2023-01");
//                    break;
//                case 2024:
//                    request.setYearMonth("2024-02");
//                    break;
//                case 2025:
//                    request.setYearMonth("2025-01");
//                    break;
//                default:
//                    request.setYearMonth(LocalDateTimeUtils.formatNow("yyyy-MM"));
//            }
//        }
        request.setYearMonth(LocalDateTimeUtils.formatNow("yyyy-MM"));
        if (!CollectionUtils.isEmpty(request.getChooseOrganizationIds())) {
            request.setChooseOrganizationIds(request.getChooseOrganizationIds().stream().filter(s -> !s.contains("ZB")).collect(Collectors.toList()));
        }

        // check操作人
        CeoBusinessOrganizationPositionRelation personRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId, request.getPerson())
                .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                .last("limit 1")
        );
        if (Objects.isNull(personRelation)) {
            throw new ApplicationException("操作人信息获取失败");
        }
        if (StringUtils.isBlank(request.getOrganizationId())) {
            request.setOrganizationId(personRelation.getOrganizationId());
        }

        // check目标人员
        CeoBusinessOrganizationPositionRelation relation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, request.getOrganizationId())
                .eq(CeoBusinessOrganizationPositionRelation::getBusinessGroup, RequestUtils.getBusinessGroup())
                .last("limit 1")
        );
        if (Objects.isNull(relation)) {
            throw ExceptionConstants.FAILED_BUSINESS("查无此人");
        }

        request.setPositionTypeId(relation.getPositionTypeId());
        if (request.getPositionTypeId() == 7) {
            request.setEmployeeId(request.getPerson());
        } else {
            request.setEmployeeId(relation.getEmployeeId());
        }

        if (Objects.nonNull(request.getSearchType()) && request.getSearchType() == 1) {
            List<String> nextOrgCode = (List<String>) redisUtil.get(NEXT_ORG_KEY + request.getOrganizationId());
            log.info("【next org code】codes:{}", nextOrgCode);
            request.setChildOrganizationIds(nextOrgCode);
        }
        return relation;
    }

    @Override
    public YearFestivalDetailVo yearFestivalDetail(YearFestivalRequest request) {
        log.info("yearFestivalDetail request:{}", request);

        if (Objects.nonNull(request.getMemberKey()) && StringUtils.isNotBlank(request.getOrganizationId())) {
            throw new ApplicationException("memberKey&组织id互斥");
        }

        if (Objects.nonNull(request.getMemberKey())) {
            EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectPartner(request.getMemberKey(), RequestUtils.getBusinessGroup());
            if (Objects.isNull(employeeInfo)) {
                throw ExceptionConstants.FAILED_BUSINESS("查无此人");
            }
            request.setOrganizationId(employeeInfo.getOrganizationId());
        }
        check(request);

        YearFestivalDetailVo vo = annualPerformanceSummaryOrganizationMapper.queryYearFestivalDetail(request);
        if (Objects.isNull(vo)) {
            vo = new YearFestivalDetailVo();
        }
        vo.setCanMap(Boolean.FALSE);

        if (request.getPositionTypeId() == 7) {
            EmployeeInfoVO employeeInfo = sfaPositionRelationMapper.selectZbEmployee(request.getEmployeeId(), RequestUtils.getBusinessGroup());
            if (Objects.nonNull(employeeInfo)) {
                vo.setAvatar(employeeInfo.getAvatar());
                vo.setEmployeeId(employeeInfo.getEmployeeId());
                vo.setEmployeeName(employeeInfo.getEmployeeName());
                vo.setPositionTypeId(7L);
                vo.setBusinessGroupId(employeeInfo.getBusinessGroup());
                vo.setBusinessGroupName(employeeInfo.getBusinessGroupName());
                vo.setOrganizationId(employeeInfo.getOrganizationId());
                vo.setOrganizationName(employeeInfo.getOrganizationName());
                if (Objects.nonNull(employeeInfo.getOnboardTime())) {
                    vo.setOnboardTime(employeeInfo.getOnboardTime());
                    vo.setOnboardDays((int) LocalDateTimeUtils.betweenTwoTime(employeeInfo.getOnboardTime().atStartOfDay(), LocalDateTime.now(), ChronoUnit.DAYS));
                }
            }
        } else {
            DailyEmployeeRequest dailyEmployeeRequest = new DailyEmployeeRequest();
            dailyEmployeeRequest.setTheDate(LocalDate.now());
            dailyEmployeeRequest.setOrganizationIds(Collections.singletonList(request.getOrganizationId()));
            dailyEmployeeRequest.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
            List<DailyEmployeeVo> dailyEmployeeVoList = dimEmpPosRoleOrgDayMapper.queryDailyEmployee(dailyEmployeeRequest);
            if (!CollectionUtils.isEmpty(dailyEmployeeVoList)) {
                Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                        .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
                CeoBusinessOrganizationEntity ceoBusinessOrganizationEntity = ceoBusinessOrganizationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
                        .eq(CeoBusinessOrganizationEntity::getOrganizationId, request.getOrganizationId())
                );
                DailyEmployeeVo employeeInfo = dailyEmployeeVoList.get(0);
                vo.setAvatar(employeeInfo.getAvatar());
                vo.setMemberKey(employeeInfo.getMemberKey());
                vo.setEmployeeInfoId(employeeInfo.getEmployeeInfoId());
                vo.setCanMap(Objects.nonNull(employeeInfo.getEmployeeId()));
                vo.setEmployeeId(employeeInfo.getEmployeeId());
                vo.setEmployeeName(employeeInfo.getEmployeeName());
                vo.setGender(employeeInfo.getGender());
                vo.setPositionTypeId(employeeInfo.getPositionTypeId());
                vo.setPositionTypeName(positionTypeMap.get(employeeInfo.getPositionTypeId()));
                vo.setBusinessGroupId(employeeInfo.getBusinessGroupId());
                vo.setBusinessGroupName(employeeInfo.getBusinessGroupName());
                vo.setOrganizationId(employeeInfo.getOrganizationId());
                if (Objects.nonNull(ceoBusinessOrganizationEntity)) {
                    vo.setOrganizationName(ceoBusinessOrganizationEntity.getOrganizationName());
                }
                vo.setOnboardTime(employeeInfo.getOnboardTime());
                vo.setOnboardDays(employeeInfo.getOnboardDays());
                vo.setTypeName(employeeInfo.getTypeName());
                vo.setEmployeeStatus(employeeInfo.getEmployeeStatus());
                vo.setSystemType(employeeInfo.getSystemType());
                vo.setSystemName(employeeInfo.getSystemName());
                //简历
                vo.setResumeUrl(applyMemberMapper.getResumeUrl(employeeInfo.getPositionId()));
            }
        }

        //累计业绩趋势
        vo.setCumulativePerformance(annualPerformanceSummaryTrendsMapper.queryYearPerformanceSummaryTrendsTotalDays(request));
        //每日业绩趋势
        vo.setDailyPerformance(annualPerformanceSummaryTrendsMapper.queryYearPerformanceSummaryTrendsOneDay(request));

        return vo;
    }

    @Override
    public List<YearFestivalDetailVo> queryPerformanceList(YearFestivalRequest request) {
        log.info("queryPerformanceList request:{}", request);
        check(request);
        List<YearFestivalDetailVo> list = new ArrayList<>();
        if (request.getIsNextRealtime() == 0) {
            // 查自己 也要查下级
            // 自己
            YearFestivalDetailVo vo = annualPerformanceSummaryOrganizationMapper.queryPerformance(request);
            if (Objects.isNull(vo)) {
                vo = new YearFestivalDetailVo();
            }
            vo.setAreaName("合计");
            list.add(vo);
        }
        // 下级
        list.addAll(annualPerformanceSummaryOrganizationMapper.queryPerformanceList(request));
        if (CollectionUtil.isNotEmpty(list)) {
//            List<String> organizationIdList = list.stream().map(YearFestivalDetailVo::getOrganizationId).collect(Collectors.toList());
//            Map<String, String> organizationMap = ceoBusinessOrganizationMapper.selectList(new LambdaQueryWrapper<CeoBusinessOrganizationEntity>()
//                    .in(CeoBusinessOrganizationEntity::getOrganizationId, organizationIdList)
//            ).stream().collect(Collectors.toMap(CeoBusinessOrganizationEntity::getOrganizationId, CeoBusinessOrganizationEntity::getOrganizationName));

            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            list.forEach(item -> {
                if (Objects.nonNull(item.getFullOrganizationId())) {
                    item.setOrgPath(Arrays.asList(item.getFullOrganizationId().split(",")));
                }
                item.setCanMap(Objects.nonNull(item.getEmployeeId()));
//                item.setOrganizationName(organizationMap.get(item.getOrganizationId()));
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setAnnualItemsSupplyTotalAchievementColor(realTimeUtils.saleGoalAchievementColor("9", item.getTheYear().toString(), item.getAnnualItemsSupplyTotalAchievementRate()));
                setSearchType(item);
            });
        }

        return list;
    }

    private void setSearchType(YearFestivalDetailVo item) {
        if (item.getPositionTypeId() == 1) {
            item.setSearchType(2);
        } else if (item.getPositionTypeId() == 12) {
            item.setSearchType(3);
        } else if (item.getPositionTypeId() == 11) {
            item.setSearchType(4);
        } else if (item.getPositionTypeId() == 2) {
            item.setSearchType(5);
        } else if (item.getPositionTypeId() == 10) {
            item.setSearchType(6);
        }
    }

    @Override
    public void downLoadPerformanceList(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadPerformanceList request:{}", request);
        check(request);
        List<YearFestivalDetailVo> list = new ArrayList<>();
        // 查自己 也要查下级
        // 自己
        YearFestivalDetailVo vo = annualPerformanceSummaryOrganizationMapper.queryPerformance(request);
        if (Objects.nonNull(vo) && vo.getPositionTypeId() != 3) {
            vo.setAreaName("合计");
            list.add(vo);
            processPerformanceList(request, list);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            Map<Integer, String> businessGroupMap = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>())
                    .stream().collect(Collectors.toMap(SfaBusinessGroupEntity::getId, SfaBusinessGroupEntity::getBusinessGroupName));

            list.forEach(item -> {
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setBusinessGroupName(businessGroupMap.get(item.getBusinessGroupId()));
            });
        }
        if (Objects.nonNull(request.getDownLoadType())) {
            if (request.getDownLoadType() == 0) {
                List<YearFestivalDetail0Vo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearFestivalDetailVo.class, YearFestivalDetail0Vo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-下拉模式", YearFestivalDetail0Vo.class, excelList);
            } else if (request.getDownLoadType() == 1) {
                List<YearFestivalDetail1Vo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearFestivalDetailVo.class, YearFestivalDetail1Vo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-天数倒数-下拉模式", YearFestivalDetail1Vo.class, excelList);
            }
        } else {
            ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-下拉模式", YearFestivalDetailVo.class, list);
        }
    }

    public void processPerformanceList(YearFestivalRequest request, List<YearFestivalDetailVo> list) {
        // 下级
        List<YearFestivalDetailVo> resultList = annualPerformanceSummaryOrganizationMapper.queryPerformanceList(request);
        if (CollectionUtil.isNotEmpty(resultList)) {
            resultList.forEach(item -> {
                list.add(item);
                request.setOrganizationId(item.getOrganizationId());
                YearFestivalDetailVo vo = annualPerformanceSummaryOrganizationMapper.queryPerformance(request);
                if (Objects.nonNull(vo) && vo.getPositionTypeId() != 3) {
                    request.setPositionTypeId(vo.getPositionTypeId().intValue());
                    processPerformanceList(request, list); // 递归调用
                }
            });
        }
    }

    @Override
    public IPage<YearFestivalDetailVo> queryPerformancePage(YearFestivalRequest request) {
        log.info("queryPerformancePage request:{}", request);
        check(request);

        Page<YearFestivalDetailVo> page = new Page<>(request.getPage(), request.getRows());
        List<YearFestivalDetailVo> list = annualPerformanceSummaryOrganizationMapper.queryPerformancePage(page, request);

        if (CollectionUtil.isNotEmpty(list)) {
            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            list.forEach(item -> {
                if (Objects.nonNull(item.getFullOrganizationId())) {
                    item.setOrgPath(Arrays.asList(item.getFullOrganizationId().split(",")));
                }
                item.setCanMap(Objects.nonNull(item.getEmployeeId()));
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setAnnualItemsSupplyTotalAchievementColor(realTimeUtils.saleGoalAchievementColor("9", item.getTheYear().toString(), item.getAnnualItemsSupplyTotalAchievementRate()));
                setSearchType(item);
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public void downLoadPerformancePage(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadPerformancePage request:{}", request);
        check(request);

        List<YearFestivalDetailVo> list = annualPerformanceSummaryOrganizationMapper.queryPerformancePage(null, request);

        if (CollectionUtil.isNotEmpty(list)) {
            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            Map<Integer, String> businessGroupMap = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>())
                    .stream().collect(Collectors.toMap(SfaBusinessGroupEntity::getId, SfaBusinessGroupEntity::getBusinessGroupName));

            list.forEach(item -> {
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setBusinessGroupName(businessGroupMap.get(item.getBusinessGroupId()));
            });
        }
        if (Objects.nonNull(request.getDownLoadType())) {
            if (request.getDownLoadType() == 0) {
                List<YearFestivalDetail0Vo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearFestivalDetailVo.class, YearFestivalDetail0Vo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-平铺模式", YearFestivalDetail0Vo.class, excelList);
            } else if (request.getDownLoadType() == 1) {
                List<YearFestivalDetail1Vo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearFestivalDetailVo.class, YearFestivalDetail1Vo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-天数倒数-平铺模式", YearFestivalDetail1Vo.class, excelList);
            }
        } else {
            ExportUtil.writeEasyExcelResponse(res, req, "年节业务数据明细-平铺模式", YearFestivalDetailVo.class, list);
        }
    }

    @Override
    public IPage<YearProductVo> queryProductPage(YearFestivalRequest request) {
        log.info("queryProductPage request:{}", request);
        check(request);

        Page<YearProductVo> page = new Page<>(request.getPage(), request.getRows());
        List<YearProductVo> list = annualPerformanceSummaryOrganizationProductMapper.queryProductPage(page, request);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (Objects.nonNull(item.getFullOrganizationId())) {
                    item.setOrgPath(Arrays.asList(item.getFullOrganizationId().split(",")));
                }
            });
        }
        page.setRecords(list);
        return page;
    }

    @Override
    public void downLoadProductPage(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadProductPage request:{}", request);
        check(request);

        List<YearProductVo> list = annualPerformanceSummaryOrganizationProductMapper.queryProductPage(null, request);

        if (request.getPositionTypeId() == 3) {
            if ("line".equals(request.getProductType())) {
                List<YearProductPartnerLineVo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductPartnerLineVo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "产品明细-线别", YearProductPartnerLineVo.class, excelList);
            } else if ("spu".equals(request.getProductType())) {
                List<YearProductPartnerSpuVo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductPartnerSpuVo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SPU", YearProductPartnerSpuVo.class, excelList);
            } else if ("sku".equals(request.getProductType())) {
                List<YearProductPartnerSkuVo> excelList = new ArrayList<>();
                BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductPartnerSkuVo.class);
                ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SKU", YearProductPartnerSkuVo.class, excelList);
            }
        } else {
            if (Objects.nonNull(request.getDownLoadType())) {
                if (request.getDownLoadType() == 0) {
                    if ("line".equals(request.getProductType())) {
                        List<YearProductLine0Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductLine0Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-线别", YearProductLine0Vo.class, excelList);
                    } else if ("spu".equals(request.getProductType())) {
                        List<YearProductSpu0Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductSpu0Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SPU", YearProductSpu0Vo.class, excelList);
                    } else if ("sku".equals(request.getProductType())) {
                        List<YearProductSku0Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductSku0Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SKU", YearProductSku0Vo.class, excelList);
                    }
                } else if (request.getDownLoadType() == 1) {
                    if ("line".equals(request.getProductType())) {
                        List<YearProductLine1Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductLine1Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-线别-天数倒数", YearProductLine1Vo.class, excelList);
                    } else if ("spu".equals(request.getProductType())) {
                        List<YearProductSpu1Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductSpu1Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SPU-天数倒数", YearProductSpu1Vo.class, excelList);
                    } else if ("sku".equals(request.getProductType())) {
                        List<YearProductSku1Vo> excelList = new ArrayList<>();
                        BeanUtils.copyProperties(list, excelList, YearProductVo.class, YearProductSku1Vo.class);
                        ExportUtil.writeEasyExcelResponse(res, req, "产品明细-SKU-天数倒数", YearProductSku1Vo.class, excelList);
                    }
                }
            } else {
                ExportUtil.writeEasyExcelResponse(res, req, "产品明细", YearProductVo.class, list);
            }
        }
    }

    @Override
    public List<YearCustomerVo> queryCustomerList(YearFestivalRequest request) {
        log.info("queryCustomerList request:{}", request);
        check(request);
        // 下级
        List<YearCustomerVo> list = annualPerformanceCustomerDetailMapper.queryCustomerList(request);

        return list;
    }

    @Override
    public IPage<YearCustomerVo> queryCustomerListPage(YearFestivalRequest request) {
        log.info("queryCustomerListPage request:{}", request);
        check(request);

        Page<YearCustomerVo> page = new Page<>(request.getPage(), request.getRows());
        List<YearCustomerVo> list = annualPerformanceCustomerDetailMapper.queryCustomerListPage(page, request);

        page.setRecords(list);
        return page;
    }

    @Override
    public void downLoadCustomerList(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadCustomerList request:{}", request);
        check(request);
        List<YearCustomerVo> list = new ArrayList<>();
//        processCustomerList(request, list);// 有点慢，暂废弃

        List<YearCustomerVo> resultList = annualPerformanceCustomerDetailMapper.queryCustomerList(request);

        if (CollectionUtil.isNotEmpty(resultList)) {
            Map<String, List<YearCustomerVo>> resultMap = resultList.stream().collect(Collectors.groupingBy(YearCustomerVo::getCustomerType));
            List<YearCustomerVo> partnerAllList = resultMap.get("合伙人");
            List<YearCustomerVo> customerAllList = resultMap.get("建档客户");
            Map<Long, List<YearCustomerVo>> customerAllMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(customerAllList)) {
                customerAllMap = customerAllList.stream().collect(Collectors.groupingBy(YearCustomerVo::getMemberKey));
            }
            List<YearCustomerVo> receiverAllList = resultMap.get("收货客户");
            Map<String, List<YearCustomerVo>> receiverAllMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(receiverAllList)) {
                receiverAllMap = receiverAllList.stream().collect(Collectors.groupingBy(x -> x.getMemberKey() + "#" + x.getCustomerId()));
            }
            // 合伙人
            if (CollectionUtil.isNotEmpty(partnerAllList)) {
                Map<Long, List<YearCustomerVo>> finalCustomerAllMap = customerAllMap;
                Map<String, List<YearCustomerVo>> finalReceiverAllMap = receiverAllMap;
                partnerAllList.forEach(partnerItem -> {
                    list.add(partnerItem);
                    if (Objects.nonNull(partnerItem) && Objects.nonNull(partnerItem.getMemberKey())) {
                        // 建档客户
                        List<YearCustomerVo> customerList = finalCustomerAllMap.get(partnerItem.getMemberKey());
                        if (CollectionUtil.isNotEmpty(customerList)) {
                            customerList.forEach(customerItem -> {
                                list.add(customerItem);
                                if (Objects.nonNull(customerItem) && Objects.nonNull(customerItem.getCustomerId())) {
                                    // 收货客户
                                    List<YearCustomerVo> receiverList = finalReceiverAllMap.get(partnerItem.getMemberKey() + "#" + customerItem.getCustomerId());
                                    if (CollectionUtil.isNotEmpty(receiverList)) {
                                        list.addAll(receiverList);
                                    }
                                }
                            });
                        }
                    }
                });
            }
        }

        if (CollectionUtil.isNotEmpty(list)) {
            Map<Long, String> positionTypeMap = ceoBusinessPositionTypeMapper.selectList(new LambdaQueryWrapper<CeoBusinessPositionType>())
                    .stream().collect(Collectors.toMap(CeoBusinessPositionType::getId, CeoBusinessPositionType::getPositionName));
            Map<Integer, String> businessGroupMap = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>())
                    .stream().collect(Collectors.toMap(SfaBusinessGroupEntity::getId, SfaBusinessGroupEntity::getBusinessGroupName));

            list.forEach(item -> {
                item.setPositionTypeName(positionTypeMap.get(item.getPositionTypeId()));
                item.setBusinessGroupName(businessGroupMap.get(item.getBusinessGroupId()));
            });
        }
        if (RequestUtils.getBusinessGroup() != 99) {
            List<YearCustomerOrgVo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerOrgVo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "组织交易客户明细-下拉模式", YearCustomerOrgVo.class, excelList);
        } else {
            List<YearCustomerOrg99Vo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerOrg99Vo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "组织交易客户明细-全组-下拉模式", YearCustomerOrg99Vo.class, excelList);
        }
    }

    public void processCustomerList(YearFestivalRequest request, List<YearCustomerVo> list) {
        // 合伙人
        List<YearCustomerVo> partnerList = annualPerformanceCustomerDetailMapper.queryCustomerList(request);
        if (CollectionUtil.isNotEmpty(partnerList)) {
            partnerList.forEach(partnerItem -> {
                list.add(partnerItem);
                if (Objects.nonNull(partnerItem) && Objects.nonNull(partnerItem.getMemberKey())) {
                    request.setMemberKey(partnerItem.getMemberKey());
                    request.setCustomerId(null);
                    request.setCustomerType("建档客户");
                    // 建档客户
                    List<YearCustomerVo> customerList = annualPerformanceCustomerDetailMapper.queryCustomerList(request);
                    if (CollectionUtil.isNotEmpty(customerList)) {
                        customerList.forEach(customerItem -> {
                            list.add(customerItem);
                            if (Objects.nonNull(customerItem) && Objects.nonNull(customerItem.getCustomerId())) {
                                request.setCustomerId(customerItem.getCustomerId());
                                request.setCustomerType("收货客户");
                                // 收货客户
                                List<YearCustomerVo> receiverList = annualPerformanceCustomerDetailMapper.queryCustomerList(request);
                                if (CollectionUtil.isNotEmpty(receiverList)) {
                                    list.addAll(receiverList);
                                }
                            }
                        });
                    }
                }
            });
        }
    }

    @Override
    public IPage<YearCustomerVo> queryCustomerPage(YearFestivalRequest request) {
        log.info("queryCustomerPage request:{}", request);
        check(request);

        Page<YearCustomerVo> page = new Page<>(request.getPage(), request.getRows());
        List<YearCustomerVo> list = annualPerformanceCustomerDetailMapper.queryCustomerPage(page, request);

        page.setRecords(list);
        return page;
    }

    @Override
    public void downLoadCustomerPage(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadCustomerPage request:{}", request);
        check(request);

        List<YearCustomerVo> list = annualPerformanceCustomerDetailMapper.queryCustomerPage(null, request);

        if (RequestUtils.getBusinessGroup() != 99) {
            List<YearCustomerOrgVo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerOrgVo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "组织交易客户明细-平铺模式", YearCustomerOrgVo.class, excelList);
        } else {
            List<YearCustomerOrg99Vo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerOrg99Vo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "组织交易客户明细-全组-平铺模式", YearCustomerOrg99Vo.class, excelList);
        }
    }

    @Override
    public IPage<YearCustomerVo> queryProductCustomerPage(YearFestivalRequest request) {
        log.info("queryProductCustomerPage request:{}", request);
        check(request);

        Page<YearCustomerVo> page = new Page<>(request.getPage(), request.getRows());
        List<YearCustomerVo> list = annualPerformanceCustomerDetailProductMapper.queryProductCustomerPage(page, request);

        page.setRecords(list);
        return page;
    }

    @Override
    public void downLoadProductCustomerPage(YearFestivalRequest request, HttpServletRequest req, HttpServletResponse res) {
        log.info("downLoadProductCustomerPage request:{}", request);
        check(request);

        List<YearCustomerVo> list = annualPerformanceCustomerDetailProductMapper.queryProductCustomerPage(null, request);

        if ("line".equals(request.getProductType())) {
            List<YearCustomerProductLineVo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerProductLineVo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "产品交易客户明细-线别", YearCustomerProductLineVo.class, excelList);
        } else if ("spu".equals(request.getProductType())) {
            List<YearCustomerProductSpuVo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerProductSpuVo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "产品交易客户明细-SPU", YearCustomerProductSpuVo.class, excelList);
        } else if ("sku".equals(request.getProductType())) {
            List<YearCustomerProductSkuVo> excelList = new ArrayList<>();
            BeanUtils.copyProperties(list, excelList, YearCustomerVo.class, YearCustomerProductSkuVo.class);
            ExportUtil.writeEasyExcelResponse(res, req, "产品交易客户明细-SKU", YearCustomerProductSkuVo.class, excelList);
        }
    }

    @Override
    public List<YearProductOptions> productOptions(YearFestivalRequest request) {
        log.info("productOptions request:{}", request);
        request.setPersonBusinessGroup(RequestUtils.getBusinessGroup());
//        check(request);
        return annualPerformanceCustomerDetailProductMapper.productOptions(request);
    }

}
