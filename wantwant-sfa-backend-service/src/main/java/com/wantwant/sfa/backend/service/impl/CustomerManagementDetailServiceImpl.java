package com.wantwant.sfa.backend.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.wantwant.commons.core.util.Assert;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.common.DictCodeConstants;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.customer.dto.CustomerLastVisitDataInfoDTO;
import com.wantwant.sfa.backend.customer.dto.PerformanceComparisonDTO;
import com.wantwant.sfa.backend.customer.dto.ProductAnalysisDTO;
import com.wantwant.sfa.backend.customer.dto.ProductPerformanceResult;
import com.wantwant.sfa.backend.customer.enums.DateTypeEnum;
import com.wantwant.sfa.backend.customer.request.*;
import com.wantwant.sfa.backend.customer.service.ICustomerAnalyseService;
import com.wantwant.sfa.backend.customer.vo.ParentMessageVo;
import com.wantwant.sfa.backend.dict.service.impl.DictCodeServiceImpl;
import com.wantwant.sfa.backend.entity.SfaCustomerTransferApply;
import com.wantwant.sfa.backend.entity.SfaCustomerTransferVerify;
import com.wantwant.sfa.backend.entity.SfaCustomerTransferVerifyDetail;
import com.wantwant.sfa.backend.entity.SfaMonthTarget;
import com.wantwant.sfa.backend.enums.ComonLanguageEnum;
import com.wantwant.sfa.backend.enums.ShopChannelTypeEnums;
import com.wantwant.sfa.backend.enums.ShopSalesRoomTypeEnums;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.positionRelation.PositionRegionMapper;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.CustomerManagementCallBackFileInfo;
import com.wantwant.sfa.backend.model.CustomerManagementCallBackInfo;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.service.CustomerManagementDetailService;
import com.wantwant.sfa.backend.util.*;
import com.wantwant.sfa.backend.vo.*;
import com.wantwant.sfa.backend.zw.vo.PartnerInfoVo;
import com.wantwant.sfa.common.base.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.jsoup.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date on 2022/2/19
 */
@Service
@Slf4j
public class CustomerManagementDetailServiceImpl implements CustomerManagementDetailService {

    @Autowired
    private DictCodeServiceImpl dictCodeServiceImpl;
    @Resource
    private CustomerManagermentDetailMapper customerManagermentDetailMapper;
    @Resource
    private CustomerInfoMapper customerInfoMapper;
    @Resource
    private CustomerInformationMapper customerInformationMapper;

    @Resource
    SfaCustomerMapper sfaCustomerMapper;

    @Autowired
    private ROOTConnectorUtil rootConnectorUtil;

    @Autowired
    private SfaCustomerTransferApplyMapper sfaCustomerTransferApplyMapper;

    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;

    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;

    @Autowired
    private SfaCustomerTransferVerifyMapper sfaCustomerTransferVerifyMapper;

    @Autowired
    private SfaCustomerTransferVerifyDetailMapper sfaCustomerTransferVerifyDetailMapper;

    @Autowired
    private SettingsMapper settingsMapper;
    @Autowired
    private SfaMonthTargetMapper sfaMonthTargetMapper;

    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;

    @Autowired
    private IAuditService iAuditService;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Resource
    private CustomerManagementCallBackInfoMapper customerManagementCallBackInfoMapper;
    @Resource
    private CustomerManagementCallBackFileInfoMapper customerManagementCallBackFileInfoMapper;

    private final static String PRE_CALLBACK_ADDITIONAL_EXPORT = "追加";

    @Resource
    private RealTimeUtils realTimeUtils;

    @Resource
    private ICustomerAnalyseService customerAnalyseService;
    @Autowired
    private CustomerVisitInfoMapper customerVisitInfoMapper;
    @Autowired
    private PositionRegionMapper positionRegionMapper;

    /**
     * 客户列表查询
     */
    @Override
    public Page<CustomerManagementDetailVO> queryCustomerManagementList(CustomerManagementDetailRequest request) {
        log.info("queryCustomerManagementList {}", request);
        log.info("loginModel {}", RequestUtils.getLoginInfo());
        ExceptionHelper.checkAndThrow(Objects.isNull(request), "必传参数不能为空");
        List<CeoBusinessOrganizationPositionRelation> relations = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(request.getEmployeeId(), RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
        if (relations == null || relations.size() == 0) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        if (ObjectUtils.isNull(request.getOrganizationId()) && relations.get(0).getPositionTypeId() != 7) {
            // 业务bd 使用 区域经理的 code
            if (RequestUtils.getLoginInfo().getPositionTypeId() == 3) {
                List<SfaPositionRelationEntity> relationEntities = sfaPositionRelationMapper.selectList(new LambdaQueryWrapper<SfaPositionRelationEntity>()
                        .eq(SfaPositionRelationEntity::getEmpId, request.getEmployeeId())
                        .eq(SfaPositionRelationEntity::getBusinessGroup, RequestUtils.getBusinessGroup())
                        .eq(SfaPositionRelationEntity::getPositionTypeId, 3)
                        .eq(SfaPositionRelationEntity::getDeleteFlag, 0)
                        .eq(SfaPositionRelationEntity::getStatus, 1));
                if (CollectionUtil.isNotEmpty(relationEntities)) {
                    request.setOrganizationIdList(relationEntities.stream().map(SfaPositionRelationEntity::getDepartmentCode).collect(Collectors.toList()));
                }
            } else {
                request.setOrganizationIdList(relations.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList()));
            }

            request.setPositionTypeId(RequestUtils.getLoginInfo().getPositionTypeId());
        }
        request.setBusinessGroupCode(RequestUtils.getBusinessGroup());
        int pageNum = request.getPageNum();
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        //开启分页
        request.setPageFlag(1);

        String organizationId = request.getOrganizationId();
        if (!StringUtil.isBlank(organizationId) && organizationId.contains("ZB")) {
            request.setOrganizationId(null);
        }

        //回访客户查询限制
        if (Objects.nonNull(request.getCallbackType()) || Objects.nonNull(request.getCallbackLimit())) {
            List<String> customerIdList = customerManagementCallBackInfoMapper.queryCustomerIdList(request.getCallbackType(), request.getCallbackLimit());
            request.setCallBackCustomerIds(customerIdList);
        }

        List<CustomerManagementDetailVO> customerManagementDetailVOS = customerManagermentDetailMapper.queryCustomerManagementDetail(request);
        // 最近一次拜访时间
        if (CollectionUtil.isNotEmpty(customerManagementDetailVOS)) {
            List<CustomerLastVisitDataInfoDTO> lastVisitDataInfoDTOS = customerVisitInfoMapper.queryMemberLastVisitByMemberKey(customerManagementDetailVOS.stream().map(CustomerManagementDetailVO::getCustomerId).collect(Collectors.toSet()));
            Map<String, LocalDateTime> localDateTimeMap = lastVisitDataInfoDTOS.stream().collect(Collectors.toMap(CustomerLastVisitDataInfoDTO::getMemberKey, CustomerLastVisitDataInfoDTO::getLastVisitTime, (v1, v2) -> v1));
            customerManagementDetailVOS.forEach(customerManagementDetailVO -> {
                LocalDateTime localDateTime = localDateTimeMap.get(customerManagementDetailVO.getCustomerId());
                if (Objects.nonNull(localDateTime)) {
                    customerManagementDetailVO.setRecentVisitTime(localDateTime.toString());
                }
            });
        }
        Integer total = customerManagermentDetailMapper.queryCustomerManagementDetailCounts(request);
        Page<CustomerManagementDetailVO> result = Page.of(total, pageNum, request.getPageSize());
        result.setList(customerManagementDetailVOS);
        return result;
    }

    /**
     * 客户列表导出
     */
    @Override
    public void exportList(CustomerManagementDetailRequest req, HttpServletRequest request, HttpServletResponse response) {
        ExceptionHelper.checkAndThrow(Objects.isNull(req), "必要参数不能为空!");
        //移除分页
        req.setPageFlag(null);
        List<CeoBusinessOrganizationPositionRelation> relations = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(req.getEmployeeId(), RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
        if (relations == null || relations.size() == 0) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        List<String> organizationIds = relations.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
        if (ObjectUtils.isNull(req.getOrganizationId()) && relations.get(0).getPositionTypeId() != 7) {
            req.setOrganizationIdList(organizationIds);
            req.setPositionTypeId(RequestUtils.getLoginInfo().getPositionTypeId());
        }
        req.setBusinessGroupCode(RequestUtils.getBusinessGroup());
        List<CustomerManagementDetailVO> customerManagementDetailVOS = customerManagermentDetailMapper.queryCustomerManagementDetail(req);
        ExceptionHelper.checkAndThrow(CollectionUtils.isEmpty(customerManagementDetailVOS), "当前暂无数据可导出!");
        String fileName = UUID.randomUUID().toString();
        ExportUtil.writeEasyExcelResponse(response, request, "客户列表" + fileName, CustomerManagementDetailVO.class, customerManagementDetailVOS);
    }

    /**
     * 客户列表导出
     */
    @Override
    public void exportAttachCallBackInfo(CustomerManagementDetailRequest req, HttpServletRequest request, HttpServletResponse response) {
        ExceptionHelper.checkAndThrow(Objects.isNull(req), "必要参数不能为空!");
        //移除分页
        req.setPageFlag(null);
        List<CeoBusinessOrganizationPositionRelation> relations = ceoBusinessOrganizationPositionRelationMapper.selectByEmployeeIdByLine(req.getEmployeeId(), RequestUtils.getBusinessGroup(), RequestUtils.getLoginInfo().getPositionTypeId());
        if (relations == null || relations.size() == 0) {
            throw new ApplicationException("操作人岗位查询失败");
        }
        List<String> organizationIds = relations.stream().map(CeoBusinessOrganizationPositionRelation::getOrganizationId).collect(Collectors.toList());
        if (ObjectUtils.isNull(req.getOrganizationId()) && relations.get(0).getPositionTypeId() != 7) {
            req.setOrganizationIdList(organizationIds);
            req.setPositionTypeId(RequestUtils.getLoginInfo().getPositionTypeId());
        }
        req.setBusinessGroupCode(RequestUtils.getBusinessGroup());
        //回访客户查询限制
        if (Objects.nonNull(req.getCallbackType()) || Objects.nonNull(req.getCallbackLimit())) {
            List<String> customerIdList = customerManagementCallBackInfoMapper.queryCustomerIdList(req.getCallbackType(), req.getCallbackLimit());
            req.setCallBackCustomerIds(customerIdList);
        }
        List<CustomerManagementDetailVO> customerManagementDetailVOS = customerManagermentDetailMapper.queryCustomerManagementDetail(req);
        ExceptionHelper.checkAndThrow(CollectionUtils.isEmpty(customerManagementDetailVOS), "当前暂无数据可导出!");
        String fileName = UUID.randomUUID().toString();

        //最终使用的VO
        List<CustomerManagementDetailAttachCallBackInfoVO> attachCallBackInfoVOS = new ArrayList<>();
        //根据已有的客户查询回访信息
        List<String> customerIdList = customerManagementDetailVOS.stream().map(CustomerManagementDetailVO::getCustomerId).collect(Collectors.toList());
        List<String> existCustomerIdList = customerManagementCallBackInfoMapper.queryExistCustomerIdList(customerIdList);
        if (!CollectionUtils.isEmpty(existCustomerIdList)) {

            customerManagementDetailVOS.forEach(customer -> {
                CustomerManagementDetailAttachCallBackInfoVO backInfoVO = BeanUtil.copyProperties(customer, CustomerManagementDetailAttachCallBackInfoVO.class);
                //存在即封装回访信息
                if (existCustomerIdList.contains(customer.getCustomerId())) {
                    CustomerManagementQueryCallBackInfoReq callBackInfoReq = new CustomerManagementQueryCallBackInfoReq();
                    callBackInfoReq.setCustomerId(customer.getCustomerId());
                    List<CustomerManagementQueryCallBackInfoVO> queryCallBackInfoVOS = this.queryCallBackList(callBackInfoReq);
                    queryCallBackInfoVOS.forEach(callBackInfo -> {
                        //回访信息
                        CustomerManagementDetailAttachCallBackInfoVO infoVO = BeanUtil.copyProperties(backInfoVO, CustomerManagementDetailAttachCallBackInfoVO.class);
                        infoVO.setCallbackType(callBackInfo.getCallBackTypeDesc());
                        infoVO.setCallbackDate(DateUtil.format(callBackInfo.getCallBackDate(), LocalDateTimeUtils.yyyy_MM_dd));
                        infoVO.setRecordInfo(callBackInfo.getRecordInfo());
                        infoVO.setCallbackEmployeeInfo(callBackInfo.getEmployeeName() + "(" + callBackInfo.getEmployeeId() + ")");
                        infoVO.setRecordCreateTime(DateUtil.format(callBackInfo.getCreateTime(), LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
                        //追踪信息拼接
                        if (!CollectionUtils.isEmpty(callBackInfo.getSubInfoList())) {
                            StringBuilder stringBuilder = new StringBuilder();
                            for (int i = 1; i <= callBackInfo.getSubInfoList().size(); i++) {
                                CustomerManagementQueryCallBackInfoSubVO subInfo = callBackInfo.getSubInfoList().get(i - 1);
                                stringBuilder.append(PRE_CALLBACK_ADDITIONAL_EXPORT + i + ":");
                                //跟进人 跟进时间 跟进情况
                                stringBuilder.append(org.apache.commons.lang3.StringUtils
                                        .join(Arrays.asList(subInfo.getEmployeeName() + "(" + subInfo.getEmployeeId() + ")",
                                                DateUtil.format(subInfo.getCallBackDate(), LocalDateTimeUtils.yyyy_MM_dd),
                                                subInfo.getRecordInfo()
                                        ), "/"));
                                stringBuilder.append(";");
                            }
                            infoVO.setAttachInfo(stringBuilder.toString());
                        }
                        attachCallBackInfoVOS.add(infoVO);
                    });

                } else {
                    //不存在就封装原始信息
                    attachCallBackInfoVOS.add(backInfoVO);
                }

            });
            ExportUtil.writeEasyExcelResponse(response, request, "客户列表" + fileName, CustomerManagementDetailAttachCallBackInfoVO.class, attachCallBackInfoVOS);
        } else {
            BeanUtils.copyProperties(customerManagementDetailVOS, attachCallBackInfoVOS, CustomerManagementDetailVO.class, CustomerManagementDetailAttachCallBackInfoVO.class);
            ExportUtil.writeEasyExcelResponse(response, request, "客户列表" + fileName, CustomerManagementDetailAttachCallBackInfoVO.class, attachCallBackInfoVOS);
        }


    }


    /**
     * 补充权限信息 支持兼岗并获取岗位类型
     *
     * @param request
     * @param organizationInfoList
     */
    private void completePermissionInfoNew(CustomerManagementDetailRequest request, List<OrganizationViewInfo> organizationInfoList) {
        ExceptionHelper.checkAndThrow(Objects.isNull(organizationInfoList), "身份异常!根据当前登陆者获取组织信息失败！");
        List<String> organizationNameList = new ArrayList<>();
        for (OrganizationViewInfo organizationInfo : organizationInfoList) {
            if (!StringUtils.isEmpty(organizationInfo.getDepartmentOfficeId())) {
                organizationNameList.add(organizationInfo.getDepartmentOfficeId());
            } else if (!StringUtils.isEmpty(organizationInfo.getBranchOfficeId())) {
                organizationNameList.add(organizationInfo.getBranchOfficeId());
            } else if (!StringUtils.isEmpty(organizationInfo.getVirtualAreaId())) {
                organizationNameList.add(organizationInfo.getVirtualAreaId());
            } else if (!StringUtils.isEmpty(organizationInfo.getProvinceId())) {
                organizationNameList.add(organizationInfo.getProvinceId());
            } else if (!StringUtils.isEmpty(organizationInfo.getRegionOfficeId()) && !organizationInfo.getRegionOfficeId().contains("ZB")) {
                organizationNameList.add(organizationInfo.getRegionOfficeId());
            }
        }
//        request.setOrganizationNameList(organizationNameList);
    }


    /**
     * 处理金额数据
     * 大于10000的显示N万
     */
    private String convertPriceForTenThousand(String price) {
        if (StringUtils.isEmpty(price)) {
            return "";
        }
        BigDecimal divide = new BigDecimal(price).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP);
        if (divide.compareTo(new BigDecimal(1)) < 0) {
            return price;
        } else {
            return divide + "万";
        }
    }

    /**
     * params:onlySaveProvinceCity是否只保留省市
     * 递归获取地区层级关系
     */
    private void completeRegionInfo(String regionCode, StringBuilder sb, boolean onlySaveProvinceCity) {
        if (Objects.isNull(sb)) {
            sb = new StringBuilder();
        }
        RegionVO parentInfo = customerInformationMapper.getParentCodeBy(regionCode);
        if (Objects.isNull(parentInfo) || parentInfo.getParentCode() == null) {
            return;
        }
        if (onlySaveProvinceCity == false || (parentInfo.getLevel() == 1 || parentInfo.getLevel() == 2)) {
            sb.insert(0, parentInfo.getRegionName() + "-");
        }
        this.completeRegionInfo(parentInfo.getParentCode(), sb, onlySaveProvinceCity);
    }


    /**
     * 查询客户订单列表
     */
    @Override
    public Page<CustomerPartnerOrderVO> queryCustomerOrderList(CustomerPartnerOrderRequest request) {
        ExceptionHelper.checkAndThrow(Objects.isNull(request), "必传参数不能为空");
        ExceptionHelper.checkAndThrow(StringUtils.isEmpty(request.getCustomerId()), "客户id不能为空");
        int pageNum = request.getPageNum();
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        List<CustomerPartnerOrderVO> customerPartnerOrderVOList = customerInfoMapper.queryOrderByCustomerId(request, RequestUtils.getBusinessGroup());
        Integer total = customerInfoMapper.queryOrderCountsByCustomerId(request, RequestUtils.getBusinessGroup());
        Page<CustomerPartnerOrderVO> result = Page.of(total, pageNum, request.getPageSize());
        if (ObjectUtils.isNotNull(customerPartnerOrderVOList)) {
            customerPartnerOrderVOList.forEach(x -> {
                x.setServiceFlag(StringUtils.isEmpty(x.getServiceAt()) ? 0 : 1);
                BigDecimal selPrice = x.getGrandTotal().subtract(x.getProductCommissionAmount());
                BigDecimal ratio = BigDecimal.ZERO;
                if (selPrice.compareTo(BigDecimal.ZERO) > 0) {
                    ratio = x.getProductCommissionAmount().divide(selPrice, 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN);
                }
                x.setProfitRatioStr(ratio.toString() + "%");

            });
        }
        result.setList(customerPartnerOrderVOList);
        return result;
    }


    /**
     * 客户对应的合伙人负责的行政区域
     */
    public PartnerInfoVo getPartnerInfoByMemberKey(String partnerMemberKey) {
        PartnerInfoVo partnerInfoVo = sfaPositionRelationMapper.selectByMemberKey(partnerMemberKey);
        List<String> list = customerInformationMapper.getRegionListByMemberKeyNew(Long.valueOf(partnerMemberKey));
        if (!CollectionUtils.isEmpty(list)) {
            partnerInfoVo.setRegionList(list);
        }

        return partnerInfoVo;
    }

    /*
     * 根据合伙人的memberKey获取产品组信息
     */
    public String getPartnerProductionGroup(String partnerMemberKey) {
        log.info("getPartnerProductionGroup: {}", partnerMemberKey);
        String productionGroup = sfaCustomerMapper.selectProductionGroupByMemberKey(partnerMemberKey);
        return productionGroup;
    }

    /**
     * 客户转移
     */
    public Boolean transferCustomer(CustomerTransferRequest request) {
        return true;
//        return rootConnectorUtil.customerTransfer(request);
    }

    /**
     * 客户转移申请
     */
    @Transactional
    public void transferCustomerApply(CustomerTransferRequest request) {
        log.info("transferCustomerApply: {}", request);
        //客户信息是否被占用
        int count = sfaCustomerTransferApplyMapper.selectProcessCountByCustomerId(request.getCustomerId());
        if (count != 0) {
            throw new ApplicationException("该客户有未审批的记录");
        }
        //判断目标memberKey是否在职
        String targetEmpIdStr = sfaPositionRelationMapper.getEmpIdByMemberKey(request.getTargetMemberKey());
        if (ObjectUtils.isNull(targetEmpIdStr)) {
            throw new ApplicationException("目标合伙人在岗查询失败");
        }
        //原合伙人信息
        String oldEmpIdStr = sfaPositionRelationMapper.getEmpIdByMemberKey(request.getMemberKey());

        //当前提报人
        Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
        List<SfaPositionRelationEntity> relationEntityList = sfaPositionRelationMapper.selectPositionByEmpId(request.getPerson(), RequestUtils.getBusinessGroup(), positionTypeId);
        if (CollectionUtils.isEmpty(relationEntityList)) {
            throw new ApplicationException("身份查询失败");
        }
        SfaPositionRelationEntity relationEntity = relationEntityList.get(0);
        SfaEmployeeInfoModel infoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("id", relationEntity.getEmployeeInfoId()));
        if (Objects.isNull(relationEntity) || relationEntity.getPositionId() == null || Objects.isNull(infoModel)) {
            throw new ApplicationException("操作人工号不正确");
        }

        CeoBusinessOrganizationPositionRelation auditPerson = new CeoBusinessOrganizationPositionRelation();
        if (positionTypeId == 10) {
            ParentMessageVo parentMessage = customerInfoMapper.queryParentMessageAndGroup(String.valueOf(infoModel.getMemberKey()), RequestUtils.getBusinessGroup());
            //提报人上级
            SelectAuditDto selectAuditDto = new SelectAuditDto();
            selectAuditDto.setChannel(3);
            selectAuditDto.setBusinessGroup(parentMessage.getBusinessGroup());
            selectAuditDto.setCurrentOrganizationId(parentMessage.getOrganizationId());
            selectAuditDto.setStandbyEmployeeId(configMapper.getValueByCode("zw_hr_employee_id"));
            auditPerson = iAuditService.chooseAuditPerson(selectAuditDto);
            if (ObjectUtils.isEmpty(auditPerson)) {
                throw new ApplicationException("查询审核人员失败");
            }
        }
        //查看是否是运营
        String customerTransferAccount = settingsMapper.getSfaSettingsByCode("customer_transfer_audit");
        boolean operatorAudit = customerTransferAccount != null && customerTransferAccount.contains(request.getPerson()) ? true : false;
        //插入申请表
        SfaCustomerTransferApply sfaCustomerTransferApply = new SfaCustomerTransferApply();
        sfaCustomerTransferApply.setApplyTime(LocalDateTime.now());
        sfaCustomerTransferApply.setApplyPositionId(relationEntity.getOrganizationCode());
        sfaCustomerTransferApply.setCustomerId(request.getCustomerId());
        sfaCustomerTransferApply.setReason(request.getReason());
        if (ObjectUtils.isNotNull(targetEmpIdStr)) {
            sfaCustomerTransferApply.setEmployeeId(Integer.valueOf(targetEmpIdStr));
        }
        if (ObjectUtils.isNotNull(oldEmpIdStr)) {
            sfaCustomerTransferApply.setOldEmployeeId(Integer.valueOf(oldEmpIdStr));

        }
        sfaCustomerTransferApplyMapper.insert(sfaCustomerTransferApply);
        //插入流程表
        SfaCustomerTransferVerify sfaCustomerTransferVerify = new SfaCustomerTransferVerify();
        sfaCustomerTransferVerify.setRequestId(Long.valueOf(sfaCustomerTransferApply.getId()));
        // 只有区域经理及以上才能申请 区域经理找上级  区域经理以上直接通过
        if (positionTypeId == 10) {
            //区域经理 (发起)-> 任意上级(审核)-> 通过
            sfaCustomerTransferVerify.setProcessStep(4);
            sfaCustomerTransferVerify.setResult(0);//待审核
        } else if (positionTypeId == 1 || positionTypeId == 2 || positionTypeId == 11 || positionTypeId == 12) {
            //分公司 省区总监 大区 战区 直接通过
            sfaCustomerTransferVerify.setProcessStep(2);
            sfaCustomerTransferVerify.setResult(1);
            rootConnectorUtil.customerTransfer(request.getTargetMemberKey(), request.getCustomerId());
        } else if (operatorAudit) {
            //运营直接通过
            sfaCustomerTransferVerify.setProcessStep(2);
            sfaCustomerTransferVerify.setResult(1);
            rootConnectorUtil.customerTransfer(request.getTargetMemberKey(), request.getCustomerId());
        } else {
            throw new ApplicationException("岗位查询失败");
        }
        sfaCustomerTransferVerify.setCreateTime(LocalDateTime.now());
        sfaCustomerTransferVerifyMapper.insert(sfaCustomerTransferVerify);
        //插入记录表
        SfaCustomerTransferVerifyDetail sfaCustomerTransferVerifyDetail = new SfaCustomerTransferVerifyDetail();
        sfaCustomerTransferVerifyDetail.setVerifyId(Long.valueOf(sfaCustomerTransferVerify.getId()));
        if (positionTypeId == 10) {
            sfaCustomerTransferVerifyDetail.setAuditPositionId(auditPerson.getOrganizationId());//审核人组织id
            sfaCustomerTransferVerifyDetail.setAuditUserName(auditPerson.getEmployeeName());
        } else if (positionTypeId == 1 || positionTypeId == 2 || positionTypeId == 11 || positionTypeId == 12) {
            //分公司 省区总监 大区 战区 直接通过
            sfaCustomerTransferVerifyDetail.setAuditPositionId(relationEntity.getOrganizationCode());
            sfaCustomerTransferVerifyDetail.setAuditUserName(infoModel.getEmployeeName());
            sfaCustomerTransferVerifyDetail.setProcessTime(LocalDateTime.now());
        } else if (operatorAudit) {
            //运营直接通过
            sfaCustomerTransferVerifyDetail.setAuditPositionId(relationEntity.getOrganizationCode());
            sfaCustomerTransferVerifyDetail.setAuditUserName("运营");
            sfaCustomerTransferVerifyDetail.setProcessTime(LocalDateTime.now());
        } else {
            throw new ApplicationException("岗位查询失败");
        }
        sfaCustomerTransferVerifyDetail.setCreateTime(LocalDateTime.now());
        sfaCustomerTransferVerifyDetailMapper.insert(sfaCustomerTransferVerifyDetail);
        //更新插入流程表
        sfaCustomerTransferVerify.setDetailId(sfaCustomerTransferVerifyDetail.getId());
        sfaCustomerTransferVerifyMapper.updateById(sfaCustomerTransferVerify);
    }

    /**
     * 客户转移申请
     */
    @Transactional
    public void transferCustomerAudit(CustomerTransferAuditRequest request) {
        log.info("transferCustomerAudit: {}", request);
        //当前的流程审批节点
        SfaCustomerTransferVerify verify = sfaCustomerTransferVerifyMapper.selectOne(new QueryWrapper<SfaCustomerTransferVerify>().eq("result", 0).eq("request_id", request.getRequestId()));
        if (Objects.isNull(verify)) {
            throw new ApplicationException("待审核信息获取失败");
        }
        if (verify.getProcessStep() != 4) {
            throw new ApplicationException("审核步骤异常");
        }
        //审批详情
        SfaCustomerTransferVerifyDetail verifyDetail = sfaCustomerTransferVerifyDetailMapper.selectById(verify.getDetailId());
        if (Objects.isNull(verifyDetail)) {
            throw new ApplicationException("待审核记录获取失败");
        }
        //检查操作人信息
        Integer positionTypeId = RequestUtils.getLoginInfo().getPositionTypeId();
        List<SfaPositionRelationEntity> relationEntityList = sfaPositionRelationMapper.selectPositionByEmpId(request.getPerson(), RequestUtils.getBusinessGroup(), positionTypeId);
        if (CollectionUtils.isEmpty(relationEntityList)) {
            throw new ApplicationException("操作人工号不正确");
        }
        List<String> organizationList = relationEntityList.stream().map(SfaPositionRelationEntity::getOrganizationCode).collect(Collectors.toList());
        //更新当前审批记录
        verifyDetail.setProcessTime(LocalDateTime.now());
        verifyDetail.setReason(request.getReason());
        //更新流程表
        sfaCustomerTransferVerifyDetailMapper.updateById(verifyDetail);

        //区域总监及以上审核
        if (verify.getProcessStep() == 4 && request.getResult() == 1) {
            //区域总监审批，新增审批记录表，更新当前审批记录，且更新流程表
            if (organizationList.contains(verifyDetail.getAuditPositionId())) {
                verify.setProcessStep(2); //区域总监及以上审批 =>通过
                verify.setUpdateTime(LocalDateTime.now());
                verify.setResult(1);
                sfaCustomerTransferVerifyMapper.updateById(verify);
                Map<String, Object> map = sfaCustomerTransferApplyMapper.selectTargetMemberKeyByApplyId(verify.getRequestId());
                rootConnectorUtil.customerTransfer(map.get("memberKey").toString(), map.get("customerId").toString());
            } else {
                throw new ApplicationException("审批人与操作人不符");
            }
        } else {//驳回流程走以下流程
            verify.setProcessStep(2); //区域总监及以上审批 =>通过
            verify.setUpdateTime(LocalDateTime.now());
            verify.setResult(2);
            sfaCustomerTransferVerifyMapper.updateById(verify);
        }

    }

    /*
     *
     * 根据客户id，查询是否有待审核的客户记录
     * 能够查询，则说明存在待审核的记录
     */
    @Override
    public String customerCanTransfer(String customerId) {
        int count = sfaCustomerTransferApplyMapper.getPendingAuditCountByCustomerId(customerId);
        return count > 0 ? "0" : "1";
    }


    @Override
    public List<CustomerTypeVO> queryCustomerType() {
        List<CustomerTypeVO> vo = customerInfoMapper.queryCustomerType();
        for (CustomerTypeVO customerTypeVO : vo) {
            if (customerTypeVO.getCustomerType() == 0) {
                //终端去查询二级分类
                List<ShopChannelType> shopChannelTypes = ShopChannelTypeEnums.getAllShopChannelTypes();
                customerTypeVO.setNextCustomerType(shopChannelTypes);
            }
        }
        return vo;
    }

    @Override
    public void monthTarget(MonthTargetRequest request) {
        SfaMonthTarget monthTargetVo = new SfaMonthTarget();
        BeanUtils.copyProperties(request, monthTargetVo);
        sfaMonthTargetMapper.insert(monthTargetVo);
    }

    @Override
    public BasicInfoVO customerManageBasicInfo(CustomerPartnerInfoRequest request) {
        BasicInfoVO basicInfoVO = null;
        log.info("customerManageBasicInfo request:{}", request);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        basicInfoVO = customerManagermentDetailMapper.queryCustomerManageBasicInfo(request);
        if (ObjectUtils.isEmpty(basicInfoVO)) {
            throw new ApplicationException("查询客户基础信息失败");
        }
        basicInfoVO.setChannelTypeStr(ShopChannelTypeEnums.getShopChannelTypeDescByCode(basicInfoVO.getChannelType()));
        basicInfoVO.setSalesRoomTypeStr(ShopSalesRoomTypeEnums.getSaleRoomTypeDesc(basicInfoVO.getSalesRoomType()));

        HashMap<String, String> map = new HashMap<>();
        map.put("A", "A类(1000m²以上)");
        map.put("B", "B类(500m²-1000m²)");
        map.put("C", "C类(200m²-500m²)");
        map.put("D", "D类(100m²-200m²)");
        map.put("E", "E类(100m²以下)");

        basicInfoVO.setStoreSize(MapUtil.getStr(map, basicInfoVO.getStoreSize(),basicInfoVO.getStoreSize()));
        //查询经销品类
        if(!StringUtils.isEmpty(basicInfoVO.getSaleCategory())){
            List<String> saleCategoryCodes = Arrays.asList(basicInfoVO.getSaleCategory().split(","));
            basicInfoVO.setSaleCategory(customerInfoMapper.querySaleCategoryTypeDesc(saleCategoryCodes));
        }

        //经销产品组
        String businessGroup = customerManagermentDetailMapper.queryCustomerManagementBusinessGroup(request.getCustomerId());
        basicInfoVO.setBusinessGroupStr(businessGroup);

//        List<UpAndDownCustomerVO> upAndDownCustomers = new ArrayList<>();
//        if(basicInfoVO.getTypeId() == 0){
//            //客户 上级客户
//            upAndDownCustomers =customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(),request.getBusinessGroup(),0,basicInfoVO.getDepartmentCode());
//            //所属人员
//            if(ObjectUtils.isNotNull(upAndDownCustomers) && upAndDownCustomers.size() > 0 ){
//                log.info("upAndDownCustomers:{}",upAndDownCustomers);
//                basicInfoVO.setBelongToPerson(upAndDownCustomers.stream().filter(u->!Objects.isNull(u)).map(UpAndDownCustomerVO::getCustomerName).collect(Collectors.toList()).toString());
//            }
//        }else {
//            //合伙人 下级客户
//            upAndDownCustomers =customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(),request.getBusinessGroup(),1,basicInfoVO.getDepartmentCode());
//            basicInfoVO.setUpAndDownCustomerCount(customerManagermentDetailMapper.queryUpAndDownCustomerCount(basicInfoVO.getCustomerId(),request.getBusinessGroup(),basicInfoVO.getDepartmentCode()));
//            basicInfoVO.setBelongToPerson("-");
//        }
//        if(ObjectUtils.isNotNull(upAndDownCustomers) && upAndDownCustomers.size() > 0){
//            basicInfoVO.setUpAndDownCustomer(upAndDownCustomers);
//        }


        //累计业绩  订单总利润
        BasicInfoVO copyVo = customerManagermentDetailMapper.queryTotalPerformance(request);
        basicInfoVO.setAccumulatePerformance(copyVo.getAccumulatePerformance());
        basicInfoVO.setOrderTotalProfit(copyVo.getOrderTotalProfit());
        // 月平均业绩
        basicInfoVO.setSameMonthAvg(customerManagermentDetailMapper.queryTheYearMonAvg(request));

        if (CommonConstant.LANGUAGE_ENGLISH.equals(RequestUtils.getLanguage())) {
            basicInfoVO.setOpenType(ComonLanguageEnum.getDescByEnv(basicInfoVO.getOpenType()));
            basicInfoVO.setCustomerType(ComonLanguageEnum.getDescByEnv(basicInfoVO.getCustomerType()));
            basicInfoVO.setCustomerLevel(ComonLanguageEnum.getDescByEnv(basicInfoVO.getCustomerLevel()));
        }


        return basicInfoVO;
    }

    @Override
    public List<BasicPerformanceVO> customerManageBasicPerformance(CustomerPartnerInfoRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<BasicPerformanceVO> list = customerManagermentDetailMapper.queryBasicPerformanceById(request);
        return list;
    }

    @Override
    public Page<CustomerManagementDetailVO> queryNextCustomers(CustomerManagementDetailRequest request) {
        request.setBusinessGroupCode(RequestUtils.getBusinessGroup());
        Page<CustomerManagementDetailVO> result = new Page();
        request.setPageFlag(1);
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        CustomerPartnerInfoRequest infoRequest = new CustomerPartnerInfoRequest();
        infoRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        infoRequest.setCustomerId(request.getCustomerInfo());
        BasicInfoVO basicInfoVO = customerManagermentDetailMapper.queryCustomerDetail(infoRequest);
        // 0 客户找上级  1 合伙人找下级
        List<UpAndDownCustomerVO> upAndDownCustomers = new ArrayList<>();
        if (basicInfoVO.getTypeId() == 0) {
            upAndDownCustomers = customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(), RequestUtils.getBusinessGroup(), 0, basicInfoVO.getDepartmentCode());
        } else {
            upAndDownCustomers = customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(), RequestUtils.getBusinessGroup(), 2, basicInfoVO.getDepartmentCode());
        }
        if (ObjectUtils.isNotNull(upAndDownCustomers) && upAndDownCustomers.size() > 0) {
            List<String> ids = upAndDownCustomers.stream().filter(u -> !Objects.isNull(u)).map(UpAndDownCustomerVO::getCustomerId).collect(Collectors.toList());
            request.setCustomerIds(ids);
            request.setCustomerInfo(null);
            request.setDepartmentId(basicInfoVO.getDepartmentCode());
            List<CustomerManagementDetailVO> customerManagementDetailVOS = customerManagermentDetailMapper.queryCustomerManagementDetail(request);
            Integer count = customerManagermentDetailMapper.queryCustomerManagementDetailCounts(request);
            result.setList(customerManagementDetailVOS);
            result.setTotalItem(count);
        }
        return result;
    }

    @Override
    public Page<TransactionArticleVO> queryTransactionArticle(TransactionArticleRequest request) {
        Page<TransactionArticleVO> result = new Page<>();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        List<TransactionArticleVO> list = customerManagermentDetailMapper.queryTransactionArticle(request);
        int total = customerManagermentDetailMapper.queryTransactionArticleCount(request);
        result.setList(list);
        result.setTotalPage(total);
        return result;
    }

    @Override
    public void customerManageBasicPerformanceExport(CustomerPartnerInfoRequest request) {
        log.info("start customerManageBasicPerformanceExport request:{}", request);
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<BasicPerformanceVO> list = customerManagermentDetailMapper.queryBasicPerformanceById(request);
        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook xssfWorkbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), BasicPerformanceVO.class, list);
        try {
            String fileName = "客户业绩数据导出";
            fileName = fileName + ".xls";
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @Override
    public void queryNextCustomersExport(CustomerManagementDetailRequest request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        CustomerPartnerInfoRequest infoRequest = new CustomerPartnerInfoRequest();
        infoRequest.setBusinessGroup(RequestUtils.getBusinessGroup());
        infoRequest.setCustomerId(request.getCustomerInfo());
        BasicInfoVO basicInfoVO = customerManagermentDetailMapper.queryCustomerDetail(infoRequest);
        List<UpAndDownCustomerVO> upAndDownCustomers = new ArrayList<>();
        List<CustomerManagementDetailVO> customerManagementDetailVOS = new ArrayList<>();
        if (basicInfoVO.getTypeId() == 0) {
            upAndDownCustomers = customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(), RequestUtils.getBusinessGroup(), 0, basicInfoVO.getDepartmentCode());
        } else {
            upAndDownCustomers = customerManagermentDetailMapper.queryUpAndDownCustomer(basicInfoVO.getCustomerId(), RequestUtils.getBusinessGroup(), 2, basicInfoVO.getDepartmentCode());
        }
        if (ObjectUtils.isNotNull(upAndDownCustomers) && upAndDownCustomers.size() > 0) {
            List<String> ids = upAndDownCustomers.stream().filter(u -> !Objects.isNull(u)).map(UpAndDownCustomerVO::getCustomerId).collect(Collectors.toList());
            request.setCustomerIds(ids);
            request.setCustomerInfo(null);
            customerManagementDetailVOS = customerManagermentDetailMapper.queryCustomerManagementDetail(request);
        }
        if (ObjectUtils.isNull(customerManagementDetailVOS)) {
            throw new ApplicationException("下级客户为空");
        }

    }

    @Override
    public void queryTransactionArticleExport(TransactionArticleRequest request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        request.setPageNum(0);
        request.setPageSize(9999);
        List<TransactionArticleVO> list = customerManagermentDetailMapper.queryTransactionArticle(request);

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook xssfWorkbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), TransactionArticleVO.class, list);
        try {
            String fileName = "客户业绩数据导出";
            fileName = fileName + ".xls";
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public void queryOrderListExport(CustomerPartnerOrderRequest request) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "系统错误！！！！");
        HttpServletResponse response = servletRequestAttributes.getResponse();

        request.setPageNum(0);
        request.setPageSize(9999);
        List<CustomerPartnerOrderVO> customerPartnerOrderVOList = customerInfoMapper.queryOrderByCustomerId(request, RequestUtils.getBusinessGroup());
        if (ObjectUtils.isNotNull(customerPartnerOrderVOList)) {
            customerPartnerOrderVOList.forEach(x -> {
                x.setServiceFlag(StringUtils.isEmpty(x.getServiceAt()) ? 0 : 1);
                BigDecimal selPrice = x.getGrandTotal().subtract(x.getProductCommissionAmount());
                BigDecimal ratio = BigDecimal.ZERO;
                if (selPrice.compareTo(BigDecimal.ZERO) > 0) {
                    ratio = x.getProductCommissionAmount().divide(selPrice, 4, BigDecimal.ROUND_HALF_UP)
                            .multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_DOWN);
                }
                x.setProfitRatioStr(ratio.toString() + "%");

            });
        }

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));
        Workbook xssfWorkbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), CustomerPartnerOrderVO.class, customerPartnerOrderVOList);
        try {
            String fileName = "客户业绩数据导出";
            fileName = fileName + ".xls";
            String userAgent = servletRequestAttributes.getRequest().getHeader("User-Agent").toLowerCase();
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setContentType("application/octet-stream");
            response.setHeader("Content-disposition", "attachment;filename=\"" + fileName + "\"");
            OutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }

    }

    @Override
    public List<PartnerInfoVo> getTransferPartnerInfo(String partnerInfo, String customerId) {
        List<PartnerInfoVo> result = new ArrayList<>();
        List<String> customerDepartMents = customerManagermentDetailMapper.queryCustomerDepartMent(customerId);
        if (!ObjectUtils.isNotNull(customerDepartMents) || customerDepartMents.size() < 1) {
            throw new ApplicationException("客户信息获取失败");
        }
        List<String> partnerMemberKeys = sfaEmployeeInfoMapper.queryMemeBerkey(partnerInfo, customerDepartMents);
        if (ObjectUtils.isNotNull(partnerMemberKeys) && partnerMemberKeys.size() > 0) {
            for (String partnerMemberKey : partnerMemberKeys) {
                PartnerInfoVo partnerInfoVo = sfaPositionRelationMapper.selectByMemberKey(partnerMemberKey);
                List<String> list = customerInformationMapper.getRegionListByMemberKeyNew(Long.valueOf(partnerMemberKey));
                if (!CollectionUtils.isEmpty(list)) {
                    partnerInfoVo.setRegionList(list);
                }
                result.add(partnerInfoVo);
            }
        }
        return result;
    }

    @Override
    @Transactional
    public void addCallBackInfo(CustomerManagementAddCallBackInfoReq request) {
        //判断是新增回访还是跟进
        if (2 == request.getRecordType()) {
            if (Objects.isNull(request.getParentCallBackRelation())) {
                throw new ApplicationException("新增跟进时 访问信息的关联信息必输");
            }
            //根据关联id查询回访信息
            //查询是否存在
            QueryWrapper<CustomerManagementCallBackInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CustomerManagementCallBackInfo::getDeleteFlag, 0)
                    .eq(CustomerManagementCallBackInfo::getRecordType, 1)
                    .eq(CustomerManagementCallBackInfo::getId, request.getParentCallBackRelation());
            CustomerManagementCallBackInfo callBackInfo = customerManagementCallBackInfoMapper.selectOne(queryWrapper);
            if (Objects.isNull(callBackInfo)) {
                throw new ApplicationException("新增跟进时 访问信息不存在");
            }
        }


        //文件信息保存
        List<CustomerManagementCallBackFileInfo> fileInfos = new ArrayList<>();
        if (!StringUtils.isEmpty(request.getFileUrl()) || CollectionUtil.isNotEmpty(request.getFileInfos())) {

            if (!StringUtils.isEmpty(request.getFileUrl())) {
                //旺铺使用一个字段 英文逗号为分隔符传多个文件
                Arrays.asList(request.getFileUrl().split(",")).forEach(url -> {
                    CustomerManagementCallBackFileInfo fileInfo = new CustomerManagementCallBackFileInfo();
                    fileInfo.setFileUrl(url);
                    fileInfos.add(fileInfo);
                });
                //清空字段
                request.setFileUrl(null);
            }
            if (CollectionUtil.isNotEmpty(request.getFileInfos())) {
                fileInfos.addAll(BeanUtil.copyToList(request.getFileInfos(), CustomerManagementCallBackFileInfo.class));
            }

        }
        //回访-->直接新增
        CustomerManagementCallBackInfo info = new CustomerManagementCallBackInfo();
        BeanUtils.copyProperties(request, info);
        customerManagementCallBackInfoMapper.insert(info);
        Long infoId = info.getId();
        //保存文件
        if (CollectionUtil.isNotEmpty(fileInfos)) {
            fileInfos.stream().forEach(file -> {
                file.setRecordId(infoId);
                customerManagementCallBackFileInfoMapper.insert(file);
            });
        }

    }

    @Override
    public List<CustomerManagementQueryCallBackInfoVO> queryCallBackList(CustomerManagementQueryCallBackInfoReq request) {
        List<CustomerManagementQueryCallBackInfoVO> callBackInfoVOS = customerManagementCallBackInfoMapper.queryCallBackList(request.getCustomerId());
        //历史文件信息处理
        if (CollectionUtil.isNotEmpty(callBackInfoVOS)) {
            Map<Integer, String> callbackTypeMap = dictCodeServiceImpl.getMapByClassCd(DictCodeConstants.CLASSCD_CALLBACK_TYPE);
            callBackInfoVOS.forEach(callBackInfo -> {
                callBackInfo.setCallBackTypeDesc(callbackTypeMap.get(callBackInfo.getCallBackType()));
                if (!StringUtils.isEmpty(callBackInfo.getFileUrl())) {
                    CustomerManagementQueryCallBackFileInfoVO infoVO = new CustomerManagementQueryCallBackFileInfoVO();
                    infoVO.setFileUrl(callBackInfo.getFileUrl());
                    infoVO.setFileName(callBackInfo.getFileName());
                    if (CollectionUtil.isEmpty(callBackInfo.getFileInfos())) {
                        callBackInfo.setFileInfos(Arrays.asList(infoVO));
                    } else {
                        List<CustomerManagementQueryCallBackFileInfoVO> fileInfos = callBackInfo.getFileInfos();
                        fileInfos.add(infoVO);
                        callBackInfo.setFileInfos(fileInfos);
                    }
                }
            });
        }
        return callBackInfoVOS;
    }

    @Override
    public void deleteCallBackInfo(CustomerManagementDeleteCallBackInfoReq request) {
        CustomerManagementCallBackInfo backInfo = customerManagementCallBackInfoMapper.selectById(request.getId());
        if (Objects.isNull(backInfo)) {
            throw new ApplicationException("记录不存在");
        }
        backInfo.setDeleteFlag(1);
        customerManagementCallBackInfoMapper.updateById(backInfo);
        //判断是否是追加  是-->直接删除  不是-->查询并删除追加数据
        if (1 == backInfo.getRecordType()) {
            //查询是否存在
            QueryWrapper<CustomerManagementCallBackInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CustomerManagementCallBackInfo::getDeleteFlag, 0)
                    .eq(CustomerManagementCallBackInfo::getParentCallBackRelation, backInfo.getId());
            //删除追加信息
            List<CustomerManagementCallBackInfo> subCallBackInfos = customerManagementCallBackInfoMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(subCallBackInfos)) {
                subCallBackInfos.forEach(info -> {
                    info.setDeleteFlag(1);
                    customerManagementCallBackInfoMapper.updateById(info);
                });
            }
        }
        //逻辑删除文件信息
        LambdaQueryWrapper<CustomerManagementCallBackFileInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CustomerManagementCallBackFileInfo::getRecordId, request.getId());
        CustomerManagementCallBackFileInfo fileInfo = new CustomerManagementCallBackFileInfo();
        fileInfo.setDeleteFlag(1);
        customerManagementCallBackFileInfoMapper.update(fileInfo, lambdaQueryWrapper);

    }

    @Override
    public CustomerPerformanceVO getCustomerPerformance(String customerId, Integer model,String yearMonth) {
        return customerManagermentDetailMapper.getCustomerPerformance(customerId, model, yearMonth,RequestUtils.getBusinessGroup());
    }

    @Override
    public PerformanceComparisonDTO getPerformanceTrend(String customerId, Integer model,String yearMonth) {

        if (model == DateTypeEnum.NATURAL_MONTH.getCode()) {
            return customerAnalyseService.getMonthlyComparison(customerId,yearMonth);
        } else if (model == DateTypeEnum.NATURAL_QUARTER.getCode()) {
            return customerAnalyseService.getQuarterlyComparison(customerId,yearMonth);
        } else if (model == DateTypeEnum.FISCAL_YEAR.getCode()) {
            return customerAnalyseService.getFiscalYearComparison(customerId,yearMonth);
        }

        return null;
    }

    @Override
    public ProductPerformanceResult getProductPerformance(Integer commodityTypeId, Integer dateTypeId, String customerId,String yearMonth) {
        return customerAnalyseService.getProductPerformance(commodityTypeId, dateTypeId, yearMonth, customerId);
    }



    @Override
    public List<ProductAnalysisDTO> getProductAnalysis(Integer commodityTypeId, Integer dateTypeId, String customerId,String yearMonth,String key) {
        return customerAnalyseService.getProductAnalysis(commodityTypeId, dateTypeId, yearMonth, customerId,key);
    }

}
