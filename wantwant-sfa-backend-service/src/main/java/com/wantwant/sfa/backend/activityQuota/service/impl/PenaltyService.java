package com.wantwant.sfa.backend.activityQuota.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.core.util.PageUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.commons.ex.ApplicationException;
import com.wantwant.commons.pagination.Page;
import com.wantwant.sfa.backend.activityQuota.api.OrgPenaltyRequest;
import com.wantwant.sfa.backend.activityQuota.dto.PenaltyCategoryDTO;
import com.wantwant.sfa.backend.activityQuota.dto.PenaltyDTO;
import com.wantwant.sfa.backend.activityQuota.dto.PenaltyMonitorDTO;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyAttendanceRegularEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyCostDetailEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyEntity;
import com.wantwant.sfa.backend.activityQuota.entity.PenaltyRegularEntity;
import com.wantwant.sfa.backend.activityQuota.model.*;
import com.wantwant.sfa.backend.activityQuota.request.*;
import com.wantwant.sfa.backend.activityQuota.service.IPenaltyService;
import com.wantwant.sfa.backend.activityQuota.vo.*;
import com.wantwant.sfa.backend.arch.OrganizationTypeEnum;
import com.wantwant.sfa.backend.audit.dto.SelectAuditDto;
import com.wantwant.sfa.backend.audit.service.IAuditService;
import com.wantwant.sfa.backend.businessGroup.entity.SfaBusinessGroupEntity;
import com.wantwant.sfa.backend.common.RequestUtils;
import com.wantwant.sfa.backend.common.model.LoginModel;
import com.wantwant.sfa.backend.domain.emp.DO.ProcessUserDO;
import com.wantwant.sfa.backend.domain.emp.service.IEmpService;
import com.wantwant.sfa.backend.domain.emp.service.impl.EmpService;
import com.wantwant.sfa.backend.interview.enums.EmployeeStatus;
import com.wantwant.sfa.backend.mapper.*;
import com.wantwant.sfa.backend.mapper.activityQuota.*;
import com.wantwant.sfa.backend.mapper.businessGroup.SfaBusinessGroupMapper;
import com.wantwant.sfa.backend.mapper.gold.GoldTypeMapper;
import com.wantwant.sfa.backend.mapper.position.SfaPositionRelationMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletAccountMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletMapper;
import com.wantwant.sfa.backend.mapper.wallet.WantWalletTypeMapper;
import com.wantwant.sfa.backend.model.ApplyMemberPo;
import com.wantwant.sfa.backend.model.CeoBusinessOrganizationPositionRelation;
import com.wantwant.sfa.backend.model.SfaEmployeeInfoModel;
import com.wantwant.sfa.backend.notify.model.TrainPenaltyModel;
import com.wantwant.sfa.backend.position.entity.SfaPositionRelationEntity;
import com.wantwant.sfa.backend.rabbitMQ.QueueConstant;
import com.wantwant.sfa.backend.rabbitMQ.RabbitMQSender;
import com.wantwant.sfa.backend.service.ICheckCustomerService;
import com.wantwant.sfa.backend.transaction.enums.PositionEnum;
import com.wantwant.sfa.backend.util.ActivityQuotaConnectorUtil;
import com.wantwant.sfa.backend.util.BeanUtils;
import com.wantwant.sfa.backend.util.RedisUtil;
import com.wantwant.sfa.backend.wallet.dto.WalletLogBatchDTO;
import com.wantwant.sfa.backend.wallet.entity.WantWalletAccountEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity;
import com.wantwant.sfa.backend.wallet.entity.WantWalletTypeEntity;
import com.wantwant.sfa.backend.wallet.enums.WalletLogTypeEnum;
import com.wantwant.sfa.backend.workReport.vo.WorkReportStaticsDetailVO;
import com.wantwant.sfa.backend.workReport.vo.WorkReportStaticsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wantwant.sfa.backend.interview.service.InterviewService.ZW_HR_EMPLOYEE_ID_CODE;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/13/下午7:48
 */
@Service
@Slf4j
public class PenaltyService implements IPenaltyService {
    private static final String PENALTY_LOCK = "sfa:penalty:org";
    @Autowired
    private PenaltyRegularMapper penaltyRegularMapper;
    @Autowired
    private SfaEmployeeInfoMapper sfaEmployeeInfoMapper;
    @Autowired
    private CeoBusinessOrganizationPositionRelationMapper ceoBusinessOrganizationPositionRelationMapper;
    @Autowired
    private OrganizationMapper organizationMapper;
    @Autowired
    private PenaltyMapper penaltyMapper;
    @Autowired
    private PenaltyCostDetailMapper penaltyCostDetailMapper;
    @Autowired
    private ICheckCustomerService checkCustomerService;
    @Autowired
    private ApplyMemberMapper applyMemberMapper;
    @Autowired
    private SfaPositionRelationMapper sfaPositionRelationMapper;
    @Autowired
    private SfaBusinessGroupMapper sfaBusinessGroupMapper;
    @Autowired
    private WantWalletAccountMapper wantWalletAccountMapper;
    @Autowired
    private WantWalletMapper wantWalletMapper;
    @Autowired
    private WantWalletLogMapper wantWalletLogMapper;
    @Autowired
    private WantWalletTypeMapper wantWalletTypeMapper;
    @Autowired
    private GoldTypeMapper goldTypeMapper;
    @Autowired
    private ActivityQuotaConnectorUtil activityQuotaConnectorUtil;
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private IAuditService auditService;
    @Resource
    private RabbitMQSender rabbitMQSender;


    private static final String ERR_MSG = "第{0}行,错误信息:{1}";
    @Resource
    private IEmpService empService;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    @Transactional
    public void eliminatePenalty(Integer employeeInfoId,Integer walletType) {
        PenaltyRegularEntity entity = penaltyRegularMapper.selectById(2);
        Integer status = entity.getStatus();
        int deleteFlag = entity.getDeleteFlag();

        // 特批汰换扣款未开启或已经删除
        if(status != 1 || deleteFlag == 1){
            log.info("【eliminate penalty regular】status:{},deleteFlag:{}",status,deleteFlag);
            return;
        }

        BigDecimal amount = entity.getAmount();
        if(amount.compareTo(BigDecimal.ZERO) == 0){
            log.info("【eliminate penalty】amount:{}",amount);
            return;
        }

        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectById(employeeInfoId);
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("合伙人信息不存在");
        }

        String companyCode = sfaEmployeeInfoModel.getCompanyCode();

        // 获取分公司岗位
        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", companyCode)
                .eq("channel", RequestUtils.getChannel())
        );

        // 递归获取上级岗位
        CeoBusinessOrganizationPositionRelation actualPosition = getParentPosition(companyPosition);


        // 大区/分公司都缺岗，不做任何操作
        if(StringUtils.isBlank(companyPosition.getEmployeeId()) && Objects.nonNull(actualPosition) && StringUtils.isBlank(actualPosition.getEmployeeId())){
            log.info("【eliminate penalty】orgCode:{},缺岗",companyCode);
            return;
        }

        // 保存扣罚记录
        savePenaltyLogMapper(companyPosition,amount,entity.getId(),StringUtils.EMPTY, null);
    }

    private CeoBusinessOrganizationPositionRelation getParentPosition(CeoBusinessOrganizationPositionRelation companyPosition) {
        String organizationParentId = companyPosition.getOrganizationParentId();
        if(StringUtils.isBlank(organizationParentId)){
            return null;
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationParentId)
        );
        if(Objects.isNull(ceoBusinessOrganizationPositionRelation)){
            return null;
        }
        if(StringUtils.isNotBlank(ceoBusinessOrganizationPositionRelation.getEmployeeId())){
            return ceoBusinessOrganizationPositionRelation;
        }

        return getParentPosition(ceoBusinessOrganizationPositionRelation);
    }

    @Override
    @Transactional
    public void pressPenalty(String organizationId, Integer applyType,boolean updateCurSurplus) {
        // 获取所有待追缴的罚款
        List<PenaltyEntity> penaltyEntities = penaltyMapper.selectList(new QueryWrapper<PenaltyEntity>()
                .eq("actual_penalty_organization_id", organizationId)
                .eq("status", 1)
                .eq("coins_type", 1)
        );

        if(CollectionUtils.isEmpty(penaltyEntities)){
            return;
        }

        log.info("【press penalty】organizationId:{},applyType:{},walletTypeId:{}",organizationId,applyType);


        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>().eq(WantWalletAccountEntity::getOrganizationId, organizationId).eq(WantWalletAccountEntity::getDeleteFlag, 0));
        if(Objects.isNull(wantWalletAccountEntity)){
            return;
        }


        for (PenaltyEntity e : penaltyEntities) {

            PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectById(e.getPenaltyRegularId());
            if (Objects.isNull(regularEntity)) {
                throw new ApplicationException("扣罚规则不存在");
            }

            List<WantWalletTypeEntity> wantWalletTypeEntities = wantWalletTypeMapper.selectList(new LambdaQueryWrapper<WantWalletTypeEntity>().orderByDesc(WantWalletTypeEntity::getWeight));
            if (CollectionUtils.isEmpty(wantWalletTypeEntities)) {
                throw new ApplicationException("无可用旺金币类型");
            }

            String applyTypeList = regularEntity.getApplyTypeList();

            // 应罚款
            BigDecimal penaltyAmount = e.getPenaltyAmount();
            // 实际已罚款
            BigDecimal actualPenaltyAmount = e.getActualPenaltyAmount();


            for (WantWalletTypeEntity walletTypeEntity : wantWalletTypeEntities) {
                LambdaQueryWrapper<WantWalletLogEntity> query = new LambdaQueryWrapper<WantWalletLogEntity>();
                query.eq(WantWalletLogEntity::getWalletAccountId, wantWalletAccountEntity.getAccountId()).eq(WantWalletLogEntity::getType, WalletLogTypeEnum.INCOME.getCode());
                query.eq(WantWalletLogEntity::getWalletTypeId, walletTypeEntity.getId());
                query.gt(WantWalletLogEntity::getSurplus, 0);
                query.eq(WantWalletLogEntity::getDeleteFlag, 0);
                if (StringUtils.isNotBlank(applyTypeList)) {
                    query.in(WantWalletLogEntity::getApplyType, Arrays.asList(applyTypeList.split(",")));
                }

                List<WantWalletLogEntity> wantWalletLogEntities = Optional.ofNullable(wantWalletLogMapper.selectList(query)).orElse(new ArrayList<>());

                for (WantWalletLogEntity l : wantWalletLogEntities) {
                    BigDecimal currentSurplus = l.getSurplus();

                    if(penaltyAmount.compareTo(actualPenaltyAmount) <= 0){
                        return;
                    }

                    // 因追缴额度
                    BigDecimal amount = penaltyAmount.subtract(actualPenaltyAmount);
                    // 本次追缴金额
                    BigDecimal pressAmount = BigDecimal.ZERO;

                    if (currentSurplus.compareTo(amount) >= 0) {
                        pressAmount = amount;
                        actualPenaltyAmount = actualPenaltyAmount.add(pressAmount);
                        e.setActualPenaltyAmount(penaltyAmount);
                        e.setUpdateTime(LocalDateTime.now());
                        e.setUpdateUserId("ROOT");
                        e.setUpdateUserName("system");
                        e.setStatus(3);
                        penaltyMapper.updateById(e);

                    } else {
                        pressAmount = currentSurplus;
                        actualPenaltyAmount = actualPenaltyAmount.add(pressAmount);
                        e.setActualPenaltyAmount(actualPenaltyAmount);
                        e.setUpdateUserId("ROOT");
                        e.setUpdateUserName("system");
                        penaltyMapper.updateById(e);
                    }

                    l.setSurplus(l.getSurplus().subtract(pressAmount));
                    l.setProcessUserId("ROOT");
                    l.setProcessUserName("system");
                    if(updateCurSurplus){
                        // 查询总剩余额度
                        BigDecimal surplus = Optional.ofNullable(wantWalletLogMapper.selectSurplusQuota(wantWalletAccountEntity.getOrganizationId(), l.getWalletTypeId(),l.getSubTypeId(), null)).orElse(BigDecimal.ZERO);
                        surplus = surplus.subtract(pressAmount);
                        l.setCurSurplus(surplus);
                    }

                    wantWalletLogMapper.updateById(l);


                    WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, l.getWalletAccountId()).eq(WantWalletEntity::getWalletTypeId, l.getWalletTypeId()));
                    if (Objects.nonNull(wantWalletEntity)) {
                        wantWalletEntity.setSurplus(wantWalletEntity.getSurplus().subtract(pressAmount));
                        wantWalletMapper.updateById(wantWalletEntity);
                    }

                    // 记录扣款金额明细
                    PenaltyCostDetailEntity penaltyCostDetailEntity = new PenaltyCostDetailEntity();
                    penaltyCostDetailEntity.setPenaltyId(e.getId());
                    penaltyCostDetailEntity.setApplyType(l.getApplyType());
                    penaltyCostDetailEntity.setWalletLogId(l.getLogId());
                    penaltyCostDetailEntity.setAmount(pressAmount);
                    penaltyCostDetailEntity.setCreateTime(LocalDateTime.now());
                    penaltyCostDetailMapper.insert(penaltyCostDetailEntity);
                }
            }

        }

    }

    @Override
    @Transactional
    public List<String> upload(MultipartFile file, String person) {
        ImportParams params = new ImportParams();
        List<PenaltyImportModel> list = new ArrayList<>();

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(person,loginInfo);



        try {
            list = ExcelImportUtil.importExcel(file.getInputStream(), PenaltyImportModel.class, params);
        } catch (Exception e) {
            log.error("【gold upload】导入失败", e);
            throw new ApplicationException("导入失败");
        }

        List<String> errMsg = new ArrayList<>();
        List<PenaltyImportModel> importModelList = new ArrayList<>();

        List<SfaBusinessGroupEntity> sfaBusinessGroupEntityList = sfaBusinessGroupMapper.selectList(new LambdaQueryWrapper<SfaBusinessGroupEntity>()
                .ne(SfaBusinessGroupEntity::getId, 99)
                .eq(SfaBusinessGroupEntity::getDeleteFlag, 0)
        );
        Map<String,Integer> businessGroupMap = sfaBusinessGroupEntityList.stream().collect(
                Collectors.toMap(SfaBusinessGroupEntity::getSuffix, SfaBusinessGroupEntity::getId));

        for(int i=0;i<list.size();i++){
            PenaltyImportModel penaltyImportModel = list.get(i);
            if(StringUtils.isNotBlank(penaltyImportModel.getPenaltyOrganizationName())){
                checkItem(list.get(i),i,errMsg,importModelList,businessGroupMap);
            }

        }

        if(!CollectionUtils.isEmpty(errMsg)){
            return errMsg;
        }

        importModelList.forEach(e -> {
            savePenaltyLogMapper(e.getActualOrganizationPosition(),new BigDecimal(e.getAmount()),e.getRegularId(),e.getRemark(),personInfo);
        });
        return errMsg;
    }



    @Override
    @Transactional
    public void updateStatus(UpdateStatusRequest request) {

        LoginModel loginInfo = RequestUtils.getLoginInfo();
        String person = request.getPerson();
        CeoBusinessOrganizationPositionRelation personInfo = null;
        if(StringUtils.isBlank(person) || person.equals("-1")){
            personInfo = new CeoBusinessOrganizationPositionRelation();
            personInfo.setEmployeeId("SYS");
            personInfo.setEmployeeName("系统操作");
        }else{
            personInfo = checkCustomerService.getPersonInfo(request.getPerson(),loginInfo);

        }


        PenaltyEntity penaltyEntity = penaltyMapper.selectById(request.getId());
        if(Objects.isNull(penaltyEntity)){
            throw new ApplicationException("获取扣罚信息失败");
        }

        penaltyEntity.setStatus(request.getStatus());
        penaltyEntity.setUpdateUserName(personInfo.getEmployeeName());
        penaltyEntity.setUpdateUserId(personInfo.getEmployeeId());
        penaltyEntity.setUpdateTime(LocalDateTime.now());
        penaltyMapper.updateById(penaltyEntity);

        Long penaltyRegularId = penaltyEntity.getPenaltyRegularId();
        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectById(penaltyRegularId);

        // 撤销重新计算
        if(request.getStatus() == 2){
            // 获取子表信息
            List<PenaltyCostDetailEntity> detailEntityList = penaltyCostDetailMapper.selectList(new QueryWrapper<PenaltyCostDetailEntity>().eq("penalty_id", penaltyEntity.getId()));

            if(!CollectionUtils.isEmpty(detailEntityList)){

                detailEntityList.stream().forEach(e -> {
                    // 额度转移
                    this.pressPenalty(penaltyEntity.getActualPenaltyOrganizationId(),e.getApplyType(),false);
                    // 扣除的额度需要释放
                    Long walletLogId = e.getWalletLogId();
                    if(Objects.nonNull(walletLogId) && walletLogId != 0){
                        WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectById(walletLogId);
                        if(Objects.nonNull(wantWalletLogEntity)){
                            wantWalletLogEntity.setSurplus(wantWalletLogEntity.getSurplus().add(e.getAmount()));
                            wantWalletLogMapper.updateById(wantWalletLogEntity);

                            WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>()
                                    .eq(WantWalletEntity::getWalletAccountId, wantWalletLogEntity.getWalletAccountId())
                                    .eq(WantWalletEntity::getWalletTypeId, wantWalletLogEntity.getWalletTypeId())
                            );
                            wantWalletEntity.setSurplus(wantWalletEntity.getSurplus().add(e.getAmount()));
                            wantWalletMapper.updateById(wantWalletEntity);
                        }
                    }

                    // 原额度设置为0
                    e.setAmount(BigDecimal.ZERO);
                    penaltyCostDetailMapper.updateById(e);
                });
            }

            // 设置主表实际扣款为0
            penaltyEntity.setActualPenaltyAmount(BigDecimal.ZERO);
            penaltyMapper.updateById(penaltyEntity);
        }
        // 开启扣罚
        else if(request.getStatus() == 1){
            String actualPenaltyOrganizationId = penaltyEntity.getActualPenaltyOrganizationId();
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>().eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, actualPenaltyOrganizationId));
            // 计算扣款金额
            processPenalty(regularEntity.getWalletTypeId(), penaltyEntity.getPenaltyAmount(), penaltyEntity, ceoBusinessOrganizationPositionRelation,regularEntity.getApplyTypeList());
        }

    }

    @Override
    @Transactional
    public void batchUpdateStatus(UpdateStatusBatchRequest request) {
        List<Integer> ids = request.getIds();
        ids.forEach(e -> {
            UpdateStatusRequest updateStatusRequest = new UpdateStatusRequest();
            BeanUtils.copyProperties(request,updateStatusRequest);
            updateStatusRequest.setId(e);
            this.updateStatus(updateStatusRequest);
        });
    }

    @Override
    public Page<PenaltyVo> selectPenalty(PenaltyQueryRequest request) {
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<PenaltyVo> list = penaltyMapper.selectPenalty(request);
        if(CollectionUtils.isEmpty(list)){
            return null;
        }

        Page<PenaltyVo> page = new Page<PenaltyVo>();
        page.setList(list);
        Integer count = penaltyMapper.selectPenaltyCount(request);
        page.setTotalItem(count);
        page.setTotalPage(PageUtil.totalPage(count,request.getRows()));
        return page;
    }

    @Override
    @Transactional
    public void transfer(CeoBusinessOrganizationPositionRelation oldPosition,CeoBusinessOrganizationPositionRelation positionRelation, BigDecimal quota, Long regularId,Integer walletType,String remark) {
        PenaltyEntity penaltyEntity = new PenaltyEntity();

        penaltyEntity.setPenaltyEmployeeId(oldPosition.getEmployeeId());
        penaltyEntity.setPenaltyEmployeeName(oldPosition.getEmployeeName());
        penaltyEntity.setPenaltyOrganizationId(oldPosition.getOrganizationId());
        penaltyEntity.setActualPenaltyEmployeeId(positionRelation.getEmployeeId());
        penaltyEntity.setActualPenaltyEmployeeName(positionRelation.getEmployeeName());
        penaltyEntity.setActualPenaltyOrganizationId(positionRelation.getOrganizationId());
        penaltyEntity.setPenaltyAmount(quota);

        penaltyEntity.setCoinsType(1);
        penaltyEntity.setOriginalPenaltyAmount(quota);
        penaltyEntity.setCreateTime(LocalDateTime.now());
        penaltyEntity.setPenaltyRegularId(regularId);
        penaltyEntity.setCreateUserId("ROOT");
        penaltyEntity.setCreateUserName("system");
        penaltyEntity.setUpdateTime(LocalDateTime.now());
        penaltyEntity.setUpdateUserName("system");
        penaltyEntity.setUpdateUserId("ROOT");

        penaltyEntity.setRemark(remark);

        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectById(regularId);

        // 计算扣款金额
        calculateActualPenaltyAmount(positionRelation.getOrganizationId(), walletType, quota,regularEntity.getApplyTypeList());


        // 计算扣款金额
        processPenalty(walletType, quota, penaltyEntity, positionRelation,regularEntity.getApplyTypeList());

    }

    @Override
    public void attendancePenalty(Integer walletType,List<PenaltyAttendanceRegularEntity> penaltyAttendanceRegularEntities, List<AbnormalAttendanceModel> abnormalAttendanceModels) {

        List<AbnormalAttendanceModel> executeList = new ArrayList<>();

        penaltyAttendanceRegularEntities.forEach(e -> {
            Integer limitLeftSignal = e.getLimitLeftSignal();
            BigDecimal limitLeft = e.getLimitLeft();
            Integer limitRightSignal = e.getLimitRightSignal();
            BigDecimal limitRight = e.getLimitRight();
            BigDecimal amount = e.getAmount();

            abnormalAttendanceModels.stream().filter(f -> checkRate(f.getAttendanceRate(),limitLeftSignal,limitLeft,limitRightSignal,limitRight)).forEach(a -> {
                a.setAmount(amount);
                executeList.add(a);
            });
        });

        executeList.forEach(e -> {

            String companyCode = e.getCompanyCode();
            CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq("organization_id", companyCode)
                    .eq("channel", 3)
            );


            // 保存扣罚记录
            savePenaltyLogMapper(companyPosition,e.getAmount(),1L,StringUtils.EMPTY, null);
        });

    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public TrainPenaltyModel ceoPenaltyProcess(CeoPenaltyRequest ceoPenaltyRequest) {

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new QueryWrapper<SfaBusinessGroupEntity>().eq("business_group_code", ceoPenaltyRequest.getBusinessGroupCode()).eq("status", 1).eq("delete_flag", 0));
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组信息获取失败");
        }

        PenaltyRegularEntity entity = penaltyRegularMapper.selectOne(new QueryWrapper<PenaltyRegularEntity>().eq("business_group",sfaBusinessGroupEntity.getId()).eq("regular_name", ceoPenaltyRequest.getPenaltyItem()).eq("status", 1).eq("delete_flag", 0));
        if(Objects.isNull(entity)){
            throw new ApplicationException("扣罚规则不存在或已关闭");
        }


        SfaEmployeeInfoModel sfaEmployeeInfoModel = sfaEmployeeInfoMapper.selectOne(new QueryWrapper<SfaEmployeeInfoModel>().eq("member_key", ceoPenaltyRequest.getMemberKey()));
        if(Objects.isNull(sfaEmployeeInfoModel)){
            throw new ApplicationException("员工信息获取失败");
        }

        ApplyMemberPo applyMemberPo = applyMemberMapper.selectById(sfaEmployeeInfoModel.getApplicationId());
        if(Objects.isNull(applyMemberPo)){
            throw new ApplicationException("报名表信息获取失败");
        }

        List<SfaPositionRelationEntity> positionRelationEntityList = sfaPositionRelationMapper.selectList(new QueryWrapper<SfaPositionRelationEntity>()
                .eq("employee_info_id", sfaEmployeeInfoModel.getId())
                .eq("business_group", sfaBusinessGroupEntity.getId())
                .eq("status", 1)
                .eq("delete_flag", 0)
        );
        if(Objects.isNull(positionRelationEntityList)){
            throw new ApplicationException("岗位信息获取失败");
        }


        SfaPositionRelationEntity positionRelationEntity = null;
        Optional<SfaPositionRelationEntity> first = positionRelationEntityList.stream().filter(f -> f.getPartTime() == 1).findFirst();
        if(first.isPresent()){
            positionRelationEntity = first.get();
        }else{
            positionRelationEntity = positionRelationEntityList.get(0);
        }


        // 获取memberKey对应岗位
        CeoBusinessOrganizationPositionRelation actualPenaltyPosition = null;
        CeoBusinessOrganizationPositionRelation parentPosition = null;
        String areaName = StringUtils.EMPTY;
        String companyName = StringUtils.EMPTY;
        String departmentName = StringUtils.EMPTY;
        // 合伙人获取上及
        if(positionRelationEntity.getPositionTypeId() == 3 || positionRelationEntity.getPositionTypeId() == 10){
            actualPenaltyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",positionRelationEntity.getParentOrganizationCode()).eq("channel",3));
        }
        // 分公司总监
        else if(positionRelationEntity.getPositionTypeId() == 2 || positionRelationEntity.getPositionTypeId() == 11 || positionRelationEntity.getPositionTypeId() == 12){
            actualPenaltyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id",positionRelationEntity.getOrganizationCode()).eq("channel",3));
        }
        else{
            throw new ApplicationException("暂不支持该岗位扣罚");
        }


        // 保存扣罚记录
        savePenaltyLogMapper(actualPenaltyPosition,ceoPenaltyRequest.getPenaltyQuota(),entity.getId(),ceoPenaltyRequest.getRemark(), null);

        TrainPenaltyModel trainPenaltyModel = new TrainPenaltyModel();
        trainPenaltyModel.setDepartmentName(departmentName);
        trainPenaltyModel.setPenaltyItem(entity.getRegularName());
        trainPenaltyModel.setCompanyName(companyName);
        trainPenaltyModel.setAreaName(areaName);
        trainPenaltyModel.setEmployeeName(sfaEmployeeInfoModel.getEmployeeName());
        trainPenaltyModel.setMobile(ceoPenaltyRequest.getMobile());
        trainPenaltyModel.setStatus(EmployeeStatus.findNameByType(sfaEmployeeInfoModel.getEmployeeStatus()));
        trainPenaltyModel.setPosition(PositionEnum.getPositionName(applyMemberPo.getCeoType(),applyMemberPo.getJobsType(),applyMemberPo.getPosition()));
        trainPenaltyModel.setQuota(ceoPenaltyRequest.getPenaltyQuota().toString());
        String employeeId = actualPenaltyPosition.getEmployeeId();
        trainPenaltyModel.setEmployeeId(employeeId);
        trainPenaltyModel.setRemark(ceoPenaltyRequest.getRemark());
        return trainPenaltyModel;
    }

    @Override
    public void exportPenalty(PenaltyQueryRequest request) {
        request.setNeedPage(false);
        request.setBusinessGroup(RequestUtils.getBusinessGroup());
        List<PenaltyVo> list = penaltyMapper.selectPenalty(request);


        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();

        String sheetName = LocalDateTimeUtils.formatNow(LocalDateTimeUtils.formatNow("yyyy-MM-dd-HH-MM-ss"));

        String name = "扣罚明细" + sheetName;

        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, sheetName), PenaltyVo.class, list);

        response.setContentType("application/vnd.ms-excel");

        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder
                    .encode(name + ".xlsx"));
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            response.setStatus(500);
        }
    }

    @Override
    public BigDecimal getTotalPenaltyAmount(String organizationId, Integer walletId) {
        log.info("【get total Penalty amount】organizationId:{},walletType:{}",organizationId,walletId);

        return penaltyMapper.getTotalPenaltyAmount(organizationId,walletId);
    }

    @Override
    public BigDecimal getActualPenaltyAmount(String organizationId) {
        return penaltyMapper.getActualPenaltyAmount(organizationId);
    }

    @Override
    public void companyPenalty(CompanyPenaltyModel companyPenaltyModel) {
        log.info("【company penalty】model:{}",companyPenaltyModel);
        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectOne(new QueryWrapper<PenaltyRegularEntity>().eq("regular_name", companyPenaltyModel.getItemName()).eq("business_group", RequestUtils.getBusinessGroup()).eq("status",1).last("limit 1"));
        if(Objects.isNull(regularEntity)){
            throw new ApplicationException("扣罚项目不存在");
        }

        CeoBusinessOrganizationPositionRelation companyPosition = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>().eq("organization_id", companyPenaltyModel.getCompanyCode()));

        CeoBusinessOrganizationPositionRelation personInfo = checkCustomerService.getPersonInfo(companyPenaltyModel.getProcessUserId(), RequestUtils.getLoginInfo());

        savePenaltyLogMapper(companyPosition,companyPenaltyModel.getPenaltyAmount(),regularEntity.getId(),companyPenaltyModel.getRemark(),personInfo);
    }

    @Override
    @Transactional
    public List<Long> orgProcess(OrgPenaltyRequest e) {

        SfaBusinessGroupEntity sfaBusinessGroupEntity = sfaBusinessGroupMapper.selectOne(new QueryWrapper<SfaBusinessGroupEntity>().eq("business_group_code", e.getBusinessGroupCode()).eq("status", 1).eq("delete_flag", 0));
        if(Objects.isNull(sfaBusinessGroupEntity)){
            throw new ApplicationException("业务组信息获取失败");
        }

        PenaltyRegularEntity entity = penaltyRegularMapper.selectOne(new QueryWrapper<PenaltyRegularEntity>().eq("business_group",sfaBusinessGroupEntity.getId()).eq("regular_name", e.getPenaltyItem()).eq("status", 1).eq("delete_flag", 0));
        if(Objects.isNull(entity)){
            throw new ApplicationException("扣罚规则不存在或已关闭");
        }

        String organizationName = e.getOrganizationName();
        String organizationId = organizationMapper.getOrganizationIdByName(organizationName, 3, sfaBusinessGroupEntity.getId());
        if(StringUtils.isBlank(organizationId)){
            throw new ApplicationException("组织信息获取失败");
        }


        CeoBusinessOrganizationPositionRelation positionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", organizationId)
        );

//        CeoBusinessOrganizationPositionRelation parentPosition = getParentPosition(positionRelation);



        // 大区/分公司都缺岗，不做任何操作
//        if(Objects.nonNull(positionRelation) && StringUtils.isBlank(positionRelation.getEmployeeId())  && Objects.nonNull(parentPosition) && StringUtils.isBlank(parentPosition.getEmployeeId())){
//            throw new ApplicationException("大区/分公司都缺岗，不做任何操作");
//        }
//

        // 保存扣罚记录
        return savePenaltyLogMapper(positionRelation,e.getPenaltyQuota(),entity.getId(),e.getRemark(), null);

    }

    @Override
    public List<PenaltyDetailVo> getPenaltyDetail(Long penaltyId) {

        PenaltyEntity penaltyEntity = penaltyMapper.selectById(penaltyId);
        if(Objects.isNull(penaltyEntity)){
            return null;
        }





        List<PenaltyCostDetailEntity> detailEntityList = penaltyCostDetailMapper.selectList(new LambdaQueryWrapper<PenaltyCostDetailEntity>().eq(PenaltyCostDetailEntity::getPenaltyId, penaltyId).gt(PenaltyCostDetailEntity::getAmount,0));
        if(CollectionUtils.isEmpty(detailEntityList)){
            return null;
        }

        List<SPUInfoModel> spuInfoModels = Optional.ofNullable(activityQuotaConnectorUtil.querySPUInfo()).orElse(new ArrayList<>());

        List<PenaltyDetailVo> result = new ArrayList<>();
        detailEntityList.forEach(e -> {
            PenaltyDetailVo detail = new PenaltyDetailVo();

            WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectById(e.getWalletLogId());


            WantWalletTypeEntity wantWalletTypeEntity = wantWalletTypeMapper.selectById(wantWalletLogEntity.getWalletTypeId());

            detail.setWalletType(wantWalletTypeEntity.getWalletTypeName());
            LocalDateTime createTime = e.getCreateTime();
            if(Objects.nonNull(createTime)){
                detail.setPenaltyTime(LocalDateTimeUtils.formatTime(createTime,LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss));
            }
            detail.setQuota(e.getAmount());

            Integer applyType = e.getApplyType();
            if(Objects.nonNull(applyType)){
                String typeName = goldTypeMapper.selectTypeNameByCode(applyType);
                detail.setApplyType(typeName);
            }

            if(wantWalletLogEntity.getWalletTypeId() == 2){
                String subTypeId = wantWalletLogEntity.getSubTypeId();
                Optional<SPUInfoModel> first = spuInfoModels.stream().filter(f -> f.getSpuId().equals(subTypeId)).findFirst();
                if(first.isPresent()){
                    SPUInfoModel spuInfoModel = first.get();
                    detail.setWalletSubType(spuInfoModel.getSpuName());
                }
            }

            result.add(detail);
        });

        return result;
    }

    @Override
    public BigDecimal getActualPenaltyAmountByWalletType(String organizationId, Integer walletTypeId,String spuId) {
        return penaltyMapper.getActualPenaltyAmountByWalletType(organizationId,walletTypeId,spuId);
    }

    @Override
    public IPage<PenaltyMonitorVO> selectPenaltyMonitor(PenaltyMonitorSearchRequest penaltyMonitorSearchRequest) {
        if(!CollectionUtils.isEmpty(penaltyMonitorSearchRequest.getOrgCodes())){
            String orgCode = penaltyMonitorSearchRequest.getOrgCodes().stream().findFirst().get();
            penaltyMonitorSearchRequest.setOrgType(organizationMapper.getOrganizationType(orgCode));
        }


        IPage page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page(penaltyMonitorSearchRequest.getPage(),penaltyMonitorSearchRequest.getRows());
        List<PenaltyMonitorVO> list = doSearchMonitor(page, penaltyMonitorSearchRequest);

        page.setRecords(list);

        return page;
    }


    private List<PenaltyMonitorVO> doSearchMonitor(IPage page,PenaltyMonitorSearchRequest penaltyMonitorSearchRequest){
        List<PenaltyMonitorDTO> penaltyMonitorDTOS =  penaltyMapper.selectPenaltyMonitor(page,penaltyMonitorSearchRequest);
        if(CollectionUtils.isEmpty(penaltyMonitorDTOS)){
            return ListUtils.EMPTY_LIST;
        }

        List<PenaltyMonitorVO> list = new ArrayList<>();

        // 查询明细
        List<PenaltyMonitorDetailVO> monitorDetailVOS = Optional.ofNullable(penaltyMapper.selectPenaltyDetail(penaltyMonitorDTOS.stream().map(PenaltyMonitorDTO::getOrganizationId).collect(Collectors.toList()),penaltyMonitorSearchRequest.getStartDate(),penaltyMonitorSearchRequest.getEndDate())).orElse(new ArrayList<>());
        Map<String, List<PenaltyMonitorDetailVO>> map = monitorDetailVOS.stream().collect(Collectors.groupingBy(PenaltyMonitorDetailVO::getOrganizationId));

        // 动态列
        List<PenaltyCategoryDTO> dynamicCol = new ArrayList<>();
        monitorDetailVOS.forEach(e -> {
            Optional<PenaltyCategoryDTO> first = dynamicCol.stream().filter(f -> f.getCategoryId().equals(e.getCategoryId()) && f.getSecondaryCategoryId().equals(e.getSecondaryCategoryId())).findFirst();
            if(!first.isPresent()){
                PenaltyCategoryDTO penaltyCategoryDTO = new PenaltyCategoryDTO();
                BeanUtils.copyProperties(e,penaltyCategoryDTO);
                dynamicCol.add(penaltyCategoryDTO);
            }
        });



        penaltyMonitorDTOS.forEach(e -> {
            String organizationType = e.getOrganizationType();
            String organizationId = e.getOrganizationId();
            List<String> orgCodes = Optional.ofNullable(sfaPositionRelationMapper.selectAllNextOrg(organizationId, organizationType, organizationMapper.getBusinessGroupById(organizationId))).orElse(new ArrayList<>());
            e.setNumbers(orgCodes.size());
            PenaltyMonitorVO penaltyMonitorVO = new PenaltyMonitorVO();
            BeanUtils.copyProperties(e,penaltyMonitorVO);
            penaltyMonitorVO.setPosition(OrganizationTypeEnum.getPositionName(organizationType));

            List<PenaltyMonitorDetailVO> penaltyMonitorDetailVOS = new ArrayList<>();

            dynamicCol.forEach(d -> {
                PenaltyMonitorDetailVO penaltyMonitorDetailVO = new PenaltyMonitorDetailVO();
                penaltyMonitorDetailVO.setOrganizationId(e.getOrganizationId());
                BeanUtils.copyProperties(d,penaltyMonitorDetailVO);
                if(Objects.nonNull(map) && map.containsKey(organizationId)){
                    List<PenaltyMonitorDetailVO> detailVOS = map.get(organizationId);
                    Optional<PenaltyMonitorDetailVO> first = detailVOS.stream().filter(f -> f.getCategoryId().equals(d.getCategoryId()) && f.getSecondaryCategoryId().equals(d.getSecondaryCategoryId())).findFirst();
                    if(first.isPresent()){
                        BeanUtils.copyProperties(first.get(),penaltyMonitorDetailVO);
                    }
                }
                penaltyMonitorDetailVOS.add(penaltyMonitorDetailVO);

            });

            penaltyMonitorVO.setPenaltyMonitorDetailVOList(penaltyMonitorDetailVOS);
            list.add(penaltyMonitorVO);
        });
        return list;
    }



    @Override
    public void exportPenaltyMonitor(PenaltyMonitorSearchRequest penaltyMonitorSearchRequest) {
        if(!CollectionUtils.isEmpty(penaltyMonitorSearchRequest.getOrgCodes())){
            String orgCode = penaltyMonitorSearchRequest.getOrgCodes().stream().findFirst().get();
            penaltyMonitorSearchRequest.setOrgType(organizationMapper.getOrganizationType(orgCode));
        }

        List<PenaltyMonitorVO> penaltyMonitorDTOS = doSearchMonitor(null, penaltyMonitorSearchRequest);



        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        HttpServletResponse response = servletRequestAttributes.getResponse();



        try {
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("扣罚监控", "UTF-8") + ".xls");
            response.setCharacterEncoding("UTF-8");

            List<List<String>> result = new ArrayList<>();
            getData(penaltyMonitorDTOS,result);

            EasyExcel.write(response.getOutputStream())
                    .head(getHeader(penaltyMonitorDTOS))
                    .sheet("扣罚监控")
                    .doWrite(result);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Override
    public void transferLeaveBalance(String organizationId) {
        log.info("【transfer leave balance】orgCode:{}",organizationId);
        // 获取所有有挂帐的单子
        List<PenaltyEntity> penaltyEntities = penaltyMapper.selectList(new LambdaQueryWrapper<PenaltyEntity>()
                .eq(PenaltyEntity::getActualPenaltyOrganizationId, organizationId).eq(PenaltyEntity::getDeleteFlag, 0).eq(PenaltyEntity::getStatus, 1));

        if(CollectionUtils.isEmpty(penaltyEntities)){
            return;
        }

        log.info("【transfer leave balance】penaltyList:{}",penaltyEntities);
        // 找到上级有人的组织
        SelectAuditDto dto = new SelectAuditDto();
        dto.setChannel(3);
        dto.setCurrentOrganizationId(organizationMapper.getOrganizationParentId(organizationId));
        String employeeId = configMapper.getValueByCode(ZW_HR_EMPLOYEE_ID_CODE);
        dto.setStandbyEmployeeId(employeeId);
        dto.setBusinessGroup(organizationMapper.getBusinessGroupById(organizationId));
        CeoBusinessOrganizationPositionRelation auditPerson = auditService.chooseAuditPerson(dto);


        penaltyEntities.forEach(e -> {
            BigDecimal penaltyAmount = e.getPenaltyAmount();
            BigDecimal actualPenaltyAmount = e.getActualPenaltyAmount();

            // 剩余需要扣罚的额度
            BigDecimal surplus = penaltyAmount.subtract(actualPenaltyAmount);

            // 将当前扣罚改为扣罚完成
            e.setPenaltyAmount(actualPenaltyAmount);
            e.setStatus(3);
            e.setUpdateTime(LocalDateTime.now());
            penaltyMapper.updateById(e);
            // 新建扣罚
            savePenaltyLogMapper(auditPerson,surplus,e.getPenaltyRegularId(),"人员离职扣罚挂帐部份由上级承担",null);
        });
    }

    @Override
    public void rewardPenaltyDeduction(RewardPenaltyOperateRequest rewardPenaltyOperateRequest) {
//        ProcessUserDO userById = empService.getUserById(rewardPenaltyOperateRequest.getPerson());

        List<RewardPenaltyDeductionRequest> rewardPenaltyDeductionRequestList = rewardPenaltyOperateRequest.getRewardPenaltyDeductionRequestList();
        for (RewardPenaltyDeductionRequest request : rewardPenaltyDeductionRequestList){

            RewardPenaltyDeductionVO rewardPenaltyDeductionVO = new RewardPenaltyDeductionVO();
            rewardPenaltyDeductionVO.setRewardsPenaltyId(request.getRewardPenaltyId());

            OrgPenaltyRequest orgPenaltyRequest = new OrgPenaltyRequest();
            orgPenaltyRequest.setPenaltyItem(request.getPenaltyItem());
            orgPenaltyRequest.setPenaltyQuota(request.getPenaltyQuota());
            orgPenaltyRequest.setRemark(request.getRemark());
            orgPenaltyRequest.setOrganizationName(request.getOrganizationName());
            orgPenaltyRequest.setBusinessGroupCode(request.getBusinessGroupCode());
            try {
                List<Long> penaltyIds = orgProcess(orgPenaltyRequest);
                rewardPenaltyDeductionVO.setPenaltyId(penaltyIds);

            } catch (Exception e) {
                log.error("【reward penalty deduction】err:{}",e.getMessage());
            }
            log.info("【reward penalty deduction】send to rabbit mq,message:{}",rewardPenaltyDeductionVO);
            // 推送至MQ
            rabbitMQSender.sendMessage(QueueConstant.REWARDS_PENALTY_DEDUCTION_EXCHANGE, QueueConstant.REWARDS_PENALTY_DEDUCTION_QUEUE,null,rewardPenaltyDeductionVO,null);
        }
    }

    private List<List<String>> getHeader(List<PenaltyMonitorVO> penaltyMonitorVOS) {
        List<List<String>> header = new ArrayList<List<String>>();


        header.add(Arrays.asList("战区","战区","战区"));
        header.add(Arrays.asList("大区","大区","大区"));
        header.add(Arrays.asList("省区","省区","省区"));
        header.add(Arrays.asList("分公司","分公司","分公司"));
        header.add(Arrays.asList("营业所","营业所","营业所"));
        header.add(Arrays.asList("成员数","成员数","成员数"));
        header.add(Arrays.asList("岗位","岗位","岗位"));
        header.add(Arrays.asList("姓名","姓名","姓名"));
        header.add(Arrays.asList("在职天数","在职天数","在职天数"));


        if(!CollectionUtils.isEmpty(penaltyMonitorVOS)){
            PenaltyMonitorVO penaltyMonitorVO = penaltyMonitorVOS.stream().findFirst().get();

            List<PenaltyMonitorDetailVO> penaltyMonitorDetailVOList = penaltyMonitorVO.getPenaltyMonitorDetailVOList();
            if(!CollectionUtils.isEmpty(penaltyMonitorDetailVOList)){
                penaltyMonitorDetailVOList.forEach(d -> {
                    List<String> penalty = new ArrayList<>();
                    penalty.add(d.getCategoryName());
                    penalty.add(d.getSecondaryCategoryName());
                    penalty.add("应扣");
                    header.add(penalty);

                    List<String> actualPenalty = new ArrayList<>();
                    actualPenalty.add(d.getCategoryName());
                    actualPenalty.add(d.getSecondaryCategoryName());
                    actualPenalty.add("实扣");
                    header.add(actualPenalty);

                    List<String> defer = new ArrayList<>();
                    defer.add(d.getCategoryName());
                    defer.add(d.getSecondaryCategoryName());
                    defer.add("挂帐");
                    header.add(defer);
                });
            }

        }

        List<String> tail1 = new ArrayList<>();
        tail1.add("合计");
        tail1.add("合计");
        tail1.add("应扣");
        header.add(tail1);

        List<String> tail2 = new ArrayList<>();
        tail2.add("合计");
        tail2.add("合计");
        tail2.add("实扣");
        header.add(tail2);


        List<String> tail3 = new ArrayList<>();
        tail3.add("合计");
        tail3.add("合计");
        tail3.add("挂帐中");
        header.add(tail3);
        return header;
    }

    private List<List<String>> getData(List<PenaltyMonitorVO> penaltyMonitorVOS, List<List<String>> result) {
        //表头list的生成，实际的表头信息可以从数据库或者缓存等处动态读取
        List<List<String>> list = new ArrayList<List<String>>();
        if (org.springframework.util.CollectionUtils.isEmpty(penaltyMonitorVOS)) {
            return null;
        }

        penaltyMonitorVOS.forEach(e -> {
            List<String> dataMap = new ArrayList<>();
            dataMap.add(e.getAreaName());
            dataMap.add(e.getVareaName());
            dataMap.add(e.getProvinceName());
            dataMap.add(e.getCompanyName());
            dataMap.add(e.getDepartmentName());
            dataMap.add(String.valueOf(Optional.ofNullable(e.getNumbers()).orElse(0)));
            dataMap.add(OrganizationTypeEnum.getPositionName(e.getOrganizationType()));
            dataMap.add(e.getEmployeeName());
            Integer onBoardDate = Optional.ofNullable(e.getOnBoardDate()).orElse(0);
            dataMap.add(String.valueOf(onBoardDate));

            List<PenaltyMonitorDetailVO> penaltyMonitorDetailVOList = Optional.ofNullable(e.getPenaltyMonitorDetailVOList()).orElse(new ArrayList<>());
            if(!CollectionUtils.isEmpty(penaltyMonitorDetailVOList)){
                penaltyMonitorDetailVOList.forEach(d -> {
                    exportData(dataMap, d);
                });
            }




            dataMap.add(Optional.ofNullable(e.getTotalPenaltyAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            dataMap.add(Optional.ofNullable(e.getTotalActualPenaltyAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            dataMap.add(Optional.ofNullable(e.getTotalDeferPayment()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
            result.add(dataMap);
        });


        return list;
    }

    private void exportData(List<String> dataMap, PenaltyMonitorDetailVO d) {
        dataMap.add(Optional.ofNullable(d.getPenaltyAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
        dataMap.add(Optional.ofNullable(d.getActualPenaltyAmount()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
        dataMap.add(Optional.ofNullable(d.getDeferPayment()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString());
    }


    private boolean checkRate(BigDecimal attendanceRate, Integer limitLeftSignal, BigDecimal limitLeft, Integer limitRightSignal, BigDecimal limitRight) {

        boolean leftCheck = false;
        boolean rightCheck = false;
        BigDecimal rate = attendanceRate.multiply(new BigDecimal(100));
        leftCheck = doCheckRate(rate,limitLeftSignal,limitLeft,0);
        rightCheck = doCheckRate(rate,limitRightSignal,limitRight,1);

        return leftCheck && rightCheck;
    }

    private boolean doCheckRate(BigDecimal attendanceRate, Integer signal, BigDecimal targetRate,int type) {
        if(Objects.isNull(signal)){
            return true;
        }

        if(type == 0){
            if(signal == 1){
                return targetRate.compareTo(attendanceRate) < 0;
            }else if(signal == 2){
                return targetRate.compareTo(attendanceRate) <= 0;
            }else if(signal == 3){
                return targetRate.compareTo(attendanceRate) == 0;
            }else if(signal == 4){
                return targetRate.compareTo(attendanceRate) > 0;
            }else if(signal == 5){
                return targetRate.compareTo(attendanceRate) >= 0;
            }else{
                return false;
            }
        }else{
            if(signal == 1){
                return attendanceRate.compareTo(targetRate) < 0;
            }else if(signal == 2){
                return attendanceRate.compareTo(targetRate) <= 0;
            }else if(signal == 3){
                return attendanceRate.compareTo(targetRate) == 0;
            }else if(signal == 4){
                return attendanceRate.compareTo(targetRate) > 0;
            }else if(signal == 5){
                return attendanceRate.compareTo(targetRate) >= 0;
            }else{
                return false;
            }
        }

    }


    private void checkItem(PenaltyImportModel penaltyImportModel, int index,List<String> errList,List<PenaltyImportModel> successList,Map<String,Integer> businessGroupMap) {
        LoginModel loginInfo = RequestUtils.getLoginInfo();
        Integer businessGroupId = businessGroupMap.get(penaltyImportModel.getBusinessGroupName());
        if(Objects.isNull(businessGroupId)){
            errList.add(MessageFormat.format(ERR_MSG,index+2,"组别不存在或未填写"));
            return;
        }

        PenaltyRegularEntity entity = penaltyRegularMapper.selectOne(new QueryWrapper<PenaltyRegularEntity>()
                .eq("regular_name", penaltyImportModel.getPenaltyRegularName())
                .eq("status", 1)
                .eq("delete_flag", 0)
                .eq("business_group", businessGroupId)
        );
        if(Objects.isNull(entity)){
            errList.add(MessageFormat.format(ERR_MSG,index+2,"扣罚规则不存在或已关闭"));
            return;
        }

        penaltyImportModel.setRegularId(entity.getId());

        String penaltyOrganizationId = organizationMapper.getOrganizationIdByName(penaltyImportModel.getPenaltyOrganizationName(), RequestUtils.getChannel(),businessGroupId);
        if(StringUtils.isBlank(penaltyOrganizationId)){
            errList.add(MessageFormat.format(ERR_MSG,index+2,"组织信息不存在"));
            return;
        }


        String penaltyOrgName = organizationMapper.getOrganizationName(penaltyOrganizationId);
        penaltyImportModel.setPenaltyOrganizationName(penaltyOrgName);


        CeoBusinessOrganizationPositionRelation positionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new QueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq("organization_id", penaltyOrganizationId)
                .eq("channel", RequestUtils.getChannel())
        );

//        if(StringUtils.isBlank(penaltyImportModel.getPenaltyEmployeeName())){
//            errList.add(MessageFormat.format(ERR_MSG,index+2,"缺少扣罚人姓名"));
//            return;
//        }

        penaltyImportModel.setActualOrganizationPosition(positionRelation);


        successList.add(penaltyImportModel);
    }

    /**
     * 保存扣罚记录
     *
     * @param position 应扣岗位ID
     * @param amount 扣罚额度
     * @param regularId 规则ID
     * @param processUser
     */
    public List<Long> savePenaltyLogMapper(CeoBusinessOrganizationPositionRelation position,
                                     BigDecimal amount, Long regularId, String remark,
                                     CeoBusinessOrganizationPositionRelation processUser) {
        PenaltyEntity entity = new PenaltyEntity();
        entity.setOriginalPenaltyAmount(amount);

        entity.setCreateTime(LocalDateTime.now());

        entity.setUpdateTime(LocalDateTime.now());

        if(Objects.isNull(processUser)){
            entity.setCreateUserId("ROOT");
            entity.setCreateUserName("system");
            entity.setUpdateUserId("ROOT");
            entity.setUpdateUserName("system");
        }else{
            entity.setCreateUserId(processUser.getEmployeeId());
            entity.setCreateUserName(processUser.getEmployeeName());
            entity.setUpdateUserId(processUser.getEmployeeId());
            entity.setUpdateUserName(processUser.getEmployeeName());
        }

        entity.setDeleteFlag(0);
        // TODO 默认造旺币
        entity.setCoinsType(1);
        entity.setPenaltyRegularId(regularId);


        // 应扣岗位为null
        entity.setPenaltyOrganizationId(position.getOrganizationId());

        PenaltyRegularEntity regularEntity = penaltyRegularMapper.selectById(regularId);

        // 应扣金额
        BigDecimal penaltyAmount = amount;

        List<Long> penaltyIds = new ArrayList<>();

        while(penaltyAmount.compareTo(BigDecimal.ZERO ) > 0){
            // 如果上级组织非战区或大区，需要检查下组织是否有额度
            PenaltyDTO actualPenaltyPosition = getActualPenaltyPosition(entity.getPenaltyOrganizationId(), penaltyAmount, regularEntity);

            CeoBusinessOrganizationPositionRelation actualPosition = actualPenaltyPosition.getCeoBusinessOrganizationPositionRelation();

            try{
                if(!redisUtil.setLockWithWait(PENALTY_LOCK, entity.getPenaltyOrganizationId(), 5, TimeUnit.SECONDS, 10000, 1000)){
                    throw new ApplicationException("发放奖励，抢占锁失败");
                }

                entity.setPenaltyAmount(actualPenaltyPosition.getAmount());
                entity.setPenaltyEmployeeId(position.getEmployeeId());
                entity.setPenaltyEmployeeName(position.getEmployeeName());
                entity.setActualPenaltyOrganizationId(actualPosition.getOrganizationId());
                entity.setActualPenaltyEmployeeId(actualPosition.getEmployeeId());
                entity.setActualPenaltyEmployeeName(actualPosition.getEmployeeName());
                if(!actualPosition.getOrganizationId().equals(position.getOrganizationId())){
                    String organizationName = organizationMapper.getOrganizationName(position.getOrganizationId());
                    String curRemark = organizationName+"扣罚额度不足,由上级承担";
                    if(StringUtils.isNotBlank(curRemark)){
                        curRemark = curRemark + ",备注("+remark+")";
                    }
                    entity.setRemark(curRemark);
                }else{
                    entity.setRemark(remark);
                }


                entity.setActualPenaltyAmount(actualPenaltyPosition.getAmount());
                entity.setWalletTypeId(regularEntity.getWalletTypeId());
                penaltyMapper.insert(entity);
                penaltyIds.add(entity.getId());
                // 计算扣款金额
                processPenalty(regularEntity.getWalletTypeId(), actualPenaltyPosition.getAmount(), entity, actualPosition,regularEntity.getApplyTypeList());

                penaltyAmount = penaltyAmount.subtract(actualPenaltyPosition.getAmount());
            }finally {
                redisUtil.unLock(PENALTY_LOCK, entity.getPenaltyOrganizationId());
            }

        }

        return penaltyIds;
    }

    private PenaltyDTO getActualPenaltyPosition(String organizationId, BigDecimal amount, PenaltyRegularEntity regularEntity) {

        String organizationType = organizationMapper.getOrganizationType(organizationId);
        if(organizationType.equals("varea") || organizationType.equals("area")){
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
            PenaltyDTO penaltyDTO = new PenaltyDTO();
            penaltyDTO.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
            penaltyDTO.setAmount(amount);
            return penaltyDTO;
        }

        if(organizationType.equals("zb")){
            CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                    .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId)
                    .eq(CeoBusinessOrganizationPositionRelation::getEmployeeId,configMapper.getValueByCode("zw_hr_employee_id"))
                    .last("limit 1")
            );
            PenaltyDTO penaltyDTO = new PenaltyDTO();
            penaltyDTO.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
            penaltyDTO.setAmount(amount);
            return penaltyDTO;
        }

        List<Integer> applyTypeIds = new ArrayList();
        String applyTypeList = regularEntity.getApplyTypeList();
        if(StringUtils.isNotBlank(applyTypeList)){
            applyTypeIds = Arrays.asList(applyTypeList.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        }

        // 检查组织是否有额度
        BigDecimal surplusQuota = Optional.ofNullable(wantWalletLogMapper.selectSurplusQuota(organizationId, null,null,applyTypeIds)).orElse(BigDecimal.ZERO);
        // 组织没额度扣罚上级额度
        if(surplusQuota.compareTo(BigDecimal.ZERO) <= 0 ){
            return getActualPenaltyPosition(organizationMapper.getOrganizationParentId(organizationId),amount,regularEntity);
        }

        CeoBusinessOrganizationPositionRelation ceoBusinessOrganizationPositionRelation = ceoBusinessOrganizationPositionRelationMapper.selectOne(new LambdaQueryWrapper<CeoBusinessOrganizationPositionRelation>()
                .eq(CeoBusinessOrganizationPositionRelation::getOrganizationId, organizationId));
        PenaltyDTO penaltyDTO = new PenaltyDTO();
        penaltyDTO.setCeoBusinessOrganizationPositionRelation(ceoBusinessOrganizationPositionRelation);
        BigDecimal actualAmount = amount;
        if(surplusQuota.compareTo(amount) < 0){
            actualAmount = surplusQuota;
        }

        penaltyDTO.setAmount(actualAmount);
        return penaltyDTO;
    }

    private void processPenalty(Integer walletTypeId, BigDecimal amount, PenaltyEntity entity, CeoBusinessOrganizationPositionRelation calculatePosition,String applyTypeList) {
        Map<String, BigDecimal> stringBigDecimalMap = calculateActualPenaltyAmount(calculatePosition.getOrganizationId(), walletTypeId, amount,applyTypeList);

        log.info("【surplus quota】map:{}",stringBigDecimalMap);
        // 实际已扣除金额
        BigDecimal result = BigDecimal.ZERO;


        Iterator<Map.Entry<String, BigDecimal>> iterator = stringBigDecimalMap.entrySet().iterator();
        List<WalletLogBatchDTO> walletBatch = new ArrayList<>();
        // 记录扣罚明细
        while(iterator.hasNext()){
            Map.Entry<String, BigDecimal> next = iterator.next();
            BigDecimal quota = next.getValue();
            String key = next.getKey();
            String logId = key.split("_")[0];
            String applyType = key.split("_")[1];
            String surplus = key.split("_")[2];
            PenaltyCostDetailEntity penaltyCostDetailEntity = new PenaltyCostDetailEntity();
            penaltyCostDetailEntity.setPenaltyId(entity.getId());
            penaltyCostDetailEntity.setApplyType(Integer.parseInt(applyType));
            penaltyCostDetailEntity.setAmount(quota);
            penaltyCostDetailEntity.setWalletLogId(Long.parseLong(logId));
            penaltyCostDetailEntity.setCreateTime(LocalDateTime.now());
            penaltyCostDetailMapper.insert(penaltyCostDetailEntity);
            result = result.add(quota);

            WalletLogBatchDTO walletLogBatchDTO = new WalletLogBatchDTO();
            walletLogBatchDTO.setLogId(Long.parseLong(logId));
            walletLogBatchDTO.setQuota(new BigDecimal(surplus).subtract(quota));
            walletBatch.add(walletLogBatchDTO);

            // 额度主表剩余额度扣除
            WantWalletLogEntity wantWalletLogEntity = wantWalletLogMapper.selectById(Long.parseLong(logId));
            if(Objects.isNull(wantWalletLogEntity)){
                throw new ApplicationException("额度记录获取失败");
            }

            WantWalletEntity wantWalletEntity = wantWalletMapper.selectOne(new LambdaQueryWrapper<WantWalletEntity>().eq(WantWalletEntity::getWalletAccountId, wantWalletLogEntity.getWalletAccountId())
                    .eq(WantWalletEntity::getWalletTypeId, wantWalletLogEntity.getWalletTypeId()).eq(WantWalletEntity::getDeleteFlag, 0));
            if(Objects.isNull(wantWalletEntity)){
                throw new ApplicationException("额度表获取失败");
            }

            wantWalletEntity.setSurplus(wantWalletEntity.getSurplus().subtract(quota));
            wantWalletMapper.updateById(wantWalletEntity);
        }

        if(!CollectionUtils.isEmpty(walletBatch)){
            wantWalletLogMapper.batchUpdate(walletBatch);
        }

        // 扣款未挂帐则设置为完成
        if(result.compareTo(amount) >= 0){
            entity.setStatus(3);
        }else{
            entity.setStatus(1);
        }
        entity.setActualPenaltyAmount(result);
        penaltyMapper.updateById(entity);
    }

    private Map<String, BigDecimal> calculateActualPenaltyAmount(String organizationId, Integer walletTypeId, BigDecimal amount,String applyTypeList){
        // 保存值key为：log_id + _ + applyType + "_" + 总额度
        Map<String,BigDecimal> map = new LinkedHashMap<>();

        // 更具账号ID+账号类型获取账单中有额度的记录
        WantWalletAccountEntity wantWalletAccountEntity = wantWalletAccountMapper.selectOne(new LambdaQueryWrapper<WantWalletAccountEntity>()
                .eq(WantWalletAccountEntity::getOrganizationId, organizationId)
                .eq(WantWalletAccountEntity::getDeleteFlag, 0));
        if(Objects.isNull(wantWalletAccountEntity)){
            return map;
        }

        List<Integer>applyTypeIds = new ArrayList<>();
        if(StringUtils.isNotBlank(applyTypeList)){
            applyTypeIds = Arrays.asList(applyTypeList.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        }




        List<WantWalletLogEntity> wantWalletLogEntities = wantWalletLogMapper.selectPenaltyLog(wantWalletAccountEntity.getAccountId(),applyTypeIds);
        if(CollectionUtils.isEmpty(wantWalletLogEntities)){
            return map;
        }


        for(WantWalletLogEntity entity: wantWalletLogEntities){
            BigDecimal surplus = entity.getSurplus();
            String key = entity.getLogId()+"_"+entity.getApplyType() + "_" + entity.getSurplus().toString();
            BigDecimal changeAmount = BigDecimal.ZERO;
            if(amount.compareTo(BigDecimal.ZERO) == 0){
                break;
            }
            if(surplus.compareTo(amount) > 0){
                changeAmount = amount;
                amount = BigDecimal.ZERO;
            }else{
                changeAmount = entity.getSurplus();
                amount = amount.subtract(changeAmount);
            }
            map.put(key,changeAmount);
        }

        return map;
    }

}
