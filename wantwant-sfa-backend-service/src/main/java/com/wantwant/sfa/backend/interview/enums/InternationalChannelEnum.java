package com.wantwant.sfa.backend.interview.enums;

import lombok.Getter;

@Getter
public enum InternationalChannelEnum {
    GT(1, "GT"),
    MT(2, "MT");

    private final Integer code;
    private final String name;

    InternationalChannelEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Integer getCode(String name) {
        for (InternationalChannelEnum value : InternationalChannelEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }

    public static String getName(Integer code) {
        for (InternationalChannelEnum value : InternationalChannelEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
