<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wantwant.sfa.backend.mapper.order.AdsOrderItemDetailMapper">

    <sql id="where_list">
        aoid.refund_flag = 0
        <if test="params.orderChannel != null and params.orderChannel != '' ">
            and aoid.statistics_channel_id LIKE CONCAT( #{params.orderChannel}, '%' )
        </if>
        <if test="params.personBusinessGroup != null and params.personBusinessGroup != '99' ">
            and aoid.product_group_id = #{params.personBusinessGroup}
        </if>
        <if test="params.personOrganizationIds != null and params.personOrganizationIds.size>0 and params.personOrganizationType != null">
            <choose>
                <when test="params.personOrganizationType == 'area'">
                    AND  aoid.cur_area_code
                </when>
                <when test="params.personOrganizationType == 'varea'">
                    AND  aoid.cur_varea_code
                </when>
                <when test="params.personOrganizationType == 'province'">
                    AND  aoid.cur_province_code
                </when>
                <when test="params.personOrganizationType == 'company'">
                    AND  aoid.cur_company_code
                </when>
                <when test="params.personOrganizationType == 'department'">
                    AND  aoid.cur_department_code
                </when>
                <otherwise>
                </otherwise>
            </choose>
            in (
            <foreach collection="params.personOrganizationIds" item="item" index="index" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="params.organizationId != null and params.organizationId != '' and params.organizationType != null ">

            <if test="params.personBusinessGroup != null and params.personBusinessGroup != '99' ">
                <choose>
                    <when test="params.organizationType == 'area'">
                        and aoid.cur_area_code = #{params.organizationId}
                    </when>
                    <when test="params.organizationType == 'varea'">
                        and aoid.cur_varea_code = #{params.organizationId}
                    </when>
                    <when test="params.organizationType == 'province'">
                        and aoid.cur_province_code = #{params.organizationId}
                    </when>
                    <when test="params.organizationType == 'company'">
                        and aoid.cur_company_code = #{params.organizationId}
                    </when>
                    <when test="params.organizationType == 'department'">
                        and aoid.cur_department_code = #{params.organizationId}
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>

            <if test="params.personBusinessGroup != null and params.personBusinessGroup == '99' ">
                <choose>
                    <when test="params.organizationType == 'area'">
                        and aoid.cur_area_code like CONCAT (#{params.organizationId},'%')
                    </when>
                    <when test="params.organizationType == 'varea'">
                        and aoid.cur_varea_code like CONCAT (#{params.organizationId},'%')
                    </when>
                    <when test="params.organizationType == 'province'">
                        and aoid.cur_province_code like CONCAT (#{params.organizationId},'%')
                    </when>
                    <when test="params.organizationType == 'company'">
                        and aoid.cur_company_code like CONCAT (#{params.organizationId},'%')
                    </when>
                    <when test="params.organizationType == 'department'">
                        and aoid.cur_department_code like CONCAT (#{params.organizationId},'%')
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="params.orderStartDate != null">
            and aoid.placedat &gt;= CONVERT_TZ(CONCAT(#{params.orderStartDate},' 00:00:00'),#{params.timeZone},@@global.time_zone)
        </if>
        <if test="params.orderEndDate != null">
            and aoid.placedat &lt;= CONVERT_TZ(CONCAT(#{params.orderEndDate},' 23:59:59'),#{params.timeZone},@@global.time_zone)
        </if>
        <if test="params.status != null and params.status != '' ">
            and aoid.status = #{params.status}
        </if>
        <if test="params.orderKey != null and params.orderKey != '' ">
            and (aoid.code like CONCAT ('%',#{params.orderKey},'%')
            or DATE_FORMAT(aoid.placedat, '%Y-%m-%d %H:%i:%s') like CONCAT ('%',#{params.orderKey},'%'))
        </if>
        <if test="params.customer != null and params.customer != '' ">
            and (aoid.employee_info_id = #{params.customer}
            or aoid.employee_id = #{params.customer}
            or aoid.memberkey = #{params.customer}
            or aoid.mobilenumber = #{params.customer}
            or aoid.username like CONCAT ('%',#{params.customer},'%')
            or aoid.employee_name like CONCAT ('%',#{params.customer},'%')
            or aoid.customer_id_new = #{params.customer}
            )
        </if>
        <if test="params.employeeName != null and params.employeeName != '' ">
            and aoid.employee_name like CONCAT ('%',#{params.employeeName},'%')
        </if>
        <if test="params.type != null">
            <choose>
                <when test="params.type == 0 ">
                    and aoid.type not in (3,9,999)
                </when>
                <otherwise>
                    and aoid.type = #{params.type}
                </otherwise>
            </choose>
        </if>
        <if test="params.method != null">
            <choose>
                <when test="params.method == 1">
                    and aoid.itemtotalamt_paid > 0
                    and aoid.free_total_amount > 0
                </when>
                <when test="params.method == 2">
                    and aoid.itemtotalamt_paid > 0
                    and ifnull(aoid.free_total_amount,0) = 0
                </when>
                <when test="params.method == 3">
                    and ifnull(aoid.itemtotalamt_paid,0) = 0
                    and aoid.free_total_amount > 0
                </when>
            </choose>
        </if>
        <if test="params.menuType != null">
            <choose>
                <when test="params.menuType == 1 ">
                    and aoid.type in (3, 90, 999)
                    and (aoid.item_supplyprice_total > 0
                    or aoid.free_total_amount > 0)
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        <if test="params.performanceStatisticsStartDate != null">
            and aoid.performance_statistics_time &gt;= CONVERT_TZ(CONCAT(#{params.performanceStatisticsStartDate},' 00:00:00'),#{params.timeZone},@@global.time_zone)
        </if>
        <if test="params.performanceStatisticsEndDate != null">
            and aoid.performance_statistics_time &lt;= CONVERT_TZ(CONCAT(#{params.performanceStatisticsEndDate},' 23:59:59'),#{params.timeZone},@@global.time_zone)
        </if>
        <if test="params.skuName != null and params.skuName != '' ">
            and aoid.ptname like CONCAT ('%',#{params.skuName},'%')
        </if>
        <if test="params.sku != null and params.sku != '' ">
            and (aoid.sku = #{params.sku}
            or aoid.ptname like CONCAT ('%',#{params.sku},'%'))
        </if>
    </sql>

    <select id="queryOrderList" resultType="com.wantwant.sfa.backend.order.vo.OrderListNewVo">
        select * from (
        select
        max(aoid.cur_area_name) as areaName,
        max(aoid.cur_varea_name) as vareaName,
        max(aoid.cur_province_name) as provinceName,
        max(aoid.cur_company_name) as companyName,
        max(aoid.cur_department_name) as departmentName,
<!--        max(aoid.branch_name) as branchName,-->
        max(aoid.organization_code) as organizationId,

        max(aoid.customer_id_new) as customerIdNew,
        max(aoid.customer_type_new) as customerTypeNew,
        max(aoid.type) as type,

        max(aoid.code) as code,
        max(aoid.status) as status,
        max(aoid.employee_name) as employeeName,
        max(aoid.mobilenumber) as mobileNumber,
        max(aoid.user_identity) as userIdentity,
        CONVERT_TZ(max(aoid.placedat),@@global.time_zone,#{params.timeZone}) as placedAt,
        CONVERT_TZ(max(aoid.deliveringat),@@global.time_zone,#{params.timeZone}) as deliveringAt,
        CONVERT_TZ(max(aoid.receivedAt),@@global.time_zone,#{params.timeZone}) as receivedAt,
        CONVERT_TZ(max(aoid.completeat),@@global.time_zone,#{params.timeZone}) as completeAt,
        max(aoid.receivermobilenumber) as receiverMobileNumber,
        max(aoid.receivername) as receiverName,
        max(CONCAT_WS('',aoid.receiverprovince,aoid.receivercity,aoid.receiverdistrict,aoid.receiverstreet)) as receiverAddress,

        <!-- 运输费用 -->
        sum(aoid.cash_delivery + aoid.want_coin_delivery) as factoryShipingFee,

        <!-- sku数量 -->
        sum(aoid.boxful_quantity) as skuQuantity,

        <!-- 经销价/标准盘价/经销单价/商品经销价 -->
        sum(aoid.item_supplyprice_total + aoid.discountFullAmount + aoid.free_total_amount_all_types) as itemSupplyPrice,
        <!-- sku现金支付金额/销售业绩/实付金额/现金小计/实付小计 -->
        sum(ifnull(aoid.itemtotalamt_paid,0)) as skuRetailSubTotal,
        <!-- 旺金币合计 -->
        sum(ifnull(aoid.free_total_amount_all_types,0)) as freeTotalAmount,
        <!-- 合伙人利润/品项利润 -->
        sum(ifnull(aoid.item_profit_total,0)) as itemProfitTotal,
        <!-- 盘价业绩 -->
        sum(ifnull(aoid.item_supplyprice_total,0)) as itemSupplyPriceTotal,
        <!-- 满减抵扣金额/预订单满减小计 -->
        sum(aoid.discountFullAmount) as discountFullAmount,

        <!-- 退货金额 -->
        sum(aoid1.itemtotalamt_paid + aoid1.free_total_amount_all_types) as returnAmount,

        <!-- 物流破损 -->
        group_concat(distinct if(acsfd.abnormal_primary_cause_cps = '物流问题',acsfd.abnormal_secondary_cause_cps ,null),'、') as abnormalSecondaryCauseCps,
        round(sum(ifnull(acsfd.box_num,0)),2) as boxNum,
        round(sum(ifnull(acsfd.goods_loss_difference_box,0)),2) as goodsLossDifferenceBox,
        round(sum(ifnull(acsfd.goods_loss_difference_box,0))/sum(ifnull(acsfd.box_num,0))*100,2) as goodsLossDifferenceRate,

        max(sei.employee_status) AS employeeStatus,
        max(aoid.memberkey) as memberKey,
        max(aoid.is_first_order_memberkey) as isFirstOrderMemberKey
        from
        ads_order_item_detail aoid
        left join ads_order_item_detail aoid1 on aoid1.refund_flag = 1 and aoid1.code = aoid.code and aoid1.order_item_detail_key = aoid.order_item_detail_key
        left join ads_code_storage_fee_detail acsfd on aoid.order_item_detail_key = acsfd.order_item_detail_key and acsfd.refund_flag = 0
        left join ods.hp_sfa_employee_info sei on aoid.memberkey = sei.member_key
        <where>
            <include refid="where_list"/>
        </where>
        group by aoid.code
        ) orderlist
        <where>
            <if test="params.priceRange != null">
                <choose>
                    <when test="params.priceRange == 1">
                        and skuRetailSubTotal <![CDATA[<]]> 1000
                    </when>
                    <when test="params.priceRange == 2">
                        and skuRetailSubTotal between 1000 and 3000
                    </when>
                    <when test="params.priceRange == 3">
                        and skuRetailSubTotal between 3001 and 5000
                    </when>
                    <when test="params.priceRange == 4">
                        and skuRetailSubTotal <![CDATA[>=]]> 5001
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="null!= params.sortName and params.sortName!=''">${params.sortName}</when>
            <otherwise>code</otherwise>
        </choose>
        <choose>
            <when test="null!= params.sortType and params.sortType!=''">${params.sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>

    <select id="queryOrderDetail" resultType="com.wantwant.sfa.backend.order.vo.OrderDetailNewVo">
        select
        max(aoid.cur_area_name) as areaName,
        max(aoid.cur_varea_name) as vareaName,
        max(aoid.cur_province_name) as provinceName,
        max(aoid.cur_company_name) as companyName,
        max(aoid.cur_department_name) as departmentName,
<!--        max(aoid.branch_name) as branchName,-->

        max(aoid.code) as code,
        max(aoid.status) as status,
        max(aoid.username) as userName,
        max(aoid.mobilenumber) as mobileNumber,
        max(aoid.employee_name) as employeeName,
        max(aoid.user_identity) as userIdentity,

        max(aoid.customer_id_new) as customerIdNew,
        max(aoid.customer_type_new) as customerTypeNew,
        max(aoid.type) as type,

        CONVERT_TZ(max(aoid.placedat),@@global.time_zone,#{params.timeZone}) as placedAt,
        CONVERT_TZ(max(aoid.deliveringat),@@global.time_zone,#{params.timeZone}) as deliveringAt,
        CONVERT_TZ(max(aoid.receivedAt),@@global.time_zone,#{params.timeZone}) as receivedAt,
        CONVERT_TZ(max(aoid.completeat),@@global.time_zone,#{params.timeZone}) as completeAt,

        max(aoid.receivermobilenumber) as receiverMobileNumber,
        max(aoid.receivername) as receiverName,
        max(CONCAT_WS('',aoid.receiverprovince,aoid.receivercity,aoid.receiverdistrict,aoid.receiverstreet)) as receiverAddress,

        <!-- 快递公司 -->
        max(aoid.deliverycompanyname) as deliveryCompanyName,
        <!-- 运输费用 -->
        sum(aoid.cash_delivery + aoid.want_coin_delivery) as factoryShipingFee,
        <!-- 旺金币抵扣运费 -->
        sum(aoid.want_coin_delivery) as wantCoinDelivery,
        if(sum(aoid.want_coin_delivery)>0,1,0) as isWantCoinDelivery,

        <!-- sku数量 -->
        sum(aoid.boxful_quantity) as skuQuantity,
        <!-- 经销价/标准盘价/经销单价/商品经销价 -->
        sum(aoid.item_supplyprice) as itemSupplyPrice,
        <!-- 经销合计=盘价业绩+满减小计+sku旺金币支付金额 -->
        sum(aoid.item_supplyprice_total + aoid.discountFullAmount + aoid.free_total_amount_all_types) as itemSupplyPriceSum,
        <!-- sku现金支付金额/销售业绩/实付金额/现金小计/实付小计 -->
        sum(ifnull(aoid.itemtotalamt_paid,0)) as skuRetailSubTotal,
        <!-- 旺金币合计 -->
        sum(ifnull(aoid.free_total_amount_all_types,0)) as freeTotalAmount,
        <!-- 合伙人利润/品项利润 -->
        sum(aoid.item_profit_total) as itemProfitTotal,
        <!-- 盘价业绩 -->
        sum(aoid.item_supplyprice_total) as itemSupplyPriceTotal,
        <!-- 满减抵扣金额/预订单满减小计 -->
        sum(aoid.discountFullAmount) as discountFullAmount,

        <!-- 退货金额 -->
        sum(aoid1.itemtotalamt_paid + aoid1.free_total_amount_all_types) as returnAmount,
        <!-- 退货数量 -->
        sum(aoid1.boxful_quantity) as returnQuantity,
        <!-- 退货应扣业绩 -->
        sum(aoid1.item_supplyprice_total) as returnItemSupplyPriceTotal,

        IFNULL(max(ohei.substitute_member_name),'-') AS substituteName,
        IFNULL(max(ohei.substitute_mobile_number),'-') AS substituteMobile

        from ads_order_item_detail aoid
        left join ads_order_item_detail aoid1 on aoid1.refund_flag = 1 and aoid1.code = aoid.code and aoid1.order_item_detail_key = aoid.order_item_detail_key
        LEFT JOIN ods.hp_order_header_extend_info ohei on aoid.code = ohei.order_header_key
        <where>
            aoid.refund_flag = 0
            and aoid.code = #{params.code}
            <if test="params.personBusinessGroup != null and params.personBusinessGroup != '99' ">
                and aoid.product_group_id = #{params.personBusinessGroup}
            </if>
        </where>
        group by aoid.code
    </select>

    <select id="queryOrderSkuList" resultType="com.wantwant.sfa.backend.order.vo.OrderSkuListNewVo">
        select

        aoid.username as userName,
        aoid.sku as sku,
        aoid.ptname as skuName,
<!--        CONCAT(aoid.ptname,aoid.flavour,aoid.sku_spec,'*',aoid.boxful_quantity) as skuSplice,-->
        CONCAT(aoid.ptname,aoid.sku_spec,aoid.flavour) as skuSplice,
        aoid.sku_spec as skuSpec,
        aoid.flavour as flavour,
        dskbi.sku_images as skuImages,
        aoid.business_group_name as businessGroupName,
        aoid.commodity_type as commodityType,
        aoid.activity_name as activityName,
        aoid.batch_mgmt_number as batchMgmtNumber,
        <!-- sku数量 -->
        aoid.boxful_quantity as skuQuantity,
        <!-- 经销价/标准盘价/经销单价/商品经销价 -->
        aoid.item_supplyprice as itemSupplyPrice,
        <!-- 经销合计=盘价业绩+满减小计+sku旺金币支付金额 -->
        aoid.item_supplyprice_total + aoid.discountFullAmount + aoid.free_total_amount_all_types as itemSupplyPriceSum,
        <!-- sku现金支付金额/销售业绩/实付金额/现金小计/实付小计 -->
        ifnull(aoid.itemtotalamt_paid,0) as skuRetailSubTotal,
        <!-- 旺金币合计 -->
        ifnull(aoid.free_total_amount_all_types,0) as freeTotalAmount,
        <!-- 合伙人利润/品项利润 -->
        aoid.item_profit_total as itemProfitTotal,
        <!-- 盘价业绩 -->
        aoid.item_supplyprice_total as itemSupplyPriceTotal,
        <!-- 满减活动/满减抵扣金额/预订单满减小计/满减小计 -->
        aoid.discountFullAmount as discountFullAmount,

        <!-- 物流破损 -->
        if(acsfd.abnormal_primary_cause_cps = '物流问题',acsfd.abnormal_secondary_cause_cps ,null) as abnormalSecondaryCauseCps,
        round(ifnull(acsfd.box_num,0), 2) as boxNum,
        round(ifnull(acsfd.goods_loss_difference_box,0), 2) as goodsLossDifferenceBox,
        round(ifnull(acsfd.goods_loss_difference_box/box_num*100,0),2) as goodsLossDifferenceRate

        from
        ads_order_item_detail aoid
        left join dim_store_sku_base_info dskbi on dskbi.sku_id = aoid.sku
        left join ads_code_storage_fee_detail acsfd on aoid.order_item_detail_key = acsfd.order_item_detail_key and acsfd.refund_flag = 0
        <where>
            aoid.refund_flag = 0
            and aoid.code = #{params.code}
            <if test="params.personBusinessGroup != null and params.personBusinessGroup != '99' ">
                and (aoid.business_group = #{params.personBusinessGroup} or aoid.business_group is null)
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="null!= params.sortName and params.sortName!=''">${params.sortName}</when>
            <otherwise>aoid.code</otherwise>
        </choose>
        <choose>
            <when test="null!= params.sortType and params.sortType!=''">${params.sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>

    <select id="exportOrderSkuList" resultType="com.wantwant.sfa.backend.order.vo.OrderSkuExportNewVo">
        select
        aoid.product_group_name as businessGroupName,
        aoid.cur_area_name as areaName,
        aoid.cur_varea_name as vareaName,
        aoid.cur_province_name as provinceName,
        aoid.cur_company_name as companyName,
        aoid.cur_department_name as departmentName,

        aoid.employee_name as employeeName,
        aoid.username as userName,
        aoid.mobilenumber as mobileNumber,
        aoid.sku as sku,
        aoid.ptname as skuName,
        aoid.flavour as flavour,
        aoid.commodity_type as commodityType,
        <!-- sku数量 -->
        ROUND(aoid.boxful_quantity,2) as skuQuantity,
        <!-- 标准盘价/经销价/经销单价/商品经销价 -->
        ROUND(aoid.item_supplyprice,2) as itemSupplyPrice,
        <!-- 经销合计=盘价业绩+满减小计+sku旺金币支付金额 -->
        ROUND(aoid.item_supplyprice_total + aoid.discountFullAmount + aoid.free_total_amount_all_types,2) as itemSupplyPriceSum,
        <!-- sku现金支付金额/销售业绩/实付金额/现金小计/实付小计 -->
        ROUND(ifnull(aoid.itemtotalamt_paid,0),2) as skuRetailSubTotal,
        <!-- 旺金币合计 -->
        ROUND(ifnull(aoid.free_total_amount_all_types,0),2) as freeTotalAmount,

        aoid.code as code,
        aoid.type as type,
        aoid.status as status,
        CONVERT_TZ(aoid.placedat,@@global.time_zone,#{params.timeZone}) as placedAt,

        aoid.receiverprovince as receiverProvince,
        aoid.receivercity as receiverCity,
        aoid.receiverdistrict as receiverDistrict,
        aoid.receiverstreet as receiverStreet,
        aoid.receivermobilenumber as receiverMobileNumber,
        ROUND(aoid.cash_delivery + aoid.want_coin_delivery,2) as factoryShipingFee,

        <!-- 满减活动/满减抵扣金额/预订单满减小计/满减小计 -->
        ROUND(aoid.discountFullAmount,2) as discountFullAmount,

        CONVERT_TZ(aoid.receivedAt,@@global.time_zone,#{params.timeZone}) as receivedAt,
        CONVERT_TZ(aoid.completeat,@@global.time_zone,#{params.timeZone}) as completeAt,

        <!-- 退货金额 -->
        ROUND(aoid1.itemtotalamt_paid + aoid1.free_total_amount_all_types,2) as returnAmount,

        <!-- 物流破损 -->
        if(acsfd.abnormal_primary_cause_cps = '物流问题',acsfd.abnormal_secondary_cause_cps ,null) as abnormalSecondaryCauseCps,
        round(ifnull(acsfd.box_num, 0), 2) as boxNum,
        round(ifnull(acsfd.goods_loss_difference_box,0), 2) as goodsLossDifferenceBox,
        round(ifnull(acsfd.goods_loss_difference_box/box_num*100,0),2) as goodsLossDifferenceRate

        from ads_order_item_detail aoid
        left join ads_order_item_detail aoid1 on aoid1.refund_flag = 1 and aoid1.code = aoid.code and aoid1.order_item_detail_key = aoid.order_item_detail_key
        left join ads_code_storage_fee_detail acsfd on aoid.order_item_detail_key = acsfd.order_item_detail_key and acsfd.refund_flag = 0
        <where>
            <include refid="where_list"/>
        </where>
        ORDER BY
        <choose>
            <when test="null!= params.sortName and params.sortName!=''">${params.sortName}</when>
            <otherwise>aoid.code</otherwise>
        </choose>
        <choose>
            <when test="null!= params.sortType and params.sortType!=''">${params.sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>

    <select id="queryNotFirstOrderMemberKeyList" resultType="com.wantwant.sfa.backend.order.vo.OrderListNewVo">

        select
        max(lastorder.placedAt) as placedAt,
        lastorder.memberKey
        from
        (select
        CONVERT_TZ(max(aoid.placedat),@@global.time_zone,#{timeZone}) as placedAt,
        max(aoid.memberkey) as memberKey
        from
        ads_order_item_detail aoid
        <where>
            aoid.refund_flag = 0
            <foreach collection="notFirstOrderMemberKeyList" item="item" open="and aoid.memberkey in (" close=")" separator=",">
                #{item.memberKey}
            </foreach>
<!--            <foreach collection="notFirstOrderMemberKeyList" item="item" open="and aoid.code not in (" close=")" separator=",">-->
<!--                #{item.code}-->
<!--            </foreach>-->
        </where>
        group by aoid.code
        ) lastorder
        group by lastorder.memberKey

    </select>

    <select id="queryDailyOrderList" resultType="com.wantwant.sfa.backend.daily.vo.DailyOrderVo">
        select
        deprod.employee_name as employeeName,
        deprod.pic_url as avatar,
        case
        when  deprod.employee_status = 1 then '试岗'
        when  deprod.employee_status in (3,4,5,6,7) then '离职'
        when  deprod.employee_status = 2 and sip.process_type != 6  then '离职中'
        when  deprod.employee_status = 2 then '在职'
        end   as employeeStatus,
        deprod.onboard_time as onboardTime,
        deprod.zw_onboard_days as onboardDays,

        deprod.position_type_id as positionTypeId,
        deprod0.department_code as organizationId,
        deprod.employee_info_id as employeeInfoId,
        deprod.emp_id as employeeId,

        deprod0.member_key as memberKey,
        deprod0.employee_name as customerEmployeeName,
        deprod0.pic_url as customerAvatar,
        deprod0.mobile as mobile,

        case deprod0.job_type_id
        when '1' then '合伙人'
        else '其他' end as customerType,
        deprod0.pos_role_name as typeName,

        tcel.trading_behavior as tradingBehavior,
        tcel.old_customers as oldCustomers,
        (case tcel.old_customers when 0 then '未及时激活(30天以内)' when 1 then '及时激活' when 2 then '未及时激活(30天以上)' else '' end) as oldCustomersDesc,
        tcel.old_customers_classify as oldCustomersClassify,
        (case tcel.old_customers_classify when 1 then '复购' when 2 then '召回'  else '' end) as oldCustomersClassifyDesc,
        tcel.old_customers_trading_cycles as oldCustomersTradingCycles,
        (case tcel.old_customers_trading_cycles
        when 'A' then '1个月内下单'
        when 'B' then '2个月内下单'
        when 'C' then '3个月内下单'
        when 'D' then '3个月前下单'
        else '' end) as oldCustomersTradingCyclesDesc,

        deprod0.business_group as businessGroupId,
        deprod0.business_group_name as businessGroupName,

        deprod0.area_code as areaCode,
        deprod0.varea_code as vareaCode,
        deprod0.province_code as provinceCode,
        deprod0.company_code as companyCode,
        deprod0.department_code as departmentCode,

        CONCAT_WS('/', deprod0.area_name ,deprod0.varea_name, deprod0.province_name,deprod0.company_name,deprod0.department_name) as fullOrganizationName,

        deprod0.area_name as areaName,
        deprod0.varea_name as vareaName,
        deprod0.province_name as provinceName,
        deprod0.company_name as companyName,
        deprod0.department_name as departmentName,
        deprod0.branch_name as branchName,

        case abo10.position_type_id
        when 4 then abo10.glgzzrs_329
        when 1 then abo10.glgzzrs_329
        when 12 then abo10.glgzzrs_329
        when 11 then abo10.glgzzrs_329
        when 2 then abo10.glgzzrs_329
        when 10 then abo10.khs_61
        else '-'
        end as memberCount,

        org.code as code,
        org.freeTotalAmount as freeTotalAmount,
        org.itemSupplyPriceTotal as itemSupplyPriceTotal,
        round(org.freeTotalAmount*100/org.itemSupplyPriceTotal,#{params.dailyScale}) as freeTotalAmountRate,
        org.currentBookingPerformance,
        org.normalOrderPerformance,
        org.specialOrderPerformance
        from(
            select
                max(aoid.organization_code) as organizationId,
        <if test="params.personBusinessGroup != null">
            <choose>
                <when test="params.personBusinessGroup != '99'">
                    max(aoid.is_first_order_group) as isFirstOrderMemberkey,
                </when>
                <when test="params.personBusinessGroup == '99'">
                    max(aoid.is_first_order_memberkey) as isFirstOrderMemberkey,
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
                group_concat(distinct aoid.code) as code,
                <!-- 旺金币合计 -->
                sum(ifnull(aoid.free_total_amount,0)) as freeTotalAmount,
                <!-- 盘价业绩 -->
                sum(ifnull(aoid.item_supplyprice_total+ aoid.free_total_amount,0)) as itemSupplyPriceTotal,
                sum(case when type = 999 then ifnull(aoid.item_supplyprice_total+ aoid.free_total_amount,0) else 0 end ) as currentBookingPerformance,
                sum(case when type = 3 then ifnull(aoid.item_supplyprice_total+ aoid.free_total_amount,0) else 0 end ) as normalOrderPerformance,
                sum(case when type = 90 then ifnull(aoid.item_supplyprice_total+ aoid.free_total_amount,0) else 0 end ) as specialOrderPerformance
            from
                ads_order_item_detail aoid
            <where>
                date_format(aoid.performance_statistics_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{params.theDateFrom}
                and date_format(aoid.performance_statistics_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{params.theDateTo}
<!--                and aoid.refund_flag = 0-->
                and aoid.type in (3,90,999)
                and (aoid.item_supplyprice_total > 0 or aoid.free_total_amount > 0)
                and aoid.is_special_commodity = 0
                <if test="params.personBusinessGroup != null and params.personBusinessGroup != '99' ">
                    and aoid.product_group_id = #{params.personBusinessGroup}
                </if>
                <if test="params.chooseBusinessGroup != null">
                    and aoid.product_group_id = #{params.chooseBusinessGroup}
                </if>
                <if test="params.chooseOrganizationIds != null and params.chooseOrganizationIds.size>0">
                    and (
                    <foreach collection="params.chooseOrganizationIds" open="aoid.area_code in (" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or
                    <foreach collection="params.chooseOrganizationIds" open="aoid.varea_code in (" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or
                    <foreach collection="params.chooseOrganizationIds" open="aoid.province_code in (" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or
                    <foreach collection="params.chooseOrganizationIds" open="aoid.company_code in (" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    or
                    <foreach collection="params.chooseOrganizationIds" open="aoid.department_code in (" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="params.organizationType != null and params.organizationType != ''">
                    <choose>
                        <when test="params.organizationType == 'area'">
                            <if test="params.organizationId != null and params.organizationId != ''">
                            and aoid.area_code = #{params.organizationId}
                            </if>
                            <if test="params.organizationIds != null and params.organizationIds.size>0">
                                <foreach collection="params.organizationIds" open=" and aoid.area_code in (" close=")" separator="," item="item">
                                    #{item}
                                </foreach>
                            </if>
                        </when>
                        <when test="params.organizationType == 'varea'">
                            <if test="params.organizationId != null and params.organizationId != ''">
                            and aoid.varea_code = #{params.organizationId}
                            </if>
                            <if test="params.organizationIds != null and params.organizationIds.size>0">
                                <foreach collection="params.organizationIds" open=" and aoid.varea_code in (" close=")" separator="," item="item">
                                    #{item}
                                </foreach>
                            </if>
                        </when>
                        <when test="params.organizationType == 'province'">
                            <if test="params.organizationId != null and params.organizationId != ''">
                            and aoid.province_code = #{params.organizationId}
                            </if>
                            <if test="params.organizationIds != null and params.organizationIds.size>0">
                                <foreach collection="params.organizationIds" open=" and aoid.province_code in (" close=")" separator="," item="item">
                                    #{item}
                                </foreach>
                            </if>
                        </when>
                        <when test="params.organizationType == 'company'">
                            <if test="params.organizationId != null and params.organizationId != ''">
                            and aoid.company_code = #{params.organizationId}
                            </if>
                            <if test="params.organizationIds != null and params.organizationIds.size>0">
                                <foreach collection="params.organizationIds" open=" and aoid.company_code in (" close=")" separator="," item="item">
                                    #{item}
                                </foreach>
                            </if>
                        </when>
                        <when test="params.organizationType == 'department'">
                            <if test="params.organizationId != null and params.organizationId != ''">
                            and aoid.department_code = #{params.organizationId}
                            </if>
                            <if test="params.organizationIds != null and params.organizationIds.size>0">
                                <foreach collection="params.organizationIds" open=" and aoid.department_code in (" close=")" separator="," item="item">
                                    #{item}
                                </foreach>
                            </if>
                        </when>
                        <otherwise>
                        </otherwise>
                    </choose>
                </if>
            </where>
        <if test="params.personBusinessGroup != null">
            <choose>
                <when test="params.personBusinessGroup != '99'">
                    group by aoid.organization_code
                </when>
                <when test="params.personBusinessGroup == '99'">
                    group by aoid.memberkey
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        ) org
        left join dim_emp_pos_role_org_day deprod0
            on deprod0.the_date = #{params.theDate}
            and deprod0.status = 1
            and deprod0.organization_code = org.organizationId
        left join trading_customer_employee_list tcel
            on tcel.date_type_id = #{params.labelDateTypeId}
            and tcel.the_year_month = #{params.labelTheDate}
            and tcel.business_group_id = #{params.personBusinessGroup}
            and tcel.department_id = deprod0.department_code
            and tcel.member_key = deprod0.member_key
        <!-- 负责人即区域经理 相关字段-->
        left join dim_emp_pos_role_org_day deprod on deprod0.department_code = deprod.organization_code and deprod.the_date = #{params.theDate} and deprod.status = 1
        left join ods.hp_sfa_interview_process sip on deprod.application_id = sip.application_id
        left join ads_bigtable_organization abo10
        on abo10.date_type_id = 10
        and abo10.the_year_month = DATE_FORMAT(deprod.the_date,'%Y-%m')
        and abo10.organization_id = deprod.organization_code
        <where>
            <if test="params.isFirstOrderMemberkey != null">
                and org.isFirstOrderMemberkey = #{params.isFirstOrderMemberkey}
            </if>
            <if test="params.oldCustomers != null">
                and tcel.old_customers = #{params.oldCustomers}
            </if>
            <if test="params.oldCustomersClassify != null">
                and tcel.old_customers_classify = #{params.oldCustomersClassify}
            </if>
            <if test="params.oldCustomersTradingCycles != null and params.organizationType != ''">
                and tcel.old_customers_trading_cycles = #{params.oldCustomersTradingCycles}
            </if>
            <if test="params.posRoleId != null">
                and deprod0.pos_role_id = #{params.posRoleId}
            </if>
            <if test="params.searchType != null">
                <choose>
                    <when test="params.searchType == 2">
                        and deprod.position_type_id = 1
                    </when>
                    <when test="params.searchType == 3">
                        and deprod.position_type_id = 12
                    </when>
                    <when test="params.searchType == 4">
                        and deprod.position_type_id = 11
                    </when>
                    <when test="params.searchType == 5">
                        and deprod.position_type_id = 2
                    </when>
                    <when test="params.searchType == 6">
                        and deprod.position_type_id = 10
                    </when>
                    <when test="params.searchType == 7">
                        and deprod.position_type_id = 3
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="null!= params.sortName and params.sortName!=''">${params.sortName}</when>
            <otherwise>itemSupplyPriceTotal</otherwise>
        </choose>
        <choose>
            <when test="null!= params.sortType and params.sortType!=''">${params.sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>

    <select id="selectOrderNo" resultType="String">
        select distinct code from ads_order_item_detail
        <where>

            and business_group = #{businessGroup}
            <if test="organizationType != null and organizationType != '' ">
                <choose>
                    <when test="organizationType == 'area'">
                        and area_code = #{organizationId}
                    </when>
                    <when test="organizationType == 'varea'">
                        and varea_code = #{organizationId}
                    </when>
                    <when test="organizationType == 'province'">
                        and province_code = #{organizationId}
                    </when>
                    <when test="organizationType == 'company'">
                        and company_code = #{organizationId}
                    </when>
                    <when test="organizationType == 'department'">
                        and department_code = #{organizationId}
                    </when>
                </choose>
            </if>
            <if test="key != null and key != '' ">
                and code like concat('%',#{key},'%')
            </if>
        </where>
        limit 50
    </select>


    <select id="selectByOrderList" resultType="com.wantwant.sfa.backend.wallet.vo.AssociateOrderVO">
        select
            max(code) as code,
            max(area_name) as areaName,
            max(varea_name) as vareaName,
            max(province_name) as provinceName,
            max(company_name) as companyName,
            max(department_name) as departmentName,
            max(DATE_FORMAT( placedat, '%Y-%m-%d %H:%i:%s')) as placedat,
            max(status) as status,
            sum(item_supplyprice_total + discountFullAmount + free_total_amount_all_types) as thirdOrderPrice,
            sum(itemtotalamt_paid) as skuRetailsubtotal
        from
            ads_order_item_detail
        <where>
            <foreach collection="orderList" item="item" open=" and code in (" close=")" separator=",">
                #{item}
            </foreach>
            and business_group = #{businessGroup}
        </where>
        group by code
    </select>

    <select id="findDeliveryInformat" resultType="com.wantwant.sfa.backend.order.vo.DeliveryInformationVo">
        select deliveryCode,deliveryCompany from ods.hp_ceo_order_delivery where orderHeaderKey=#{code}
    </select>
</mapper>