<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.WwOrganizationAreaMapper">

    <select id="selectOrganizationRegionList" resultType="com.wantwant.sfa.backend.market.vo.OrganizationRegiontVo">
        SELECT
            swa.area_organization_name AS areaName,
            swa.company_organization_name AS companyName,
            swa.branch_organization_name AS departmentName,
            swa.province_name AS provinceName,
            swa.city_name AS cityName,
            swa.district_name AS districtName,
            GROUP_CONCAT(swa.street_name) AS streetName,
            ROUND(SUM(sr.population)/10000,2) AS population
        FROM ceo_business_organization_view v
        INNER JOIN sfa_organization_bind_relation sobr ON sobr.`level` =3 and sobr.channel =3 and sobr.status =1 and sobr.sfa_org_code = v.organization_id and sobr.business_group =#{params.businessGroup}
        INNER JOIN sfa_wworganization_area swa ON swa.branch_organization_id = sobr.org_code
        INNER JOIN sfa_region sr ON sr.market_code = swa.street_code AND sr.`status` = 1
        <where>
            v.channel = 3
            and v.business_group =#{params.businessGroup}
            AND v.organization_type = 'department'
            <if test="params.regionLevel != null and params.regionLevel != '' ">
                <choose>
                    <when test="params.regionLevel == 0 ">
                        and province_code = #{params.regionCode}
                    </when>
                    <when test="params.regionLevel == 1 ">
                        and city_code =  #{params.regionCode}
                    </when>
                    <otherwise>
                        and district_code =  #{params.regionCode}
                    </otherwise>
                </choose>
            </if>
            <include refid="organizationSearch" />
        </where>
        GROUP BY swa.branch_organization_id,swa.district_code
    </select>
    <sql id="organizationSearch">
        <if test="null != params.areaOrganizationIds and params.areaOrganizationIds.size()>0 ">
            <foreach collection="params.areaOrganizationIds" open=" and v.organization_id3 in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="null != params.vareaOrganizationIds and params.vareaOrganizationIds.size()>0  ">
            <foreach collection="params.vareaOrganizationIds" open=" and v.virtual_area_id in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="null != params.provinceOrganizationIds and params.provinceOrganizationIds.size()>0  ">
            <foreach collection="params.provinceOrganizationIds" open=" and v.province_id in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="null != params.companyOrganizationIds and params.companyOrganizationIds.size()>0  ">
            <foreach collection="params.companyOrganizationIds" open=" and v.organization_id2 in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="null != params.departmentIds  and params.departmentIds.size()>0  ">
            <foreach collection="params.departmentIds" open=" and v.department_id in (" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="selectDistrictCodeList" resultType="java.lang.String">
        SELECT
        distinct
        <choose>
            <when test="params.queryRegionType == 'province'">
                RPAD(swa.province_code, 6, '0')
            </when>
            <when test="params.queryRegionType == 'city'">
                RPAD(swa.city_code, 6, '0')
            </when>
            <when test="params.queryRegionType == 'district'">
                swa.district_code
            </when>
        </choose>
        FROM sfa_wworganization_area swa
        INNER JOIN sfa_organization_bind_relation sobr ON sobr.channel =3 and sobr.status =1
        <where>
            <if test="null != params.departmentIds and params.departmentIds.size()>0  ">
                swa.branch_organization_id = sobr.org_code
                and sobr.`level` =3
                <foreach collection="params.departmentIds" open=" and sobr.sfa_org_code in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="null != params.companyIds and params.companyIds.size()>0  ">
                swa.company_organization_id = sobr.org_code
                and sobr.`level` =2
                <foreach collection="params.companyIds" open=" and sobr.sfa_org_code in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectIdDistrictCodeList" resultType="java.lang.String">
        SELECT
        distinct
        <choose>
            <when test="params.queryRegionType == 'province'">
                RPAD(swa.province_code, 6, '0')
            </when>
            <when test="params.queryRegionType == 'city'">
                RPAD(swa.city_code, 6, '0')
            </when>
            <when test="params.queryRegionType == 'district'">
                swa.district_code
            </when>
        </choose>
        FROM sfa_wworganization_area_i18n swa
        INNER JOIN sfa_organization_bind_relation_i18n sobr ON sobr.channel =3 and sobr.status =1
        <where>
            <if test="null != params.departmentIds and params.departmentIds.size()>0  ">
                swa.branch_organization_id = sobr.org_code
                and sobr.`level` =3
                <foreach collection="params.departmentIds" open=" and sobr.sfa_org_code in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="null != params.companyIds and params.companyIds.size()>0  ">
                swa.company_organization_id = sobr.org_code
                and sobr.`level` =2
                <foreach collection="params.companyIds" open=" and sobr.sfa_org_code in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>