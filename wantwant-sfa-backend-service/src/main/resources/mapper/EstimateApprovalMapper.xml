<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.domain.estimate.mapper.EstimateApprovalMapper">

    <select id="selectApprovalList" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalDTO">
        select
            seav.approval_id ,
            `month` as yearMonth,
            ses.start_date ,
            ses.end_date ,
            ses.`type` ,
            scsr.store_name,
            cbov.organization_name3 as areaName ,
            cbov.organization_name2 as companyName,
            cbov.department_name,
            seav.create_time as applyTime,
            seav.apply_user_name ,
            seav.estimate_price ,
            seav.audit_price ,
            fid.process_step,
            fid.process_time as auditTime,
            fid.process_result,
            fi.result as finalResult,
            fd.flow_code,
            seav.is_submit ,
            fr.process_name,
            cbo.organization_type,
            fid.process_role_id as roleId,
            sesp.name as shipPeriodName,
            sesp.delivery_deadline ,
            sesp.batch_period,
            cbo.organization_id,
            sum(seadv.estimate_quantity) as estimateQuantity,
            sum(seadv.audit_quantity) as auditQuantity
        from
        sfa_estimate_approval_v2 seav
        inner join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id  = seav.approval_id
        and seadv.delete_flag  = 0
        inner join flow_instance fi on
        fi.instance_id = seav.instance_id
        and seav.delete_flag = 0
        inner join flow_definition fd on
        fd.flow_id = fi.flow_id
        inner join flow_instance_detail fid on
        fid.instance_id = fi.instance_id
        and fid.delete_flag = 0
        <choose>
            <when test="request.processStep == 10 and request.flowCode == '**********' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 20 and request.flowCode == '**********' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 30 and request.flowCode == 'TS00000004' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 40 and request.flowCode == 'TS00000004' ">
                and fid.process_step = 2
            </when>
            <when test="request.processStep == 50 and request.flowCode == 'TS00000004' ">
                and fid.process_step in (2,3)
            </when>
        </choose>
        inner join flow_rule fr on fr.step = fi.process_step
        and fr.delete_flag = 0 and fr.flow_id = fi.flow_id
        inner join ceo_business_organization_view cbov on
        cbov.organization_id = seav.organization_id
        inner join sfa_company_store_relation scsr on
        scsr.company_code = cbov.organization_id2
        inner join ceo_business_organization cbo on
        cbo.organization_id = seav.organization_id
        inner join sfa_estimate_schedule ses on
        ses.schedule_id = seav.schedule_id
        and ses.delete_flag = 0
        inner join sfa_estimate_ship_period sesp on sesp.id  = ses.ship_period_id
        and sesp.delete_flag  = 0 and sesp.status  = 1
        <where>
            and fd.flow_code = #{request.flowCode}
            and seav.delete_flag = 0
            <if test="request.status != null">
                and fi.result = #{request.status}
            </if>
            <if test="request.type != null ">
                and ses.type = #{request.type}
            </if>
            <if test="request.yearMonth != null and request.yearMonth != '' ">
                and ses.the_year_month = #{request.yearMonth}
            </if>
            <if test="request.searchProcessType != null">
                <choose>
                    <when test="request.searchProcessType == 1">
                        and fi.process_step = 1 and fi.result = 0
                        and fd.flow_code = 'TS00000004'
                        and fi.result = 0
                    </when>
                    <when test="request.searchProcessType == 2">
                        and fi.process_step = 2 and fi.result = 0
                        and fd.flow_code = 'TS00000004'
                        and fi.result = 0
                    </when>
                    <when test="request.searchProcessType == 3">
                        and fi.result = 1
                    </when>
                    <when test="request.searchProcessType == 4">
                        and fi.result = 2
                    </when>
                </choose>
            </if>
            <if test="request.shipPeriodId != null">
                and ses.ship_period_id = #{request.shipPeriodId}
            </if>

            <if test="request.organizationType != null and request.organizationType != ''">
                <choose>
                    <when test="request.organizationType == 'area'">
                        and cbov.organization_id3
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and cbov.virtual_area_id
                    </when>
                    <when test="request.organizationType == 'province'">
                        and cbov.province_id
                    </when>
                    <when test="request.organizationType == 'company'">
                        and cbov.organization_id2
                    </when>
                    <when test="request.organizationType == 'department'">
                        and cbov.department_id
                    </when>
                </choose>
                <foreach collection="request.organizationIds" item="item" separator="," open=" in (" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by seav.approval_id
        order by seav.create_time desc
    </select>


    <select id="selectApprovalSummary" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalSummaryDTO">
        select
        sum(ifnull(temp.estimate_price, 0)) as totalEstimatePrice,
        sum(ifnull(temp.audit_price, 0)) as totalAuditPrice,
        sum(ifnull(temp.estimateQuantity, 0)) as totalEstimateQuantity,
        sum(ifnull(temp.auditQuantity, 0)) as totalAuditQuantity
        from (
        select
        seav.estimate_price,
        seav.audit_price,
        sum(seadv.estimate_quantity) as estimateQuantity,
        sum(seadv.audit_quantity) as auditQuantity
        from
        sfa_estimate_approval_v2 seav
        inner join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id = seav.approval_id
        and seadv.delete_flag = 0
        inner join flow_instance fi on
        fi.instance_id = seav.instance_id
        and seav.delete_flag = 0
        inner join flow_definition fd on
        fd.flow_id = fi.flow_id
        inner join flow_instance_detail fid on
        fid.instance_id = fi.instance_id
        <choose>
            <when test="request.processStep == 10 and request.flowCode == '**********' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 20 and request.flowCode == '**********' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 30 and request.flowCode == 'TS00000004' ">
                and fid.process_step = 1
            </when>
            <when test="request.processStep == 40 and request.flowCode == 'TS00000004' ">
                and fid.process_step = 2
            </when>
            <when test="request.processStep == 50 and request.flowCode == 'TS00000004' ">
                and fid.process_step in (2,3)
            </when>
        </choose>
        inner join flow_rule fr on fr.step = fi.process_step
        and fr.delete_flag = 0 and fr.flow_id = fi.flow_id
        inner join ceo_business_organization_view cbov on
        cbov.organization_id = seav.organization_id
        inner join ceo_business_organization cbo on
        cbo.organization_id = seav.organization_id
        inner join sfa_estimate_schedule ses on
        ses.schedule_id = seav.schedule_id
        and ses.delete_flag = 0
        inner join sfa_estimate_ship_period sesp on sesp.id = ses.ship_period_id
        and sesp.delete_flag = 0 and sesp.status = 1
        <where>
            and fd.flow_code = #{request.flowCode}
            and seav.delete_flag = 0
            <if test="request.status != null">
                and fi.result = #{request.status}
            </if>
            <if test="request.type != null ">
                and ses.type = #{request.type}
            </if>
            <if test="request.yearMonth != null and request.yearMonth != '' ">
                and ses.the_year_month = #{request.yearMonth}
            </if>
            <if test="request.shipPeriodId != null">
                and ses.ship_period_id = #{request.shipPeriodId}
            </if>

            <if test="request.organizationType != null and request.organizationType != ''">
                <choose>
                    <when test="request.organizationType == 'area'">
                        and cbov.organization_id3
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and cbov.virtual_area_id
                    </when>
                    <when test="request.organizationType == 'province'">
                        and cbov.province_id
                    </when>
                    <when test="request.organizationType == 'company'">
                        and cbov.organization_id2
                    </when>
                    <when test="request.organizationType == 'department'">
                        and cbov.department_id
                    </when>
                </choose>
                <foreach collection="request.organizationIds" item="item" separator="," open=" in (" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="request.searchProcessType != null">
                <choose>
                    <when test="request.searchProcessType == 1">
                        and fi.process_step = 1 and fi.result = 0
                        and fd.flow_code = 'TS00000004'
                        and fi.result = 0
                    </when>
                    <when test="request.searchProcessType == 2">
                        and fi.process_step = 2 and fi.result = 0
                        and fd.flow_code = 'TS00000004'
                        and fi.result = 0
                    </when>
                    <when test="request.searchProcessType == 3">
                        and fi.result = 1
                    </when>
                    <when test="request.searchProcessType == 4">
                        and fi.result = 2
                    </when>
                </choose>
            </if>
        </where>
        group by seav.approval_id
        )temp
    </select>


    <select id="selectProcessStatus" resultType="String">
        select
            case  when fi.`result` = 1 then '已通过'
            when fi.`result` = 0 and sea.is_submit = 1 then '审核中'
            when sea.is_submit = 0 then '已驳回' end
        from
            sfa_estimate_approval_v2 sea
        inner join flow_instance fi on
            fi.instance_id = sea.instance_id
        <where>
            and sea.approval_id  =  #{approvalId}
            and sea.delete_flag = 0
        </where>
        limit 1
    </select>


    <select id="selectCanSubmitSku" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalItemDTO">
        select
            distinct ses2.sku,
            ses2.sku_name ,
            ses2.full_case_spec ,
            ses2.flavor ,
            ses2.line_name ,
            ses2.spu ,
            ses2.spu_name,
            ses2.third_order_price  as salePrice,
            ses2.expect_list_month ,
            ses2.tag_name,
            date_format(date_sub(concat(seav.`month`,'-01'),interval 2 month),'%Y-%m') as lastLastMonth,
            ifnull(seadh.estimate_quantity,0) + ifnull(seadh.append_quantity,0) as  lastLastEstimateCount,
            date_format(date_sub(concat(seav.`month`,'-01'),interval 1 month),'%Y-%m') as lastMonth,
            ifnull(seadh2.estimate_quantity,0) + ifnull(seadh2.append_quantity,0) as  lastEstimateCount,
            ifnull(seadv.estimate_quantity,0) as estimateCount,
            seadv.audit_quantity as auditCount
        from
            sfa_estimate_approval_v2 seav
        inner join ceo_business_organization_view cbov on
        cbov.organization_id = seav.organization_id and seav.delete_flag = 0
        inner join sfa_estimate_schedule ses on
        ses.schedule_id = seav.schedule_id and ses.delete_flag  = 0
        inner join sfa_estimate_sku_group sesg on sesg.group_id  = ses.group_id
        and sesg.delete_flag = 0
        inner join sfa_estimate_sku_organization_relation sesor on sesor.group_id  = sesg.group_id
        and (sesor.organization_id  = cbov.organization_id2  or sesor.organization_id = 'all')
        and sesor.delete_flag  = 0
        inner join sfa_estimate_sku ses2 on ses2.sku_id  = sesor.sku_id
        and ses2.delete_flag  = 0
        left join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id  = seav.approval_id
        and seadv.sku  = ses2.sku
        and seadv.delete_flag  = 0
        left join sfa_estimate_approval_detail_history seadh on seadh.organization_id  = seav.organization_id
        and seadh.sku = ses2.sku
        and seadh.the_year_month  = date_format(date_sub(concat(seav.`month`,'-01'),interval 2 month),'%Y-%m')
        and seadh.delete_flag  = 0
        left join sfa_estimate_approval_detail_history seadh2 on seadh2.organization_id  = seav.organization_id
        and seadh2.sku = ses2.sku
        and seadh2.the_year_month  = date_format(date_sub(concat(seav.`month`,'-01'),interval 1 month),'%Y-%m')
        and seadh2.delete_flag  = 0
        <where>
            and seav.approval_id = #{approvalId}
        </where>

    </select>


    <select id="selectSubmitSkuBySchedule" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalItemDTO">
        select
        ses2.sku_name ,
        ses2.full_case_spec ,
        ses2.flavor ,
        ses2.line_name ,
        ses2.spu ,
        ses2.spu_name,
        ses2.sku,
        ses2.third_order_price  as salePrice,
        ses2.expect_list_month ,
        ses2.tag_name,
        date_format(date_sub(concat(ses.the_year_month,'-01'),interval 2 month),'%Y-%m') as lastLastMonth,
        ifnull(seadh.estimate_quantity,0) + ifnull(seadh.append_quantity,0) as  lastLastEstimateCount,
        date_format(date_sub(concat(ses.the_year_month,'-01'),interval 1 month),'%Y-%m') as lastMonth,
        ifnull(seadh2.estimate_quantity,0) + ifnull(seadh2.append_quantity,0) as  lastEstimateCount,
        seadv.estimate_quantity as estimateCount,
        seadv.audit_quantity as auditCount
        from
        sfa_estimate_schedule ses
        inner join (
        select distinct schedule_id from sfa_estimate_schedule_organization_relation
        where delete_flag  = 0  and organization_id =#{companyCode}
        )ssr on ssr.schedule_id = ses.schedule_id
        inner join sfa_estimate_sku_group sesg on sesg.group_id  = ses.group_id
        and sesg.delete_flag = 0
        inner join (
        select distinct sku_id,group_id from sfa_estimate_sku_organization_relation where (organization_id = #{companyCode} or organization_id = 'all')
        and delete_flag  = 0
        )	sesor on
        sesor.group_id = sesg.group_id
        inner join sfa_estimate_sku ses2 on ses2.sku_id  = sesor.sku_id
        and ses2.delete_flag  = 0
        left join sfa_estimate_approval_v2 seav on
        seav.schedule_id  = ses.schedule_id and seav.delete_flag = 0
        and seav.organization_id  =  #{organizationId}
        left join sfa_estimate_approval_detail_v2 seadv on
        seadv.approval_id  = seav.approval_id
        and seadv.sku = ses2.sku
        and seadv.delete_flag = 0
        left join sfa_estimate_approval_detail_history seadh on seadh.organization_id  = #{organizationId}
        and seadh.sku = ses2.sku
        and seadh.the_year_month  = date_format(date_sub(concat(ses.the_year_month ,'-01'),interval 2 month),'%Y-%m')
        and seadh.delete_flag  = 0
        left join sfa_estimate_approval_detail_history seadh2 on seadh2.organization_id  = #{organizationId}
        and seadh2.sku = ses2.sku
        and seadh2.the_year_month  = date_format(date_sub(concat(ses.the_year_month,'-01'),interval 1 month),'%Y-%m')
        and seadh2.delete_flag  = 0
        <where>
            and ses.schedule_id = #{scheduleId}
        </where>
    </select>

    <select id="selectSubmitSku" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalItemDTO">
        select
            ses2.sku_name ,
            ses2.full_case_spec ,
            ses2.flavor ,
            ses2.sku,
            ses2.line_name ,
            ses2.spu ,
            ses2.spu_name,
            ses2.third_order_price  as salePrice,
            ses2.expect_list_month ,
            ses2.tag_name,
            seadv.estimate_quantity estimateCount,
            seadv.audit_quantity auditCount
        from
            sfa_estimate_approval_v2 seav
        inner join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id  = seav.approval_id
        and seadv.delete_flag = 0 and seav.delete_flag = 0
        inner join ceo_business_organization cbo on cbo.organization_id = seav.organization_id
        inner join sfa_estimate_sku ses2 on ses2.sku  = seadv.sku
        and ses2.delete_flag  = 0  and ses2.business_group = cbo.business_group
        <where>
            and seav.approval_id = #{approvalId}
        </where>
    </select>



    <select id="selectEstimateSubmit" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateSubmitDTO">
        select
            ses.schedule_id,
            seav.approval_id ,
            ses.the_year_month  as yearMonth,
            ses.start_date ,
            ses.end_date ,
            ses.`type` ,
            fi.create_time  as submitTime,
            seav.estimate_price ,
            seav.audit_price ,
            sesp.name as shipPeriodName,
            sesp.batch_period ,
            sesp.delivery_deadline,
            fi.process_step ,
            fid.process_time as processTime,
            fi.result as processResult,
            fid.process_user_name,
            fid.process_role_id ,
            fid.organization_id,
            cbo.organization_type,
            cbov.virtual_area_name as vareaName,
            cbov.organization_name2 as companyName,
            fi.create_user_name as submitUserName,
            fr.process_name
        from sfa_estimate_schedule ses
        inner join sfa_estimate_ship_period sesp on sesp.id  = ses.ship_period_id
        and sesp.delete_flag  = 0 and sesp.status  = 1
        cross join ceo_business_organization cbo
        inner join ceo_business_organization_view cbov on cbov.organization_id  = cbo.organization_id
        inner join (
        select distinct schedule_id from sfa_estimate_schedule_organization_relation sesor where
        sesor.organization_id = #{companyCode} and sesor.delete_flag=0
        )s on s.schedule_id = ses.schedule_id

        left join sfa_estimate_approval_v2 seav on seav.schedule_id  = ses.schedule_id
        and seav.organization_id  = cbo.organization_id and seav.delete_flag = 0
        left join flow_instance fi on fi.instance_id  = seav.instance_id
        left join flow_instance_detail fid on fid.instance_id  = fi.instance_id
        <choose>

            <when test="processStep == 10 and flowCode == '**********' ">
                and fid.process_step = 1
            </when>
            <when test="processStep == 20 and flowCode == 'TS00000004' ">
                and fid.process_step = 1
            </when>

        </choose>
        left join flow_definition fd on fd.flow_id  = fi.flow_id
        left join flow_rule fr on fr.flow_id  = fd.flow_id
        and fr.step  = fi.process_step  and fr.delete_flag  = 0
        <where>
            and ses.the_year_month  >= date_format(now(),'%Y-%m')
            and cbo.organization_id = #{organizationId}
            -- 10.营业所审核 20.分公司审核 30.大区审核 40.营运审核 50.产销审核
            <choose>
                <when test="processStep == 10">
                    and cbo.organization_type  = 'department'
                </when>
                <otherwise>
                    and cbo.organization_type  = 'company'
                </otherwise>
            </choose>
        </where>
        order by ses.the_year_month ,ses.start_date ,ses.end_date ,cbo.organization_id ,fi.create_time
    </select>

    <sql id="estimateDetailSelect">
        <where>
            <if test="!request.allBusinessGroup">
                and cbov.business_group = #{request.businessGroup}
            </if>

            <if test="request.yearMonth != null and request.yearMonth != '' ">
                and seav.`month`  = #{request.yearMonth}
            </if>
            <if test="request.organizationIds != null and request.organizationIds.size() > 0 ">
                <choose>
                    <when test="request.organizationType == 'area'">
                        and cbov.organization_id3
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and cbov.virtual_area_id
                    </when>
                    <when test="request.organizationType == 'province'">
                        and cbov.province_id
                    </when>
                    <when test="request.organizationType == 'company'">
                        and cbov.organization_id2
                    </when>
                    <when test="request.organizationType == 'department'">
                        and cbov.department_id
                    </when>
                </choose>
                <foreach collection="request.organizationIds" item="item" open=" in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="request.storeName != null and request.storeName != '' ">
                and scsr.store_name = #{request.storeName}
            </if>

            <if test="request.shipPeriodId != null">
                and sesp.id = #{request.shipPeriodId}
            </if>

            <if test="request.submitLayer != null">
                <choose>
                    <when test="request.submitLayer == 10">
                        and cbov.organization_type = 'department'
                    </when>
                    <otherwise>
                        and cbov.organization_type = 'company'
                    </otherwise>
                </choose>
            </if>

            <choose>
                <when test="request.searchProcessType == 1">
                    and fi.process_step = 1 and fi.result = 0
                    and fd.flow_code = 'TS00000004'
                    and fi.result = 0
                </when>
                <when test="request.searchProcessType == 2">
                    and fi.process_step = 2 and fi.result = 0
                    and fd.flow_code = 'TS00000004'
                    and fi.result = 0
                </when>
                <when test="request.searchProcessType == 3">
                    and fi.result = 1
                </when>
                <when test="request.searchProcessType == 4">
                    and fi.result = 2
                </when>
            </choose>

            <if test="request.lineId != null and request.lineId !=  ''">
                and ses.line_id = #{request.lineId}
            </if>
            <if test="request.spu != null  and request.spu !=  '' ">
                and ses.spu = #{request.spu}
            </if>
            <if test="request.sku != null and request.sku !=  ''">
                and ses.sku = #{request.sku}
            </if>
            <if test="request.skuCode != null and request.skuCode !=  ''">
                and ses.sku = #{request.skuCode}
            </if>
            <if test="request.processResult != null">
                and fi.`result` = #{request.processResult}
            </if>
        </where>
    </sql>

    <select id="selectDetail" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateDetailDTO">
        select
            seav.approval_id ,
            seadv.detail_id,
            cbov.virtual_area_name as vareaName,
            cbov.organization_name2 as companyName,
            cbov.department_name ,
            fi.create_time as submitTime,
            seav.`type` ,
            ses.line_name,
            scsr.store_name ,
            ses.sku ,
            ses.sku_name ,
            ses.flavor ,
            ses.full_case_spec ,
            ifnull( seadv.estimate_quantity,0)  as estimateCount,
            ifnull( seadv.audit_quantity,0)  as auditCount,
            ifnull( seadv.estimate_quantity,0)  * ifnull( ses.third_order_price,0)  as estimatePrice,
            ifnull( seadv.audit_quantity ,0)  * ifnull( ses.third_order_price,0)  as auditPrice,
            fi.`result`,
            fi.process_step,
            fd.flow_code,
            sesp.name as shipPeriodName,
            sesp.delivery_deadline ,
            sesp.batch_period,
            sbg.business_group_name
        from
            sfa_estimate_approval_v2 seav
        inner join ceo_business_organization_view cbov on
        cbov.organization_id = seav.organization_id
        inner join sfa_business_group sbg on sbg.id = cbov.business_group
        and sbg.delete_flag = 0
        inner join sfa_estimate_schedule ss on ss.schedule_id  = seav.schedule_id
        and ss.delete_flag  = 0
        inner join sfa_estimate_ship_period sesp on sesp.id = ss.ship_period_id
        inner join sfa_estimate_approval_detail_v2 seadv  on seadv.approval_id  = seav.approval_id
        and seadv.delete_flag  = 0 and seav.delete_flag = 0
        inner join sfa_estimate_sku ses on ses.sku  = seadv.sku
        and ses.delete_flag  = 0 and ses.business_group = cbov.business_group
        inner join flow_instance fi on
        fi.instance_id = seav.instance_id
        inner join flow_definition fd on fd.flow_id = fi.flow_id
        inner join sfa_company_store_relation scsr on scsr.company_code  = cbov.organization_id2
        <include refid="estimateDetailSelect"></include>
    </select>

    <select id="selectDetailSummary" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateApprovalSummaryDTO">
        select
            sum(ifnull( seadv.estimate_quantity,0))  as totalEstimateQuantity,
            sum(ifnull( seadv.audit_quantity,0))  as totalAuditQuantity,
            sum(ifnull( seadv.estimate_quantity,0)  * ifnull( ses.third_order_price,0))  as totalEstimatePrice,
            sum(ifnull( seadv.audit_quantity ,0)  * ifnull( ses.third_order_price,0))  as totalAuditPrice
        from
        sfa_estimate_approval_v2 seav
        inner join ceo_business_organization_view cbov on
        cbov.organization_id = seav.organization_id
        inner join sfa_estimate_schedule ss on ss.schedule_id  = seav.schedule_id
        and ss.delete_flag  = 0
        inner join sfa_estimate_ship_period sesp on sesp.id = ss.ship_period_id
        inner join sfa_estimate_approval_detail_v2 seadv  on seadv.approval_id  = seav.approval_id
        and seadv.delete_flag  = 0 and seav.delete_flag = 0
        inner join sfa_estimate_sku ses on ses.sku  = seadv.sku
        and ses.delete_flag  = 0 and ses.business_group = cbov.business_group
        inner join flow_instance fi on
        fi.instance_id = seav.instance_id
        inner join flow_definition fd on fd.flow_id = fi.flow_id
        inner join sfa_company_store_relation scsr on scsr.company_code  = cbov.organization_id2
        <include refid="estimateDetailSelect"></include>
    </select>

    <select id="getStoreName" resultType="String">
        select scsr.store_name  from sfa_company_store_relation scsr
        inner join ceo_business_organization_view cbov  on cbov.organization_id2  = scsr.company_code
        and cbov.organization_id  = #{organizationId}
        limit 1
    </select>


    <select id="getEstimateProcessCount" resultType="int">
        select
            count(*)
        from
        sfa_estimate_approval_v2 seav
        inner join ceo_business_organization_view cbov on cbov.organization_id  = seav.organization_id
        and seav.delete_flag = 0
        inner join sfa_estimate_schedule ses on
        ses.schedule_id = seav.schedule_id
        inner join flow_instance fi on
        fi.instance_id = seav.instance_id
        inner join flow_definition fd on
        fd.flow_id = fi.flow_id
        inner join flow_instance_detail fid on
        fid.instance_id = fi.instance_id
        and fid.is_current = 1
        and fid.delete_flag = 0
        <where>
            and fi.`result`  = 0
            and fd.flow_code = #{flowCode}
            and ses.start_date &lt;= date_format(now(),'%Y-%m-%d')
            and ses.end_date  >= date_format(now(),'%Y-%m-%d')
            and (
            <!-- 组织选择 -->
            <choose>
                <when test="orgType == 'department'">
                    cbov.department_id
                </when>
                <when test="orgType == 'company'">
                    cbov.organization_id2
                </when>
                <when test="orgType == 'province'">
                    cbov.province_id
                </when>
                <when test="orgType == 'varea'">
                    cbov.virtual_area_id
                </when>
                <otherwise>
                    cbov.organization_id3
                </otherwise>
            </choose>
            <foreach collection="orgCodes" item="item" open=" in (" close=")" separator=",">
                #{item}
            </foreach>
            <!-- 角色 -->
            or
            <foreach collection="roleIds" item="item" open=" fid.process_role_id  in (" close=")" separator=",">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="getSubmitCount" resultType="int">
        select count(1) from sfa_estimate_schedule ses
        inner join sfa_estimate_schedule_organization_relation sesor on sesor.schedule_id  = ses.schedule_id
        and sesor.delete_flag  = 0
        left join sfa_estimate_approval_v2 seav on seav.schedule_id  = ses.schedule_id
        and seav.delete_flag  = 0 and sesor.organization_id  = sesor.organization_id
        <foreach collection="orgCodes" item="item" open=" and seav.organization_id in  (" close=")" separator=",">
            #{item}
        </foreach>
        <where>
            and ses.start_date &lt;= date_format(now(),'%Y-%m-%d')
            and ses.end_date  >= date_format(now(),'%Y-%m-%d')
            and ses.schedule_id is not null and seav.approval_id  is null
        </where>
    </select>


    <select id="selectMOQ" resultType="com.wantwant.sfa.backend.estimate.vo.EstimateMOQVO">
        select
        case ses2.`type` when 1 then '常规品项' when 2 then '综合品项' when 3 then '集团经典品项' end as `typeName`,
        ses2.line_name,
        ses2.spu_name,
        ses2.sku,
        ses2.sku_name,
        ses2.flavor,
        ses2.full_case_spec,
        ifnull(ses2.MOQ,0) MOQ,
        sum(ifnull(seadh.estimate_quantity,0)) + sum(ifnull(seadh.append_quantity,0)) as totalBox,
        ifnull(ses2.MOQ,0) - (sum(ifnull(seadh.estimate_quantity,0)) + sum(ifnull(seadh.append_quantity,0))) as diff,
        ifnull(sea.status,0) status
        from  sfa_estimate_approval_detail_history seadh
        inner join ceo_business_organization cbo on cbo.organization_id = seadh.organization_id
        and cbo.organization_type = 'company'
        inner join sfa_estimate_ship_period sesp on sesp.id  = seadh.ship_period_id
        and sesp.status  = 1 and sesp.delete_flag  = 0
        inner join sfa_estimate_sku ses2  on ses2.business_group = cbo.business_group
        and ses2.delete_flag  = 0 and seadh.sku = ses2.sku
        left join sfa_estimate_adjust sea on sea.sku = ses2.sku
        and sea.`month` = seadh.the_year_month
        and sea.ship_period_id = seadh.ship_period_id
        and sea.delete_flag = 0
        and sea.is_current  = 1

        <where>
            and seadh.ship_period_id  = #{req.shipPeriodId}
            and seadh.the_year_month  = #{req.yearMonth}
            <if test="req.lineId != null and req.lineId != ''">
                and ses2.line_id = #{req.lineId}
            </if>
            <if test="req.spu != null and req.spu != ''">
                and ses2.spu = #{req.spu}
            </if>
            <if test="req.sku != null and req.sku != ''">
                and ses2.sku = #{req.sku}
            </if>
            <if test="req.skuCode != null and req.skuCode != ''">
                and ses2.sku = #{req.skuCode}
            </if>
            <if test="req.status != null ">
                <choose>
                    <when test="req.status  == 0">
                        and (sea.status = #{req.status} or sea.adjust_id is null )
                    </when>
                    <otherwise>
                        and sea.status = #{req.status}
                    </otherwise>
                </choose>

            </if>
        </where>

        group by ses2.sku,ses2.MOQ
        <!-- 增加预定单，去除该逻辑
        <if test="req.underMOQFlag != null and req.underMOQFlag == 1">
            having  ifnull(ses2.MOQ,0) - (sum(ifnull(normal.quantity,0)) + sum(ifnull(append.quantity,0))) > 0
            and  sum(ifnull(normal.quantity,0)) + sum(ifnull(append.quantity,0)) > 0
        </if>
        -->
        order by ses2.type,ses2.line_id,ses2.spu,ses2.sku
    </select>

    <select id="selectMOQDetail" resultType="com.wantwant.sfa.backend.estimate.vo.MOQSkuVO">
        <!-- 常规提报 -->
        select
        organization_id,
        businessGroupName,
        areaName,
        vareaName,
        province_name ,
        companyName,
        submitTime,
        typeStr,
        type,
        store_name,
        estimateBox ,
        auditBox
        from
        (
        select
        organization_id,
        businessGroupName,
        areaName,
        vareaName,
        province_name ,
        companyName,
        submitTime,
        typeStr,
        type,
        store_name,
        estimateBox ,
        auditBox
        from (
        select
        seav.organization_id,
        sbg.business_group_name as businessGroupName,
        cbov.organization_name3 as areaName,
        cbov.virtual_area_name as vareaName,
        cbov.province_name as province_name ,
        cbov.organization_name2 as companyName,
        date_format(seav.create_time,'%Y-%m-%d %H:%i' ) as submitTime,
        '常规提报' as typeStr,
        ses.type,
        scsr.store_name ,
        seadv.estimate_quantity as estimateBox,
        seadv.audit_quantity as auditBox
        from sfa_estimate_approval_v2 seav
        inner join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id = seav.approval_id
        and seadv.delete_flag  = 0
        inner join sfa_estimate_schedule ses on ses.schedule_id = seav.schedule_id
        and ses.delete_flag = 0
        inner join sfa_estimate_ship_period sesp on sesp.id = ses.ship_period_id
        and sesp.delete_flag = 0 and sesp.status = 1
        inner join flow_instance fi on fi.instance_id = seav.instance_id
        inner join flow_definition fd on fd.flow_id = fi.flow_id
        and fd.flow_code = 'TS00000004'
        and fi.process_step = 2 and fi.`result` = 1
        inner join ceo_business_organization_view cbov on cbov.organization_id = seav.organization_id
        inner join sfa_business_group sbg on sbg.id = cbov.business_group
        inner join sfa_company_store_relation scsr on scsr.company_code = cbov.organization_id2
        <where>
            and seav.`month` = #{yearMonth }
            and sesp.id = #{shipPeriodId}
            and seadv.sku = #{sku}
            and ses.type = 1
        </where>
            order by seav.approval_id desc
        ) e1
        group by organization_id,type
        ) normal

        union all
        (
        select
        seav.organization_id,
        sbg.business_group_name as businessGroupName,
        cbov.organization_name3 as areaName,
        cbov.virtual_area_name as vareaName,
        cbov.province_name as province_name ,
        cbov.organization_name2 as companyName,
        date_format(seav.create_time,'%Y-%m-%d %H:%i' ) as submitTime,
        '追加提报' as typeStr,
        ses.type,
        scsr.store_name ,
        seadv.estimate_quantity as estimateBox,
        seadv.audit_quantity as auditBox
        from sfa_estimate_approval_v2 seav
        inner join sfa_estimate_approval_detail_v2 seadv on seadv.approval_id = seav.approval_id
        inner join sfa_estimate_schedule ses on ses.schedule_id = seav.schedule_id
        and ses.delete_flag = 0
        inner join sfa_estimate_ship_period sesp on sesp.id = ses.ship_period_id
        and sesp.delete_flag = 0 and sesp.status = 1
        inner join flow_instance fi on fi.instance_id = seav.instance_id
        inner join flow_definition fd on fd.flow_id = fi.flow_id
        and fd.flow_code = 'TS00000004'
        and fi.process_step = 2 and fi.`result` = 1
        inner join ceo_business_organization_view cbov on cbov.organization_id = seav.organization_id
        inner join sfa_business_group sbg on sbg.id = cbov.business_group
        inner join sfa_company_store_relation scsr on scsr.company_code = cbov.organization_id2
        <where>
            and seav.`month` = #{yearMonth}
            and sesp.id = #{shipPeriodId}
            and seadv.
            sku = #{sku}
            and ses.type = 2
        </where>
        )
        <!-- 追加提报 -->
    </select>


    <select id="selectHistoryBySku" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateHistoryDTO">
        select seadh.the_year_month  ,
               seadh.sku,
               sum(seadh.estimate_quantity)+ sum(seadh.append_quantity)  as estimateCount
        from sfa_estimate_approval_detail_history seadh
        where the_year_month  =  #{theYearMonth}
        and seadh.delete_flag = 0
        and seadh.organization_id  = #{organizationId}
        and (
        <foreach collection="skuList" item="sku" separator="or" open="" close="">
            sku = #{sku}
        </foreach>
        )
        group by seadh.sku
    </select>

    <select id="selectHistoryByOrgCode" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateHistoryDTO">
        select seadh.the_year_month  ,
               seadh.ship_period_id,
        seadh.sku,
        sum(seadh.estimate_quantity)+ sum(seadh.append_quantity)  as estimateCount
        from sfa_estimate_approval_detail_history seadh
        where the_year_month  =  #{month}
        and seadh.delete_flag = 0
        and seadh.organization_id  = #{organizationId}
        group by seadh.sku,seadh.ship_period_id
    </select>


    <select id="selectHistoryByResult" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.EstimateSummaryDTO">
        select seadh.the_year_month  as yearMonth,
               seadh.sku,
               seadh.organization_id,
               sum(ifnull( seadh.estimate_quantity,0)) + sum(ifnull(seadh.append_quantity,0)) as  totalCount,
               (sum(ifnull( seadh.estimate_quantity,0)) + sum(ifnull(seadh.append_quantity,0))) * seadh.sale_price as totalPrice
        from  sfa_estimate_approval_detail_history seadh
        <where>
            <foreach collection="list" item="item" open="(" close=")" separator="or">
                <!-- 上月数据 -->
                (
                    (
                    seadh.the_year_month = #{item.lastMonth}
                    and seadh.organization_id = #{item.organizationId}
                    and seadh.sku = #{item.sku}
                    )
                    or
                    (
                    seadh.the_year_month = #{item.lastLastMonth}
                    and seadh.organization_id = #{item.organizationId}
                    and seadh.sku = #{item.sku}
                    )
                )
            </foreach>
            and seadh.delete_flag = 0
        </where>
        group by seadh.the_year_month,seadh.organization_id,seadh.sku
    </select>



    <select id="selectMOQByResult" resultType="com.wantwant.sfa.backend.domain.estimate.repository.dto.MOQ">
        select
        sku ,
        sum(estimate_quantity) + sum(append_quantity) currentMOQ,
        seadh.the_year_month,
        seadh.organization_id
        from
        sfa_estimate_approval_detail_history seadh
        inner join ceo_business_organization cbo on cbo.organization_id = seadh.organization_id

        <where>
            <foreach collection="list" item="item" open="(" close=")" separator="or">
                (
                seadh.the_year_month = #{item.yearMonth}
                and seadh.organization_id = #{item.companyCode}
                and seadh.sku = #{item.sku}
                )
            </foreach>
            and seadh.delete_flag = 0
        </where>
        group by
        the_year_month,sku
    </select>


    <select id="selectLastSubmitPO" resultType="com.wantwant.sfa.backend.domain.estimate.repository.po.EstimateApprovalDetailPO">
        select seadv.* from sfa_estimate_approval_detail_v2 seadv
        inner join sfa_estimate_approval_v2 seav on seadv.approval_id  = seav.approval_id
        and seav.schedule_id  = #{scheduleId}
        and seav.organization_id  = #{organizationId}
        and seadv.delete_flag = 0
    </select>

    <select id="selectSubmitPrice" resultType="com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO">
        SELECT
            seadv.sku,
            seadv.sale_price,
            if(fi.process_step  = 1 and fi.result = 0,seadv.estimate_quantity,seadv.audit_quantity) as auditCount
        FROM
            sfa_estimate_approval_v2 seav
            INNER JOIN sfa_estimate_approval_detail_v2 seadv ON seadv.approval_id = seav.approval_id
            INNER JOIN flow_instance fi ON fi.instance_id = seav.instance_id
        <where>
            <!-- 查找大区审核通过的以及产销未审核的 -->
            AND ((fi.process_step  = 1 and fi.result in (0,1)) or (fi.process_step  = 2 and fi.result = 0))
            AND seav.month = #{theYearMonth}
            AND seav.organization_id = #{organizationId}
            AND seadv.delete_flag = 0
            AND seav.delete_flag = 0
            <foreach collection="skuList" item="sku" separator="," open=" and seadv.sku in(" close=")">
                #{sku}
            </foreach>
            <if test="approvalId != null">
                and seav.approval_id != #{approvalId}
            </if>
        </where>

    </select>

    <select id="selectFinishedBySku" resultType="com.wantwant.sfa.backend.domain.estimate.DO.EstimateApprovalDetailDO">
        SELECT
            sku,
            estimate_quantity as auditCount,
            sale_price
        FROM
            sfa_estimate_approval_detail_history
        <where>
            and delete_flag = 0
            and the_year_month = #{theYearMonth}
            and organization_id = #{organizationId}
            <foreach collection="skuList" item="sku" separator="," open=" and sku in(" close=")">
                #{sku}
            </foreach>

        </where>
    </select>
</mapper>
