<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wantwant.sfa.backend.mapper.SfaEmployeeInfoMapper">

    <select id="zwInterviewList" resultType="com.wantwant.sfa.backend.zw.vo.ZWEmployeeInfoVo">
        SELECT r.id id, DATE_FORMAT(m.apply_time,'%Y-%m-%d') applyTime, m.source source, m.area area, m.varea_organization_name as varea, m.province_organization_name as province, m.company company,m.branch_organization_name departmentName,
        m.user_name userName, m.user_mobile userMobile, m.employ_id employeeId, m.ceo_flag ceoFlag, m.jobs_type
        jobsType,

        case when m.ceo_type = 1 and m.position = 1 and m.jobs_type = 1 then 1
        when  m.ceo_type = 1 and m.position = 1 and m.jobs_type = 2 then 2
        when  m.ceo_type = 3 and m.position = 1 then 3
        when  m.ceo_type = 2 and m.position = 1 then 4
        when  m.position = 4 then 5
        when  m.position = 2 then 6
        when  m.ceo_type = 6 and m.position = 7 and m.jobs_type = 1 then 8
        when  m.ceo_type = 7 and m.position = 7 then 9
        when  m.ceo_type = 6 and m.position = 7 and m.jobs_type = 2 then 10
        when  m.position = 3 then 12
        when  m.position = 5 then 13
        when  m.position = 6 then 14
        when  m.position = 8 then 15
        end as `position`,


        m.agent_province agentProvince, m.agent_city agentCity, m.agent_district agentDistrict, m.superior_employ_id
        superiorEmployId,
        m.superior_name superiorName, m.superior_mobile superiorMobile, p.process_result processResult,
        m.resume_url resumeUrl, m.ww_position wwPosition, DATE_FORMAT(p.interview_time,'%Y-%m-%d') interviewTime,
        TIMESTAMPDIFF(DAY,m.created_time,now()) as interviewDate,
        m.superior_company superiorCompany,
        r.branch_name branch,
        case m.registration_source
        when '1' then '睿秉'
        when '2' then '英格瑪'
        when '3' then '卡思'
        when '4' then '宣导组'
        when '5' then '旺旺'
        when '6' then '魔方'
        when '7' then '易才'
        when '8' then '華服'
        when '9' then '大瀚'
        when '10' then '合伙人推荐'
        when '11' then '安可人力'
        when '12' then '人瑞'
        when '13' then '万宝盛华'
        when '14' then '斗米'
        when '15' then '杰博'
        when '16' then 'CDP'
        else '全渠道' end
        as distributionChannels,
        case m.gender
        when '0' then '未知'
        when '1' then '男'
        when '2' then '女'
        else '未知' end
        as gender,
        p.reports_name as reportsName,
        ifnull(r.attitude_intention,0)+ifnull(r.experience_ability,0)+ifnull(r.customer_resources,0)
        as reportsTotalScore,
        r.attitude_intention as reportsAttitudeIntention,
        r.experience_ability as reportsExperienceAbility,
        r.customer_resources as reportsCustomerResources,
        p.superior_score_name as superiorScoreName,
        ifnull(p.superior_attitude_intention,0)+ifnull(p.superior_experience_ability,0)+ifnull(p.superior_customer_resources,0)
        as superiorScoreTotalScore,
        p.superior_attitude_intention as superiorAttitudeIntention,
        p.superior_experience_ability as superiorExperienceAbility,
        p.superior_customer_resources as superiorCustomerResources,
        r.company_code as organizationCompanyId,
        m.position as organizationType,
        r.pending_reason as pendingReason,
        p.process_type as processType,
        case when p.process_type = 1 and p.process_result = 3 then '面试中'
        when p.process_type = 2 and p.process_result in (0,3) and m.position not in (3,5) then '复试中'
        when p.process_type in (2,4,5) and p.process_result in (0,3) and m.position in (3,5) then '审核中'
        end as statusName,
        m.residence_years as residenceYears,
        m.sales_years as salesYears,
        m.management_experience_years as managementExperienceYears,
        TIMESTAMPDIFF(YEAR,m.birth_date,DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')) AS age

        from sfa_apply_member m
        left join sfa_interview_process p on p.application_id = m.id
        left join sfa_interview_process_record r on r.id = p.interview_record_id
        <include refid="where_fragment"/>
        <choose>
            <when test="null != orderName">
                order by CONVERT(${orderName}, decimal(5,2))
            </when>
            <otherwise>
                order by m.apply_time
            </otherwise>
        </choose>
        <choose>
            <when test="orderType == 1">
                asc
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>

    </select>

    <select id="zwInterviewListCount" resultType="int">
        SELECT count(1)
        from sfa_apply_member m
        left join sfa_interview_process p on p.application_id = m.id
        left join sfa_interview_process_record r on r.id = p.interview_record_id
        <include refid="where_fragment"/>
    </select>

    <sql id="where_fragment">
        <if test="empKey != null and empKey != ''">
            and (m.user_name like concat('%',#{empKey},'%') or m.user_mobile like concat('%',#{empKey},'%'))

        </if>

        <if test="superiorKey != null and superiorKey != '' ">
            and (m.superior_name like concat('%',#{superiorKey},'%') or m.superior_mobile like concat('%',#{superiorKey},'%'))

        </if>
        <if test="endDate != null and endDate.length()>0">
            <bind name="endDate" value="endDate +' 23:59:59'"/>
        </if>

        <where>
            and m.position != 1

            <if test="ceoFlag != null">and m.ceo_flag = #{ceoFlag}</if>
            <if test="jobsType != null">and m.jobs_type = #{jobsType}</if>
            <if test="source != null">and m.source = #{source}</if>
            <if test="position != null">
                <choose>

                    <when test="position == 1">
                        and m.ceo_type = 1 and m.position = 1 and m.jobs_type = 1
                    </when>
                    <when test="position == 2">
                        and m.ceo_type = 1 and m.position = 1 and m.jobs_type = 2
                    </when>
                    <when test="position == 3">
                        and m.ceo_type = 3 and m.position = 1
                    </when>
                    <when test="position == 4">
                        and m.ceo_type = 2 and m.position = 1
                    </when>
                    <when test="position == 5">
                        and m.position = 4
                    </when>
                    <when test="position == 6">
                        and m.position = 2
                    </when>
                    <when test="position == 7">
                        and m.position = 1
                    </when>
                    <when test="position == 8">
                        and m.ceo_type = 6 and m.position = 7 and m.jobs_type = 1
                    </when>
                    <when test="position == 9">
                        and m.ceo_type = 7 and m.position = 7
                    </when>
                    <when test="position == 10">
                        and m.ceo_type = 6 and m.position = 7 and m.jobs_type = 2
                    </when>
                    <when test="position == 11">
                        and m.position = 7
                    </when>
                    <when test="position == 12">
                        and m.position = 3
                    </when>

                    <when test="position == 13">
                        and m.position = 5
                    </when>
                    <when test="position == 14">
                        and m.position = 6
                    </when>
                    <when test="position == 15">
                        and m.position = 8
                    </when>
                </choose>
            </if>

            <if test="beginDate != null and beginDate.length()>0">and m.apply_time &gt;= #{beginDate}</if>
            <if test="endDate != null and endDate.length()>0">and m.apply_time &lt;= #{endDate}</if>
            <if test="superiorCompany != null and superiorCompany.length()>0">and m.superior_company =
                #{superiorCompany}
            </if>

            <if test="null != areaOrganizationIds and areaOrganizationIds.size()>0 ">
                <foreach collection="areaOrganizationIds" open=" and area_organization_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="null != vareaOrganizationIds and vareaOrganizationIds.size()>0  ">
                <foreach collection="vareaOrganizationIds" open=" and varea_organization_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="null != provinceOrganizationIds and provinceOrganizationIds.size()>0  ">
                <foreach collection="provinceOrganizationIds" open=" and province_organization_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="null != companyOrganizationIds and companyOrganizationIds.size()>0  ">
                <foreach collection="companyOrganizationIds" open=" and company_organization_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>

            <if test="null != departmentIds  and departmentIds.size()>0  ">
                <foreach collection="departmentIds" open=" and branch_organization_id in (" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="null != organizationIds  and organizationIds.size()>0  ">
                <foreach collection="organizationIds" item="item" open=" and r.organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and r.process_user_id = #{empId}

            <if test="type != null">
                <choose>
                    <when test="type == 1">
                        and (
                                (p.process_type = 1 and p.process_result = 0)
                                or
                                (p.process_type in (2,4,5) and m.position in (2,6) and p.process_result = 0)
                                or
                                (p.process_type in (1,2)  and m.position in (3,4,5) and p.process_result = 0)
                                or
                                (p.process_type = 2  and m.position in (1,7,8) and p.process_result = 0)
                        )
                    </when>
                    <when test="type == 2">
                        and (
                                (p.process_type = 1 and p.process_result = 3)
                                or
                                (p.process_type in (2,4,5) and m.position in (2,6) and p.process_result = 3)
                                or
                                (p.process_type in (1,2)  and m.position in (3,4,5) and p.process_result = 3)
                                or
                                (p.process_type = 2 and m.position in (2,4) and p.process_result = 3)
                                or
                                (p.process_type = 2  and m.position in (1,7,8) and p.process_result = 3)

                            )
                    </when>
                </choose>
            </if>

            <if test="pendingReasonType != null">
                and r.pending_reason_type = #{pendingReasonType}
            </if>

        </where>
    </sql>


    <select id="zwEnrollmentApplicationAndReviewListCount"
            parameterType="com.wantwant.sfa.backend.zw.request.ZWDetailedInfoRequest"
            resultType="int">
        SELECT
            count(distinct p.id)
        FROM
            sfa_interview_process p
            INNER JOIN sfa_interview_process_record r ON p.interview_record_id = r.id
        WHERE
            p.process_type = 4
            AND p.process_result IN ( 0, 3 )
            AND r.process_user_id = #{empId}
            <if test="null != organizationIds  and organizationIds.size()>0  ">
                <foreach collection="organizationIds" item="item" open=" and r.organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="selectOnboardCount" resultType="int">
        select count(1) from sfa_employee_info sei
        inner join sfa_apply_member a on sei.application_id = a.id
        <where>
            and sei.employee_status = 2
            <choose>
                <when test="position == 1">
                    and(
                    (
                        a.position = 1
                        and sei.company_code = #{orgCode}
                        and sei.post_type = 1
                    )
                    or
                    (
                        a.ceo_type = 3
                        and sei.company_code = #{orgCode}
                    ))
                </when>
                <when test="position == 4">
                    and a.position = 4
                    and sei.company_code = #{orgCode}
                </when>
                <when test="position == 7">
                    and sei.department_code = #{orgCode}
                    and a.position = 7
                    and ((a.ceo_type = 6 and a.jobs_type = 1) or (a.ceo_type = 7))

                </when>
            </choose>
        </where>

    </select>



    <select id="selectMobileByOrg" resultType="String">
        select e.mobile from ceo_business_organization_position_relation cbopr
        inner join sfa_employee_info e on cbopr.position_id = e.position_id
        where cbopr.organization_id = #{organizationId} and cbopr.channel = #{channel}
        and e.employee_status = 2
        limit 1
    </select>



    <select id="selectRecommendCeoList" resultType="com.wantwant.sfa.backend.employeeInfo.model.RecommendModel">
        SELECT
            sam.id as applyId,
            sam.user_name as name,
            sam.user_mobile as mobileNumber,
            sei.onboard_time as onboardTime,
            sam.apply_time as registrationTime,
            sip.process_type,
            sip.process_result
        FROM
            sfa_apply_member sam
            INNER JOIN sfa_interview_process sip ON sip.application_id = sam.id
            LEFT JOIN sfa_employee_info sei ON sei.application_id = sam.id
        where sam.superior_mobile = #{mobile}
        order by sam.apply_time desc
    </select>


    <update id="updateByApplicationId">
        update sfa_employee_info
        <trim prefix="set" suffixOverrides=",">
            <if test="params.province!=null and params.province!=''">province = #{params.province},</if>
            <if test="params.city!=null and params.city!=''">city = #{params.city},</if>
            <if test="params.district!=null and params.district!=''">district = #{params.district},</if>
            update_time = now()
        </trim>
        where application_id = #{params.applicationId}
    </update>

    <select id="selectRecommendCeoDetail" resultType="com.wantwant.sfa.backend.employeeInfo.model.RecommendModel">
        SELECT
            sam.user_name as name,
            sam.user_mobile as mobileNumber,
            sei.onboard_time as onboardTime,
            sam.gender as sex,
            sam.industry,
            sam.sales_years as salesExperience,
            sam.customer_resources as customerResources,
            sam.remark as personalDescription,
            sip.process_type,
            sip.process_result,
            sei.id as employeeId,
            concat(sam.agent_province,sam.agent_city,sam.agent_district) as territory,
            sam.position as `position`,
            sam.jobs_type as jobsType,
            sei.type as `type`
        FROM
            sfa_apply_member sam
            INNER JOIN sfa_interview_process sip ON sip.application_id = sam.id
            LEFT JOIN sfa_employee_info sei ON sei.application_id = sam.id
        where sam.id = #{applyId}
        limit 1
    </select>


    <select id="selectEmployeeInfoByMemberKey" resultType="com.wantwant.sfa.backend.employeeInfo.model.EmployeeInfoModel">
        SELECT
            sei.area_name,
            sei.area_code,
            sei.company_code,
            sei.company_name,
            sei.employee_name,
            sei.branch_code,
            sei.branch_name,
            sei.department_code,
			sei.department_name,
            sei.mobile,
            sam.position,
            sam.jobs_type,
            sei.type,
            sei.onboard_time,
            cbopr.employee_id,
            sei.joining_company as joinCompany,
            sei.type,
            sei.position_id
        FROM
            sfa_employee_info sei
            INNER JOIN sfa_customer sc ON sei.position_id = sc.position_id
            AND sc.mobile_number = sei.mobile
            INNER JOIN sfa_apply_member sam ON sei.application_id = sam.id
            INNER JOIN ceo_business_organization_position_relation cbopr on cbopr.position_id = sc.position_id
        where sc.memberKey = #{memberKey}
        limit 1
    </select>


    <select id="getMarketVisitCount" resultType="int">
        select count(1) from sfa_interview_process p
        inner join sfa_interview_process_record r on p.interview_record_id = r.id
        inner join sfa_apply_member a on p.application_id = a.id
        where p.process_type = 2
        and a.ceo_type = 1 and a.position = 4
        and p.process_result in (0,3)
        and r.process_user_id = #{empId}
        <if test="null != organizationIds  and organizationIds.size()>0  ">
            <foreach collection="organizationIds" item="item" open=" and r.organization_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCeoEmployeeInfo" resultType="com.wantwant.sfa.backend.employee.vo.CeoEmployeeInfoVo">
        SELECT
            <choose>
                <when test="positionType == 3">
                    cbopr.employee_name  as userName,
                    o.organization_name as areaName
                    from ceo_business_organization_position_relation cbopr
                    inner join ceo_business_organization o on o.organization_id = cbopr.organization_id
                    where cbopr.position_type_id = 1 and cbopr.channel = 3
                    and cbopr.employee_id is not null and cbopr.employee_id != ''
                    <if test="userName != null and userName != '' ">
                        and cbopr.employee_name like concat('%',#{userName},'%')
                    </if>
                </when>
                <otherwise>
                    sei.employee_name as userName,
                    sei.mobile as userMobile,
                    sei.area_name,
                    sei.company_name,
                    sei.branch_name
                    FROM
                    sfa_employee_info sei
                    INNER JOIN sfa_apply_member a ON sei.application_id = a.id
                    WHERE
                    sei.employee_status IN ( 1, 2 )
                    AND a.position = #{positionType}
                    <if test="userName != null and userName != '' ">
                        and sei.employee_name like concat('%',#{userName},'%')
                    </if>
                    <if test="userMobile != null and userMobile != '' ">
                        and sei.mobile like concat('%',#{userMobile},'%')
                    </if>
                </otherwise>
            </choose>
        limit #{offset},#{limit}
    </select>

    <select id="selectCeoEmployeeInfoCount" resultType="int">
        SELECT
        <choose>
            <when test="positionType == 3">
                count(1)
                from ceo_business_organization_position_relation cbopr
                inner join ceo_business_organization o on o.organization_id = cbopr.organization_id
                where cbopr.position_type_id = 1 and cbopr.channel = 3
                and cbopr.employee_id is not null and cbopr.employee_id != ''
                <if test="userName != null and userName != '' ">
                    and cbopr.employee_name like concat('%',#{userName},'%')
                </if>
            </when>
            <otherwise>
                count(1)
                FROM
                sfa_employee_info sei
                INNER JOIN sfa_apply_member a ON sei.application_id = a.id
                WHERE
                sei.employee_status IN ( 1, 2 )
                AND a.position = #{positionType}
                <if test="userName != null and userName != '' ">
                    and sei.employee_name like concat('%',#{userName},'%')
                </if>
                <if test="userMobile != null and userMobile != '' ">
                    and sei.mobile like concat('%',#{userMobile},'%')
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="selectTransferEmployeeInfoByCardNo" resultType="com.wantwant.sfa.backend.interview.model.TransferEmployeeInfoModel">
        select sc.memberKey,sei.mobile,sei.branch_code as organizationId
        from sfa_apply_member a inner join sfa_employee_info sei
        on a.id = sei.application_id
        inner join sfa_customer sc on sei.position_id = sc.position_id
        where a.position = 1 and a.idCard_num = #{idCardNum}
    </select>

    <select id="getCompanyEmployeeInfoCountByParams" resultType="com.wantwant.sfa.backend.model.SfaEmployeeInfoModel">
        SELECT
            i.id,i.area_name, i.company_name,i.department_name,i.onboard_time
        FROM
            sfa_employee_info i
                INNER JOIN sfa_apply_member m ON m.id = i.application_id
        WHERE
            m.position in (2,3,4,5,6)
        <if test="companyName != null and companyName != '' ">
            AND i.company_name = #{companyName}
        </if>
        <if test="departmentName != null and departmentName != '' ">
            and i.department_name = #{departmentName}
        </if>
        <if test="employeeName != null and employeeName != '' ">
            and i.employee_name = #{employeeName}
        </if>

    </select>

    <select id="selectOverPerson" resultType="java.lang.String">
        SELECT
        distinct sei.member_key
        FROM
        sfa_position_relation   pr
        INNER JOIN sfa_employee_info sei ON pr.employee_info_id = sei.id
        WHERE
        pr.STATUS = 1
        AND pr.delete_flag =0

        <choose>
            <when test="params.organizationType == 'area'">
                AND pr.area_code IN
            </when>
            <when test="params.organizationType == 'varea'">
                AND pr.varea_code IN
            </when>
            <when test="params.organizationType == 'province'">
                AND pr.province_code IN
            </when>
            <when test="params.organizationType == 'company'">
                AND pr.company_code IN
            </when>
            <when test="params.organizationType == 'department'">
                AND pr.department_code IN
            </when>
            <when test="params.organizationType == 'branch'">
                AND pr.branch_code IN
            </when>
        </choose>

        <if test="params.deOrganizationIds != null and params.deOrganizationIds.size() > 0">
            <foreach collection="params.deOrganizationIds" item="organizationId" index="index" separator="," open="(" close=")">
                #{organizationId}
            </foreach>
        </if>
    </select>
    <select id="queryPartnerVo" resultType="com.wantwant.sfa.backend.vo.PartnerVo">
        SELECT
            ei.area_name AS regionOffice,
            ei.company_name  AS branchOffice,
            ei.department_name AS departMentName,
            ei.employee_name AS partnerName,
            sc.memberKey AS memberKey
        FROM
            sfa_customer sc
                INNER JOIN sfa_employee_info ei ON sc.position_id = ei.position_id
                AND sc.mobile_number = sc.mobile_number
        AND sc.memberKey in
        <foreach collection="params" item="memberKe" open="(" close=")" separator=",">
            #{memberKe}
        </foreach>
        GROUP BY
        sc.memberKey
    </select>


    <select id="selectJoiningCompany" resultType="com.wantwant.sfa.backend.interview.dto.JoiningCompanyConfigDto">
        SELECT DISTINCT
            sjcc.dept_id,
            d.dept_name,
            sjcc.contract_company
        FROM
            `hotkidceo_open_server`.sfa_join_company_config sjcc
            INNER JOIN `hotkidceo_open_server`.sys_dept d on d.dept_id = sjcc.dept_id
        WHERE
            sjcc.company_code = #{companyCode}

            AND sjcc.position = #{position}

            AND sjcc.delete_flag = 0
    </select>


    <select id="checkJoiningCompany" resultType="int">
        select count(1) from hotkidceo_open_server.sys_dept
        where dept_name = #{joiningCompany}
        and del_flag = 0
    </select>


    <select id="checkDuplicate" resultType="com.wantwant.sfa.backend.interview.model.DuplicateModel">
        SELECT
            a.id  as applyId,
            a.user_name as employeeName,
            a.user_mobile as employeeMobile,
            sei.employee_status,
            r.id  as interviewRecordId
        FROM
            sfa_apply_member a
            LEFT JOIN sfa_employee_info sei ON sei.application_id = a.id
            INNER JOIN sfa_interview_process p ON p.application_id = a.id
            INNER JOIN sfa_interview_process_record r ON r.id = p.interview_record_id
        WHERE
            a.idCard_num = #{idCardNum}
            AND a.id != #{applyId}
    </select>

    <select id="selectSgEmployee" resultType="com.wantwant.sfa.backend.model.WeeklyEmployeeSnapshotPO">
        select sei.employee_name,sei.mobile,sei.area_code,sei.area_name,sei.company_code,sei.company_name,
        sei.department_code,sei.department_name,sei.branch_code,sei.branch_name,1 as state,spr.business_group,
        sei.province_organization_id as provinceId,sei.province_organization_name as provinceName,sei.varea_organization_id as virtualAreaId,sei.varea_organization_name as virtualAreaName
        from sfa_apply_member sam
        INNER JOIN sfa_interview_process sip on sam.id = sip.application_id
        INNER JOIN sfa_employee_info sei on sam.id = sei.application_id
        INNER JOIN sfa_position_relation spr on spr.employee_info_id = sei.id
        and spr.status = 1 and spr.delete_flag = 0
		where (sip.process_type = 3 AND sip.process_result = 3)
    </select>

    <select id="selectToBeEmployee" resultType="com.wantwant.sfa.backend.model.WeeklyEmployeeSnapshotPO">
        SELECT sam.area as areaName,sam.area_organization_id as areaCode,
        sam.company as companyName,sam.company_organization_id as companyCode,
        sei.department_code,sei.department_name,sei.branch_code,sei.branch_name,
        sam.user_name as employeeName,sam.user_mobile as mobile,2 as state,spr.business_group,
        sei.province_organization_id as provinceId,sei.province_organization_name as provinceName,sei.varea_organization_id as virtualAreaId,sei.varea_organization_name as virtualAreaName
        from sfa_apply_member sam
        INNER JOIN sfa_interview_process sip on sam.id = sip.application_id
        INNER JOIN sfa_employee_info sei on sam.id = sei.application_id
        INNER JOIN sfa_position_relation spr on spr.employee_info_id = sei.id
        and spr.status = 1 and spr.delete_flag = 0
        where (sip.process_type in (4,5,6) and sip.process_result in (0,3)) or (sip.process_type = 9 AND sip.process_result = 0)
        and sam.position in (1,2,4)
    </select>


    <select id="onBoardCount" resultType="int">
        select count(1) from sfa_position_relation where position_type_id = #{position} and `status` = 1
        and delete_flag = 0
        and area_code = #{areaCode}
    </select>

    <select id="getPicUrl" resultType="java.lang.String">
        select a.pic_url
        from sfa_employee_info sei
        INNER JOIN sfa_position_relation spr on spr.employee_info_id = sei.id
        INNER JOIN sfa_apply_member a on a.id = sei.application_id
        where sei.id = #{employeeInfoId} limit 1
    </select>
    <select id="queryCurrentOrganizationId" resultType="java.lang.String">
        SELECT
            DISTINCT r.organization_code AS organizationId
        FROM
            sfa_employee_info ei
                LEFT JOIN sfa_position_relation r ON ei.id = r.employee_info_id
                LEFT JOIN sfa_business_group sbp ON r.business_group = sbp.id
        WHERE
            ei.member_key = #{memberKey}
          AND sbp.business_group_code = #{businessGroup}
          AND r.status = 1
        LIMIT 1
    </select>


    <select id="selectBusinessBDPersonal" resultType="com.wantwant.sfa.backend.employee.vo.BusinessBDPersonnelVo">
        SELECT
            sei.id as employeeInfoId,
            sei.member_key,
            sei.employee_name as empName,
            a.jobs_type,
            a.position,
            a.ceo_type
        FROM
            sfa_position_relation spr
            INNER JOIN sfa_employee_info sei ON sei.id = spr.employee_info_id
            AND spr.`status` = 1
            AND spr.delete_flag = 0
            INNER JOIN sfa_apply_member a ON a.id = sei.application_id
            AND a.position IN (1) and a.ceo_type in (1,2,3,8)
        WHERE
            spr.department_code = #{departmentCode}
            AND sei.employee_status in (1,2)
            order by a.position desc;

    </select>
    <select id="getBusinessBDPersonnel" resultType="com.wantwant.sfa.backend.transaction.vo.BusinessBDSelectedVo">
        SELECT
            sei.id as employeeInfoId,
            sei.employee_name,
            a.ceo_type,
            a.jobs_type,
            a.position,
            case when sbbs.id is null then 0 else 1 end as checked

        FROM
            sfa_position_relation spr
            INNER JOIN sfa_employee_info sei on sei.id = spr.employee_info_id
            and spr.`status` = 1 and spr.delete_flag = 0
            INNER JOIN sfa_apply_member a on a.id = sei.application_id
            and a.position =1 and a.ceo_type in (1,2,3)
            LEFT JOIN sfa_business_bd_server sbbs ON sbbs.server_employee_info_id = spr.employee_info_id
            and sbbs.employee_info_id = #{employeeInfoId} and sbbs.delete_flag = 0 and sbbs.`status` = 1
        where  sei.employee_status in (1,2)
        and spr.department_code = #{departmentCode}
    </select>

    <select id="queryMemeBerkey" resultType="java.lang.String">
        SELECT
            distinct ei.member_key
        FROM
            sfa_employee_info ei
            LEFT JOIN sfa_position_relation r ON ei.id = r.employee_info_id
        WHERE
            r.position_type_id IN (10, 3)
            and ei.employee_status IN (1,2)
            and (ei.employee_name like CONCAT('%',#{partnerInfo}, '%') or ei.mobile Like CONCAT('%',#{partnerInfo},'%'))
            and r.department_name IN
            <foreach collection="departments" item="department" open="(" close=")" separator=",">
            #{department}
        </foreach>
    </select>



    <select id="selectIdsByAreaCodes" resultType="Integer">
        select sei.id  from sfa_employee_info sei
        inner join sfa_apply_member a on sei.application_id = a.id
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id
        and spr.`status` = 1  and spr.delete_flag = 0
        <where>
            and a.position = 1
            and sei.employee_status in (1,2)
            and spr.position_type_id = 3
            <foreach collection="areaCodes" item="item" open=" and spr.area_code in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>

    </select>

    <select id="selectParentOrgEmp" resultType="com.wantwant.sfa.backend.display.dto.ParentOrganizationDTO">
        SELECT t.organization_parent_id as areaOrganizationId,o.organization_name as areaOrganizationName,
        r.employee_id as areaEmployeeId,r.employee_name as areaEmployeeName,t.`level`,
        case when r.position_type_id =1 then '总督导'
        when r.position_type_id =2 then '区域总监'
        when r.position_type_id =7 then '总部'
        when r.position_type_id =10 then '区域经理'
        when r.position_type_id =11 then '省区总监'
        when r.position_type_id =12 then '大区总监'
        else '' end as positionId,
        o.organization_type
        from ceo_business_organization_tree t
        INNER JOIN ceo_business_organization_position_relation r on r.organization_id = t.organization_parent_id and r.employee_id is not null
        INNER JOIN ceo_business_organization o on o.organization_id = t.organization_parent_id
        where t.organization_id = #{organizationId} and t.`level` > 0 ORDER BY t.`level` LIMIT 1
    </select>

    <select id="getPicUrlByEmpId" resultType="java.lang.String">
        select a.pic_url
        from sfa_employee_info sei
        INNER JOIN sfa_position_relation spr on spr.employee_info_id = sei.id
        INNER JOIN sfa_apply_member a on a.id = sei.application_id
        where sei.employee_id = #{employeeId} limit 1
    </select>

    <select id="queryEmployeeInfo" resultType="com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,sei.onboard_time as onboardTime
        ,DATEDIFF(now(),sei.onboard_time)+1 as onboardDays
        ,sam.pic_url as avatar
        ,CONCAT_WS(sama.province,sama.city,sama.district,sama.street) as address
        ,sama.longitude
        ,sama.latitude
        from sfa_employee_info sei
        LEFT JOIN sfa_apply_member sam on sam.id = sei.application_id
        LEFT JOIN sfa_apply_member_addition sama on sam.id = sama.apply_id and sama.is_delete = 0
        <where>
            sei.id = #{employeeInfoId}
        </where>
    </select>

    <select id="queryEmployeeInfoByMemberKey" resultType="com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,sam.pic_url as avatar
        from sfa_employee_info sei
        LEFT JOIN sfa_apply_member sam on sam.id = sei.application_id
        <where>
            sei.member_key = #{memberKey}
        </where>
    </select>

    <select id="queryEmployeeInfoByMobile" resultType="com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,spr.position_type_id
        ,spr.organization_code
        from sfa_employee_info sei
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag =0 and spr.status =1 and spr.channel= 3
        and spr.part_time = 0
        <where>
            sei.mobile = #{mobile}
            and sei.employee_status in (1,2)
            and sei.status = 1
        </where>
        limit 1
    </select>

    <select id="queryAllEmployeeIdList" resultType="java.lang.String">
        select
        distinct spr.emp_id
        from ceo_business_organization org
        inner join sfa_position_relation spr on spr.organization_code = org.organization_id
                and spr.delete_flag =0 and spr.status =1 and spr.channel= 3
                and spr.part_time =0 and spr.emp_id is not null and spr.emp_id != ''
        <where>
            org.business_group !=99
            and org.organization_type !='branch'
            and org.organization_type !='zb'
        </where>
    </select>

    <select id="queryEmployeeStatusList" resultType="com.wantwant.sfa.backend.model.SfaEmployeeInfoModel">
        select
        sei.`id`,
        sei.employee_id,
        sei.mobile,
        case
        when sei.employee_status = 2 then
            case
            when sl_latest.`id` is not null then 1
            when cvi_latest.`id` is not null then 4
            else 0
            end
        when sei.employee_status = 1 then 2
        when sei.employee_status not in (1,2)  then 3
        end as employeeStatus
        from sfa_employee_info sei
        LEFT JOIN (
            SELECT sl.*
            FROM sfa_leave sl
            WHERE sl.leave_status = 1
            AND sl.delete_flag = 0
            AND date_format(sl.leave_start_time,'%Y-%m-%d')&lt;= #{date}
            AND date_format(sl.leave_end_time,'%Y-%m-%d')&gt;= #{date}
            group by sl.apply_employee_info_id
        ) sl_latest ON sl_latest.apply_employee_info_id = sei.`id`
        LEFT JOIN (
            SELECT cvi.*
            FROM customer_visit_info cvi
            WHERE cvi.delete_flag = 0
            AND cvi.start_time >= '2024-01-01'
            AND cvi.start_time &lt;= #{completeStartTime}
            AND (cvi.end_time &gt;= #{completeStartTime} OR cvi.end_time IS NULL)
            group by cvi.partner_member_key
        ) cvi_latest ON cvi_latest.partner_member_key = sei.member_key
        <where>
            <if test="null != employeeInfoIdSet and employeeInfoIdSet.size()>0 ">
                <foreach collection="employeeInfoIdSet" item="item" open=" and sei.id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="queryMemberKeyByMobileAndDepartmentCode" resultType="Long">
        select
        sei.member_key
        from sfa_employee_info sei
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag =0 and spr.status =1 and spr.channel= 3
        and spr.department_code = #{departmentCode}
        <where>
            sei.employee_status = 2
            and sei.status = 1
            and sei.mobile = #{mobile}
        </where>
        limit 1
    </select>


    <select id="queryParentList" resultType="com.wantwant.sfa.backend.model.SfaEmployeeInfoModel">
        select
            sei.`id`
            ,cbopr.employee_id
            ,cbopr.employee_name
        from ceo_business_organization_tree cbot
        inner join ceo_business_organization_position_relation cbopr on cbopr.organization_id = cbot.organization_parent_id
        inner join sfa_employee_info sei on sei.employee_id = cbopr.employee_id and sei.employee_status = 2 and sei.employee_name !="代理督导"
        <where>
            cbot.organization_id = (select organization_id from ceo_business_organization_position_relation where position_id =#{positionId})
            and cbot.`level` >0
        </where>
        order by cbot.`level`
    </select>

    <!--查询本人的所有直属上级，（1:本人主岗的上级，2:上级可以是兼职的，3：上级一定有人，3：上级必须在职，4：合伙人employee_id为空）-->
    <select id="queryParentListByEmployeeInfoIds" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
            sei.`id` as employeeInfoId
            ,sei.employee_id
            ,cbot.`level`
            ,parent_sei.`id` as parentEmployeeInfoId
            ,parent_sei.employee_id as parentEmployeeId
            ,parent_sei.employee_name as parentEmployeeName
        from ceo_business_organization_tree cbot
        inner join ceo_business_organization_position_relation cbopr on cbopr.organization_id = cbot.organization_id
        inner join sfa_employee_info sei on sei.position_id = cbopr.position_id
        inner join ceo_business_organization_position_relation parent_cbopr on parent_cbopr.organization_id = cbot.organization_parent_id and ifnull(parent_cbopr.employee_id,'')!=''
        inner join sfa_employee_info parent_sei on parent_sei.employee_id = parent_cbopr.employee_id and parent_sei.employee_status = 2 and parent_sei.employee_name !="代理督导"
        <where>
            cbot.`level` >0
            <foreach collection="employeeInfoIds" item="item" open=" and sei.id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询本人的所有直属上级，（1:本人主兼岗的上级，2:上级可以是兼职的，3：上级一定有人，3：上级必须在职，4：合伙人employee_id为空）-->
    <select id="queryParentListByEmployeeInfoIds1" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,cbot.`level`
        ,parent_sei.`id` as parentEmployeeInfoId
        ,parent_sei.employee_id as parentEmployeeId
        ,parent_sei.employee_name as parentEmployeeName
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation spr on spr.organization_code = cbot.organization_id and spr.delete_flag =0 and spr.status =1 and spr.channel= 3
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        inner join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
        inner join sfa_employee_info parent_sei on parent_sei.id = parent_spr.employee_info_id and parent_sei.employee_status = 2 and parent_sei.employee_name !="代理督导"
        <where>
            cbot.`level` >0
            and spr.business_group = #{businessGroup}
            <foreach collection="employeeInfoIds" item="item" open=" and sei.id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询本人的所有直属上级，（0:本人是否包含离职/异动，1:本人主兼岗的上级，2:上级可以是兼职的，3：上级一定有人，3：上级必须在职，4：合伙人employee_id为空）-->
    <select id="queryParentListByEmployeeInfoIds2" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,spr.organization_code as organizationId
        ,cbot.`level`
        ,parent_sei.`id` as parentEmployeeInfoId
        ,parent_sei.employee_id as parentEmployeeId
        ,parent_sei.employee_name as parentEmployeeName
        ,CONCAT_WS('-',parent_spr.area_name,parent_spr.varea_name,parent_spr.province_name,parent_spr.company_name,parent_spr.department_name,parent_sei.employee_name) as parentOrganizationPositionEmployeeName
        ,parent_spr.organization_code as parentOrganizationId
        ,parent_org.organization_name as parentOrganizationName
        ,parent_spr.business_group as parentBusinessGroup
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation spr on spr.organization_code = cbot.organization_id and spr.delete_flag =0 and spr.channel= 3
        <if test="params.partTime != null">
            and spr.part_time = #{params.partTime}
        </if>
        <if test="params.status != null">
            and spr.status = #{params.status}
        </if>
        <if test="params.businessGroup != null">
            and spr.business_group = #{params.businessGroup}
        </if>
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        inner join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
        inner join sfa_employee_info parent_sei on parent_sei.id = parent_spr.employee_info_id and parent_sei.employee_status = 2
        <if test="params.isExclude != null and params.isExclude == 1">
            and parent_sei.employee_name !="代理督导"
        </if>
        inner join ceo_business_position_type parent_pos on parent_pos.id = parent_spr.position_type_id
        inner join ceo_business_organization parent_org on parent_org.organization_id = parent_spr.organization_code
        <where>
            cbot.`level` >0
            <foreach collection="params.employeeInfoIds" item="item" open=" and sei.id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询组织的所有直属上级，（2:上级可以是兼职的，3：上级一定有人，3：上级必须在职，4：合伙人employee_id为空）-->
    <select id="queryParentListByOrganizationIds" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        cbot.organization_id
        ,cbot.`level`
        ,parent_sei.`id` as parentEmployeeInfoId
        ,parent_sei.employee_id as parentEmployeeId
        ,parent_sei.employee_name as parentEmployeeName
        ,parent_spr.organization_code as parentOrganizationId
        ,parent_spr.position_id as parentPositionId
        ,parent_spr.position_type_id as parentPositionTypeId
        ,parent_sam.pic_url as parentAvatar
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
        inner join sfa_employee_info parent_sei on parent_sei.id = parent_spr.employee_info_id and parent_sei.employee_status = 2
        <if test="params.isExclude != null and params.isExclude == 1">
            and parent_sei.employee_name !="代理督导"
        </if>
        LEFT JOIN sfa_apply_member parent_sam on parent_sam.id = parent_sei.application_id
        <where>
            <if test="params.isIncludingSelf != null and params.isIncludingSelf == 0">
                cbot.`level` >0
            </if>
            <foreach collection="params.organizationIds" item="item" open=" and cbot.organization_id in (" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询本人的所有直属上级，（0:本人是否包含离职/异动，1:本人主兼岗的上级，2:上级可以是兼职的，3：上级一定有人，3：上级必须在职，4：合伙人employee_id为空）-->
    <select id="queryParents" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,spr.organization_code as organizationId
        ,cbot.`level`
        ,parent_sei.`id` as parentEmployeeInfoId
        ,parent_sei.employee_id as parentEmployeeId
        ,parent_sei.employee_name as parentEmployeeName
        ,parent_spr.organization_code as parentOrganizationId
        ,parent_spr.position_id as parentPositionId
        ,parent_spr.position_type_id as parentPositionTypeId
        ,parent_spr.business_group as parentBusinessGroup
        ,parent_sam.pic_url as parentAvatar
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation spr on spr.organization_code = cbot.organization_id and spr.delete_flag =0 and spr.channel= 3
        <if test="params.partTime != null">
            and spr.part_time = #{params.partTime}
        </if>
        <if test="params.status != null">
            and spr.status = #{params.status}
        </if>
        <if test="params.businessGroup != null">
            and spr.business_group = #{params.businessGroup}
        </if>
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        inner join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
        inner join sfa_employee_info parent_sei on parent_sei.id = parent_spr.employee_info_id and parent_sei.employee_status = 2
        <if test="params.isExclude != null and params.isExclude == 1">
            and parent_sei.employee_name !="代理督导"
        </if>
        LEFT JOIN sfa_apply_member parent_sam on parent_sam.id = parent_sei.application_id
        <where>
            cbot.`level` >0
            <if test="params.employeeInfoId != null">
                and sei.`id` = #{params.employeeInfoId}
            </if>
            <if test="params.employeeInfoIds != null and params.employeeInfoIds.size() > 0 ">
                <foreach collection="params.employeeInfoIds" item="item" open=" and sei.id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询本人的所有各组上级，（0:本人是否包含离职/异动，1:本人主兼岗的上级，2:上级可以是兼职的）-->
    <select id="queryParents1" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,spr.organization_code as organizationId
        ,spr.business_group as businessGroup
        ,cbot.`level`
        ,cbot.organization_parent_id as parentOrganizationId
        ,parent_spr.employee_info_id as parentEmployeeInfoId
        ,all_parent_cbopr.position_type_id as parentPositionTypeId
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation spr on spr.organization_code = cbot.organization_id and spr.delete_flag =0 and spr.channel= 3
        <if test="params.partTime != null">
            and spr.part_time = #{params.partTime}
        </if>
        <if test="params.status != null">
            and spr.status = #{params.status}
        </if>
        <if test="params.businessGroup != null">
            and spr.business_group = #{params.businessGroup}
        </if>
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        left join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
        left join ceo_business_organization_position_relation all_parent_cbopr on all_parent_cbopr.organization_id = cbot.organization_parent_id
        <where>
            cbot.`level` >0
            <if test="params.employeeInfoId != null">
                and sei.`id` = #{params.employeeInfoId}
            </if>
            <if test="params.employeeInfoIds != null and params.employeeInfoIds.size() > 0 ">
                <foreach collection="params.employeeInfoIds" item="item" open=" and sei.id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询本人的所有合伙人（既有直接下属合伙人，又包含下n级合伙人）  合伙人可以有多个所 且都是主岗，但不会同组-->
    <select id="queryChildrenListByEmployeeInfoIds" resultType="com.wantwant.sfa.backend.employee.vo.ParentVo">
        select
        sei.`id` as employeeInfoId
        ,cbot.organization_id
<!--        ,sei.employee_id-->
<!--        ,cbot.`level`-->
<!--        ,parent_sei.`id` as parentEmployeeInfoId-->
<!--        ,parent_sei.employee_id as parentEmployeeId-->
        from ceo_business_organization_tree cbot
        inner join sfa_position_relation spr on spr.organization_code = cbot.organization_id and spr.delete_flag =0 and spr.status =1 and spr.channel= 3
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        inner join sfa_position_relation parent_spr on parent_spr.organization_code = cbot.organization_parent_id and parent_spr.delete_flag =0 and parent_spr.status =1 and parent_spr.channel= 3
<!--        inner join ceo_business_organization_position_relation parent_cbopr on parent_cbopr.organization_id = cbot.organization_parent_id-->
<!--        inner join sfa_employee_info parent_sei on parent_sei.employee_id = parent_cbopr.employee_id and parent_sei.employee_name !="代理督导"-->
        <where>
            parent_spr.emp_id = #{employeeId}
            and parent_spr.business_group = #{businessGroup}
            <choose>
                <!--登陆人是战区督导，合伙人tree表的level=5-->
                <when test="organizationType == 'area'">
                    and cbot.`level` =5
                </when>
                <when test="organizationType == 'varea'">
                    and cbot.`level` =4
                </when>
                <when test="organizationType == 'province'">
                    and cbot.`level` =3
                </when>
                <when test="organizationType == 'company'">
                    and cbot.`level` =2
                </when>
                <when test="organizationType == 'department'">
                    and cbot.`level` =1
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </where>
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询所有下级，（1:包含自己，2:下级主兼岗都查出来(partTime可选)，3：下级一定有人，4：本人&下级排除总部&合伙人（但不排除业务BD、承揽BD）））-->
    <select id="queryChildListByParentOrg" resultType="com.wantwant.sfa.backend.employee.vo.ChildVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,cbot.organization_id
        ,case sei.type
        when 6 then parent_cbo.organization_name
        when 7 then parent_cbo.organization_name
        else cbo.organization_name end as organizationName
        ,cbot.`level`
        ,
        IF(spr.position_type_id = 3, concat(spr.position_type_id,spr.type, spr.post_type), spr.position_type_id) as postTypeId,
        case
        when sei.type=6 and sei.post_type=1 then '全职业务BD'
        when sei.type=6 and sei.post_type=2 then '兼职业务BD'
        when sei.type=7 then '承揽业务BD'
        else cbpt.position_name end as positionName
        ,sam.pic_url as avatar
        from ceo_business_organization_tree cbot
        <if test="params.organizationIds != null ">
        INNER JOIN ceo_business_organization_view v on v.organization_id = cbot.organization_id and v.business_group = #{params.businessGroup}
        </if>
        inner join sfa_position_relation spr on spr.organization_code  = cbot.organization_id and spr.delete_flag =0 and spr.status =1 and spr.channel=3
        inner join ceo_business_organization cbo on cbo.organization_id  = cbot.organization_id
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id and sei.employee_name !="代理督导"
        inner join ceo_business_organization_position_relation cbopr on cbopr.organization_id = cbot.organization_id
        left join ceo_business_organization parent_cbo on parent_cbo.organization_id  = cbopr.organization_parent_id
        LEFT JOIN ceo_business_position_type cbpt on cbpt.id = spr.position_type_id
        LEFT JOIN sfa_apply_member sam on sam.id = sei.application_id
        <where>
            and cbot.business_group = #{params.businessGroup}
            <!-- 排除总部-->
            and spr.position_type_id != 7
            <!-- spr的position_type_id=3是合伙人或者BD，不能直接排除3，sei的type in(6,7)是BD-->
            and (sei.type in (6,7) or spr.position_type_id !=3)
            <if test="params.organizationIds != null ">
                <choose>
                    <!-- 查询使用 -->
                    <when test="params.organizationType == 'area'">
                        <foreach collection="params.organizationIds" item="item" open="and v.organization_id3 in (" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="params.organizationType == 'varea'">
                        <foreach collection="params.organizationIds" item="item" open="and v.virtual_area_id in (" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="params.organizationType == 'province'">
                        <foreach collection="params.organizationIds" item="item" open="and v.province_id in (" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="params.organizationType == 'company'">
                        <foreach collection="params.organizationIds" item="item" open="and v.organization_id2 in (" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <when test="params.organizationType == 'department'">
                        <foreach collection="params.organizationIds" item="item" open="and v.department_id in (" close=")" separator=",">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            <if test="params.positionTypeId != null">
                and spr.position_type_id = #{params.positionTypeId}
            </if>
            <if test="params.partTime != null">
                and spr.part_time = #{params.partTime}
            </if>
            <if test="params.employeeInfoId != null">
                and spr.employee_info_id = #{params.employeeInfoId}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and cbot.organization_parent_id = #{params.organizationId}
            </if>
            <if test="params.employeeInfo != null and params.employeeInfo != '' ">
                and (sei.employee_id like CONCAT('%',#{params.employeeInfo},'%') or sei.employee_name like CONCAT('%',#{params.employeeInfo},'%') or sei.mobile like CONCAT('%',#{params.employeeInfo},'%'))
            </if>
        </where>
        group by cbot.organization_id
        order by cbot.organization_id ,cbot.`level`

    </select>

    <!--查询所有业务人员，（1:在职，2:主岗， 3：一定有人，4：排除总部&合伙人）-->
    <select id="queryAllBusinessPerson" resultType="com.wantwant.sfa.backend.employee.vo.BusinessPersonVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,spr.organization_code as organizationId
        ,spr.position_type_id as positionTypeId
        from sfa_position_relation spr
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        <where>
            spr.delete_flag =0
            and spr.status =1
            and spr.channel=3
            and spr.part_time = 0
            and spr.position_type_id NOT in (3,7)
        </where>

    </select>

    <!--查询所有BD -->
    <select id="queryBrotherBD" resultType="com.wantwant.sfa.backend.employee.vo.BrotherVo">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_id
        ,sei.employee_name
        ,sei.member_key
        from sfa_employee_info sei
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag =0 and spr.status =1 and spr.channel=3
        <where>
            spr.business_group = #{params.businessGroup}
            and spr.position_type_id =3
            and sei.type in (6,7)
        </where>
        group by sei.id

    </select>

    <select id="searchAllocateCeo" resultType="com.wantwant.sfa.backend.meeting.vo.CeoVO">
        SELECT
            spr.company_name,
            spr.department_name,
            dme.employee_name as managerName,
            a.pic_url as avatar,
            spr.organization_code AS organizationId,
            sei.member_key,
            sei.employee_name,
            spr.business_group,
            sei.mobile
        FROM
            sfa_position_relation spr
            INNER JOIN sfa_employee_info sei ON sei.id = spr.employee_info_id
            and spr.status = 1 and spr.delete_flag = 0 and spr.position_type_id = 3
            LEFT JOIN sfa_position_relation dm on dm.organization_code = spr.department_code
            and dm.`status` = 1 and dm.delete_flag  = 0 and dm.position_type_id = 10
            LEFT JOIN sfa_employee_info dme on dme.id = dm.employee_info_id
            LEFT JOIN sfa_apply_member a on a.id = dme.application_id
        <where>

            and spr.department_code = #{departmentCode}
            and (sei.employee_name like concat('%',#{key},'%') or sei.mobile like concat('%',#{key},'%'))
        </where>

    </select>

    <select id="queryBusinessCurrentEmployees" resultType="map">
        select
            sei.id,
            sei.employee_name as employeeName,
            sei.employee_id as employeeId,
            IFNULL(sei.company_name, t.organization_name) as companyName,
            sam.position
        from
            sfa_employee_info sei
                inner join sfa_apply_member sam on
                sam.id = sei.application_id
                left join (
                select
                    organization_id,
                    organization_name
                from
                    ceo_business_organization
                where
                    organization_type = 'company'
            ) t on
                t.organization_id = sam.work_place
        where
            sei.employee_status = 2
          and sei.member_key is not null
          and sam.`position` in (2, 3, 4, 5, 6)
        <if test="excludeList != null and excludeList.size() > 0 ">
            <foreach collection="excludeList" item="item" open="and sei.id not in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>



    <select id="selectEmpNameByMemberKey" resultType="com.wantwant.sfa.backend.domain.emp.DO.EmpBaseDO">
        select sei.employee_name,a.pic_url as avatar from sfa_employee_info sei
        inner join sfa_apply_member a on sei.application_id = a.id
        where sei.member_key = #{memberKey}
        limit 1
    </select>




    <select id="selectEmpInfoByOrgCode" resultType="com.wantwant.sfa.backend.domain.emp.DO.EmpDO">
             select sei.employee_name ,sei.mobile ,sam.pic_url as avatar,
                    cbo.organization_id ,cbo.organization_name ,eb.start_valid_date as onBoardDate,
                    datediff(if(eb.end_valid_date > now(),now(),eb.end_valid_date),eb.start_valid_date) as workDate
            from
            ceo_business_organization cbo
            left join
            (
            select
                spr.employee_info_id,
                spr.start_valid_date ,
                spr.end_valid_date ,
                spr.organization_code
            from
                sfa_position_relation spr
            where spr.organization_code = #{organizationId}
            and spr.delete_flag  = 0
            order by spr.id  desc
            limit 1
            ) eb on eb.organization_code = cbo.organization_id
            left join sfa_employee_info sei
             on eb.employee_info_id = sei.id
             left join sfa_apply_member sam  on sam.id = sei.application_id
             where cbo.organization_id  = #{organizationId}
            limit 1
    </select>

    <select id="selectManager" resultType="com.wantwant.sfa.backend.domain.emp.DO.EmpDO">
        select
            sei.employee_name ,
            sei.mobile ,
            cbo.organization_name ,
            cbo.organization_id ,
            spr.start_valid_date ,
            sei.employee_id,
            sam.pic_url as avatar
        from
            sfa_position_relation spr
        inner join sfa_employee_info sei on
            sei.id = spr.employee_info_id
            and spr.status = 1
            and spr.delete_flag = 0
        inner join sfa_apply_member sam on
            sam.id = sei.application_id
        inner join ceo_business_organization cbo
        on cbo.organization_id  = spr.organization_code
        <where>
            cbo.organization_id = #{organizationId}
        </where>
    </select>


    <select id="selectEmpByMemberKey" resultType="com.wantwant.sfa.backend.domain.emp.DO.EmpDO">
         select
            sei.employee_name ,
            spr.business_group,
            sei.mobile ,
            spr.position_id,
            cbo.organization_name ,
            cbo.organization_id ,
            spr.start_valid_date ,
            spr.position_type_id,
            sei.employee_id,
            sam.pic_url as avatar
        from
            sfa_position_relation spr
        inner join sfa_employee_info sei on
         sei.id = spr.employee_info_id
         and spr.delete_flag = 0
        inner join sfa_apply_member sam on
            sam.id = sei.application_id
        inner join ceo_business_organization cbo
        on cbo.organization_id  = spr.organization_code
        inner join sfa_business_group sbg on sbg.id = spr.business_group
		<where>
            and sei.member_key  = #{memberKey}
            and sbg.business_group_code  = #{productGroupId}
        </where>
        limit 1

    </select>



    <select id="selectServerObj" resultType="com.wantwant.sfa.backend.domain.emp.DO.ServerObjDO">
        select
            sei.employee_name ,
            sei.member_key
        from
            sfa_business_bd_server sbbs
        inner join sfa_employee_info sei on
        sbbs.server_employee_info_id  = sei.id
        inner join sfa_apply_member sam on sam.id = sei.application_id
        and sam.position = 1 and sam.ceo_type in (1,2,8)
        <where>
            and sbbs.employee_info_id = #{employeeInfoId}
            and sbbs.delete_flag = 0 and sbbs.status =1
        </where>
    </select>


    <select id="getPerformanceRate" resultType="com.wantwant.sfa.backend.domain.emp.DO.PerformanceDO">

        select
        sum(ifnull(sape.target_price_performance,0))/if(sum(ifnull( sape.target_population_target,0)) = 0,1,sum(ifnull( sape.target_population_target,0))) as lastMonthPerformanceAchievementRate,
        sum(ifnull(sape2.target_price_performance_quarter,0))/if(sum(ifnull( sape2.target_population_target_quarter,0)) = 0,1,sum(ifnull( sape2.target_population_target_quarter,0))) as lastQuarterPerformanceAchievementRate
        from sfa_area_performance_evaluation sape
        left join sfa_area_performance_evaluation sape2 on sape.employee_info_id = sape2.employee_info_id
        and sape2.assessment_month  = #{lastQuarter}
        where sape.employee_info_id  = #{employeeInfoId} and sape.assessment_month  = #{lastMonth}
        group by sape.employee_info_id

        union all

        select
        sum(ifnull(scpe.target_price_performance,0))/if(sum(ifnull( scpe.target_population_target,0)) = 0,1,sum(ifnull( scpe.target_population_target,0))) as lastMonthPerformanceAchievementRate,
        sum(ifnull(scpe2.target_price_performance_quarter,0))/if(sum(ifnull( scpe2.target_population_target_quarter,0)) = 0,1,sum(ifnull( scpe2.target_population_target_quarter,0))) as lastQuarterPerformanceAchievementRate
        from sfa_company_performance_evaluation scpe
        left join sfa_company_performance_evaluation scpe2 on scpe.employee_info_id = scpe2.employee_info_id
        and scpe2.assessment_month  = #{lastQuarter}
        where scpe.employee_info_id  = #{employeeInfoId} and scpe.assessment_month  =  #{lastMonth}
        group by scpe.employee_info_id

        union all


        select
             sum(ifnull(sdpe.target_price_performance,0))/if(sum(ifnull( sdpe.target_population_target,0)) = 0,1,sum(ifnull( sdpe.target_population_target,0))) as lastMonthPerformanceAchievementRate,
             sum(ifnull(sdpe2.target_price_performance_quarter,0))/if(sum(ifnull( sdpe2.target_population_target_quarter,0)) = 0,1,sum(ifnull( sdpe2.target_population_target_quarter,0))) as lastQuarterPerformanceAchievementRate
        from sfa_department_performance_evaluation sdpe
        left join sfa_department_performance_evaluation sdpe2 on sdpe.employee_info_id = sdpe2.employee_info_id
        and sdpe2.assessment_month  between  date_format( date_sub(date_format(concat(#{lastQuarter},'-01'),'%Y-%m-%d'),interval 2 month),'%Y-%m')
        and  #{lastQuarter}
        where sdpe.employee_info_id =  #{employeeInfoId}  and sdpe.assessment_month = #{lastMonth}
        group by sdpe.employee_info_id


        limit 1
    </select>



    <select id="selectCheckedServerObj" resultType="String">
        select group_concat(sei.member_key)  from sfa_business_bd_server sbbs
        inner join sfa_employee_info sei on sei.id  = sbbs.server_employee_info_id
        and sbbs.delete_flag  = 0 and sbbs.status = 1
        where sbbs.employee_info_id  = #{employeeInfoId}
        group by sbbs.employee_info_id
    </select>

    <select id="selectEmployeeInfoByKey" resultType="com.wantwant.sfa.backend.domain.emp.repository.model.EmpModel">
        select
        sei.employee_name ,
        sei.mobile ,
        spr.status ,
        sei.member_key
        from sfa_position_relation spr
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        and spr.delete_flag  = 0
        inner join sfa_apply_member sam  on sam.id  = sei.application_id
        and sam.`position`  = 1
        and spr.position_type_id  = 3
        <where>
            and (sei.employee_name like concat('%',#{key},'%') or
            sei.mobile like concat('%',#{key},'%'))
            <choose>
                <when test="positionTypeId != 7">
                    <choose>
                        <when test="positionTypeId == 1">
                            and spr.area_code
                        </when>
                        <when test="positionTypeId == 12">
                            and spr.varea_code
                        </when>
                        <when test="positionTypeId == 11">
                            and spr.province_code
                        </when>
                        <when test="positionTypeId == 2">
                            and spr.company_code
                        </when>
                        <when test="positionTypeId == 10">
                            and spr.department_code
                        </when>
                    </choose>
                    <foreach collection="orgCode" item="item" separator="," open=" in (" close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and spr.business_group = #{businessGroup}
                </otherwise>
            </choose>
        </where>
        group by sei.id
    </select>


    <select id="getBusinessBDCount" resultType="int">
        select count(*) from sfa_position_relation spr
        inner join sfa_employee_info sei on sei.id = spr.employee_info_id
        inner join sfa_apply_member sam on sam.id = sei.application_id
        inner join sfa_business_bd_server sbbs on sbbs.employee_info_id  = sei.id
        inner join sfa_employee_info sei2 on sei2.id = sbbs.server_employee_info_id
        <where>
            and sei.employee_status in (1,2)
            and spr.status  = 1 and spr.delete_flag  =0
            and sei2.member_key = #{memberKey}
            and spr.business_group = #{businessGroup}
            <choose>
                <when test="type == 1">
                    and sam.ceo_type = 7 and sam.jobs_type = 2 and sam.position = 7
                </when>
                <otherwise>
                    and sam.ceo_type = 6 and sam.jobs_type = 1 and sam.position = 7
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectJobTaskBDCount" resultType="int">
        select ifnull(t1.total,0) + ifnull(t2.total,0)
        from (select count(*) total from sfa_job_position_task sjpt
        inner join sfa_apply_member sam on sam.id = sjpt.apply_id
        and sjpt.`type` = 1
        inner join sfa_interview_process sip on sip.application_id = sam.id
        inner join sfa_interview_process_record sipr on sipr.id = sip.interview_record_id
        <where>
            and sjpt.status in (0,2)
            and sam.company_organization_id = #{companyOrgCode}
            and sipr.context_emp = #{serverEmployeeInfoId}
            <choose>
                <when test="type == 1">
                    and sam.ceo_type = 7
                    and sam.jobs_type = 2
                    and sam.position = 7
                </when>
                <otherwise>
                    and sam.ceo_type = 6
                    and sam.jobs_type = 1
                    and sam.position = 7
                </otherwise>
            </choose>

        </where>
        )t1,
        (
            select
            count(*) total
            from
            sfa_job_position_task sjpt
            inner join sfa_apply_member sam on
            sam.id = sjpt.apply_id
            inner join sfa_transaction_apply sta on
            sta.id = sjpt.transaction_id
            inner join sfa_employee_info sei on sei.application_id  = sjpt.apply_id
            inner join sfa_business_bd_server sbbs  on sbbs.employee_info_id  = sei.id
            <where>
                and sjpt.`type` = 3
                and sjpt.status in (0,2)
                and sta.transaction_type = 8

                and sbbs.server_employee_info_id  = #{serverEmployeeInfoId}
                <choose>
                    <when test="type == 1">
                        and sam.ceo_type = 7
                        and sam.jobs_type = 2
                        and sam.position = 7
                    </when>
                    <otherwise>
                        and sam.ceo_type = 6
                        and sam.jobs_type = 1
                        and sam.position = 7
                    </otherwise>
                </choose>

            </where>
        ) t2
    </select>

    <select id="selectBusinessBDByMemberKey" resultType="com.wantwant.sfa.backend.domain.emp.DO.BusinessBDDO">
        select
            sei.id as employeeInfoId,
            sei.mobile ,
            sei.employee_name ,
            sam.id as applyId,
            sam.ceo_type ,
            sam.jobs_type ,
            sam.`position`,
            sam.business_group
        from
            sfa_business_bd_server sbbs
        inner join sfa_employee_info server on
            sbbs.server_employee_info_id = server.id
        inner join sfa_employee_info sei on
            sei.member_key = sbbs.member_key
        inner join sfa_apply_member sam  on sam.id = sei.application_id
        inner join sfa_business_group sbg on sbg.id  = sam.business_group
        where server.member_key = #{memberKey} and sbg.business_group_code = #{businessGroupCode}
        and sei.employee_status  in (1,2)
        order by sei.onboard_time
    </select>


    <select id="queryEmployeeInfoList" resultType="com.wantwant.sfa.backend.employee.vo.EmployeeInfoVO">
        select
        sei.`id` as employeeInfoId
        ,sei.employee_name as employeeName
        ,spr.organization_code as organizationId
        ,spr.position_type_id as positionTypeId
        ,cbpt.position_name as positionTypeName
        ,spr.business_group as businessGroup
        ,CONCAT_WS('-',spr.area_name,spr.varea_name,spr.province_name,spr.company_name,spr.department_name) as organizationNames
        ,sam.pic_url as avatar
        from sfa_employee_info sei
        inner join sfa_position_relation spr on spr.employee_info_id = sei.id and spr.delete_flag =0 and spr.channel= 3 and spr.status = 1
        inner join ceo_business_position_type cbpt on cbpt.id = spr.position_type_id
        LEFT JOIN sfa_apply_member sam on sam.id = sei.application_id
        <where>
            sei.employee_name !="代理督导"
            <if test="businessGroupList != null and businessGroupList.size() > 0 ">
                <foreach collection="businessGroupList" item="item" open=" and spr.business_group in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="positionTypeIdList != null and positionTypeIdList.size() > 0 ">
                <foreach collection="positionTypeIdList" item="item" open=" and spr.position_type_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
<!--            <if test="areaCodeList != null and areaCodeList.size() > 0 ">-->
<!--                <foreach collection="areaCodeList" item="item" open=" and spr.area_code in (" close=")" separator=",">-->
<!--                    #{item}-->
<!--                </foreach>-->
<!--            </if>-->
        </where>
    </select>

    <select id="selectOffBoardId" resultType="Integer">
        select distinct sei.id  from sfa_employee_info sei
        inner join sfa_apply_member sam  on sam.id  = sei.application_id
        inner join sfa_interview_process sip on sip.application_id  = sam.id
        <where>
            <choose>
                <when test="lastMonth != null and lastMonth != '' and beforeLastMonth != null and beforeLastMonth != ''">
                    and (sip.off_time  like (concat(date_format(#{lastMonth},'%Y-%m'),'%')) or sip.off_time  like (concat(date_format(#{beforeLastMonth},'%Y-%m'),'%')))
                </when>
                <otherwise>
                    and (sip.off_time  like (concat(date_format(now(),'%Y-%m'),'%')) or sip.off_time  like (concat(date_format(DATE_SUB(NOW(),INTERVAL 1 MONTH),'%Y-%m'),'%')))
                </otherwise>
            </choose>
            and sei.employee_status  >2
            and sam.`position`  != 1
        </where>
    </select>

    <select id="selectEmployeeInfoByIds" resultType="com.wantwant.sfa.backend.model.employee.EmployeeDTO">
        SELECT
            sei.id as employeeInfoId,
            sei.employee_status,
            sip.interview_record_id,
            cbopr.position_type_id
        FROM
            sfa_employee_info sei
                INNER JOIN sfa_interview_process sip ON sip.application_id = sei.application_id
                INNER JOIN ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id
        <where>
            <foreach collection="employeeInfoIds" item="item" separator="," open=" and sei.id in (" close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="selectEmpByMemberKeys" resultType="com.wantwant.sfa.backend.model.employee.EmployeeDTO">
        SELECT
        sei.id as employeeInfoId,
        sei.employee_status,
        sip.interview_record_id,
        cbopr.position_type_id,
        sei.member_key
        FROM
        sfa_employee_info sei
        INNER JOIN sfa_interview_process sip ON sip.application_id = sei.application_id
        INNER JOIN ceo_business_organization_position_relation cbopr on cbopr.position_id = sei.position_id
        <where>
            <foreach collection="memberKeys" item="item" separator="," open=" and sei.member_key in (" close=")">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="searchInternationalEmpList" resultType="com.wantwant.sfa.backend.interview.model.InternationalEmpModel">
       select
            sei.employee_name,
            sei.gender,
            sei.mobile,
            sam.idCard_num as idCard,
            sei.area_name,
            sei.varea_organization_name as vareaName,
            sei.company_name,
            sama.province as homeProvince,
            sama.city as homeCity,
            sama.district as homeDistrict,
            sama.street as homeStreet,
            sam.agent_province as salesProvince,
            sam.agent_district as salesDistrict,
            sam.agent_city as salesCity,
            sei.onboard_time as hireDate,
            p.off_time as offBoardDate,
            cbo.organization_name as officeLocation,
            sam.channel,
            sei.employee_status,
            sam.ceo_type,
            sam.jobs_type,
            sam.position
        from sfa_employee_info sei
        inner join sfa_apply_member sam on sam.id = sei.application_id
        inner join sfa_interview_process p on p.application_id = sam.id
        left join sfa_apply_member_addition sama on sama.apply_id = sam.id
        left join ceo_business_organization cbo on cbo.organization_id = sam.work_place
        and sama.is_delete = 0
        <where>
            and sam.business_group = #{req.businessGroup}
            <if test="req.employeeKey != null and req.employeeKey != ''">
                and (sei.employee_name like concat('%',#{req.employeeKey},'%') or sei.mobile like concat('%',#{req.employeeKey},'%'))
            </if>
            <if test="req.organizationType != null and req.organizationType != ''">
                <choose>
                    <when test="req.organizationType == 'area'">
                        and sei.area_code = #{req.organizationId}
                    </when>
                    <when test="req.organizationType == 'varea'">
                        and sei.varea_organization_id = #{req.organizationId}
                    </when>
                    <when test="req.organizationType == 'province'">
                        and sei.province_organization_id = #{req.organizationId}
                    </when>
                    <when test="req.organizationType == 'company'">
                        and sei.company_code = #{req.organizationId}
                    </when>
                    <when test="req.organizationType == 'department'">
                        and sei.department_code = #{req.organizationId}
                    </when>
                </choose>
            </if>
            <if test="req.positionId != null">
                and sam.position = #{req.positionId}
            </if>
            <if test="req.onBoardDate != null">
                and sei.onboard_time like concat(#{req.onBoardDate},'%')
            </if>
        </where>
    </select>
</mapper>
