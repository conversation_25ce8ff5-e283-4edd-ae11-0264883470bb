<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.wantwant.sfa.backend.mapper.AttendanceMapper">

	<select id="getAttendanceList" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceWeeklyDetailVo">
		SELECT distinct
			CASE
				DAYNAME( sa.attendance_time )
				WHEN 'Monday' THEN
					'周一'
				WHEN 'Tuesday' THEN
					'周二'
				WHEN 'Wednesday' THEN
					'周三'
				WHEN 'Thursday' THEN
					'周四'
				WHEN 'Friday' THEN
					'周五'
				WHEN 'Saturday' THEN
					'周六'
				WHEN 'Sunday' THEN
					'周日' ELSE ''
				END AS dateStr,
			sa.attendance_time,
			IF
				( sa.attendance_status = 0, '正常', '异常' ) AS attendanceStatusStr,
			IF
				( sa.attendance_status = 0, '-', '迟到' ) AS reason,
			CONCAT(
					sa.province,
					sa.city,
					sa.district,
					sa.street
				) AS attendanceAddress,
			sa.pic_url,
			sa.longitude,
			sa.latitude,
			sa.face_url,
			CASE
				sa.zb_audit_status
				WHEN 0 THEN
					'未稽核'
				WHEN 1 THEN
					'正常'
				WHEN 2 THEN
					'异常' ELSE ''
				END AS zbAuditStatusStr,
			CASE
				sa.zb_audit_reason
				WHEN 1 THEN
					'居家打卡'
				WHEN 2 THEN
					'代打卡'
				WHEN 3 THEN
					'电子设备打卡'
				WHEN 4 THEN
					'未穿工装'
				WHEN 5 THEN
					'打卡地不符'
				WHEN 6 THEN
					'打卡背景不符'
				ELSE '-'
				END AS zbAuditReasonStr
		FROM
			sfa_attendance_v2 sa
				INNER JOIN sfa_employee_info sei ON sa.employee_info_id = sei.id
		WHERE
			sei.id = #{employeeInfoId} AND sa.create_type = 0 AND attendance_type = 1
			AND sa.attendance_time BETWEEN #{startDate}
			AND concat(#{endDate}, ' 23:59:59')
	</select>

	<select id="getLatestVisitInfo" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceVisitInfoVo">
		SELECT
			cvi.start_time as lastVisitDateTime,
			CONCAT(
					ci.contact_address,
					IFNULL( ci.detail_address, '' )) as visitStoreAddress
		FROM
			sfa_attendance_v2 sa
			INNER JOIN sfa_employee_info sei ON sei.id = sa.employee_info_id
			INNER JOIN customer_visit_info cvi ON cvi.partner_member_key = sei.member_key
			INNER JOIN customer_info ci force INDEX (customer_id_index) ON ci.customer_id = cvi.customer_id
		WHERE
			sa.id = #{attendanceId}
		  AND cvi.delete_flag = 0
		  AND ci.delete_flag = 0
		  AND DATE_FORMAT(sa.attendance_time,'%Y-%m-%d') = DATE_FORMAT(cvi.start_time,'%Y-%m-%d')
		ORDER BY
			cvi.start_time DESC
			LIMIT 1;
	</select>


	<select id="getLatestVisitInfoByParams" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceVisitInfoVo">
		SELECT
			cvi.start_time as lastVisitDateTime,
			CONCAT(
			ci.contact_address,
			IFNULL( ci.detail_address, '' )) as visitStoreAddress
		FROM
			sfa_employee_info sei
		INNER JOIN customer_visit_info cvi
			ON cvi.partner_member_key = sei.member_key
			AND cvi.delete_flag = 0
			and cvi.start_time  <![CDATA[ >= ]]> CONCAT(#{attendanceDate},' 00:00:00')
			and cvi.start_time  <![CDATA[ <= ]]> CONCAT(#{attendanceDate},' 23:59:59')
		INNER JOIN customer_info ci ON ci.customer_id = cvi.customer_id AND ci.delete_flag = 0
		WHERE
			sei.id = #{employeeInfoId}
		ORDER BY
		cvi.start_time DESC
		LIMIT 1
	</select>

	<select id="getAttendanceInfoByParams" resultType="com.wantwant.sfa.backend.model.attendanceTask.Attendance">
		SELECT
			id,
		    attendance_status
		FROM
			sfa_attendance_v2
		WHERE
			employee_info_id = #{employeeInfoId}
		  AND attendance_type = 1
		  AND date_format( attendance_time, '%Y-%m-%d' ) = #{attendanceTime}
		limit 1
	</select>

	<select id="getAttendanceInitCalendar" resultType="Map">
		select
			calendar_date as  calendarDate,
			calendar_year as calendarYear,
			calendar_month as calendarMonth,
			calendar_day as calendarDay,
			is_work_day as isWorkDay,
			company
		from
			hotkidceo_member.member_calendar mc
		where
			mc.delete_state = 0
		  and calendar_date = #{date}
	</select>

	<select id="getAttendanceV2List" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceListV2Vo">
	SELECT
		sav2.work_day_type,
		sav2.id,
		sav2.calendar_date as `date`,
		sei.area_name as area,
		sei.varea_organization_name as virtualAreaName,
		sei.province_organization_name as provinceName,
		sei.company_name as company,
		sei.department_name as departmentName,
		sav2.employee_id as memberId,
		sav2.employee_name as employeeName,
		sav2.attendance_time AS attendanceTime,
	    sav2.longitude,
		sav2.latitude,
		sav2.pic_url AS picUrl,
		sav2.face_url AS faceUrl,
	    sei.onboard_time AS onboardTime,
		if(sav2.attendance_execption_type=3, 2, sav2.attendance_execption_type) as attendanceExecptionType,
		CONCAT(sav2.province,sav2.city,sav2.district,IFNULL(sav2.street, '')) as attendanceAddress,
		sav2.attendance_status,
		sav2.attendance_point_status,
        case
        when position_type_id = 3 and sei.post_type = 1  and sei.type = 6 then 361
        when position_type_id = 3 and sei.post_type = 2  and sei.type = 6 then 362
        when position_type_id = 3 and sei.post_type = 2  and sei.type = 7 then 372
        else position_type_id end as positionTypeId
		<choose>
			<when test="loginOrganizationType == 'zb'">
			,sav2.zb_audit_status AS auditStatus,
			sav2.zb_audit_reason AS auditReason,
			sav2.zb_audit_name AS auditName,
	        sav2.face_similar_score AS faceSimilarScore
			</when>
			<otherwise>
			,sav2.business_audit_status AS auditStatus,
			sav2.business_audit_reason AS auditReason,
			sav2.business_audit_name AS auditName
			</otherwise>
		</choose>
		FROM
		sfa_attendance_v2 sav2
		INNER JOIN sfa_employee_info sei ON sei.id = sav2.employee_info_id
		INNER JOIN ceo_business_organization_position_relation cbopr ON sei.position_id = cbopr.position_id

		WHERE
		sav2.attendance_type = 1 AND sav2.delete_flag = 0 AND sav2.attendance_time BETWEEN #{request.searchStartTime} AND #{request.searchEndTime}
		<if test="request.positionType != null">
			<choose>
				<when test="request.positionType == 1
				or  request.positionType == 2
				or  request.positionType == 10
				or  request.positionType == 11
				or  request.positionType == 12
				">
					AND cbopr.position_type_id = #{request.positionType}
				</when>
				<when test="request.positionType == 6">
					AND sei.type = 6 and sei.post_type = 1
				</when>
				<when test="request.positionType == 7">
					AND sei.type = 6 and sei.post_type = 2
				</when>
				<when test="request.positionType == 8">
					AND sei.type = 7 and sei.post_type = 2
				</when>
				<otherwise>
					AND cbopr.position_type_id = #{request.positionType}
				</otherwise>
			</choose>

		</if>
		<if test="request.business != null and request.business != ''">
			AND (sav2.employee_id like concat('%', #{request.business}, '%') or sav2.employee_name like concat('%', #{request.business}, '%'))
		</if>
		<if test="request.organizationIds != null and request.organizationIds.size>0 and organizationType != null">
			<choose>
				<when test="organizationType == 'area'">
					AND  sei.area_code
				</when>
				<when test="organizationType == 'varea'">
					AND  sei.varea_organization_id
				</when>
				<when test="organizationType == 'province'">
					AND  sei.province_organization_id
				</when>
				<when test="organizationType == 'company'">
					AND  sei.company_code
				</when>
				<when test="organizationType == 'department'">
					AND  sei.department_code
				</when>
				<otherwise>
				</otherwise>
			</choose>
				in (
					<foreach collection="request.organizationIds" item="item" index="index" separator=",">
						#{item}
					</foreach>
				)
		</if>
		<if test="request.attendanceStatus != null ">
			AND  sav2.attendance_status = #{request.attendanceStatus}
		</if>
		<if test="request.attendanceExecptionType != null ">
			<choose>
				<when test="request.attendanceExecptionType == 2">
					AND  sav2.attendance_execption_type in (2,3)
				</when>
				<otherwise>
					AND  sav2.attendance_execption_type = #{request.attendanceExecptionType}
				</otherwise>
			</choose>
		</if>
		<choose>
			<when test="loginOrganizationType == 'zb'">
				<choose>
					<when test="request.needAudit != null and request.needAudit == 1">
						AND sav2.zb_audit_status = 0
					</when>
					<otherwise>
						<if test="request.auditStatus != null ">
							AND  sav2.zb_audit_status = #{request.auditStatus}
						</if>
					</otherwise>
				</choose>
				<if test="request.auditStatusList != null and request.auditStatusList.size>0">
					AND sav2.zb_audit_status in
					<foreach collection="request.auditStatusList" item="item" open="(" close=")" separator=",">
						#{item}
					</foreach>
				</if>
			</when>
			<otherwise>
				<choose>
					<when test="request.needAudit != null and request.needAudit == 1">
						AND sav2.business_audit_person = #{request.person} AND sav2.business_audit_status = 0
					</when>
					<otherwise>
						<if test="request.auditStatus != null ">
							AND  business_audit_status = #{request.auditStatus}
						</if>
					</otherwise>
				</choose>
				<if test="request.auditStatusList != null and request.auditStatusList.size>0">
					AND business_audit_status in
					<foreach collection="request.auditStatusList" item="item" open="(" close=")" separator=",">
						#{item}
					</foreach>
				</if>
			</otherwise>
		</choose>
		<if test="request.attendancePointStatus != null ">
			AND sav2.attendance_point_status = #{request.attendancePointStatus}
		</if>
		<!--face_similar_score有为空的情况，增加ifnull处理保证每次排序结果一致-->
		order by ifnull(sav2.face_similar_score,0)
	</select>

	<select id="getAttendanceV2ListForZBRole" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceListV2Vo">
		SELECT
		sav2.work_day_type,
		sav2.id,
		sav2.calendar_date as `date`,
		sav2.employee_id as memberId,
		sav2.employee_name as employeeName,
		sav2.attendance_time AS attendanceTime,
		if(sav2.attendance_execption_type=3, 2, sav2.attendance_execption_type) as attendanceExecptionType,
		CONCAT(sav2.province,sav2.city,sav2.district,IFNULL(sav2.street, '')) as attendanceAddress,
		sav2.attendance_status,
		'总部' as positionName,
		sav2.pic_url AS picUrl,
		sav2.face_url AS faceUrl,
		sav2.longitude,
		sav2.latitude,
		sav2.zb_audit_status AS auditStatus,
		sav2.zb_audit_reason AS auditReason,
		sav2.zb_audit_name AS auditName
		FROM
		sfa_attendance_v2 sav2
		WHERE
		sav2.attendance_type = 1 AND sav2.delete_flag = 0 AND sav2.attendance_time BETWEEN #{request.searchStartTime} AND #{request.searchEndTime}
		AND sav2.employee_info_id is null
		<if test="null != employeeIds  and employeeIds.size()>0  ">
			<foreach collection="employeeIds" open=" and sav2.employee_id in (" close=")" separator="," item="item">
				#{item}
			</foreach>
		</if>
		<if test="request.business != null and request.business != ''">
			AND (sav2.employee_id like concat('%', #{request.business}, '%') or sav2.employee_name like concat('%', #{request.business}, '%'))
		</if>
		<if test="request.attendanceStatus != null ">
			AND  sav2.attendance_status = #{request.attendanceStatus}
		</if>
		<if test="request.attendancePointStatus != null ">
			AND sav2.attendance_point_status = #{request.attendancePointStatus}
		</if>
		<choose>
			<when test="request.attendanceExecptionType == 2">
				AND  sav2.attendance_execption_type in (2,3)
			</when>

		</choose>
		<if test="request.attendanceExecptionType != null ">
			<choose>
				<when test="request.attendanceExecptionType == 2">
					AND  sav2.attendance_execption_type in (2,3)
				</when>
				<otherwise>
					AND  sav2.attendance_execption_type = #{request.attendanceExecptionType}
				</otherwise>
			</choose>
		</if>
	</select>

	<select id="getAttendanceV2Detail" resultType="com.wantwant.sfa.backend.attendance.vo.AttendanceListV2Vo">
		SELECT
			sav2.work_day_type,
			sav2.id,
			sav2.calendar_date as `date`,
			sei.area_name as area,
			sei.id as employeeInfoId,
			sei.varea_organization_name as virtualAreaName,
			sei.province_organization_name as provinceName,
			sei.company_name as company,
			sei.department_name as departmentName,
			sav2.employee_id as memberId,
			sav2.employee_name as employeeName,
			sav2.attendance_time AS attendanceTime,
			sav2.longitude,
			sav2.latitude,
			sav2.pic_url AS picUrl,
			sav2.attendance_point_status,
			sav2.face_url AS faceUrl,
			ifnull(sei.onboard_time, cbopr.onboard_time) AS onboardTime,
			if(sav2.attendance_execption_type=3, 2, sav2.attendance_execption_type) as attendanceExecptionType,
			CONCAT(sav2.province,sav2.city,sav2.district,IFNULL(sav2.street, '')) as attendanceAddress,
			sav2.attendance_status,
		    sav2.sign_up_pic_url AS signUpPicUrl,
		    sav2.face_similar_score AS faceSimilarScore,
			sapr.distance_exceeded,
			sapr.exceeds_range
		<choose>
			<when test="loginOrganizationType == 'zb'">
				,sav2.zb_audit_reason AS auditReason
			</when>
			<otherwise>
				,sav2.business_audit_reason AS auditReason
			</otherwise>
		</choose>
		FROM
		sfa_attendance_v2 sav2
		LEFT JOIn sfa_attendance_partner_relation sapr on sapr.attendance_id = sav2.id
		and sapr.delete_flag = 0
		LEFT JOIN sfa_employee_info sei ON sei.id = sav2.employee_info_id
		LEFT JOIN ceo_business_organization_position_relation cbopr on cbopr.employee_id = sav2.employee_id and cbopr.business_group  = #{businessGroup}
		WHERE sav2.id = #{id}
		limit 1
	</select>
	<sql id="mapAttendenceAllInfo">
		sav2.id AS id,
		DATE(CONVERT_TZ(sav2.attendance_time, @@global.time_zone, #{params.timezone})) AS calendarDate,
		YEAR(CONVERT_TZ(sav2.attendance_time, @@global.time_zone, #{params.timezone})) AS calendarYear,
		MONTH(CONVERT_TZ(sav2.attendance_time, @@global.time_zone, #{params.timezone})) AS calendarMonth,
		DAY(CONVERT_TZ(sav2.attendance_time, @@global.time_zone, #{params.timezone})) AS calendarDay,
		sav2.work_day_type AS workDayType,
		sav2.company AS company,
		sav2.employee_info_id AS employeeInfoId,
		sav2.employee_id AS employeeId,
		sav2.employee_name AS employeeName,
		CONVERT_TZ(sav2.attendance_standard_time, @@global.time_zone, #{params.timezone}) AS attendanceStandardTime,
		sav2.attendance_type AS attendanceType,
		sav2.attendance_execption_type AS attendanceExecptionType,
		sav2.attendance_status AS attendanceStatus,
		CONVERT_TZ(sav2.attendance_time, @@global.time_zone, #{params.timezone}) AS attendanceTime,
		sav2.province AS province,
		sav2.city AS city,
		sav2.district AS district,
		sav2.street AS street,
		sav2.longitude AS longitude,
		sav2.latitude AS latitude,
		sav2.face_similar_score AS faceSimilarScore,
		sav2.sign_up_pic_url AS signUpPicUrl,
		sav2.pic_url AS picUrl,
		sav2.pic_name AS picName,
		sav2.face_url AS faceUrl,
		sav2.business_audit_status AS businessAuditStatus,
		sav2.business_audit_reason AS businessAuditReason,
		sav2.business_audit_person AS businessAuditPerson,
		sav2.business_audit_name AS businessAuditName,
		sav2.zb_audit_status AS zbAuditStatus,
		sav2.zb_audit_reason AS zbAuditReason,
		sav2.zb_audit_person AS zbAuditPerson,
		sav2.zb_audit_name AS zbAuditName,
		CONVERT_TZ(sav2.create_time, @@global.time_zone, #{params.timezone}) AS createTime,
		sav2.create_person AS createPerson,
		CONVERT_TZ(sav2.updated_time, @@global.time_zone, #{params.timezone}) AS updatedTime,
		sav2.update_person AS updatePerson,
		sav2.create_type AS createType,
		sav2.delete_flag AS deleteFlag
	</sql>
	<select id="selectAttendanceV2ListForMap" resultType="com.wantwant.sfa.backend.model.attendanceTask.Attendance">
		SELECT
			sbt.trip_id is not null as isTrip,
			CONCAT(sav2.province,sav2.city,sav2.district,IFNULL(sav2.street, '')) as address,
			<include refid="mapAttendenceAllInfo"/>
		FROM
		sfa_attendance_v2 sav2
		left join sfa_business_trip sbt on sbt.created_by = sav2.employee_info_id and sbt.status=2 and sbt.is_delete=0
		and sbt.start_date <![CDATA[ <= ]]> sav2.calendar_date
		and sbt.end_date <![CDATA[ >= ]]> sav2.calendar_date
		WHERE
		sav2.attendance_type in (1,2) AND sav2.delete_flag = 0
		and sav2.attendance_time &gt;= CONVERT_TZ(#{params.attendanceStartDateTime},#{params.timezone},@@global.time_zone)
		and sav2.attendance_time &lt;= CONVERT_TZ(#{params.attendanceEndDateTime},#{params.timezone},@@global.time_zone)
		AND sav2.employee_info_id = #{params.employeeInfoId}
		group by sav2.id
	</select>

	<select id="selectAttendanceV2ListForMapSame" resultType="com.wantwant.sfa.backend.model.attendanceTask.Attendance">
		SELECT
			<include refid="mapAttendenceAllInfo"/>
		FROM
		sfa_attendance_v2 sav2
		WHERE
		sav2.attendance_type in (1,2) AND sav2.delete_flag = 0
		and sav2.attendance_time &gt;= CONVERT_TZ(#{params.attendanceStartDateTime},#{params.timezone},@@global.time_zone)
		and sav2.attendance_time &lt;= CONVERT_TZ(#{params.attendanceEndDateTime},#{params.timezone},@@global.time_zone)
		<foreach collection="params.sameEmployeeInfoIdList" open=" and sav2.employee_info_id in (" close=")" separator="," item="item">
			#{item}
		</foreach>
		group by sav2.id
	</select>

	<select id="selectAttendanceV2LatelyForMap" resultType="com.wantwant.sfa.backend.model.attendanceTask.Attendance">
		SELECT
		sav2.*
		FROM
		sfa_attendance_v2 sav2
		WHERE
		sav2.attendance_type = 1 AND sav2.delete_flag = 0
		and sav2.calendar_date &lt; #{params.attendanceStartDate}
		AND sav2.employee_info_id = #{params.employeeInfoId}
		order by sav2.id desc
		limit 1
	</select>

	<select id="selectAttendanceV2ListForDaily" resultType="com.wantwant.sfa.backend.model.attendanceTask.Attendance">
		SELECT
		sav2.*
		FROM
		sfa_attendance_v2 sav2
		WHERE
		sav2.attendance_type in (1,2) AND sav2.delete_flag = 0
		and sav2.calendar_date = #{params.theDate}
		<if test="null != params.employeeInfoIdList and params.employeeInfoIdList.size()>0 ">
			<foreach collection="params.employeeInfoIdList" open=" and sav2.employee_info_id in (" close=")" separator="," item="item">
				#{item}
			</foreach>
		</if>
		group by sav2.employee_info_id
	</select>
	
	<select id="selectMemberCalendarByCompany" resultType="com.wantwant.sfa.backend.attendance.vo.MemberCalendarVO">

		select

			DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') as  calendarDate,
			CASE
				DAYNAME( mc.calendar_date )
				WHEN 'Monday' THEN
					'周一'
				WHEN 'Tuesday' THEN
					'周二'
				WHEN 'Wednesday' THEN
					'周三'
				WHEN 'Thursday' THEN
					'周四'
				WHEN 'Friday' THEN
					'周五'
				WHEN 'Saturday' THEN
					'周六'
				WHEN 'Sunday' THEN
					'周日' ELSE ''
				END AS dayOfWeekStr,
			DAYNAME( mc.calendar_date ) AS dayOfWeek,
			mc.calendar_year as calendarYear,
			mc.calendar_month as calendarMonth,
			mc.calendar_day as calendarDay,
			mc.is_work_day as isWorkDay,
			mc.company
		FROM
			sfa_apply_member sam
				inner join sfa_employee_info sei on
				sam.id = sei.application_id
				inner join hotkidceo_member.member_calendar mc on
				mc.company = IF(LOCATE('分',sam.company)>0,SUBSTRING(sam.company,1,LOCATE('分',sam.company)),sam.company)
		where
			sei.id = #{employeeInfoId}
		  and DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
		  and DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
		  and mc.delete_state = 0
		order by
			mc.calendar_date

	</select>

	<select id="selectMemberCalendarByWorkPlace" resultType="com.wantwant.sfa.backend.attendance.vo.MemberCalendarVO">
		select
			DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') as  calendarDate,
			CASE
				DAYNAME( mc.calendar_date )
				WHEN 'Monday' THEN
					'周一'
				WHEN 'Tuesday' THEN
					'周二'
				WHEN 'Wednesday' THEN
					'周三'
				WHEN 'Thursday' THEN
					'周四'
				WHEN 'Friday' THEN
					'周五'
				WHEN 'Saturday' THEN
					'周六'
				WHEN 'Sunday' THEN
					'周日' ELSE ''
				END AS dayOfWeekStr,
			DAYNAME( mc.calendar_date ) AS dayOfWeek,
			mc.calendar_year as calendarYear,
			mc.calendar_month as calendarMonth,
			mc.calendar_day as calendarDay,
			mc.is_work_day as isWorkDay,
			mc.company
		FROM
			hotkidceo_member.member_calendar mc
			where
			    mc.company = (
			        select
						IF(LOCATE('分',cbo.organization_name)>0,SUBSTRING(cbo.organization_name,1,LOCATE('分',cbo.organization_name)),cbo.organization_name)
						from sfa_apply_member sam
						inner join sfa_employee_info sei on sam.id = sei.application_id
			        	inner join ceo_business_organization cbo on sam.work_place = cbo.organization_id
			            where sei.id = #{employeeInfoId}
				)
			    and DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') <![CDATA[ >= ]]> #{startDate}
			    and DATE_FORMAT(mc.calendar_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{endDate}
				and mc.delete_state = 0
		order by
			mc.calendar_date
	</select>

	<select id="selectNearestPartner" resultType="com.wantwant.sfa.backend.attendance.vo.NearestPartnerVo">
		SELECT
			sei.member_key as nearestPartnerMemberKey,
			ifnull(a.partner_company_name,a.user_name) as nearestPartnerName,
			sab.address as nearestPartnerAddress,
			sab.type as nearestPartnerAddressType,
			sab.door_photo as nearestPartnerDoorPhotoUrlStr,
			sab.latitude as nearestPartnerAddressLatitude,
			sab.longitude as nearestPartnerAddressLongitude
		FROM
			sfa_attendance_partner_relation sapr
			inner join sfa_employee_info sei on sei.member_key = sapr.member_key
			inner join sfa_apply_member a on a.id = sei.application_id
			left join sfa_address_book sab on sab.id = sapr.address_id
			and sab.delete_flag = 0
		where sapr.delete_flag = 0 and sapr.attendance_id = #{attendanceId}
		limit 1
	</select>


</mapper>