<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.arch.DeptMapper">

    <select id = "selectDeptList" resultType="com.wantwant.sfa.backend.arch.entity.DepartEntity">
        select d.id,d.dept_code,d.status,d.dept_name,d.ancestors,d.superior_dept_id,d.delete_flag,d.leader_id,count(distinct sedr.employee_id) as employeeSize,d.create_time  from sfa_dept d
        left join sfa_employee_dept_relation sedr on d.id = sedr.dept_id
        and sedr.delete_flag = 0

        <where>
            <if test="deptName != null and deptName !='' ">
                and d.dept_name like concat('%',#{deptName},'%')
            </if>
            <if test="status != null ">
                and d.status = #{status}
            </if>
            <if test="createStartDate != null and createStartDate != '' ">
                and d.create_time >= #{createStartDate}
            </if>
            <if test="createEndDate != null and createEndDate != '' ">
                and d.create_time <![CDATA[ <= ]]> #{createEndDate}
            </if>
        </where>
        group by d.id
    </select>



    <select id="getUserListByDeptCode" resultType="com.wantwant.sfa.backend.arch.vo.UserVo">
        select distinct cbopr.employee_id as userId,cbopr.employee_name as userName
        from sfa_employee_dept_relation sedp
        inner join sfa_dept d on d.id = sedp.dept_id
        and sedp.delete_flag = 0 and d.delete_flag = 0
        inner join ceo_business_organization_position_relation cbopr on cbopr.employee_id = sedp.employee_id
        and cbopr.channel = 3
        where d.dept_code = #{deptCode}
    </select>

    <select id="selectDeptByEmployeeId" resultType="java.lang.String">
        SELECT
        distinct dept_code
        from sfa_employee_dept_relation sedp
        inner join sfa_dept d on d.id = sedp.dept_id
        and sedp.delete_flag = 0 and d.delete_flag = 0
        where sedp.employee_id = #{employeeId}
    </select>

    <select id="selectDept1ByEmployeeId" resultType="java.lang.String">
        SELECT
        distinct dept_code
        from sfa_employee_dept_relation sedp
        inner join sfa_dept d on d.id = sedp.dept_id
        and sedp.delete_flag = 0 and d.delete_flag = 0
        where sedp.employee_id = #{employeeId}
        UNION ALL
		SELECT
        distinct d1.dept_code
        from sfa_employee_dept_relation sedp
        inner join sfa_dept d on d.id = sedp.dept_id and sedp.delete_flag = 0 and d.delete_flag = 0
        inner join sfa_dept d1 on d1.id = d.superior_dept_id and d1.delete_flag = 0
        where sedp.employee_id = #{employeeId}
    </select>

    <select id="getDeptEmpList" resultType="com.wantwant.sfa.backend.arch.vo.ArchEmpVo">
        SELECT
            cbopr.employee_id as empId,
            cbopr.employee_name as empName,
            group_concat(distinct sp.position_name) as `position`,
            spe.avator as avatar,
            sper.part_time as partTime,
            sp.position_id
        FROM
            sfa_employee_dept_relation sedr
            INNER JOIN ceo_business_organization_position_relation cbopr ON cbopr.employee_id = sedr.employee_id
            and sedr.delete_flag = 0
            INNER JOIN sfa_dept d on d.id = sedr.dept_id
            and d.delete_flag = 0
            INNER JOIN sfa_position_emp spe on spe.emp_id = cbopr.employee_id
            and spe.delete_flag = 0
          INNER JOIN sfa_position_employee_relation sper on sper.position_emp_id = spe.id
            and sper.delete_flag = 0
            INNER JOIN sfa_position sp on sp.position_id = sper.position_id
            and sp.delete_flag = 0 and sp.dept_code = d.dept_code
            where d.dept_code = #{deptCode}
        group by d.dept_code,cbopr.employee_id

    </select>

    <select id="selectEmpByDept" resultType="java.lang.String">
        SELECT r.employee_id from sfa_dept d
        LEFT JOIN sfa_dept cd on cd.superior_dept_id = d.id
        INNER JOIN sfa_employee_dept_relation r on (r.dept_id = d.id or r.dept_id = cd.id )
        where d.dept_code = #{deptCode} and d.delete_flag = 0
    </select>
    <select id="selectEmployeeNameByDepartCodeAndEmpId" resultType="java.lang.String">
        SELECT
            DISTINCT cbopr.employee_name AS userName
        FROM
            sfa_employee_dept_relation sedp
                INNER JOIN sfa_dept d ON d.id = sedp.dept_id
                AND sedp.delete_flag = 0
                AND d.delete_flag = 0
                INNER JOIN ceo_business_organization_position_relation cbopr ON cbopr.employee_id = sedp.employee_id
                AND cbopr.channel = 3
        WHERE
            d.dept_code = #{deptCode}
          AND cbopr.employee_id = #{employeeId}
            LIMIT 1
    </select>
    <select id="queryDeprByName" resultType="com.wantwant.sfa.backend.arch.entity.DepartEntity">
        select
            d.id,
            d.dept_code,
            d.status,
            d.dept_name,
            d.ancestors,
            d.superior_dept_id,
            d.delete_flag,
            d.leader_id,
            d.create_time
        from
            sfa_dept d
        where
            d.dept_name = #{deptName}
        and status = 1
        and delete_flag = 0
    </select>
    <select id="queryDeptByEmpId" resultType="com.wantwant.sfa.backend.arch.entity.DepartEntity">
        select
            d.id,
            d.dept_code,
            d.status,
            d.dept_name,
            d.ancestors,
            d.superior_dept_id,
            d.delete_flag,
            d.leader_id,
            d.create_time
        from
            sfa_employee_dept_relation t
                left join sfa_dept d on t.dept_id = d.id
        where
            t.employee_id = #{empId}
          and d.status = 1
          and d.delete_flag = 0
          and t.delete_flag = 0
        LIMIT 1
    </select>
</mapper>