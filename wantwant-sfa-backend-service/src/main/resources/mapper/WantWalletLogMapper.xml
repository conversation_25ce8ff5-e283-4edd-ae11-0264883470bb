<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper

        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"

        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
   mapper标签:配置各类声明
   namespace：名称空间，由于映射文件有多个，为了防止crud语句的唯一标识被重复，可以设置空间名称。
 -->
<mapper namespace="com.wantwant.sfa.backend.mapper.wallet.WantWalletLogMapper">

    <update id="batchUpdate">
        update sfa_want_wallet_log
        set  surplus=
        <foreach collection="walletBatch" item="item"
                 separator=" " open="case log_id " close="end">
            when #{item.logId} then #{item.quota}
        </foreach>
        where log_id in
        <foreach collection="walletBatch" item="item"
                 separator="," open="(" close=")">
            #{item.logId,jdbcType=BIGINT}
        </foreach>

    </update>



    <select id="selectTotalQuota" resultType="java.math.BigDecimal">
        SELECT
            sum(sww.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            <if test="walletTypeId != null">
                and sww.wallet_type_id = #{walletTypeId}
            </if>
            and sww.delete_flag = 0
            and account.delete_flag = 0
        where account.organization_id = #{organizationId}
    </select>


    <select id="selectSurplusQuota" resultType="java.math.BigDecimal">
        select sum(swwl.surplus) from sfa_want_wallet_account swwa
        inner join sfa_want_wallet_log swwl on swwl.wallet_account_id  = swwa.account_id
        and swwl.delete_flag  = 0 and swwl.`type`  = 1
        <if test="walletTypeId != null">
            and swwl.wallet_type_id  = #{walletTypeId}
        </if>

        where swwa.organization_id  = #{organizationId}
        <if test="walletTypeId != null and walletTypeId == 2 and walletSubTypeId != null">
            and swwl.sub_type_id =  #{walletSubTypeId}
        </if>

        <if test="applyTypeIds != null and applyTypeIds.size() > 0">
            <foreach collection="applyTypeIds" item="item" open=" and swwl.apply_type in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectCurMonthAddQuota" resultType="java.math.BigDecimal">
        SELECT
            sum(l.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            <if test="walletTypeId != null">
                and sww.wallet_type_id = #{walletTypeId}
            </if>
            and sww.delete_flag = 0
            and account.delete_flag = 0
            INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
			and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
        where account.organization_id = #{organizationId} and l.type = 1
        and l.create_time between date_add(curdate(), interval - day(curdate()) + 1 day)
		and concat(last_day(curdate()),'T23:00:00');
    </select>

    <select id="selectUsedQuota" resultType="java.math.BigDecimal">
         SELECT
            sum(l.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            <if test="walletTypeId != null">
                and sww.wallet_type_id = #{walletTypeId}
            </if>
            and sww.delete_flag = 0
            and account.delete_flag = 0
            INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
			and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
        where account.organization_id = #{organizationId} and l.type = 2
    </select>

    <select id="selectLockedQuota"  resultType="java.math.BigDecimal">
        SELECT
            sum(l.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            <if test="walletTypeId != null">
                and sww.wallet_type_id = #{walletTypeId}
            </if>
            and sww.delete_flag = 0
            and account.delete_flag = 0
            INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
			and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
			<if test="subTypeId != null and subTypeId != '' ">
                and l.sub_type_id = #{subTypeId}
            </if>
        where account.organization_id = #{organizationId} and l.type = 5
    </select>

    <select id="selectHistoryAllocationQuota" resultType="java.math.BigDecimal">
         SELECT
            sum(l.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            <if test="walletTypeId != null">
                and sww.wallet_type_id = #{walletTypeId}
            </if>
            and sww.delete_flag = 0
            and account.delete_flag = 0
            INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
			and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
        where account.organization_id = #{organizationId} and l.type = 2
        and l.create_time <![CDATA[ < ]]> date_add(curdate(), interval - day(curdate()) + 1 day)
    </select>


    <select id="selectQuotaFromLog" resultType="java.math.BigDecimal">
        SELECT
            sum(l.quota)
        FROM
            sfa_want_wallet_account account
            INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
            and sww.wallet_type_id = #{walletTypeId}
            and sww.delete_flag = 0
            and account.delete_flag = 0
            INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
			and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
        where account.organization_id = #{organizationId} and l.type = #{type}
        <if test="subTypeId != null and subTypeId != '' ">
            and l.sub_type_id =  #{subTypeId}
        </if>
    </select>

    <select id="selectSurplusFromLog" resultType="java.math.BigDecimal">
        SELECT
        sum(l.surplus)
        FROM
        sfa_want_wallet_account account
        INNER JOIN sfa_want_wallet sww ON account.account_id = sww.wallet_account_id
        <if test="walletTypeId != null">
            and sww.wallet_type_id = #{walletTypeId}
        </if>
        and sww.delete_flag = 0
        and account.delete_flag = 0
        INNER JOIN sfa_want_wallet_log l on l.wallet_account_id = sww.wallet_account_id
        and l.wallet_type_id = sww.wallet_type_id and l.delete_flag = 0
        where account.organization_id = #{organizationId} and l.type = 1
        <if test="subTypeId != null and subTypeId != '' ">
            and l.sub_type_id =  #{subTypeId}
        </if>
    </select>

    <select id="selectFinancialYearAddQuota" resultType="java.math.BigDecimal">
        select sum(swwl.quota) from sfa_want_wallet_account swwa
        inner join sfa_want_wallet_log swwl  on swwl.wallet_account_id  = swwa.account_id
        and swwa.organization_id  = #{organizationId} and swwl.create_time  >= #{startDate} and swwl.create_time  <![CDATA[ <= ]]>  concat(#{endDate},"T23:59:59")
        and swwl.`type`  = 1 and swwl.delete_flag  = 0
    </select>


    <select id="selectIncome" resultType="java.math.BigDecimal">
        select sum(swwl.quota) from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        where swwl.delete_flag  = 0 and swwa.organization_id  = #{organizationId}
        and swwl.`type`  = 1
        <if test="yearMonth != null and yearMonth != '' ">
            and swwl.create_time  like concat(#{yearMonth},'%')
        </if>
        <if test="startDate != null">
            and swwl.create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            and swwl.create_time &lt;= concat(#{endDate},'T23:59:59')
        </if>
    </select>


    <select id="selectFreeze" resultType="String">
        select distinct(cbo.organization_name) from ceo_business_organization_tree cbot
        inner join ceo_business_organization cbo  on cbo.organization_id  = cbot.organization_parent_id
        inner join sfa_position_relation spr on spr.organization_code  = cbot.organization_parent_id
        and spr.status  = 1 and spr.business_group  = cbo.business_group
        inner join sfa_want_wallet_black_list swwbl  on swwbl.employee_id  = spr.emp_id
        and swwbl.delete_flag  = 0
        where cbot.organization_id  = #{organizationId} and cbot.`level` > 0
    </select>


    <select id="selectTotalSurplusQuota" resultType="java.math.BigDecimal">
        select sum(swwl.surplus) from sfa_want_wallet_account swwa
        inner join sfa_want_wallet_log swwl on swwl.wallet_account_id  = swwa.account_id
        where swwa.organization_id  = #{organizationId} and swwl.delete_flag  = 0
        and swwl.`type`  = 1
    </select>


    <select id="selectSurplus" resultType="java.math.BigDecimal">
        select sum(swwl.surplus)  from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        where swwa.organization_id  = #{organizationId}
        and swwl.`type`  = 1
        and swwl.delete_flag  = 0
    </select>

    <select id="selectOldInCome" resultType="java.math.BigDecimal">
        select sum(quota)  from sfa_activity_quota_log saql
        where saql.organization_id  = #{organizationId}
        and saql.delete_flag  = 0
        and saql.type = 1
        and saql.process_user_id  != '********'
        and saql.create_time  >= '2023-07-01'
    </select>

    <select id="selectExceptionOrg" resultType="String">
        select added.organization_id from
        (
        select swwl.wallet_account_id,cbo.organization_id ,swwl.wallet_type_id ,sum(quota) quota from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        inner join ceo_business_organization cbo  on cbo.organization_id  = swwa.organization_id
        where swwl.`type`  = 1
        group by swwl.wallet_account_id,swwl.wallet_type_id
        ) added
        inner join
        (
        select swwl.wallet_account_id,cbo.organization_id ,swwl.wallet_type_id ,sum(surplus) quota from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        inner join ceo_business_organization cbo  on cbo.organization_id  = swwa.organization_id
        where swwl.`type`  = 1
        group by swwl.wallet_account_id ,swwl.wallet_type_id
        ) total on total.wallet_account_id = added.wallet_account_id
        and total.wallet_type_id = added.wallet_type_id
        left join
        (
        select swwl.wallet_account_id,cbo.organization_id  ,swwl.wallet_type_id ,sum(quota) quota from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        inner join ceo_business_organization cbo  on cbo.organization_id  = swwa.organization_id
        where swwl.`type`  = 2
        group by swwl.wallet_account_id,swwl.wallet_type_id
        ) used  on used.wallet_account_id = added.wallet_account_id
        and used.wallet_type_id = added.wallet_type_id
        left join
        (
        select swwl.wallet_account_id,cbo.organization_id ,swwl.wallet_type_id ,sum(quota) quota from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_account swwa on swwa.account_id  = swwl.wallet_account_id
        inner join ceo_business_organization cbo  on cbo.organization_id  = swwa.organization_id
        where swwl.`type`  = 5
        group by swwl.wallet_account_id ,swwl.wallet_type_id
        ) locked on locked.wallet_account_id = added.wallet_account_id
        and locked.wallet_type_id = added.wallet_type_id

        left join (
            select sp.actual_penalty_organization_id,swwl.wallet_type_id ,sum(spcd.amount) quota   from sfa_penalty sp
            inner join sfa_penalty_cost_detail spcd on spcd.penalty_id  = sp.id
            inner join sfa_want_wallet_log swwl on swwl.log_id  = spcd.wallet_log_id
            where sp.delete_flag  = 0
            and sp.status  in (1,3)
            group by sp.actual_penalty_organization_id,swwl.wallet_type_id
        )penalty on penalty.actual_penalty_organization_id = added.organization_id
        and penalty.wallet_type_id = added.wallet_type_id
         where added.quota - ifnull(used.quota,0) - ifnull(locked.quota,0) -ifnull(penalty.quota,0) != total.quota
    </select>

    <select id="selectPenaltyLog" resultType="com.wantwant.sfa.backend.wallet.entity.WantWalletLogEntity">
        select swwl.* from sfa_want_wallet_log swwl
        inner join sfa_want_wallet_type swwt on swwt.id  = swwl.wallet_type_id
        where swwl.wallet_account_id  = #{accountId} and swwl.delete_flag = 0
        and swwl.surplus  > 0 and swwl.type = 1
        <if test="applyTypeIds != null and applyTypeIds.size() > 0">
            <foreach collection="applyTypeIds" item="item" open=" and swwl.apply_type in (" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by swwt.weight desc,swwl.log_id
    </select>



    <select id="selectHasSurplusSpuId" resultType="String">
        select distinct swwl.sub_type_id  from sfa_want_wallet_account swwa
        inner join sfa_want_wallet_log swwl  on swwl.wallet_account_id  = swwa.account_id
        and swwl.delete_flag  = 0 and swwl.`type`  = 1 and swwl.quota  > 0
        and swwl.wallet_type_id  = 2
        <where>
            and swwa.organization_id  = #{organizationId}
        </where>
    </select>

    <select id="selectOrgSurplus" resultType="com.wantwant.sfa.backend.wallet.dto.OrgWallet">
        select swwa.organization_id orgCode,sum(sww.surplus) as surplus from sfa_want_wallet_account swwa
        inner join sfa_want_wallet sww on sww.wallet_account_id  = swwa.account_id
        <where>
            <foreach collection="orgList" item="item" separator="," open=" and swwa.organization_id in (" close=")">
                #{item}
            </foreach>
        </where>
        group by swwa.account_id

    </select>

    <select id="searchCeoQuotaDetail" resultType="com.wantwant.sfa.backend.wallet.dto.CeoQuotaDetailDTO">
        SELECT
            l.wallet_type_id,
            l.sub_type_id,
            sum(l.surplus) as surplusAmount
        FROM
        sfa_want_wallet_log l
                INNER JOIN sfa_want_wallet_account a ON a.account_id = l.wallet_account_id
                INNER JOIN ceo_business_organization cbo ON cbo.organization_id = a.organization_id
                INNER JOIN sfa_business_group sbg on sbg.id = cbo.business_group
        <where>
            <if test="organizationName != null and organizationName != ''">
                and cbo.organization_name = #{organizationName}
            </if>
            <if test="businessGroupCode != null and businessGroupCode != ''">
                and sbg.business_group_code = #{businessGroupCode}
            </if>
            <if test="walletType != null ">
                and l.wallet_type_id = #{walletType}
            </if>
            <if test="subTypeId != null and subTypeId != '' ">
                and l.sub_type_id = #{subTypeId}
            </if>
            and l.type = 1 and l.delete_flag = 0
        </where>
        group by l.wallet_type_id
         <if test="walletType != null and  walletType == 2">
            ,l.sub_type_id
         </if>

    </select>
</mapper>