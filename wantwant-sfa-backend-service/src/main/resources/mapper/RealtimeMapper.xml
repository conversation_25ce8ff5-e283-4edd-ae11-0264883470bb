<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wantwant.sfa.backend.mapper.RealtimeMapper">

    <resultMap id="mainResultMap" type="com.wantwant.sfa.backend.display.vo.ExportOrganizationMainVO">
        <id column="organizationId" property="organizationId"/>
        <collection ofType="com.wantwant.sfa.backend.realData.vo.MainProductsDataVo" property="mainList">
            <result column="mainProductsName" property="mainProductsName"/>
            <result column="mainProductsItemsSupplyTotal" property="mainProductsItemsSupplyTotal"/>
            <result column="mainProductsSalegoal" property="mainProductsSalegoal"/>
            <result column="mainProductsSaleGoalAchievementRate" property="mainProductsSaleGoalAchievementRate"/>
        </collection>
    </resultMap>

    <resultMap id="mainResultMonth" type="com.wantwant.sfa.backend.display.vo.ExportOrganizationMainVO">
        <id column="organizationId" property="organizationId"/>
        <collection ofType="com.wantwant.sfa.backend.realData.vo.MainProductsDataVo" property="mainList">
            <result column="mainProductsName" property="mainProductsName"/>
            <result column="mainProductsItemsSupplyTotal" property="mainProductsItemsSupplyTotal"/>
            <result column="mainProductsSalegoal" property="mainProductsSalegoal"/>
            <result column="mainProductsSaleGoalAchievementRate" property="mainProductsSaleGoalAchievementRate"/>
        </collection>
    </resultMap>

    <resultMap id="mainResultMonthQuarter" type="com.wantwant.sfa.backend.realData.vo.RealtimeMainProductVo">
        <id column="positionTypeId" property="positionTypeId"/>
        <id column="organizationId" property="organizationId"/>
        <id column="areaName" property="areaName"/>
        <id column="virtualAreaName" property="virtualAreaName"/>
        <id column="provinceName" property="provinceName"/>
        <id column="companyName" property="companyName"/>
        <id column="departmentName" property="departmentName"/>
        <id column="post" property="post"/>
        <id column="avatar" property="avatar"/>
        <id column="name" property="name"/>
        <id column="onboardDays" property="onboardDays"/>
        <id column="isNextRealtime" property="isNextRealtime"/>
        <id column="fullOrganizationName" property="fullOrganizationName"/>
        <id column="memberCount" property="memberCount"></id>
        <collection ofType="com.wantwant.sfa.backend.realData.vo.MainProductVo" property="mainList">
            <result column="mainProductName" property="mainProductName"/>
            <result column="biddingPerformance" property="biddingPerformance"/>
            <result column="goal" property="goal"/>
            <result column="goalAchievementRate" property="goalAchievementRate"/>
            <result column="sequentialGrowthRate" property="sequentialGrowthRate"/>
            <result column="yearGrowthRate" property="yearGrowthRate"/>
            <result column="invoicingPartnersNumber" property="invoicingPartnersNumber"/>
            <result column="partnerBillingRate" property="partnerBillingRate"/>
            <result column="billingRateSequentialIncreaseRate" property="billingRateSequentialIncreaseRate"/>
            <result column="perCapitaPerformance" property="perCapitaPerformance"/>
            <result column="perCapitaPerformanceGrowthRate" property="perCapitaPerformanceGrowthRate"/>
            <result column="tradingClients" property="tradingClients"/>
            <result column="numberOfDisplayCustomers" property="numberOfDisplayCustomers"/>
            <result column="guestUnitPrice" property="guestUnitPrice"/>
            <result column="mainProductsCashPerformance" property="mainProductsCashPerformance"></result>
        </collection>
    </resultMap>


    <resultMap id="TeamResultMonthly" type="com.wantwant.sfa.backend.realData.vo.TeamResultsVo">
        <id column="isNextRealtime" property="isNextRealtime"/>
        <id column="organizationId" property="organizationId"/>
        <id column="organizationName" property="organizationName"/>
        <id column="area" property="area"/>
        <id column="varea" property="varea"/>
        <id column="province" property="province"/>
        <id column="company" property="company"/>
        <id column="departmentName" property="departmentName"/>
        <id column="positionName" property="positionName"/>
        <id column="positionType" property="positionType"/>
        <id column="url" property="url"/>
        <id column="employeeName" property="employeeName"/>
        <id column="workingDays" property="workingDays"/>
        <id column="population" property="population"/>
        <id column="populationProportion" property="populationProportion"/>
        <id column="contractNum" property="contractNum"/>
        <id column="onJobNum" property="onJobNum"/>
        <id column="departmentNumsCm" property="departmentNumsCm"/>
        <id column="cityManagerHeadCount" property="cityManagerHeadCount"/>
        <id column="cityManagerOnBoardRate" property="cityManagerOnBoardRate"/>
        <id column="onBoardNumsCm" property="onBoardNumsCm"/>
        <id column="partTimeNum" property="partTimeNum"/>
        <id column="fullTimeNum" property="fullTimeNum"/>
        <id column="enterpriseNum" property="enterpriseNum"/>
        <id column="inductionNum" property="inductionNum"/>
        <id column="departureNum" property="departureNum"/>
        <id column="departureRate" property="departureRate"/>
        <id column="employmentRateSeason" property="employmentRateSeason"/>
        <id column="employmentRateYears" property="employmentRateYears"/>
        <id column="curItemsSupplyTotalCm" property="curItemsSupplyTotalCm"/>
        <id column="itemsSupplyTotalCmBudgetAvgGmv" property="itemsSupplyTotalCmBudgetAvgGmv"/>
        <id column="saleGoalAchievementRate" property="saleGoalAchievementRate"/>
        <id column="itemSupplyTotalYearOnYear" property="itemSupplyTotalYearOnYear"/>

        <collection ofType="com.wantwant.sfa.backend.realData.vo.TeamPerformanceVo" property="list">
            <result column="title" property="title"/>
            <result column="titleMonth" property="titleMonth"/>
            <result column="biddingPerformance" property="biddingPerformance"/>
            <result column="goal" property="goal"/>
            <result column="goalAchievementRate" property="goalAchievementRate"/>
            <result column="performanceAchievementRate" property="performanceAchievementRate"/>
            <result column="yearAchievementRate" property="yearAchievementRate"/>
            <result column="managementPositionPerPerformance" property="managementPositionPerPerformance"/>
            <result column="employmentRate" property="employmentRate"/>
            <result column="curItemsSupplyTotalCm" property="curItemsSupplyTotalCm"/>
            <result column="managementPositionTarget" property="managementPositionTarget"/>
            <result column="managementPositionGoalRate" property="managementPositionGoalRate"/>
            <result column="managementPositionChainRatio" property="managementPositionChainRatio"/>
            <result column="managementPositionYearOnYear" property="managementPositionYearOnYear"/>
        </collection>
    </resultMap>

    <sql id="column_list">
        d.employee_name as employeeName,d.biz_organization_id as organizationId,d.biz_office_name as organizationName,
        d.onboard_time as onboardTime,d.onboard_days as onboardDays,
        round(d.itemsSupplyTotal_cday,2) as itemsSupplyTotalCday,round(d.itemsSupplyTotal_cm,2) as itemsSupplyTotalCm,
        round(d.month_ring_ratio * 100,2) as monthRingRatio,round(d.itemsSupplyTotal_cm_avg,2) as itemsSupplyTotalCmAvg,round(d.profit_rate * 100,2) as profitRate,
        d.saleGoal as saleGoal,round(d.sale_goal_achievement_rate * 100,2) as saleGoalAchievementRate,
        d.cus as cus,d.filing_customer_cm as filingCustomerCm,round(d.repurchase_rate * 100,2) as repurchaseRate,
        d.filing_customer_no_order_cm as filingCustomerNoOrderCm,
        d.region_onboard_nums as regionOnboardNums,d.branch_onboard_nums as branchOnboardNums,
        d.biz_onboard_nums as bizOnboardNums,d.region_new_onboard_nums as regionNewOnboardNums,
        d.branch_new_onboard_nums as branchNewOnboardNums,d.biz_new_onboard_nums as bizNewOnboardNums,
        d.region_off_nums as regionOffNums,d.branch_off_nums as branchOffNums,
        d.biz_off_nums as bizOffNums,round(d.region_off_rate * 100,2) as regionOffRate,
        round(d.branch_off_rate * 100,2) as branchOffRate,round(d.biz_off_rate * 100,2) as bizOffRate,
        d.pic_url as picUrl,d.position_type_id as positionTypeId
    </sql>

    <select id="pageEmpGoal" resultType="com.wantwant.sfa.backend.realData.vo.EmployeeGoalVO">
        select
        tsg.region_name as area,tsg.branch_co_name as company,tsg.employee_name,tsg.position_id,
        tsg.mobile,tsg.itemsSupplyTotal_lm as quotationPriceLm,tsg.suggest_goals as suggestionGoal
        from tt_suggest_goals tsg
        <where>
            and tsg.the_year_mon = #{params.yearMonth}
            <if test="params.organizationId != 'ZB_Z' and params.organizationId != 'ZB_S' and params.organizationId != 'ZB' ">
                and (tsg.region_organization_id = #{params.organizationId} or tsg.branch_organization_id =
                #{params.organizationId})
            </if>
            <if test="params.areaOrganizationId !=null and params.areaOrganizationId != '' ">
                and tsg.region_organization_id = #{params.areaOrganizationId}
            </if>
            <if test="params.companyOrganizationName !=null and params.companyOrganizationName != '' ">
                and tsg.branch_co_name = #{params.companyOrganizationName}
            </if>
            <if test="params.employeeNameOrMobile !=null and params.employeeNameOrMobile != '' ">
                and (tsg.employee_name like concat('%',#{params.employeeNameOrMobile},'%') or tsg.mobile like
                concat('%',#{params.employeeNameOrMobile},'%'))
            </if>
        </where>
    </select>

    <select id="getSkuAnalyseListForSelectWorkPeriodDetail" resultType="com.wantwant.sfa.backend.workReport.vo.WorkReportSkuVO">
        select
            abos.sku_id as sku,
            dssbi.line_name as line,
            dssbi.spu_id as spuId,
            dssbi.line_id as lineId,
            dssbi.spu_name as spu,
            CONCAT_WS(' ',dssbi.sku_name,dssbi.sku_spec,dssbi.flavor) as name,
            dssbi.sku_images as skuImages,
            <!--
                ifnull(sa.quantity_cm,0) as quantityCm,
                ifnull(sa.audit_quantity_sum_cm,0) as monthlySalesEstimatedTarget,
                ifnull(round(sa.estimate_ratio_cm*100,2),0) as monthlySalesEstimatedTargetRate,
            -->
            ifnull(abos.sjchxs_582,0) as quantityCm,
            ifnull(abos.xsygxs_580,0) as monthlySalesEstimatedTarget,
            ifnull(round(abos.xsygdcl_51*100,2),0) as monthlySalesEstimatedTargetRate,

            abos.fpxs_581 as productionMarketingCoordinationAssignNum,

            ifnull(abos.zwbppjyj_cur_405,0) as pricePerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as pricePerformanceRatio,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            abos.wjbyj_cur_409 as goldenCoinPerformance,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio
        from ads_bigtable_organization_sku abos
            inner join dim_store_sku_base_info dssbi on dssbi.sku_id = abos.sku_id
        where abos.date_type_id = '10'
            and abos.sku_id is not null
            and abos.organization_id =#{organizationId}
            and  abos.the_year_month =  #{yearMonth}
        order by abos.zwbppjyj_cur_405 desc
    </select>

    <select id="getSkuAnalyseList" resultType="com.wantwant.sfa.backend.workReport.vo.WorkReportSkuVO">
        select
            abos.sku_id as sku,
            dssbi.line_name as line,
            dssbi.spu_name as spu,
            dssbi.spu_id as spuId,
            dssbi.line_id as lineId,
            CONCAT_WS(' ',dssbi.sku_name,dssbi.sku_spec,dssbi.flavor) as name,
            dssbi.sku_images as skuImages,

            <!--
                ifnull(sa.quantity_cm,0) as quantityCm,
                ifnull(sa.audit_quantity_sum_cm,0) as monthlySalesEstimatedTarget,
                ifnull(round(sa.estimate_ratio_cm*100,2),0) as monthlySalesEstimatedTargetRate,
            -->
            ifnull(abos.sjchxs_582,0) as quantityCm,
            ifnull(abos.xsygxs_580,0) as monthlySalesEstimatedTarget,
            ifnull(round(abos.xsygdcl_51*100,2),0) as monthlySalesEstimatedTargetRate,

            ifnull(abos.fpxs_581,0) as productionMarketingCoordinationAssignNum,

            ifnull(abos.zwbppjyj_cur_405,0) as pricePerformance,
            round(abos.zwbppjyj_cur_405_hb*100,1) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,1) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,1) as pricePerformanceRatio,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            abos.wjbyj_cur_409 as goldenCoinPerformance,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio
        from ads_bigtable_organization_sku abos
        inner join dim_store_sku_base_info dssbi on dssbi.sku_id = abos.sku_id
        where abos.date_type_id = '10'
            and abos.sku_id is not null
            and abos.organization_id =#{organizationId}
            and  abos.the_year_month =  #{yearMonth}
        order by abos.zwbppjyj_cur_405 desc
    </select>


    <select id="getBusinessWeeklyPerformanceList" resultType="com.wantwant.sfa.backend.workReport.vo.WorkReportSkuWeekVO">
        select
        sa.sku_id as sku,
        sa.week_day_of_month as weeks,
        ifnull(sa.supply_price_total_week,0) as skuActualPrice,
        ifnull(sa.quantity_total_week,0) as actualBoxNumber
        from business_weekly_performance_product sa
        where sa.the_year_month= #{yearMonth} and sa.organization_id=#{organizationId}
    </select>


    <select id="getWeekOfMonthRealTimeData" resultType="com.wantwant.sfa.backend.workReport.vo.WeekOfMonthRealTimeDataVo">
        select
        sa.week_day_of_month as weeks,
        ifnull(sa.supply_price_total_week,0) as weekActualPrice
        from business_weekly_performance sa
        where sa.the_year_month= #{yearMonth} and sa.organization_id=#{organizationId}
    </select>



    <select id="selarea" parameterType="string" resultType="string">
        select
        <if test="positionType==10" >
            branch_name
        </if>
        <if test="positionType==2" >
            province_name
        </if>
        <if test="positionType==11" >
            virtual_area_name
        </if>
        <if test="positionType==12" >
            region_name
        </if>
        <if test="businessGroup!=99">
            from  dash_board_details_record where the_year_mon = #{month}
        </if>
        <if test="businessGroup==99">
            from  dash_board_details_record_allbusinessgroup where the_year_mon = #{month}
        </if>
        <if test="positionType==10" >
            and  department_name= #{organizationName}
        </if>
        <if test="positionType==2" >
            and  branch_name = #{organizationName}
        </if>
        <if test="positionType==11" >
            and province_name = #{organizationName}
        </if>
        <if test="positionType==12" >
            and virtual_area_name = #{organizationName}
        </if>
        <if test="null != businessGroup" >
            and business_group=#{businessGroup}
        </if>
        limit 1
    </select>

    <select id="getFinancialYearOrQuarterNearestMonth" resultType="java.lang.String">
        <if test="dateTypeId == 11">
            select distinct the_year_month from dim_td_date d
            where the_year_quarter = #{dateParam}
            order by the_year_month
        </if>

        <if test="dateTypeId == 2">
            select distinct the_year_month from dim_td_date d
            where d.financial_year = #{dateParam}
            order by the_year_month
        </if>

    </select>

    <select id="getPersonalPerformanceInfo" resultType="com.wantwant.sfa.backend.realData.vo.RealTimeRankingPersonalInfoVo">

        <if test="'branch' == organizationType and 99!=businessGroup">
            SELECT
            di.pic_url as picUrl,
            abp.department_name as branchName,
            di.onboard_time as onboardTime,
            di.employee_name as employeeName,
            di.position_id as positionId,
            abp.business_group_id as businessGroupId,
            abp.zwbppjyj_cur_405 as zwProductPerformance,
            abp.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            abp.organization_name as organizationName,
            abp.area_name as areaName,
            abp.varea_name as virtualAreaName,
            abp.province_name as provinceName,
            abp.company_name as companyName,
            abp.department_name as departmentName
            from
            ads_bigtable_partner abp
            left join dim_emp_pos_role_org_mon_partner di
            on di.member_key  = abp.member_key and di.job_type_id = 1 and di.the_year_month = #{theDate}
            WHERE
            abp.the_year_month = #{yearMonth}
            and abp.date_type_id = #{dateTypeId}
            and abp.member_key = #{organizationId}
            and abp.business_group_id = #{businessGroup}
        </if>

        <if test="'branch' != organizationType">
            SELECT
            ampm.pic_url as picUrl,
            ampm.employee_name as employeeName,
            ampm.zw_onboard_days as onboardDays,
            ampm.onboard_time as onboardTime,
            ampm.position_id as positionId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            abo.organization_name as organizationName,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.business_group_id = #{businessGroup}
            and abo.position_type_id= #{positionTypeId}
            and abo.organization_id = #{organizationId}
        </if>

    </select>

    <select id="getPartnerNationalRanking" resultType="com.wantwant.sfa.backend.po.RealTimeRankingInfoPO">
        SELECT
        'national' as rankingType,
        organization_id as organizationId,
        abp.member_key as memberKey,
        ifnull(abp.zwbppjyj_cur_405,0) as zwProductPerformance,
        ifnull(abp.zwbppjyj_cur_405_tb,0) as zwProductPerformanceYearRatio,
        ifnull(abp.zwbppjyj_cur_405_hb,0) as zwProductPerformanceRatio,
        ifnull(abp.pjyjmbdcl_32,0) as performanceAchievementRate
        from
        ads_bigtable_partner abp
        WHERE
        abp.the_year_month = #{yearMonth}
        and abp.date_type_id = #{dateTypeId}
        and abp.business_group_id = #{businessGroup}
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abp.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abp.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abp.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abp.zwbppjyj_cur_405_tb
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abp.member_key asc
            </when>
            <otherwise>
                order by abp.zwbppjyj_cur_405 desc,abp.member_key asc
            </otherwise>
        </choose>
    </select>

    <select id="getPartnerRegionRanking" resultType="com.wantwant.sfa.backend.po.RealTimeRankingInfoPO">

        SELECT
        'region' as rankingType,
        organization_id as organizationId,
        abp.member_key as memberKey,
        ifnull(abp.zwbppjyj_cur_405,0) as zwProductPerformance,
        ifnull(abp.zwbppjyj_cur_405_tb,0) as zwProductPerformanceYearRatio
        from
        ads_bigtable_partner abp
        WHERE
        abp.the_year_month = #{yearMonth}
        and abp.date_type_id = #{dateTypeId}
        and abp.business_group_id = #{businessGroup}
        <if test="regionRankings !=null and regionRankings.size>0">
            <foreach collection="regionRankings" open="and abp.member_key in (" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abp.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abp.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abp.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abp.zwbppjyj_cur_405_tb
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abp.member_key asc
            </when>
            <otherwise>
                order by abp.zwbppjyj_cur_405 desc,abp.member_key asc
            </otherwise>
        </choose>
    </select>

    <select id="getNationalRanking" resultType="com.wantwant.sfa.backend.po.RealTimeRankingInfoPO">

        SELECT
        'national' as rankingType,
        abo.organization_id as organizationId,
        round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
        abo.kdj_37 as perCustomer,
        round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
        abo.glgzzrs_329 as managementOnJobNum,
        ifnull(abo.zwbppjyj_cur_405,0) as zwProductPerformance,
        ifnull(abo.zwbppjyj_cur_405_tb,0) as zwProductPerformanceYearRatio,
        ifnull(abo.zwbppjyj_cur_405_hb,0) as zwProductPerformanceRatio,
        ifnull(abo.pjyjmbdcl_32,0) as performanceAchievementRate
        from
        ads_bigtable_organization abo
        WHERE
        abo.the_year_month = #{yearMonth}
        and abo.date_type_id = #{dateTypeId}
        and abo.position_type_id = #{positionTypeId}
        and abo.business_group_id = #{businessGroup}
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abo.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abo.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abo.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abo.zwbppjyj_cur_405_tb
                    </when>
                    <when test="sortName == 'managementPerPerformance'">
                        abo.glgrjyj_cur_483
                    </when>
                    <when test="sortName == 'managementPositionCustomerNum'">
                        abo.glgrjjykhs_486
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abo.organization_id asc
            </when>

            <otherwise>
                order by
                <choose>
                    <when test="queryDataType == 0">
                         abo.zwbppjyj_cur_405 desc
                    </when>
                    <when test="queryDataType == 1">
                        abo.glgrjyj_cur_483 desc
                    </when>
                    <when test="queryDataType == 2">
                        abo.kdj_37 desc
                    </when>
                </choose>
                ,abo.organization_id asc
            </otherwise>
        </choose>

    </select>

    <select id="getRegionRanking" resultType="com.wantwant.sfa.backend.po.RealTimeRankingInfoPO">

        SELECT
        'region' as rankingType,
        abo.organization_id as organizationId,
        round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
        abo.kdj_37 as perCustomer,
        round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
        abo.glgzzrs_329 as managementOnJobNum,
        ifnull(abo.zwbppjyj_cur_405,0) as zwProductPerformance,
        ifnull(abo.zwbppjyj_cur_405_tb,0) as zwProductPerformanceYearRatio
        from
        ads_bigtable_organization abo
        WHERE
        abo.the_year_month = #{yearMonth}
        and abo.date_type_id = #{dateTypeId}
        and abo.position_type_id = #{positionTypeId}
        and abo.business_group_id = #{businessGroup}
        and abo.organization_id in
        <foreach collection="regionRankings" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abo.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abo.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abo.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abo.zwbppjyj_cur_405_tb
                    </when>
                    <when test="sortName == 'managementPerPerformance'">
                        abo.glgrjyj_cur_483
                    </when>
                    <when test="sortName == 'managementPositionCustomerNum'">
                        abo.glgrjjykhs_486
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abo.organization_id asc
            </when>

            <otherwise>
                order by
                <choose>
                    <when test="queryDataType == 0">
                        abo.zwbppjyj_cur_405 desc
                    </when>
                    <when test="queryDataType == 1">
                        abo.glgrjyj_cur_483 desc
                    </when>
                    <when test="queryDataType == 2">
                        abo.kdj_37 desc
                    </when>
                </choose>
                ,abo.organization_id asc
            </otherwise>
        </choose>

    </select>

    <select id="queryRankingListPartnerDetail" resultType="com.wantwant.sfa.backend.realData.vo.RealTimeRankingListDetailVo">

        SELECT
        di.pic_url as picUrl,
        abp.department_name as branchName,
        DATE_FORMAT(di.onboard_time,'%Y-%m-%d') as onboardTime,
        di.employee_name as employeeName,
        abp.member_key as memberKey,
        abp.business_group_id as businessGroupId,
        abp.zwbppjyj_cur_405 as zwProductPerformance,
        abp.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
        abp.zwbppjyj_cur_405_hb as zwProductPerformanceRatio,
        abp.pjyjmb_378 as goal,
        abp.pjyjmbdcl_32 as performanceAchievementRate,
        coalesce(NULLIF(CONCAT_WS('/', abp.area_name ,abp.varea_name, abp.province_name,abp.company_name,abp.department_name),'' ),abp.organization_name) as fullOrganizationName,
        abp.area_name as areaName,
        abp.varea_name as virtualAreaName,
        abp.province_name as provinceName,
        abp.company_name as companyName,
        abp.department_name as departmentName,
        abp.organization_id as organizationId
        from
        ads_bigtable_partner abp
        left join dim_emp_pos_role_org_mon_partner di
        on di.member_key  = abp.member_key and di.job_type_id = 1 and di.the_year_month = #{theDate}
        WHERE
        abp.the_year_month = #{yearMonth}
        and abp.date_type_id = #{dateTypeId}
        and abp.business_group_id = #{businessGroup}

        <if test="organizationIds !=null and organizationIds.size != 0">
            and abp.member_key in
            <foreach collection="organizationIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abp.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abp.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abp.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abp.zwbppjyj_cur_405_tb
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abp.member_key asc
            </when>
            <otherwise>
                order by abp.zwbppjyj_cur_405 desc,abp.member_key asc
            </otherwise>
        </choose>

    </select>

    <select id="queryRankingListDetail"  resultType="com.wantwant.sfa.backend.realData.vo.RealTimeRankingListDetailVo">
        SELECT
        <if test="99 !=businessGroup">
            ampm.pic_url as picUrl,
            ampm.employee_name as employeeName,
            DATE_FORMAT(ampm.onboard_time,'%Y-%m-%d') as onboardTime,
        </if>
        abo.zwbppjyj_cur_405 as zwProductPerformance,
        abo.zwbppjyj_cur_405_hb as zwProductPerformanceRatio,
        abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
        abo.pjyjmb_378 as goal,
        abo.pjyjmbdcl_32 as performanceAchievementRate,
        coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
        abo.area_name as areaName,
        abo.varea_name as virtualAreaName,
        abo.province_name as provinceName,
        abo.company_name as companyName,
        abo.department_name as departmentName,
        abo.khfgl_145 as repurchaseRate,
        abo.khfgl_145_hb as repurchaseRateRatio,
        abo.khfgl_145_tb as repurchaseRateYearRatio,
        abo.kdj_37 as perCustomer,
        abo.kdj_37_hb as perCustomerRatio,
        abo.kdj_37_tb as perCustomerYearRatio,
        abo.jykhs_36 as tradingCustomerNum,
        abo.jykhs_36_hb as tradingCustomerNumRateRatio,
        abo.jykhs_36_tb as tradingCustomerNumRateYearRatio,
        abo.glgzzrs_329 as managementOnJobNum,
        abo.glgzzrs_329_hb as managementOnJobNumChainRatio,
        abo.glgzzrs_329_tb as managementOnJobNumYearRatio,
        abo.glgrjyj_cur_483 as managementPerPerformance,
        abo.glgrjyj_cur_483_hb as managementPerPerformanceChainRatio,
        abo.glgrjyj_cur_483_tb as managementPerPerformanceYearRatio,
        abo.glgrjjykhs_486 as managementPositionCustomerNum,
        abo.glgrjjykhs_486_hb as managementPositionCustomerNumRatio,
        abo.glgrjjykhs_486_tb as managementPositionCustomerNumYearRatio,
        abo.organization_id as organizationId
        from
        ads_bigtable_organization abo
        <if test="99 !=businessGroup">
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
        </if>

        WHERE
        abo.the_year_month = #{yearMonth}
        and abo.date_type_id = #{dateTypeId}
        and abo.position_type_id = #{positionTypeId}
        and abo.business_group_id = #{businessGroup}
        <if test="12 != positionTypeId ">
            <if test="organizationIds !=null and organizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="organizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <choose>
            <when test="orderField != null and orderField != ''">
                order by
                <choose>
                    <when test="sortName == 'zwProductPerformance'">
                        abo.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'performanceAchievementRate'">
                        abo.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'zwProductPerformanceRatio'">
                        abo.zwbppjyj_cur_405_hb
                    </when>
                    <when test="sortName == 'zwProductPerformanceYearRatio'">
                        abo.zwbppjyj_cur_405_tb
                    </when>
                    <when test="sortName == 'managementPerPerformance'">
                        abo.glgrjyj_cur_483
                    </when>
                    <when test="sortName == 'managementPerPerformanceChainRatio'">
                        abo.glgrjyj_cur_483_hb
                    </when>
                    <when test="sortName == 'managementPerPerformanceYearRatio'">
                        abo.glgrjyj_cur_483_tb
                    </when>
                    <when test="sortName == 'managementPositionCustomerNum'">
                        abo.glgrjjykhs_486
                    </when>
                    <when test="sortName == 'managementPositionCustomerNumRatio'">
                        abo.glgrjjykhs_486_hb
                    </when>
                    <when test="sortName == 'managementPositionCustomerNumYearRatio'">
                        abo.glgrjjykhs_486_tb
                    </when>
                    <when test="sortName == 'repurchaseRate'">
                        abo.khfgl_145
                    </when>
                    <when test="sortName == 'repurchaseRateRatio'">
                        abo.khfgl_145_hb
                    </when>
                    <when test="sortName == 'repurchaseRateYearRatio'">
                        abo.khfgl_145_tb
                    </when>
                    <when test="sortName == 'perCustomer'">
                        abo.kdj_37
                    </when>
                    <when test="sortName == 'perCustomerRatio'">
                        abo.kdj_37_hb
                    </when>
                    <when test="sortName == 'perCustomerYearRatio'">
                        abo.kdj_37_tb
                    </when>
                    <when test="sortName == 'tradingCustomerNum'">
                        abo.jykhs_36
                    </when>
                    <when test="sortName == 'tradingCustomerNumRateRatio'">
                        abo.jykhs_36_hb
                    </when>
                    <when test="sortName == 'tradingCustomerNumRateYearRatio'">
                        abo.jykhs_36_tb
                    </when>
                    <when test="sortName == 'managementOnJobNum'">
                        abo.glgzzrs_329
                    </when>
                    <when test="sortName == 'managementOnJobNumChainRatio'">
                        abo.glgzzrs_329_hb
                    </when>
                    <when test="sortName == 'managementOnJobNumYearRatio'">
                        abo.glgzzrs_329_tb
                    </when>
                </choose>
                <choose>
                    <when test="orderType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
                ,abo.organization_id asc
            </when>
            <otherwise>
                order by
                <choose>
                    <when test="queryDataType == 0">
                         abo.zwbppjyj_cur_405 desc
                    </when>
                    <when test="queryDataType == 1">
                        abo.glgrjyj_cur_483 desc
                    </when>
                    <when test="queryDataType == 2">
                        abo.kdj_37 desc
                    </when>
                </choose>
                ,abo.organization_id asc
            </otherwise>
        </choose>

    </select>

    <select id="getRankingListGroup99" resultType="com.wantwant.sfa.backend.realData.vo.RealTimeRankingVo">

        <if test="'zb' == organizationType or 'area' == organizationType">
            select  m1.* from (
            SELECT
            'area' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            abo.organization_id as organizationId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '1'
            and abo.business_group_id = #{businessGroup}

            <if test="areaOrganizationIds !=null and areaOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="areaOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m1

        </if>
        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType">
            <if test=" 'varea' != organizationType">
                union all
            </if>
            select m2.* from (
            SELECT
            'varea' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            abo.organization_id as organizationId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '12'
            and abo.business_group_id = #{businessGroup}
            <if test="'varea' != organizationType">
                <if test="virtualAreaOrganizationIds !=null and virtualAreaOrganizationIds.size != 0">
                    and abo.organization_id in
                    <foreach collection="virtualAreaOrganizationIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m2

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType ">
            <if test="'province' != organizationType">
                union all
            </if>
            select m3.* from (
            SELECT
            'province' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            abo.organization_id as organizationId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '11'
            and abo.business_group_id = #{businessGroup}
            <if test="provinceOrganizationIds !=null and provinceOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="provinceOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.glgrjjykhs_486 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m3

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType or 'company'== organizationType ">
            <if test="'company'!= organizationType">
                union all
            </if>
            select m4.* from (
            SELECT
            'company' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            abo.organization_id as organizationId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo

            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '2'
            and abo.business_group_id = #{businessGroup}
            <if test="companyOrganizationIds !=null and companyOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="companyOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m4

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType or 'company'== organizationType or 'department' == organizationType ">
            <if test="'department' != organizationType">
                union all
            </if>
            select m5.* from (
            SELECT
            'department' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            abo.organization_id as organizationId,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '10'
            and abo.business_group_id = #{businessGroup}

            <if test="departmentOrganizationIds !=null and departmentOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="departmentOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m5

        </if>

    </select>

    <select id="getRankingList" resultType="com.wantwant.sfa.backend.realData.vo.RealTimeRankingVo">
        select m1.* from (
        SELECT
        'branch' as organizationType,
        coalesce(NULLIF(CONCAT_WS('/', abp.area_name ,abp.varea_name, abp.province_name,abp.company_name,abp.department_name),'' ),abp.organization_name) as fullOrganizationName,
        abp.member_key as memberKey,
        abp.organization_id as organizationId,
        di.pic_url as picUrl,
        di.department_name as branchName,
        di.employee_name as employeeName,
        abp.business_group_id as businessGroupId,
        abp.zwbppjyj_cur_405 as zwProductPerformance,
        abp.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
        0 as managementPositionCustomerNum,
        0 as perCustomer,
        0 as managementPerPerformance,
        0 as managementOnJobNum,
        abp.area_name as areaName,
        abp.varea_name as virtualAreaName,
        abp.province_name as provinceName,
        abp.company_name as companyName,
        abp.department_name as departmentName
        from
        ads_bigtable_partner abp
        left join dim_emp_pos_role_org_mon_partner di
        on di.member_key = abp.member_key and di.job_type_id = 1 and di.the_year_month = #{theDate}
        WHERE
        abp.the_year_month = #{yearMonth}
        and abp.date_type_id = #{dateTypeId}
        and abp.business_group_id = #{businessGroup}

        <if test="branchOrganizationIds !=null and branchOrganizationIds.size != 0">
            and abp.member_key in
            <foreach collection="branchOrganizationIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>

        order by abp.zwbppjyj_cur_405 desc,abp.member_key asc
        limit 20
        ) m1


        <if test="'zb' == organizationType or 'area' == organizationType">
            union all
            select m2.* from (
            SELECT
            'area' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            '-' as memberKey,
            abo.organization_id as organizationId,
            ampm.pic_url as picUrl,
            '' as branchName,
            ampm.employee_name as employeeName,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '1'

            and abo.business_group_id = #{businessGroup}

            <if test="areaOrganizationIds !=null and areaOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="areaOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m2

        </if>
        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType">
            union all
            select m3.* from (
            SELECT
            'varea' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            '-' as memberKey,
            abo.organization_id as organizationId,
            ampm.pic_url as picUrl,
            '' as branchName,
            ampm.employee_name as employeeName,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '12'
            and abo.business_group_id = #{businessGroup}

            <if test="'varea' != organizationType">
                <if test="virtualAreaOrganizationIds !=null and virtualAreaOrganizationIds.size != 0">
                    and abo.organization_id in
                    <foreach collection="virtualAreaOrganizationIds" open="(" close=")" item="item" separator=",">
                        #{item}
                    </foreach>
                </if>
            </if>

            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m3

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType ">
            union all
            select m4.* from(
            SELECT
            'province' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            '-' as memberKey,
            abo.organization_id as organizationId,
            ampm.pic_url as picUrl,
            '' as branchName,
            ampm.employee_name as employeeName,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '11'
            and abo.business_group_id = #{businessGroup}
            <if test="provinceOrganizationIds !=null and provinceOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="provinceOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m4

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType or 'company'== organizationType ">
            union all
            select m5.* from (
            SELECT
            'company' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            '-' as memberKey,
            abo.organization_id as organizationId,
            ampm.pic_url as picUrl,
            '' as branchName,
            ampm.employee_name as employeeName,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '2'
            and abo.business_group_id = #{businessGroup}
            <if test="companyOrganizationIds !=null and companyOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="companyOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m5

        </if>

        <if test="'zb' == organizationType or 'area' == organizationType or 'varea' == organizationType or 'province'== organizationType or 'company'== organizationType or 'department' == organizationType ">
            union all
            select m6.* from (
            SELECT
            'department' as organizationType,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            '-' as memberKey,
            abo.organization_id as organizationId,
            ampm.pic_url as picUrl,
            '' as branchName,
            ampm.employee_name as employeeName,
            abo.business_group_id as businessGroupId,
            abo.zwbppjyj_cur_405 as zwProductPerformance,
            abo.zwbppjyj_cur_405_tb as zwProductPerformanceYearRatio,
            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            round(abo.kdj_37,1) as perCustomer,
            round(abo.glgrjyj_cur_483, 1) as managementPerPerformance,
            abo.glgzzrs_329 as managementOnJobNum,
            abo.area_name as areaName,
            abo.varea_name as virtualAreaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName
            from
            ads_bigtable_organization abo
            left join ads_member_personal_month ampm
            on ampm.organization_code  = abo.organization_id and ampm.the_year_month = #{theDate}
            WHERE
            abo.the_year_month = #{yearMonth}
            and abo.date_type_id = #{dateTypeId}
            and abo.position_type_id = '10'
            and abo.business_group_id = #{businessGroup}

            <if test="departmentOrganizationIds !=null and departmentOrganizationIds.size != 0">
                and abo.organization_id in
                <foreach collection="departmentOrganizationIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            order by
            <choose>
                <when test="queryDataType == 0">
                    abo.zwbppjyj_cur_405 desc
                </when>
                <when test="queryDataType == 1">
                    abo.glgrjyj_cur_483 desc
                </when>
                <when test="queryDataType == 2">
                    abo.kdj_37 desc
                </when>
            </choose>
            ,abo.organization_id asc
            limit 20
            ) m6

        </if>

    </select>




    <sql id="organization">
        <if test="null != type and type !='' and ( type==2 or  type==3 or type==10 or type==11 or type==12)">
            <!--type 1总督导,2区域总监,3合伙人,10区域经理,11省区总监 12大区总监   organizationType 1总督导2分公司3营业所10区域经理11省区总监12大区总监   -->
            <if test="null != positionType and  positionType== 1 ">
                <!--组织类型为区，则看总督导下的数据 -->
                and d.region_name=#{organizationName}
            </if>
            <if test="null != positionType and ( positionType== 2 or positionType== 3 )">
                <!--组织类型为分公司，刚看分公司下的数据 -->
                and d.branch_name=#{organizationName}
            </if>
            <if test="null != positionType and  positionType== 10">
                <!--组织类型为区域经理，则看区域经理下的数据 -->
                and d.department_name=#{organizationName}
            </if>
            <if test="null != positionType and  positionType== 11 and params.dateType==1">
                <!--组织类型为省区，则看省区总监下的数据 -->
                and d.province_name=#{organizationName}
            </if>
            <if test="null != positionType and  positionType== 12 and params.dateType==1">
                <!--组织类型为大区，则看城市总监下的数据 -->
                and d.virtual_area_name=#{organizationName}
            </if>
        </if>
    </sql>


    <select id="getMainProducts" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultType="com.wantwant.sfa.backend.realData.vo.MainProductsVo">
        SELECT
        main_products_name as mainProductsName,
        max(round(main_products_itemsSupplyTotal,2)) as mainProductsItemsSupplyTotal,
        max(round(main_products_salegoal,2)) as mainProductsSalegoal,
        max(round(main_products_saleGoal_achievement_rate*100,2)) as mainProductsSaleGoalAchievementRate,
        max(round(main_products_itemsSupplyTotal_year_on_year*100,2)) as yearOnYearGrowthRate
        <if test="monthlyType==0 ">
            ,max(round(customers_unit_price,2)) as guestUnitPrice
        </if>
        from
        <include refid="selMainProducts"></include>
        the_year_mon = #{yearMonth}
        and biz_organization_id=#{organizationId}
        group by
        main_products_name
    </select>

    <select id="getMainProductsNew" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultType="com.wantwant.sfa.backend.realData.vo.MainProductsVo">
        SELECT
        main_products_name as mainProductsName,
        max(round(main_products_itemsSupplyTotal,2)) as mainProductsItemsSupplyTotal,
        max(round(main_products_cash_performance,2)) as mainProductsCashPerformance,
        max(round(main_products_salegoal,2)) as mainProductsSalegoal,
        max(round(main_products_saleGoal_achievement_rate*100,2)) as mainProductsSaleGoalAchievementRate,
        max(round(main_products_itemsSupplyTotal_year_on_year*100,2)) as yearOnYearGrowthRate,
        max(round(customers_unit_price,2)) as guestUnitPrice
        from
        <if test="businessGroup !=99">
            sfa_background_real_time_data_four_main_products
        </if>
        <if test="businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup
        </if>
        where
        the_year_mon = #{yearMonth}
        and biz_organization_id=#{organizationId}
        and date_type_id =#{dateTypeId}
        group by
        main_products_name
    </select>

    <select id="getMainProductsYear" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultType="com.wantwant.sfa.backend.realData.vo.MainProductsVo">
        SELECT
        main_products_name as mainProductsName,
        round(sum(main_products_itemsSupplyTotal_cumulative), 2) as mainProductsItemsSupplyTotal,
        round(sum(main_products_salegoal_cumulative), 2) as mainProductsSalegoal,
        round(sum(main_products_itemsSupplyTotal_cumulative)/ sum(main_products_salegoal_cumulative)* 100, 2) as mainProductsSaleGoalAchievementRate
        from
        <if test="businessGroup!=99">
            sfa_background_real_time_four_main_products_annual
        </if>
        <if test="businessGroup==99">
            sfa_background_four_main_products_annual_allbusinessgroup
        </if>
        where
        the_year_mon like CONCAT('%',#{year},'%')
        and the_year_mon &lt;=#{yearMonth}
        and organization_id=#{organizationId}
        and date_type_id = #{dateTypeId}
        and time_type_id=0
        group by main_products_name
    </select>

    <select id="getMainProductsThis" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultType="com.wantwant.sfa.backend.realData.vo.MainProductsDataVo">
    SELECT
        main_products_name as mainProductsName,
        round(main_products_itemsSupplyTotal,2) as mainProductsItemsSupplyTotal,
        round(main_products_salegoal,2) as mainProductsSalegoal,
        round(main_products_saleGoal_achievement_rate*100,2) as mainProductsSaleGoalAchievementRate
    from
        sfa_background_real_time_data_four_main_products
    where
        the_year_mon = #{yearMonth}
        and biz_organization_id=#{organizationId}
    </select>

    <select id="getMainProductsByName" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.MainProductsVo">
        SELECT
        the_year_mon as month,
        max(main_products_name) as mainProductsName,
        max(round(main_products_itemsSupplyTotal_cumulative,2)) as mainProductsItemsSupplyTotal,
        max(round(main_products_salegoal_cumulative,2)) as mainProductsSalegoal,
        max(Round(main_products_saleGoal_achievement_annual_rate*100,2)) as mainProductsSaleGoalAchievementRate,
        max(Round(main_products_itemsSupplyTotal_cumulative_yoy_growth_rate*100,2)) as yearOnYearGrowthRate
        from
        <if test="businessGroup!=99">
            sfa_background_real_time_four_main_products_annual
        </if>
        <if test="businessGroup==99">
            sfa_background_four_main_products_annual_allbusinessgroup
        </if>
        where
        the_year_mon like CONCAT('%',#{year},'%')
        and the_year_mon &lt;=#{yearMonth}
        <if test="isName==1">
            and main_products_name = #{name}
        </if>
        and organization_id = #{organizationId}
        and time_type_id=0
        group by
        the_year_mon
    </select>

    <select id="getMainProductsModel" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.MainProductsVo">
        SELECT
        the_year_mon as month,
        '' as mainProductsName,
        0 as mainProductsItemsSupplyTotal,
        0 as mainProductsSalegoal,
        0 as mainProductsSaleGoalAchievementRate,
        0 as yearOnYearGrowthRate
        from
        <if test="businessGroup!=99">
            sfa_background_real_time_four_main_products_annual
        </if>
        <if test="businessGroup==99">
            sfa_background_four_main_products_annual_allbusinessgroup
        </if>
        where
        the_year_mon like CONCAT('%',#{year},'%')
        and the_year_mon &lt;=#{yearMonth}
        and organization_id = #{organizationId}
        and time_type_id=0
        group by
        the_year_mon
    </select>
    <select id="getMainOrganizationTypeNew" parameterType="com.wantwant.sfa.backend.realData.request.RealtimeMainProductNewRequest" resultType="Integer">

        select position_type_id from
        <if test="businessGroup !=99">
            sfa_background_real_time_data_four_main_products
        </if>
        <if test="businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup
        </if>
        where
        biz_organization_id=#{organizationId}
        and the_year_mon=#{yearMonth}
        and date_type_id=#{dateTypeId}
        limit 1
    </select>

    <select id="getMainOrganizationType" resultType="Integer">
        <if test="type == 0">
            select position_type_id from
            <include refid="selMainProducts"></include>
            biz_organization_id=#{organizationId}
            and the_year_mon=#{date}
            and business_group_id=#{businessGroup}
        </if>
        <if test="type != 0">
            select position_type_id from
            <if test="businessGroup !=99">
                sfa_background_real_time_four_main_products_annual
            </if>
            <if test="businessGroup ==99">
                sfa_background_four_main_products_annual_allbusinessgroup
            </if>
            where
            biz_organization_id=#{organizationId}
            and the_year_mon=#{date}
            and time_type_id=#{type}
            and business_group=#{businessGroup}
        </if>
        limit 1
    </select>

    <select id="getResultsDateNew"  parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.ServiceDataDetailVo">
        SELECT
        abo.business_group_id as `group`,

        arop.virtual_area_id  as area,
        arop.province_name as varea,
        arop.branch_name as province,
        arop.department_name as company,
        arop.biz_office_name as departmentName,
        arop.biz_office_name as organizationName,
        arop.biz_organization_id as organizationId,

        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,

        sbr.pos_role_name as postName,
        sbr.pic_url as url,
        sbr.employee_name as employeeName,
        sbr.onboard_time as onboardTime,
        sbr.position_type_id as positionTypeId,
        sbr.employee_status as employeeStatus,
        sbr.off_time as dischargeDate,
        sbr.zw_onboard_days as onboardDays,
        sbr.employee_info_id as employeeInfoId,


        abo.zwbppjyj_past_cn_30 as fiscalYearPerformance,
        abo.pjyjmb_cn_378  as fiscalYearGoal,
        round(abo.pjyjmbdcl_cn_32*100,2)  as fiscalYearRate,
        round(abo.zwbppjyj_cur_cn_405_tb*100,2)  as fiscalYearRatio,
        abo.zwbppjyj_past_jd_30  as quarterPerformance,
        abo.pjyjmb_jd_378  as quarterGoal,
        round(abo.pjyjmbdcl_jd_32*100,2)  as quarterRate,
        round(abo.zwbppjyj_cur_jd_405_tb*100,2)  as quarterYearRatio,
        abo.zwbppjyj_cur_405  as overallPerformance,
        abo2.zwbppjyj_cur_405 as performanceYoy,
        abo.rksrjyj_320 as performancePerPopulation,
        abo.pjyjmb_378  as overallPerformanceGoal,
        round(abo.pjyjmbdcl_32*100,2) as overallPerformanceRate,
        round(abo.zwbppjyj_cur_405_tb*100,2)  as overallPerformanceYearRatio,
        abo.ztpzyj_418 as overallMainProductPerformance,
        abo.ztpztyjmb_417 as overallMainProductGoal,
        round(abo.ztpdcl_52*100,2) as overallMainProductRate,
        round(abo.ztpztyj_cur_418_tb*100,2) as overallMainProductYearRatio,

        round(abo.ztpztyj_past_416,2) as mainProductCashPerformance,
        round(abo.zwbppjyj_cur_405_hb*100,2) as overallPerformanceChainRatio,
        round(abo.ztpztyj_cur_418_hb*100,2) as overallMainProductChainRatio,

        abo.yddyj_past_379 as bookingPerformance,
        round(abo.yddyjzb_377*100,2) as bookingPerformanceRate,
        abo.yddwfhyj_past_318 as bookingUnshippedPerformance,
        abo.glgzzrs_329 as managementOnJobNum,
        abo.glgrjyj_cur_483 as managementPerPerformance,
        abo.jykhs_36 as tradingClientNum,
        abo.kdj_37 as perCustomer,
        round(abo.khfgl_145*100,2) as repurchaseRate,
        abo.xkhs_41 as dealerNewCustomerNum,
        abo.xkhkdj_344 as dealerNewPerCustomer,
        abo.lkhs_42 as dealerOldCustomerNum,
        abo.lkhkdj_343 as dealerOldPerCustomer,
        round(abo.cjdfgljxs_388*100,2) as nextQuarterRepurchaseRate
        <if test="dateTypeId==10">
            ,
            coalesce(srt.zwPerformance,0) as dayPerformance
        </if>
        from ads_bigtable_organization abo
        left join ads_bigtable_organization abo2
        on abo.organization_id = abo2.organization_id
           and abo.date_type_id = abo2.date_type_id
           and abo2.the_year_month = #{yearMonthYoy}
           and abo.business_group_id = abo2.business_group_id
        left join ads_realtime_organization_parent arop
        on abo.organization_id=arop.biz_organization_id

        left join ads_member_personal_month sbr on sbr.organization_code = abo.organization_id and sbr.the_year_month = #{relationMonth}

        <if test="dateTypeId==10">
            left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id organization_id,
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup!=99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup==99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            filter_type = '线别'
            and  the_date = #{theDate}
            group by
            1,2,3
            ) srt on abo.the_year_month = srt.the_year_month and abo.organization_id = srt.organization_id
        </if>

        where  abo.organization_id=#{organizationId}
        and abo.the_year_month = #{yearMonth}
        and abo.date_type_id=#{dateTypeId}
    </select>


    <select id="getResultsDateNextNew"  parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.ServiceDataDetailVo">
        SELECT
        <if test="organizationType==4">
            arop.biz_office_name  as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            arop.department_name  as area,
            arop.biz_office_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            arop.branch_name  as area,
            arop.department_name as varea,
            arop.biz_office_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            arop.province_name  as area,
            arop.branch_name as varea,
            arop.department_name as province,
            arop.biz_office_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            arop.virtual_area_name  as area,
            arop.province_name as varea,
            arop.branch_name as province,
            arop.department_name as company,
            arop.biz_office_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            arop.region_name  as area,
            arop.virtual_area_name as varea,
            arop.province_name as province,
            arop.branch_name as company,
            arop.department_name as departmentName,
            1 as isNextRealtime,
        </if>
        abo.business_group_id as `group`,
        abo.organization_name as organizationName,
        abo.organization_id as organizationId,

        coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,

        sbr.pos_role_name as postName,
        sbr.pic_url as url,
        sbr.employee_name as employeeName,
        sbr.employee_name as name,
        sbr.onboard_time as onboardTime,
        sbr.position_type_id as positionTypeId,
        sbr.employee_status as employeeStatus,
        sbr.off_time as dischargeDate,
        sbr.zw_onboard_days as onboardDays,
        sbr.employee_info_id as employeeInfoId,

        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        abo.yczye_319 as preStoredValuePerformance,
        abo.yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
        abo.yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

        round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
        round(abo.glgrjxkhs_451,1)   as managementPositionNewCustomerNum,
        round(abo.glgrjlkhs_456,1) as managementPositionOldCustomerNum,

        abo.zwbppjyj_past_cn_30 as fiscalYearPerformance,
        abo.pjyjmb_cn_378  as fiscalYearGoal,
        round(abo.pjyjmbdcl_cn_32*100,2)  as fiscalYearRate,
        round(abo.zwbppjyj_cur_cn_405_tb*100,2)  as fiscalYearRatio,
        abo.zwbppjyj_past_jd_30  as quarterPerformance,
        abo.pjyjmb_jd_378  as quarterGoal,
        round(abo.pjyjmbdcl_jd_32*100,2)  as quarterRate,
        round(abo.zwbppjyj_cur_jd_405_tb*100,2)  as quarterYearRatio,
        abo.zwbppjyj_cur_405  as overallPerformance,
        abo2.zwbppjyj_cur_405 as performanceYoy,
        abo.rksrjyj_320 as performancePerPopulation,
        abo.pjyjmb_378  as overallPerformanceGoal,
        round(abo.pjyjmbdcl_32*100,2) as overallPerformanceRate,
        round(abo.zwbppjyj_cur_405_tb*100,2)  as overallPerformanceYearRatio,
        abo.ztpzyj_418 as overallMainProductPerformance,
        abo.ztpztyjmb_417 as overallMainProductGoal,
        round(abo.ztpdcl_52*100,2) as overallMainProductRate,
        round(abo.ztpztyj_cur_418_tb*100,2) as overallMainProductYearRatio,

        round(abo.ztpztyj_past_416,2) as mainProductCashPerformance,
        round(abo.zwbppjyj_cur_405_hb*100,2) as overallPerformanceChainRatio,
        round(abo.ztpztyj_cur_418_hb*100,2) as overallMainProductChainRatio,

        abo.yddyj_past_379 as bookingPerformance,
        round(abo.yddyjzb_377*100,2) as bookingPerformanceRate,
        abo.yddwfhyj_past_318 as bookingUnshippedPerformance,
        abo.glgzzrs_329 as managementOnJobNum,
        abo.glgrjyj_cur_483 as managementPerPerformance,
        abo.jykhs_36 as tradingClientNum,
        abo.kdj_37 as perCustomer,
        round(ifnull(abo.khfgl_145,0)*100,2) as repurchaseRate,

        abo.xkhs_41 as dealerNewCustomerNum,

        abo.xkhkdj_344 as dealerNewPerCustomer,
        abo.lkhs_42 as dealerOldCustomerNum,
        abo.lkhkdj_343 as dealerOldPerCustomer,
        round(abo.cjdfgljxs_388*100,2) as nextQuarterRepurchaseRate
        <if test="dateTypeId==10">
            ,
            coalesce(srt.zwPerformance,0) as dayPerformance
        </if>

        from ads_bigtable_organization abo
        left join ads_bigtable_organization abo2
        on abo.organization_id = abo2.organization_id
            and abo.date_type_id = abo2.date_type_id
            and abo2.the_year_month = #{yearMonthYoy}
            and abo.business_group_id = abo2.business_group_id
        left join ads_realtime_organization_parent arop
        on abo.organization_id=arop.biz_organization_id
        left join ads_member_personal_month sbr on sbr.organization_code = abo.organization_id and sbr.the_year_month = #{relationMonth}
        <if test="dateTypeId==10">
            left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id organization_id,
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup!=99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup==99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and filter_type = '线别'
            and  the_date = #{theDate}
            group by
            1,2,3
            ) srt on abo.the_year_month = srt.the_year_month and abo.organization_id = srt.organization_id
        </if>

        where
            abo.the_year_month = #{yearMonth}
            and abo.business_group_id = #{businessGroup}
            and abo.date_type_id=#{dateTypeId}
        <if test="organizationType == 4 ">
            and abo.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and abo.position_type_id=12 and arop.department_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and abo.position_type_id=11 and arop.department_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and abo.position_type_id=2 and arop.department_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and abo.position_type_id=10 and arop.department_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and abo.position_type_id=3 and arop.department_id = #{organizationId}
        </if>

    </select>



    <select id="getResultBySearchType" resultType="com.wantwant.sfa.backend.realData.vo.ServiceDataDetailVo">
        SELECT
        abo.business_group_id as `group`,
        arop.biz_office_name as organizationName,
        arop.biz_organization_id as organizationId,

        case arop.position_type_id when 1 then arop.biz_office_name
        when 12 then arop.department_name
        when 11 then  arop.branch_name
        when 2 then arop.province_name
        when 10 then arop.virtual_area_name
        when 3 then arop.region_name
        else ''
        end as area,

        case arop.position_type_id when 12 then arop.biz_office_name
        when 11 then arop.department_name
        when 2 then  arop.branch_name
        when 10 then arop.province_name
        when 3 then arop.virtual_area_name
        else ''
        end as varea,


        case arop.position_type_id when 11 then arop.biz_office_name
        when 2 then arop.department_name
        when 10 then  arop.branch_name
        when 3 then arop.province_name
        else ''
        end as province,

        case arop.position_type_id when 2 then arop.biz_office_name
        when 10 then arop.department_name
        when 3 then  arop.branch_name
        else ''
        end as company,


        case arop.position_type_id when 10 then arop.biz_office_name
        when 3 then arop.department_name
        else ''
        end as departmentName,

        coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,

        sbr.pos_role_name as postName,
        sbr.pic_url as url,
        sbr.employee_name as name,
        sbr.onboard_time as onboardTime,
        sbr.position_type_id as positionTypeId,
        sbr.employee_status as employeeStatus,
        sbr.off_time as dischargeDate,
        sbr.zw_onboard_days as onboardDays,
        sbr.employee_info_id as employeeInfoId,

        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        abo.yczye_319 as preStoredValuePerformance,
        abo.yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
        abo.yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

        round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
        round(abo.glgrjxkhs_451,1)   as managementPositionNewCustomerNum,
        round(abo.glgrjlkhs_456,1) as managementPositionOldCustomerNum,

        abo.zwbppjyj_past_cn_30 as fiscalYearPerformance,
        abo.pjyjmb_cn_378  as fiscalYearGoal,
        round(abo.pjyjmbdcl_cn_32*100,2)  as fiscalYearRate,
        round(abo.zwbppjyj_cur_cn_405_tb*100,2)  as fiscalYearRatio,
        abo.zwbppjyj_past_jd_30  as quarterPerformance,
        abo.pjyjmb_jd_378  as quarterGoal,
        round(abo.pjyjmbdcl_jd_32*100,2)  as quarterRate,
        round(abo.zwbppjyj_cur_jd_405_tb*100,2)  as quarterYearRatio,
        abo.zwbppjyj_cur_405  as overallPerformance,
        abo2.zwbppjyj_cur_405 as performanceYoy,
        abo.rksrjyj_320 as performancePerPopulation,
        abo.pjyjmb_378  as overallPerformanceGoal,
        round(abo.pjyjmbdcl_32*100,2) as overallPerformanceRate,
        round(abo.zwbppjyj_cur_405_tb*100,2)  as overallPerformanceYearRatio,
        abo.ztpzyj_418 as overallMainProductPerformance,
        abo.ztpztyjmb_417 as overallMainProductGoal,
        round(abo.ztpdcl_52*100,2) as overallMainProductRate,
        round(abo.ztpztyj_cur_418_tb*100,2) as overallMainProductYearRatio,

        round(abo.ztpztyj_past_416,2) as mainProductCashPerformance,
        round(abo.zwbppjyj_cur_405_hb*100,2) as overallPerformanceChainRatio,
        round(abo.ztpztyj_cur_418_hb*100,2) as overallMainProductChainRatio,

        abo.yddyj_past_379 as bookingPerformance,
        round(abo.yddyjzb_377*100,2) as bookingPerformanceRate,
        abo.yddwfhyj_past_318 as bookingUnshippedPerformance,
        abo.glgzzrs_329 as managementOnJobNum,
        abo.glgrjyj_cur_483 as managementPerPerformance,
        abo.jykhs_36 as tradingClientNum,
        abo.kdj_37 as perCustomer,
        round(abo.khfgl_145*100,2) as repurchaseRate,
        abo.xkhs_41 as dealerNewCustomerNum,
        abo.xkhkdj_344 as dealerNewPerCustomer,
        abo.lkhs_42 as dealerOldCustomerNum,
        abo.lkhkdj_343 as dealerOldPerCustomer,
        round(abo.cjdfgljxs_388*100,2) as nextQuarterRepurchaseRate

        from ads_bigtable_organization abo
        left join ads_bigtable_organization abo2
        on abo.organization_id = abo2.organization_id
            and abo.date_type_id = abo2.date_type_id
            and abo2.the_year_month = #{yearMonthYoy}
            and abo.business_group_id = abo2.business_group_id
        left join ads_realtime_organization_parent arop
        on abo.organization_id=arop.biz_organization_id
        left join ads_member_personal_month sbr on sbr.organization_code = abo.organization_id and sbr.the_year_month = #{relationMonth}

        <where>
            and abo.the_year_month = #{yearMonth}
            and abo.business_group_id = #{businessGroup}
            and abo.date_type_id = #{dateTypeId}

            <if test="organizationIds !=null and organizationIds.size != 0">
                and (

                <foreach collection="organizationIds" open="abo.area_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abo.varea_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abo.province_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abo.company_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abo.department_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>

            <choose>
                <!-- 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人 -->
                <when test="searchType == 1">
                    <foreach collection="nextOrgCodes" item="item" open="and abo.organization_id in (" close=")" separator=",">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    <choose>
                        <when test="organizationType == 'area'">
                            and abo.area_id = #{organizationId}
                        </when>
                        <when test="organizationType == 'varea'">
                            and abo.varea_id = #{organizationId}
                        </when>
                        <when test="organizationType == 'province'">
                            and abo.province_id = #{organizationId}
                        </when>
                        <when test="organizationType == 'company'">
                            and abo.company_id = #{organizationId}
                        </when>
                        <when test="organizationType == 'department'">
                            and abo.department_id = #{organizationId}
                        </when>
                    </choose>
                    <choose>
                        <when test="searchType == 2">
                            and abo.position_type_id = 1
                        </when>
                        <when test="searchType == 3">
                            and abo.position_type_id = 12
                        </when>
                        <when test="searchType == 4">
                            and abo.position_type_id = 11
                        </when>
                        <when test="searchType == 5">
                            and abo.position_type_id = 2
                        </when>
                        <when test="searchType == 6">
                            and abo.position_type_id = 10
                        </when>
                        <when test="searchType == 7">
                            and abo.position_type_id = 3
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </where>

        <choose>
            <when test="sortName != null and sortName != ''">
                order by
                <choose>
                    <when test="sortName == 'overallPerformance'">
                        abo.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'fiscalYearPerformance'">
                        abo.zwbppjyj_past_cn_30
                    </when>
                    <when test="sortName == 'fiscalYearGoal'">
                        abo.pjyjmb_cn_378
                    </when>
                    <when test="sortName == 'fiscalYearRate'">
                        abo.pjyjmbdcl_cn_32
                    </when>
                    <when test="sortName == 'fiscalYearRatio'">
                        abo.zwbppjyj_cur_cn_405_tb
                    </when>
                    <when test="sortName == 'quarterPerformance'">
                        abo.zwbppjyj_past_jd_30
                    </when>
                    <when test="sortName == 'quarterGoal'">
                        abo.pjyjmb_jd_378
                    </when>
                    <when test="sortName == 'quarterRate'">
                        abo.pjyjmbdcl_jd_32
                    </when>
                    <when test="sortName == 'quarterYearRatio'">
                        abo.zwbppjyj_cur_jd_405_tb
                    </when>
                    <when test="sortName == 'overallPerformanceGoal'">
                        abo.pjyjmb_378
                    </when>
                    <when test="sortName == 'overallPerformanceRate'">
                        abo.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'overallPerformanceYearRatio'">
                        abo.zwbppjyj_cur_405_tb
                    </when>
                    <when test="sortName == 'overallMainProductPerformance'">
                        abo.ztpztyj_past_416
                    </when>
                    <when test="sortName == 'overallMainProductGoal'">
                        abo.ztpztyjmb_417
                    </when>
                    <when test="sortName == 'overallMainProductRate'">
                        abo.ztpdcl_52
                    </when>
                    <when test="sortName == 'overallMainProductYearRatio'">
                        abo.ztpztyj_cur_418_tb
                    </when>
                    <when test="sortName == 'bookingPerformance'">
                        abo.yddyj_past_379
                    </when>
                    <when test="sortName == 'bookingPerformanceRate'">
                        abo.yddyjzb_377
                    </when>
                    <when test="sortName == 'bookingUnshippedPerformance'">
                        abo.yddwfhyj_past_318
                    </when>
                    <when test="sortName == 'managementOnJobNum'">
                        abo.glgzzrs_329
                    </when>
                    <when test="sortName == 'managementPerPerformance'">
                        abo.glgrjyj_cur_483
                    </when>
                    <when test="sortName == 'tradingClientNum'">
                        abo.jykhs_36
                    </when>
                    <when test="sortName == 'perCustomer'">
                        abo.kdj_37
                    </when>
                    <when test="sortName == 'dealerNewCustomerNum'">
                        abo.xkhs_41
                    </when>
                    <when test="sortName == 'dealerNewPerCustomer'">
                        abo.xkhkdj_344
                    </when>
                    <when test="sortName == 'dealerOldCustomerNum'">
                        abo.lkhs_42
                    </when>
                    <when test="sortName == 'nextQuarterRepurchaseRate'">
                        abo.cjdfgljxs_388
                    </when>
                    <when test="sortName == 'repurchaseRate'">
                        abo.khfgl_145
                    </when>
                    <when test="sortName == 'performancePerPopulation'">
                        abo.rksrjyj_320
                    </when>
                    <when test="sortName == 'performanceYoy'">
                        abo2.zwbppjyj_cur_405
                    </when>
                    <otherwise>
                        abo.zwbppjyj_cur_405
                    </otherwise>
                </choose>
                <choose>
                    <when test="sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by abo.zwbppjyj_cur_405 desc
            </otherwise>
        </choose>

    </select>

    <select id="getPerformanceTrends" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsVo">
        SELECT
        the_date as theYearDate,
        round(zw_performance,2) as zwPerformance,
        <if test="''!=filterType  and filterType=='线别'">
            line_name as line
        </if>
        <if test="''!=filterType  and filterType=='合伙人'">
            partner_type as line
        </if>
        from
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup
        </if>
        where
        biz_organization_id = #{organizationId}
        and the_year_month =#{yearMonth}
        <if test="null != filterType and ''!=filterType">
            and filter_type=#{filterType}
        </if>
        <if test="null != queryName and ''!=queryName">
            and (partner_type like CONCAT('%',#{queryName},'%') or line_name like CONCAT('%',#{queryName},'%'))
        </if>
        order by the_date asc
    </select>

    <select id="getPerformanceTrendsPartnerThisMonth" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        <if test="null != filterType and filterType=='合伙人'">
            SELECT
            td.the_date as theYearDate,
            0 as zwPerformance,
            '当月业绩' as name
            from
            td_date td
            left join (
            SELECT
            '当月业绩' as name,
            0 as zwPerformance,
            the_date as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and biz_organization_id like '%ZB_Z%'
            and filter_type = #{filterType}
            and partner_type='全部'
            group by
            the_date,partner_type
            order by the_date asc
            ) as sbrtpt on
            td.the_date = sbrtpt.theYearDate
            where
            td.the_year_month = #{yearMonth}
        </if>
        <if test="null != filterType and filterType=='线别'">
            SELECT
            td.the_date as theYearDate,
            0 as zwPerformance,
            '当月业绩' as name
            from
            td_date td
            left join (
            SELECT
            '当月业绩' as name,
            0 as zwPerformance,
            the_date as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and biz_organization_id like '%ZB_Z%'
            and filter_type = #{filterType}
            group by
            the_date
            order by the_date asc
            ) as sbrtpt on
            td.the_date = sbrtpt.theYearDate
            where
            td.the_year_month = #{yearMonth}
        </if>
    </select>

    <select id="queryYearMonthList" resultType="java.lang.String">
        <choose>
            <when test="dateTypeId ==2">
                select
                    dtd.financial_year
                from ads.dim_td_date dtd
                where dtd.financial_year >= #{startDate}
                  and dtd.financial_year <![CDATA[ <= ]]> #{endDate}
                group by dtd.financial_year
                order by dtd.financial_year asc
            </when>
            <when test="dateTypeId ==11">
                select
                    REPLACE(dtd.the_year_quarter,'-0','-Q')
                from ads.dim_td_date dtd
                where dtd.the_year_quarter >= REPLACE(#{startDate},'-Q','-0')
                and dtd.the_year_quarter <![CDATA[ <= ]]> REPLACE(#{endDate},'-Q','-0')
                group by dtd.the_year_quarter
                order by dtd.the_year_quarter asc
            </when>
            <otherwise>
                select
                    the_year_month
                from ads.dim_td_date dtd
                where dtd.the_year_month >= #{startDate}
                and dtd.the_year_month <![CDATA[ <= ]]> #{endDate}
                group by dtd.the_year_month
                order by dtd.the_year_month asc
            </otherwise>
        </choose>
    </select>

    <select id="getMonthDays"
            resultType="java.lang.String">
        select dtd.the_date from ads.dim_td_date dtd
        where
            dtd.the_year_month = #{yearMonth}
        order by dtd.the_date
    </select>

    <select id="getQuarterDays"
            resultType="java.lang.String">
        select dtd.the_date from ads.dim_td_date dtd
        where
            dtd.the_quarter = SUBSTRING_INDEX(#{yearMonth},'-Q', -1)
          and dtd.the_year = SUBSTRING_INDEX(#{yearMonth},'-Q', 1)
        order by dtd.the_date
    </select>

    <select id="getFiscalYearQuarterByMonth" resultType="java.lang.String">
        select DISTINCT(REPLACE(dtd.financial_year_quarter, '-0', '-Q'))
            from ads.dim_td_date dtd
        where
            dtd.the_year_month = #{yearMonth}
    </select>

    <select id="getYearQuarterByMonth" resultType="java.lang.String">
        select DISTINCT(REPLACE(dtd.the_year_quarter, '-0', '-Q'))
        from ads.dim_td_date dtd
        where
            dtd.the_year_month = #{yearMonth}
    </select>

    <select id="getQuarterMonths"
            resultType="java.lang.String">
        select DISTINCT(dtd.the_year_month) from ads.dim_td_date dtd
        where
            dtd.the_quarter = SUBSTRING_INDEX(#{yearMonth},'-Q', -1)
          and dtd.the_year = SUBSTRING_INDEX(#{yearMonth},'-Q', 1)

    </select>

    <select id="getFiscalYearMonths"
            resultType="java.lang.String">
        select DISTINCT(dtd.the_year_month) from ads.dim_td_date dtd
        where
            dtd.financial_year = #{yearMonth}
        order by dtd.the_year_month

    </select>

    <select id="getLastQuarterDays"
            resultType="java.lang.String">
        select
            d.the_date
        from
            ads.dim_td_date d
        where
                CONCAT(d.the_year, '-0', d.the_quarter) =
                (
                    select
                        DISTINCT(dtd.the_year_quarter_lm)
                    from
                        ads.dim_td_date dtd
                    where
                        dtd.the_quarter = SUBSTRING_INDEX(#{yearMonth},'-Q', -1)
                      and dtd.the_year = SUBSTRING_INDEX(#{yearMonth},'-Q', 1)
                )
        GROUP by
            d.the_date

    </select>

    <select id="getLastQuarterMonths"
            resultType="com.wantwant.sfa.backend.realData.dto.YearQuarterDTO">
        select dtd.the_year_quarter as theyearQuarter,dtd.the_year_month as theYearMonth from ads.dim_td_date dtd
        where
                the_year_quarter =
                (
                    select DISTINCT(dtd.the_year_quarter_lm) from ads.dim_td_date dtd
                    where
                        dtd.the_quarter = SUBSTRING_INDEX(#{yearMonth},'-Q', -1)
                      and dtd.the_year = SUBSTRING_INDEX(#{yearMonth},'-Q', 1)
                )

        GROUP by dtd.the_year_quarter,dtd.the_year_month

    </select>

    <select id="getFiscalYearPerformanceTrends" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        SELECT '1'as name,
               the_year_month as theYearDate,
               zwbppjyj_cur_405 as zwPerformance
        FROM ads.ads_bigtable_organization
        where organization_id = #{organizationId}
          and the_year_month in (select DISTINCT(dtd.the_year_month) from ads.dim_td_date dtd where dtd.financial_year = #{yearMonth} )
          and date_type_id = '10'
        union all
        SELECT '2' as name,
               the_year_month as theYearDate,
               zwbppjyj_cur_405 as zwPerformance
        FROM ads.ads_bigtable_organization
        where organization_id = #{organizationId}
          and the_year_month in (select DISTINCT(dtd.the_year_month) from ads.dim_td_date dtd where dtd.financial_year = #{lastYearMonth} )
          and date_type_id = '10'

    </select>



    <select id="getTotalPerformanceTrendsMonth" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        SELECT
            the_date as theYearDate,
            sum(round(ifnull(zw_performance,0),2)) as zwPerformance
        from
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
        where
            biz_organization_id = #{organizationId}
          and the_year_month in
            <foreach collection="yearMonthList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
          and filter_type = '线别'
        group by 1
        order by the_date asc
    </select>

    <select id="getLinePerformanceTrendsMonth" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        SELECT
            the_date as theYearDate,
            round(zw_performance,2) as zwPerformance,
            line_name as name
        from
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
        where
            biz_organization_id = #{organizationId}
        and the_year_month in
            <foreach collection="yearMonthList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
          and filter_type = '线别'
        <if test="null != queryNames and queryNames.size>0">
            and line_name in
            <foreach collection="queryNames" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        order by the_date asc
    </select>



    <select id="getPerformanceTrendsPartner" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        <if test="null != filterType and filterType=='合伙人'">
            select
            *
            from (
            SELECT
            max(
            case partner_type
            when '全部' then '当月业绩'
            else '' end
            ) as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and biz_organization_id = #{organizationId}
            and filter_type = #{filterType}
            and partner_type='全部'
            group by
            the_date,partner_type
            union all
            SELECT
            '上月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            date(DATE_ADD(the_date, interval 1 month)) as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{lastYearMonth}
            and biz_organization_id = #{organizationId}
            and filter_type = #{filterType}
            and partner_type='全部'
            group by
            the_date
            )a
            order by theYearDate;
        </if>
        <if test="null != filterType and filterType=='线别'">
            select
            *
            from (
            SELECT
            case line_name
            when '乳品' then '乳品业绩'
            when '休闲' then '休闲业绩'
            else '' end as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and biz_organization_id = #{organizationId}
            and filter_type = '商品大类'
            group by
            the_date,line_name
            union all
            SELECT
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{yearMonth}
            and biz_organization_id = #{organizationId}
            and filter_type = #{filterType}
            group by
            the_date
            union all
            SELECT
            '上月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            date(DATE_ADD(the_date, interval 1 month)) as theYearDate
            FROM
            <if test="businessGroup != 99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup == 99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            the_year_month = #{lastYearMonth}
            and biz_organization_id = #{organizationId}
            and filter_type = #{filterType}
            group by
            the_date
            )a
            order by theYearDate;
        </if>
    </select>

    <select id="getPerformanceTrendsPartnerContrast" parameterType="com.wantwant.sfa.backend.realData.request.PerformanceTrendsRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceTrendsPerformanceVo">
        select
        *
        from
        (
        SELECT
        '当月业绩' as name, if ( current_date() >= d.the_date,
        coalesce(Round(sum(zw_performance) over (order by d.the_date), 1), 0),
        0) as zwPerformance, d.the_date as theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{yearMonth}
        union all
        select
        name, round(sum(zwPerformance) over (
        order by theYearDate),1) zwPerformance, theYearDate
        from
        (
        SELECT
        '上月业绩' as name, coalesce(Round(sum(zw_performance), 1),
        0) as zwPerformance, DATE_ADD(d.the_date,
        interval 1 month) theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{lastYearMonth}
        group by DATE_ADD(d.the_date,
        interval 1 month) )a
        union all
        select
        '成长业绩' as name, if ( current_date() >= a.theYearDate ,
        Round(a.zwPerformance -b.zwPerformance, 1),
        0) as zwPerformance, a.theYearDate as theYearDate
        from
        (
        SELECT
        '当月业绩' as name, Round(sum(zw_performance) over (order by d.the_date), 1) as zwPerformance, d.the_date as
        theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{yearMonth}
        )a
        join (
        select
        name, sum(zwPerformance) over (
        order by theYearDate)zwPerformance, theYearDate
        from
        (
        SELECT
        '上月业绩' as name, Round(sum(zw_performance), 1) as zwPerformance, DATE_ADD(d.the_date,
        interval 1 month) theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{lastYearMonth}
        group by DATE_ADD(d.the_date,
        interval 1 month) )a ) b on
        a.theYearDate = b.theYearDate

        union all
        select
        name, round(sum(zwPerformance) over (
        order by theYearDate),1) zwPerformance, theYearDate
        from
        (
        SELECT
        '同期业绩' as name, Round(sum(coalesce(zw_performance,0)), 1) as zwPerformance, DATE_ADD(d.the_date,
        interval 12 month) theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{lastYearThisMonth}
        group by DATE_ADD(d.the_date,
        interval 12 month) ) a

        union all

        select
        '同期成长业绩' as name, if ( current_date() >= a.theYearDate ,
        Round(a.zwPerformance -b.zwPerformance, 1),
        0) as zwPerformance, a.theYearDate as theYearDate
        from
        (
        SELECT
        '当月业绩' as name, Round(sum(coalesce(zw_performance,0)) over (order by d.the_date), 1) as zwPerformance,
        d.the_date as theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{yearMonth}
        )a
        join (
        select
        name, sum(zwPerformance) over (
        order by theYearDate)zwPerformance, theYearDate
        from
        (
        SELECT
        '同期业绩' as name, Round(sum(coalesce(zw_performance,0)), 1) as zwPerformance, DATE_ADD(d.the_date,
        interval 12 month) theYearDate
        FROM
        ads.td_date d
        left join
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends as tpt
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup as tpt
        </if>
        on
        d.the_year_month = tpt.the_year_month
        and d.the_date = tpt.the_date
        and biz_organization_id =
        #{organizationId}
        and filter_type = '线别'
        WHERE
        d.the_year_month =
        #{lastYearThisMonth}
        group by DATE_ADD(d.the_date,
        interval 12 month) )a ) b on
        a.theYearDate = b.theYearDate
        )tmp
        order by
        theYearDate;
    </select>

    <select id="getLineName" parameterType="com.wantwant.sfa.backend.realData.request.LineRequest" resultType="String">
        SELECT
        line_name
        from
        <if test="businessGroup != 99">
            ads.sfa_background_real_time_performance_trends
        </if>
        <if test="businessGroup == 99">
            ads.sfa_background_real_time_performance_trends_allbusinessgroup
        </if>
        where filter_type='线别' and  line_name is not null
        and the_year_month =#{yearMonth}
        <if test="null != name and name != ''">
            and line_name like CONCAT('%',#{name},'%')
        </if>
        <if test="null != businessGroup and businessGroup != ''">
            and business_group =#{businessGroup}
        </if>
        group by line_name
    </select>

    <select id="selPerformance" parameterType="com.wantwant.sfa.backend.realData.request.BranchTradeRequest" resultType="com.wantwant.sfa.backend.realData.vo.BranchTradeInformationVo">
        select
        ifnull(gmv_cd,0) as gmvCd,
        ifnull(gmv,0) as gmv,
        ifnull(gmv_growth_rate,0) as gmvGrowthRate,
        ifnull(profit_rate,0) as profitRate,
        ifnull(replacing_target,0) as replacingTarget,
        ifnull(is_standards,0) as isStandards,
        ifnull(gmv_featured_products,0) as mainProductGmv,
        ifnull(goal_amount_featured_products,0) as mainProductTarget,
        ifnull(goal_amount_featured_products_achievement_rate,0) as mainProductTargetRate,
        ifnull(file_client_gmv,0) as fileClientGmv,
        ifnull(file_client_nums,0) as fileClientNums,
        ifnull(file_client_per_customer_transaction,0) as fileClientPerCustomerTransaction,
        ifnull(independent_stock_gmv,0) as independentStockGmv,
        ifnull(independent_stock_nums,0) as independentStockNums,
        ifnull(independent_stock_per_customer_transaction,0) as independentStockPerCustomerTransaction,
        ifnull(small_market_target_achievement_rate,0) as smallMarketTargetRate,
        ifnull(small_market_target_achievement_rate,0) as smallMarketTargetAchievementRate,
        ifnull(goal_amount_featured_products_achievement_rate,0) as goalAmountFeaturedProductsAchievementRate,
        ifnull(sales_estimate_deviation,0) as salesEstimateDeviation,
        ifnull(inventory_rate,0) as inventoryRate,
        ifnull(replacing_target_achievement_rate,0) as replacingTargetAchievementRate,
        ifnull(small_market_target,0) as smallMarketTarget,
        ifnull(small_market_population,0) as populationNums,
        small_market_name as smallMarketName,
        ifnull(gmv_cd,0) as allPricePerformance,
        ifnull(gmv,0) as allOfferPricePerformance,
        ifnull(gmv_growth_rate,0) as allOfferPricePerformanceMonthOnMonth,
        ifnull(gmv_zw_products,0) as ThisBiddingPerformance,
        ifnull(goal_amount_zw_products,0) as ThisPerformanceTarget,
        ifnull(goal_amount_zw_products_achievement_rate,0) as ThisPerformanceTargetAchievementRate,
        ifnull(gmv_market_products,0) as dueMarketBiddingPerformance,
        ifnull(goal_amount_market_products,0) as dueMarketPerformanceTarget,
        ifnull(goal_amount_market_products_achievement_rate,0) as dueMarketTargetAchievementRate
        from performance_influencing_factors_realtime
        where
        the_year_mon=#{yearMonth}
        <if test="null != organizationId and organizationId !='' ">
            and biz_organization_id=#{organizationId}
        </if>
        limit 1
    </select>

    <select id="selInformation" parameterType="com.wantwant.sfa.backend.realData.request.BranchTradeRequest" resultType="com.wantwant.sfa.backend.realData.vo.BranchTradeInformationVo">
     select
     region_name as area,
     branch_name as company,
     pic_url as url,
     name as name,
     case gender
     when 1 then '男'
     when 2 then '女'
     else '未知' end as sex,
      birth_date as birthday,
     case type
     when 1 then '业务'
     when 2 then '企业'
     when 3 then '承揽'
     else '未知' end as partnerType,
     case employee_status
     when 1 then '试岗'
     when 2 then '入职'
     when 3 then '试岗失败'
     when 4 then '主动汰换'
     when 5 then '被动汰换'
     when 6 then '员工辞职'
     when 7 then '入职终止'
     else '未知' end as partnerState,
    joining_company as signingCompany,
     onboard_time as inductionDate,
     working_days as onJobDay,
     resume_url as resumeUrl
     from
     partners_basic_information
        where
            biz_organization_id=#{organizationId}
    </select>

    <select id="selPartnersClientPortrait" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.PortraitDetailVo">
        select client_type as name,client_nums as value  from
        <if test="businessGroup != 99">
            partners_client_portrait
        </if>
        <if test="businessGroup == 99">
            partners_client_portrait_allbusinessgroup
        </if>
        where
        the_year_mon=#{yearMonth}
        and portrait_type_id=#{type}
        and coalesce(if(biz_organization_id ='',NULL,biz_organization_id),
        if(department_id='',NULL,department_id),
        if(branch_organization_id='',NULL,branch_organization_id),
        if(province_id='',NULL,province_id),
        if(virtual_area_id='',NULL,virtual_area_id),
        if(region_organization_id='',NULL,region_organization_id)) = #{organizationId}
        order by `value` DESC
    </select>

    <select id="queryMarketList" parameterType="com.wantwant.sfa.backend.realData.request.BranchTradeRequest" resultType="com.wantwant.sfa.backend.realData.vo.CustomerMarketVo">
        SELECT
            ssm.small_market_name  AS marketName,
            ROUND(ssm.population_nums,0)  AS personNums,
            ROUND(ssm.branch_population_rate * 100,0) AS marketRate
        FROM
            sfa_background_real_time_data_four_partner_small_market ssm
        WHERE the_year_mon = #{yearMonth}
          AND biz_organization_id = #{organizationId}
    </select>

    <select id="selectPerformanceInfluenceFactorsYear" resultType="com.wantwant.sfa.backend.realData.vo.PerformanceInfluenceVO">
        select
        f.the_year_mon as `year`,
        f.gmv as gmv,
        round(f.sale_goal_achievement_rate * 100,2) as saleGoalAchievementRate,
        round(f.sale_goal_achievement_growth_rate * 100,2) as saleGoalAchievementGrowthRate,
        round(f.sale_goal_achievement_yoy_growth_rate * 100,2) as yearOnYearAchievementGrowthRate
        from
        ads.influencing_factors_realtime  f
        where
        f.time_type_id = 2
        and f.the_year_mon is not null
        <if test="areaOrganizationId != null and areaOrganizationId != '' ">
            AND f.region_organization_id = #{areaOrganizationId}
        </if>
        <if test="virtualAreaid != null and virtualAreaid != '' ">
            AND f.varea_id = #{virtualAreaid} and f.position_type_id = 12
        </if>
        <if test="provinceId != null and provinceId != '' ">
            AND f.province_id = #{provinceId} and f.position_type_id = 11
        </if>
        <if test="companyOrganizationId != null and companyOrganizationId != '' ">
            AND f.branch_organization_id = #{companyOrganizationId} AND f.biz_organization_id = 2
        </if>
        <if test="branchOrganizationId != null and branchOrganizationId != '' ">
            AND  f.department_id = #{branchOrganizationId} AND f.position_type_id = 10
        </if>
        <if test="partnerType != null">
            AND f.business_type_id = #{partnerType}
        </if>
    </select>

    <sql id="selPerformance">
        f.id,
        f.time_type_id,
        left(f.the_year_mon,4) as year,
        if(f.time_type_id = 0, f.the_year_mon, if(f.time_type_id = 1,
        concat(f.the_year_mon, '季度'),concat(f.the_year_mon, '年'))) theYearMon,
        f.region_name,
        f.region_name,
        f.region_organization_id,
        f.branch_name,
        f.branch_organization_id,
        f.department_name,
        f.department_id,
        f.biz_office_name,
        f.biz_organization_id,
        f.position_type_id,
        round(ifnull(f.gmv,0),2) as gmv,
        round(f.gmv_growth_rate * 100,2) as gmvGrowthRate,
        round(f.gmv_yoy_growth_rate * 100,2) as gmvYoyGrowthRate,
        round(ifnull(sale_goal,0),2) as saleGoal,
        ifnull(round(sale_goal_growth_rate * 100,2),0) as saleGoalGrowthRate,
        ifnull(round(sale_goal_achievement_rate * 100,2),0) as saleGoalAchievementRate,
        ifnull(round(sale_goal_achievement_growth_rate * 100,2),0) as saleGoalAchievementGrowthRate,
        ifnull(round(sale_goal_achievement_yoy_growth_rate * 100,2),0) as yearOnYearAchievementGrowthRate,
        round(sale_goal_achievement_annual_growth_rate * 100,2) as annualAccumulationGrowthRate,
        on_job_nums,
        round(on_job_nums_growth_rate * 100,2) as onJobNumsGrowthRate,
        on_job_complement_nums,
        round(on_job_complement_nums_achievement_rate * 100,2) as onJobComplementNumsAchievementRate,
        round(on_job_complement_nums_achievement_growth_rate * 100,2) as onJobComplementNumsAchievementGrowthRate,
        round(on_job_complement_nums_achievement_yoy_growth_rate * 100,2) as onJobYearOnYearAchievementGrowthRate,
        round(on_job_complement_nums_achievement_annual_growth_rate * 100,2) as onJobAnnualAccumulationGrowthRate,
        round(avg_gmv,2) as avgGmv,
        round(avg_gmv_growth_rate * 100,2) as avgGmvGrowthRate,
        round(avg_gmv_yoy_growth_rate * 100,2) as avgGmvYearOnYearAchievementGrowthRate,
        round(avg_gmv_annual_growth_rate * 100,2) as avgGmvAnnualAccumulationGrowthRate,
        induction_nums,
        round(induction_nums_growth_rate * 100,2) as inductionNumsGrowthRate,
        departure_nums,
        round(departure_nums_growth_rate * 100,2) as departureNumsGrowthRate,
        round(departure_nums_rate * 100,2) as departureNumsRate,
        round(departure_nums_rate_growth_rate * 100) as departureNumsRateGrowthRate,
        round(departure_nums_rate_yoy_growth_rate * 100) as departureYearOnYearAchievementGrowthRate,
        round(departure_nums_rate_annual_growth_rate * 100) as departureAnnualAccumulationGrowthRate,
        round(employment_expenses_rate * 100,2) as employmentExpensesRate,
        round(employment_expenses_rate_growth_rate * 100) as employmentExpensesRateGrowthRate,
        round(employment_expenses_rate_yoy_growth_rate * 100,2) as emplYearOnYearAchievementGrowthRate,
        round(employment_expenses_rate_annual_growth_rate * 100,2) as emplAnnualAccumulationGrowthRate,

        nums as transactionCustomerNum,
        round(nums_growth_rate * 100,2) as transactionCustomerExpensesRateGrowthRate,
        round(file_client_gmv,2) as fileClientGmv,
        round(file_client_gmv_growth_rate * 100,2) as fileClientGmvGrowthRate,
        file_client_nums,
        round(file_client_nums_growth_rate * 100,2) as fileClientNumsGrowthRate,
        round(file_client_per_customer_transaction,2) as fileClientPerCustomerTransaction,
        round(file_client_per_customer_transaction_growth_rate * 100,2) as fileClientPerCustomerTransactionGrowthRate,
        file_client_code_nums,
        round(file_client_code_nums_growth_rate * 100,2) as fileClientCodeNumsGrowthRate,
        round(file_client_trading_frequency,2) as fileClientTradingFrequency,
        round(file_client_trading_frequency_growth_rate * 100,2) as fileClientTradingFrequencyGrowthRate,
        round(file_client_per_code_transaction,2) as fileClientPerCodeTransaction,
        round(file_client_per_code_transaction_growth_rate * 100,2) as fileClientPerCodeTransactionGrowthRate,
        file_client_first_transaction_nums,
        round(file_client_first_transaction_nums_growth_rate * 100,2) as fileClientFirstTransactionNumsGrowthRate,
        round(file_client_rebuy_rate * 100,2) as fileClientRebuyRate,
        round(file_client_rebuy_rate_growth_rate * 100,2) as fileClientRebuyRateGrowthRate,
        round(independent_gmv,2) as independentGmv,
        round(independent_gmv_growth_rate * 100,2) as independentGmvGrowthRate,
        independent_nums,
        round(independent_nums_growth_rate * 100,2) as independentNumsGrowthRate,
        round(independent_per_customer_transaction,2) as independentPerCustomerTransaction,
        round(independent_per_customer_transaction_growth_rate * 100,2) as independentPerCustomerTransactionGrowthRate,
        independent_code_nums,
        round(independent_code_nums_growth_rate * 100,2) as independentCodeNumsGrowthRate,
        round(independent_trading_frequency,2) as independentTradingFrequency,
        round(independent_trading_frequency_growth_rate * 100,2) as independentTradingFrequencyGrowthRate,
        round(independent_per_code_transaction,2) as independentPerCodeTransaction,
        round(independent_per_code_transaction_growth_rate * 100,2) as independentPerCodeTransactionGrowthRate,
        f.etl_date,
        round(v2.on_job_nums_year_on_year * 100,2) as onJobNumsGrowthYearOnYearRate,
        round(v2.onboard_nums_year_on_year * 100,2) as inductionNumsGrowthYearOnYearRate,
        round(v2.off_nums_year_on_year * 100,2) as departureNumsGrowthYearOnYearRate,
        round(v2.trading_customers_year_on_year * 100,2) as transactionCustomerExpensesYearOnYearRate,
        v2.custoemr_unit_price as customerUnitPrice,
        round(v2.custoemr_unit_price_ring_ratio * 100,2) as customerUnitPriceGrowthRate,
        round(v2.custoemr_unit_price_year_on_year * 100,2) as customerUnitPriceYearOnYear,
        v2.trading_times as transactionFrequency,
        round(v2.trading_times_ring_ratio * 100,2) as transactionFrequencyGrowthRate,
        round(v2.trading_times_year_on_year * 100,2) as transactionFrequencyYearOnYear,
        v2.first_order_performance as firstOrderPerformance,
        round(v2.first_order_performance_year_on_year * 100,2) as  firstOrderPerformanceYearOnYear,
        round(v2.first_order_performance_ring_ratio * 100,2) as firstOrderPerformanceGrowthRate,
        round(v2.next_month_repurchase_rate * 100,2) as repurchaseRate,
        round(v2.next_month_repurchase_rate_year_on_year * 100,2) as repurchaseRateYearOnYear,
        round(v2.next_month_repurchase_rat_ring_ratio * 100,2) as  repurchaseGrowthRate,
        round(v2.loss_rate * 100,2) as attritionRate,
        round(v2.loss_rate_year_on_year * 100,2) as attritionRateYearOnYear,
        round(v2.loss_rate_ring_ratio * 100,2)  as attritionRateGrowthRate,
        f.management_avg_gmv as managementAvgGmv,
        round(f.management_avg_gmv_mom * 100,2) as managementAvgGmvMom,
        round(f.management_avg_gmv_yoy * 100,2) as managementAvgGmvYoy,
        round(f.management_avg_gmv_grate_ytd * 100,2) as managementAvgGmvGrateYtd
    </sql>

    <select id="selectPerformanceInfluenceFactors" resultType="com.wantwant.sfa.backend.realData.vo.PerformanceInfluenceVO">
        select
        <include refid="selPerformance"></include>
        <if test="businessGroup!=99">
            from influencing_factors_realtime f
            left join influencing_factors_realtime_v2 v2
        </if>
        <if test="businessGroup==99">
            from influencing_factors_realtime_allbusinessgroup f
            left join influencing_factors_realtime_v2_allbusinessgroup v2
        </if>
        on f.the_year_mon=v2.the_year_mon
        and f.business_type_id=v2.business_type_id
        and f.time_type_id=v2.time_type_id
        <if test="areaOrganizationId != null and areaOrganizationId != '' ">
            and f.position_type_id =v2.position_type_id
            and f.region_organization_id=v2.region_organization_id
        </if>
        <if test="virtualAreaid != null and virtualAreaid != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.varea_id = v2.varea_id
        </if>
        <if test="provinceId != null and provinceId != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.province_id = v2.province_id
        </if>
        <if test="companyOrganizationId != null and companyOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.branch_organization_id=v2.branch_organization_id
        </if>
        <if test="branchOrganizationId != null and branchOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.department_id=v2.department_id
        </if>
        <where>
            f.the_year_mon != '2021-10'
            <if test="areaOrganizationId != null and areaOrganizationId != '' ">
                AND f.region_organization_id = #{areaOrganizationId}
                <if test="areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 0
                </if>
                <if test="!areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 1
                </if>
            </if>
            <if test="virtualAreaid != null and virtualAreaid != '' ">
                AND f.varea_id = #{virtualAreaid} and f.position_type_id = 12
            </if>
            <if test="provinceId != null and provinceId != '' ">
                AND f.province_id = #{provinceId} and f.position_type_id = 11
            </if>
            <if test="companyOrganizationId != null and companyOrganizationId != '' ">
                AND f.branch_organization_id = #{companyOrganizationId} and f.position_type_id=2
            </if>
            <if test="branchOrganizationId != null and branchOrganizationId != '' ">
                AND  f.department_id = #{branchOrganizationId} AND f.position_type_id = 10
            </if>
            <if test="partnerType != null">
                AND f.business_type_id = #{partnerType}
            </if>
            and f.time_type_id=0
        </where>
        order by f.time_type_id desc, f.the_year_mon
        union all
        select
        <include refid="selPerformance"></include>
        <if test="businessGroup!=99">
            from influencing_factors_realtime f
            left join influencing_factors_realtime_v2 v2
        </if>
        <if test="businessGroup==99">
            from influencing_factors_realtime_allbusinessgroup f
            left join influencing_factors_realtime_v2_allbusinessgroup v2
        </if>
        on f.the_year_mon=v2.the_year_mon
        and f.business_type_id=v2.business_type_id
        and f.time_type_id=v2.time_type_id
        <if test="areaOrganizationId != null and areaOrganizationId != '' ">
            and f.position_type_id =v2.position_type_id
            and f.region_organization_id=v2.region_organization_id
        </if>
        <if test="virtualAreaid != null and virtualAreaid != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.varea_id = v2.varea_id
        </if>
        <if test="provinceId != null and provinceId != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.province_id = v2.province_id
        </if>
        <if test="companyOrganizationId != null and companyOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.branch_organization_id=v2.branch_organization_id
        </if>
        <if test="branchOrganizationId != null and branchOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.department_id=v2.department_id
        </if>
        <where>
            f.the_year_mon != '2021-10'
            <if test="areaOrganizationId != null and areaOrganizationId != '' ">
                AND f.region_organization_id = #{areaOrganizationId}
                <if test="areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 0
                </if>
                <if test="!areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 1
                </if>
            </if>
            <if test="virtualAreaid != null and virtualAreaid != '' ">
                AND f.varea_id = #{virtualAreaid} and f.position_type_id = 12
            </if>
            <if test="provinceId != null and provinceId != '' ">
                AND f.province_id = #{provinceId} and f.position_type_id = 11
            </if>
            <if test="companyOrganizationId != null and companyOrganizationId != '' ">
                AND f.branch_organization_id = #{companyOrganizationId}  AND f.position_type_id=2
            </if>
            <if test="branchOrganizationId != null and branchOrganizationId != '' ">
                AND  f.department_id = #{branchOrganizationId} AND f.position_type_id = 10
            </if>
            <if test="partnerType != null">
                AND f.business_type_id = #{partnerType}
            </if>
            and f.time_type_id=1
        </where>
        order by f.time_type_id desc, f.the_year_mon
        union all
        select
        <include refid="selPerformance"></include>
        <if test="businessGroup!=99">
            from influencing_factors_realtime f
            left join influencing_factors_realtime_v2 v2
        </if>
        <if test="businessGroup==99">
            from influencing_factors_realtime_allbusinessgroup f
            left join influencing_factors_realtime_v2_allbusinessgroup v2
        </if>
        on f.the_year_mon=v2.the_year_mon
        and f.business_type_id=v2.business_type_id
        and f.time_type_id=v2.time_type_id
        <if test="areaOrganizationId != null and areaOrganizationId != '' ">
            and f.position_type_id =v2.position_type_id
            and f.region_organization_id=v2.region_organization_id
        </if>
        <if test="virtualAreaid != null and virtualAreaid != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.varea_id = v2.varea_id
        </if>
        <if test="provinceId != null and provinceId != '' ">
            and f.position_type_id =v2.position_type_id
            AND f.province_id = v2.province_id
        </if>
        <if test="companyOrganizationId != null and companyOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.branch_organization_id=v2.branch_organization_id
        </if>
        <if test="branchOrganizationId != null and branchOrganizationId != '' ">
            and f.position_type_id=v2.position_type_id
            and f.department_id=v2.department_id
        </if>
        <where>
            f.the_year_mon != '2021-10'
            <if test="areaOrganizationId != null and areaOrganizationId != '' ">
                AND f.region_organization_id = #{areaOrganizationId}
                <if test="areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 0
                </if>
                <if test="!areaOrganizationId.contains('ZB') ">
                    and  f.position_type_id = 1
                </if>
            </if>
            <if test="virtualAreaid != null and virtualAreaid != '' ">
                AND f.varea_id = #{virtualAreaid} and f.position_type_id = 12
            </if>
            <if test="provinceId != null and provinceId != '' ">
                AND f.province_id = #{provinceId} and f.position_type_id = 11
            </if>
            <if test="companyOrganizationId != null and companyOrganizationId != '' ">
                AND f.branch_organization_id = #{companyOrganizationId}  AND f.position_type_id=2
            </if>
            <if test="branchOrganizationId != null and branchOrganizationId != '' ">
                AND  f.department_id = #{branchOrganizationId} AND f.position_type_id = 10
            </if>
            <if test="partnerType != null">
                AND f.business_type_id = #{partnerType}
            </if>
            and f.time_type_id=2
        </where>
        order by f.time_type_id desc, f.the_year_mon
    </select>

    <select id="queryAdditionalInfo" resultType="com.wantwant.sfa.backend.organizationGoal.vo.AdditionalInfoVo">
        SELECT
            abo.khs_61 as customerCount,
            abo.rks_466 as population
        from ads_bigtable_organization abo
        where abo.organization_id = #{organizationId} and abo.the_year_month = #{yearMonth} and abo.date_type_id = '10'
    </select>



    <select id="selectPersonnelList" resultType="com.wantwant.sfa.backend.realData.vo.PersonnelVO">
        select
        ta.id,
        ta.the_year_month,
        ta.biz_office_name as organizationName,
        ta.biz_organization_id as organizationId,
        ta.item_supplyprice_range as itemSupplypriceRange,
        IFNULL(ta.partner_nums,0) as partnerNums,
        IFNULL(round(ta.partner_rate * 100,2),0) as partnerRate,
        IFNULL(ta.onboard_days,0) as onboardDays,
        IFNULL(ta.onboard_days_median,0) as onboardMiddleDays,
        round(ta.partner_nums_mom * 100,2) as partnerNumsMom,
        round(ta.partner_nums_yoy * 100,2) as partnerNumsYoy,
        ta.position_type_id,
        ta.etl_date,
        ta.pt
        from personnel_achievement_trend_analysis ta
        where ta.the_year_month = #{yearMonth}
        <if test="organizationId != null ">
            <choose>
                <when test="organizationId == 'ZB_Z' or organizationId == 'ZB' ">
                    and ta.position_type_id = 1
                </when>
                <otherwise>
                    and ta.branch_organization_id = #{organizationId}
                </otherwise>
            </choose>
        </if>
        <if test="partnerType != null">and ta.partner_type = #{partnerType}</if>
        order by ta.item_supplyprice_range + 0
    </select>

    <select id="selectByOrganizationId" resultType="com.wantwant.sfa.backend.realData.vo.PersonnelVO">
        select
        ta.id,
        ta.the_year_month,
        ta.biz_office_name as organizationName,
        ta.biz_organization_id as organizationId,
        ta.item_supplyprice_range as itemSupplypriceRange,
        IFNULL(ta.partner_nums,0) as partnerNums,
        IFNULL(round(ta.partner_rate * 100,2),0) as partnerRate,
        IFNULL(ta.onboard_days,0) as onboardDays,
        IFNULL(ta.onboard_days_median,0) as onboardMiddleDays,
        round(ta.partner_nums_mom * 100,2) as partnerNumsMom,
        round(ta.partner_nums_yoy * 100,2) as partnerNumsYoy,
        ta.position_type_id,
        ta.etl_date,
        ta.pt
        from personnel_achievement_trend_analysis ta
        where ta.biz_organization_id = #{organizationId}
        <if test="partnerType != null">and ta.partner_type = #{partnerType}</if>
        <choose>
            <when test="yearMonth != null and yearMonth != '' ">
                and ta.the_year_month = #{yearMonth}
            </when>
            <otherwise>
                and ta.item_supplyprice_range != '合计' order by ta.the_year_month
            </otherwise>
        </choose>
    </select>

    <select id="selectOrganizationAchievements" resultType="com.wantwant.sfa.backend.realData.vo.OrganizationAchVO">
        SELECT
        ta.id,
        ta.the_year_month,
        ta.region_name,
        ta.region_organization_id,
        ta.branch_name,
        ta.branch_organization_id,
        ta.biz_office_name as organizationName,
        ta.biz_organization_id as organizationId,
        ta.item_supplyprice_range as itemSupplypriceRange,
        IFNULL(ta.biz_organization_nums,0) as bizOrganizationNums,
        IFNULL(round(ta.biz_organization_nums_rate * 100,2),0) as bizOrganizationNumsRate,
        IFNULL(ta.customer_nums_avg,0) as customerNumsAvg,
        IFNULL(ta.customer_all,0) as customerAll,
        IFNULL(round(ta.goal_achievement_rate_avg * 100,2),0) as goalAchievementRateAvg,
        IFNULL(round(ta.organization_nums_mom * 100,2),0) as organizationNumsMom,
        IFNULL(round(ta.organization_nums_yoy * 100,2),0) as organizationNumsYoy,
        ta.position_type_id,
        ta.etl_date,
        ta.pt
        from organization_achievement_trend_analysis ta
        where ta.the_year_month = #{yearMonth}
        <if test="organizationId != null ">
            <choose>
                <when test="organizationId == 'ZB_Z' or organizationId == 'ZB' ">
                    and ta.position_type_id = 1
                </when>
                <otherwise>
                    and ta.branch_organization_id = #{organizationId}
                </otherwise>
            </choose>
        </if>
        <if test="type != null ">
            and ta.type = #{type}
        </if>
        order by ta.item_supplyprice_range + 0
    </select>

    <select id="selectAchievementByOrganizationId" resultType="com.wantwant.sfa.backend.realData.vo.OrganizationAchVO">
        SELECT
        ta.id,
        ta.the_year_month,
        ta.region_name,
        ta.region_organization_id,
        ta.branch_name,
        ta.branch_organization_id,
        ta.biz_office_name as organizationName,
        ta.biz_organization_id as organizationId,
        ta.item_supplyprice_range as itemSupplypriceRange,
        IFNULL(ta.biz_organization_nums,0) as bizOrganizationNums,
        IFNULL(round(ta.biz_organization_nums_rate * 100,2),0) as bizOrganizationNumsRate,
        IFNULL(ta.customer_nums_avg,0) as customerNumsAvg,
        IFNULL(ta.customer_all,0) as customerAll,
        IFNULL(round(ta.goal_achievement_rate_avg * 100,2),0) as goalAchievementRateAvg,
        IFNULL(round(ta.organization_nums_mom * 100,2),0) as organizationNumsMom,
        IFNULL(round(ta.organization_nums_yoy * 100,2),0) as organizationNumsYoy,
        ta.position_type_id,
        ta.etl_date,
        ta.pt
        from organization_achievement_trend_analysis ta
        where ta.the_year_month = #{yearMonth} and ta.biz_organization_id = #{organizationId}
        <if test="type != null ">
            and ta.type = #{type}
        </if>
    </select>


    <select id="selectHjByOrganizationId" resultType="com.wantwant.sfa.backend.realData.vo.PersonnelVO">
        select
        IFNULL(ta.partner_nums,0) as partnerNums,
        IFNULL(round(ta.partner_rate * 100,2),0) as partnerRate,
        IFNULL(ta.onboard_days,0) as onboardDays,
        IFNULL(ta.onboard_days_median,0) as onboardMiddleDays,
        round(ta.partner_nums_mom * 100,2) as partnerNumsMom,
        round(ta.partner_nums_yoy * 100,2) as partnerNumsYoy
        from personnel_achievement_trend_analysis ta
        where ta.the_year_month = #{yearMonth} and ta.biz_organization_id = #{organizationId} and ta.position_type_id = 99
        <if test="partnerType != null">and ta.partner_type = #{partnerType}</if>
    </select>

    <select id="selectSkuDataList" resultType="com.wantwant.sfa.backend.realData.vo.ProductSkuDataVO">
        select
        sku as sku,
        sku_name as skuName,
        flavour as flavor,
        sku_images as skuImages,
        sku_spec as specification,
        ifnull(round(gmv_cm,2),0) as pricePerformance,
        ifnull(round(quantity_lm,2),0) as lastMonthQuantity,
        ifnull(round(quantity_cm,2),0) as thisMonthQuantity,
        ifnull(round(gmv_cm_rate*100,2),0) as pricePerformanceRate,
        ifnull(round(cus_nums,2),0) as transactionCustomers,
        ifnull(round(per_customer_transaction,2),0) as customerPrice,
        ifnull(round(gmv_cm_growth_rate*100,2),0) as linkRatiogRowthRate,
        ifnull(round(gmv_td,2),0) as onTheDayResults,
        ifnull(round(quantity_td,2),0) as onTheDaySales,
        ifnull(round(gmv_td_rate*100,2),0) as onTheDayResultsRate
        from sku_analyse
        where the_year_mon = DATE_FORMAT(#{param.startDate},'%Y-%m') and organization_id = #{param.organizationId}
        <choose>
            <when test="type == 1 ">
                and gmv_cm_growth_rate &lt; 0
                order by gmv_cm_growth_rate limit 10
            </when>
            <when test="type == 2 ">
                and gmv_cm_growth_rate > 0
                order by gmv_cm_growth_rate desc limit 10
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="selectInventoryNum" resultType="com.wantwant.sfa.backend.realData.dto.InventoryDTO">
        SELECT psi.sku,IFNULL(ROUND(sum(psi.normal_inventory),0),0) as inventoryNum
        FROM production_sale_inventory_second_edition psi
        <if test="organizationId != null and organizationId != '' and organizationId.indexOf('ZB') != 0">
            inner join td_channel_area_info tcai on psi.data_date = tcai.the_date and psi.channel_id = tcai.channel_id
            and tcai.organization_id = #{organizationId}
        </if>
        WHERE psi.data_date = #{endDate}
        AND psi.isShow IN (1, 10)
        AND psi.inventory_num >= 0 AND psi.label IS NOT NULL and psi.business_group_id = #{businessGroup}
        GROUP BY sku
    </select>

    <select id="selectSkuExpectNum" resultType="com.wantwant.sfa.backend.realData.vo.ExpectSkuVO">
        SELECT psi.sku,psi.sku_images,psi.sku_name,psi.sku_spec as specification,psi.flavor,psi.channel_name,
        IFNULL(psi.expect_num,0) as expectNum,psi.expect_date,psi.high_quality_inventory
        FROM production_sale_inventory_second_edition psi
        <if test="organizationId != null and organizationId != '' and organizationId.indexOf('ZB') != 0">
            inner join td_channel_area_info tcai on psi.data_date = tcai.the_date and psi.channel_id = tcai.channel_id
            and tcai.organization_id = #{organizationId}
        </if>
        WHERE
        psi.data_date = #{endDate} AND psi.expect_date > #{endDate} AND psi.expect_date &lt; date_add(#{endDate},INTERVAL 7 DAY )
        AND psi.isShow IN (1, 10)
        AND psi.inventory_num >= 0 AND psi.label IS NOT NULL and psi.business_group_id = #{businessGroup}
        order by psi.high_quality_inventory
    </select>

    <select id="selectIndicators" resultType="com.wantwant.sfa.backend.realData.vo.RealtimeDataFourVO">
        SELECT
        v1.employee_name AS employeeName,
        v1.biz_organization_id AS organizationId,
        v1.biz_office_name AS organizationName,
        v1.position_type_id AS positionTypeId,
        v1.onboard_time AS onboardTime,
        v1.onboard_days AS onboardDays,
        v1.pic_url AS picUrl,
        ifnull( v1.itemsSupplyTotal_cday, 0 ) AS itemsSupplyTotalCday,
        ifnull( v1.itemsSupplyTotal_cm, 0 ) AS itemsSupplyTotalCm,
        ifnull( v2.itemsSupplyTotal_cm_all, 0 ) as allOfferPricePerformance,
        ifnull( v1.itemsSupplyTotal_cm, 0 ) as thisProductPricePerformance,
        ifnull( v2.itemsSupplyTotal_cm_marketable, 0 ) as allMarketPricePerformance,
        ROUND(ifnull( v1.saleGoal_achievement_rate,0),2) AS saleGoalAchievementRate,
        ifnull( v1.saleGoal, 0 ) AS saleGoal,
        ROUND(ifnull( v1.month_ring_ratio,0),2) AS monthRingRatio,
        ROUND(ifnull( v1.itemsSupplyTotal_cm_avg, 0 ),2) AS itemsSupplyTotalCmAvg,
        ROUND(ifnull( v2.supervisor_all, 0 ),0) AS supervisionNum,
        ROUND(ifnull( v1.all_nums_cm, 0 ),0) AS partnerNum,
        ROUND(ifnull( v2.director_all, 0 ),0) AS chiefInspectorNum,
        ROUND(IFNULL( v2.city_manager_all, 0 ),0) AS departmentNum,
        ROUND(IFNULL(v2.itemsSupplyTotal_year_on_year ,0),2) AS monthAchievement,
        IFNULL(v2.employment_expenses_total_rate ,0) AS expenseRate,
        ROUND(IFNULL(v2.trading_customers_all, 0 ),0) AS totalCustomerNums,
        ROUND(IFNULL(v2.customer_price_total , 0 ),2) AS totalCustomerCm,

        IFNULL(v1.business_partner_onboard_full_time_nums,0) AS fullTimeOnJobNums,
        IFNULL(v1.business_partner_probation_full_time_nums,0 ) AS fullTimeTrialNums,
        ROUND(IFNULL(v1.itemsSupplyTotal_business_partner_full_time_cm_avg, 0 ),2) AS fullTimeCmAvg,
        ROUND(IFNULL(v2.full_time_business_partner_itemsSupplyTotal_cm ,0 ),2) AS fullTimeTotal,
        ROUND(IFNULL(v2.full_time_business_partner_itemsSupplyTotal_year_on_year * 100, 0 ),0) AS fullTimeYearRate,
        ROUND(IFNULL(v2.full_time_business_partner_itemsSupplyTotal_ring_ratio * 100, 0 ),0) AS fullTimeMonthRate,
        ROUND(IFNULL(v2.full_time_business_partner_customers, 0 ),0) AS fullTimeCustomerNums,
        ROUND(IFNULL(v2.full_time_business_partner_customers_year_on_year * 100, 0 ),0) AS fullTimeCustomerRate,
        ROUND(IFNULL(v2.full_time_business_partner_customers_price, 0 ),2) AS fullTimeCustomerCm,
        ROUND(IFNULL(v2.full_time_business_partner_waiting_onboard, 0 ),0) as fullTimeWaitingInductionNum,
        ROUND(IFNULL(v2.full_time_business_partner_partner_fee_rate*100, 0 ),2) as fullTimeEmploymentRate,

        ROUND(IFNULL(v1.business_partner_onboard_part_time_nums, 0 ),0) AS partTimeOnJobNums,
        ROUND(IFNULL(v1.business_partner_probation_part_time_nums, 0 ),0) AS partTimeTrialNums,
        ROUND(IFNULL(v1.itemsSupplyTotal_business_partner_part_time_cm_avg, 0 ),2) AS partTimeCmAvg,
        ROUND(IFNULL(v2.part_time_business_partner_itemsSupplyTotal_cm,0 ),2) AS partTimeTotal,
        ROUND(IFNULL(v2.part_time_business_partner_itemsSupplyTotal_year_on_year * 100, 0 ),0) AS partTimeYearRate,
        ROUND(IFNULL(v2.part_time_business_partner_itemsSupplyTotal_ring_ratio * 100, 0 ),0) AS partTimeMonthRate,
        ROUND(IFNULL(v2.part_time_business_partner_customers, 0 ),0) AS partTimeCustomerNums,
        ROUND(IFNULL(v2.part_time_business_partner_customers_year_on_year * 100, 0 ),0) AS partTimeCustomerRate,
        ROUND(IFNULL(v2.part_time_business_partner_customers_price, 0 ),2) AS partTimeCustomerCm,
        IFNULL(v2.part_time_business_partner_waiting_onboard, 0 ) as partTimeWaitingInductionNum,
        ROUND(IFNULL(v2.part_time_business_partner_partner_fee_rate*100, 0 ),2) as partTimeEmploymentRate,

        ROUND(IFNULL(v1.enterprise_partner_onboard_nums, 0 ),0) AS enterpriseOnJobNums,
        ROUND(IFNULL(v2.enterprise_partner_probation_nums, 0 ),0) AS enterpriseTrialNums ,
        ROUND(IFNULL(v1.itemsSupplyTotal_enterprise_partner_cm, 0 ),2) AS enterpriseTotal,
        ROUND(IFNULL(v2.enterprise_partner_itemsSupplyTotal_year_on_year * 100, 0 ),0) AS enterpriseYearRate,
        ROUND(IFNULL(v2.enterprise_partner_itemsSupplyTotal_ring_ratio * 100, 0 ),0) AS enterpriseMonthRate,
        ROUND(IFNULL(v1.itemsSupplyTotal_enterprise_partner_cm_avg, 0 ),2) AS enterpriseCmAvg,
        ROUND(IFNULL(v2.enterprise_partner_customers, 0 ),0) AS enterpriseCustomerNums,
        ROUND(IFNULL(v2.enterprise_partner_customers_year_on_year * 100, 0 ),0) AS enterpriseCustomerRate,
        ROUND(IFNULL(v2.enterprise_partner_customers_price, 0 ),2) AS enterpriseCustomerCm,
        ROUND(IFNULL(v2.enterprise_partner_waiting_onboard, 0 ),0) as enterpriseWaitingInductionNum,
        ROUND(IFNULL(v2.enterprise_partner_partner_fee_rate*100, 0 ),2) as enterpriseEmploymentRate,

        ROUND(IFNULL(v2.contract_partner_onboard_nums, 0 ),0) AS contractOnJobNums,
        ROUND(IFNULL(v2.contract_partner_probation_nums, 0 ),0) AS  contractTrialNums,

        ROUND(IFNULL(v2.contract_partner_probation_nums + v1.business_partner_probation_nums + v1.business_partner_probation_full_time_nums + v1.business_partner_probation_part_time_nums,0),0) as inPostNum,
        ROUND(IFNULL(v2.full_time_business_partner_waiting_onboard + v2.part_time_business_partner_waiting_onboard + v2.enterprise_partner_waiting_onboard + v2.contract_partner_waiting_onboard,0),0) as pendingNum,

        ROUND(IFNULL(v2.contract_partner_itemsSupplyTotal_cm, 0 ),2) AS contractTotal,
        ROUND(IFNULL(v2.contract_partner_partner_cm_avg, 0 ),2) AS contractCmAvg,
        ROUND(IFNULL(v2.contract_partner_itemsSupplyTotal_year_on_year * 100, 0 ),0) AS contractYearRate,
        ROUND(IFNULL(v2.contract_partner_itemsSupplyTotal_ring_ratio *100, 0 ),0) AS contractMonthRate,
        ROUND(IFNULL(v2.contract_partner_customers, 0 ),0) AS contractCustomerNums,
        ROUND(IFNULL(v2.contract_partner_customers_year_on_year * 100, 0 ),0) AS contractCustomerRate,
        ROUND(IFNULL(v2.contract_partner_customers_price, 0 ),2) AS contractCustomerCm,
        ROUND(IFNULL(v2.contract_partner_waiting_onboard, 0 ),0) as contractWaitingInductionNum,
        ROUND(IFNULL(v2.contract_partner_partner_fee_rate*100, 0 ),2) as contractEmploymentRate,
        ROUND(IFNULL(v2.trading_customers_year_on_year * 100, 0 ),0) AS totalCustomerNumsRate,
        ROUND(IFNULL(v2.customer_price_total_year_on_year * 100, 0 ),0) AS totalCustomerCmRate,
        v2.filling__customer_number_cm as fillingCustomerNumberCm,
        v2.no_order_customer_number_cm as noOrderCustomerNumberCm,
        v1.management_avg_gmv as managementAvgGmv
        from sfa_background_real_time_data_four_daily_retained v1
        LEFT JOIN sfa_background_real_time_data_four_v2_daily_retained v2 ON v1.biz_organization_id = v2.biz_organization_id
        AND v1.the_year_mon = v2.the_year_mon AND v1.the_date = v2.the_date
        where v1.the_date = #{param.endDate} and v1.biz_organization_id = #{param.organizationId}
        and v1.the_year_mon = DATE_FORMAT(#{param.endDate},'%Y-%m')
    </select>

    <select id="selectDataFourV3" resultType="com.wantwant.sfa.backend.realData.vo.RealtimeDataFourVO">
        select
        ROUND(IFNULL(v3.city_manager_avg_itemsSupplyTotal, 0 ),2) as cityManagerAvgItemsSupplyTotal,
        ROUND(IFNULL(v3.city_manager_avg_itemsSupplyTotal_ring_ratio  * 100, 0 ),2) as cityManagerAvgItemsSupplyTotalRingRatio,
        ROUND(IFNULL(v3.city_manager_avg_itemsSupplyTotal_year_on_year  * 100, 0 ),2) as cityManagerAvgItemsSupplyTotalYearOnYear,
        ROUND(IFNULL(v3.partner_billing_rate  * 100, 0 ),2) as partnerBillingRate,
        ROUND(IFNULL(v3.itemsSupplyTotal_cm_avg_year_on_year  * 100, 0 ),2) as itemsSupplyTotalCmAvgYearOnYear,
        ROUND(IFNULL(v3.trading_customers_all_ring_ratio * 100, 0 ),0) AS tradingCustomersAllRingRatio
        from sfa_background_real_time_data_four_v3_daily_retained v3
        where v3.the_date = #{param.endDate} and v3.biz_organization_id = #{param.organizationId}
        and v3.the_year_mon = DATE_FORMAT(#{param.endDate},'%Y-%m')
    </select>

    <select id="selectLineList" resultType="com.wantwant.sfa.backend.realData.vo.WeeklyLineVO">
        select line,gmv_cm,
        ROUND(IFNULL(gmv_cm_rate  * 100, 0 ),2) as gmvCmRate,
        ROUND(IFNULL(gmv_cm_growth_rate  * 100, 0 ),2) as gmvCmGrowthRate,
        ROUND(IFNULL(gmv_sm_growth_rate  * 100, 0 ),2) as gmvSmGrowthRate
        from sku_analyse_line_day
        where the_year_mon = DATE_FORMAT(#{param.endDate},'%Y-%m') and pt = #{param.endDate} and organization_id = #{param.organizationId}
        ORDER BY gmv_cm desc
    </select>

    <select id="selectSpuList" resultType="com.wantwant.sfa.backend.realData.vo.WeeklySpuVO">
        select spu_name,gmv_cm,
        ROUND(IFNULL(gmv_cm_rate  * 100, 0 ),2) as gmvCmRate,
        ROUND(IFNULL(gmv_cm_growth_rate  * 100, 0 ),2) as gmvCmGrowthRate,
        ROUND(IFNULL(gmv_sm_growth_rate  * 100, 0 ),2) as gmvSmGrowthRate
        from sku_analyse_spu_day
        where the_year_mon = DATE_FORMAT(#{param.endDate},'%Y-%m') and pt = #{param.endDate} and organization_id = #{param.organizationId}
        ORDER BY gmv_cm desc
    </select>

    <select id="selectTradingPartners" resultType="com.wantwant.sfa.backend.realData.vo.EmpInfoVO" >
        select
        v.organization_name3 as area,
        v.organization_name2 as company,
        v.department_name,
        v2.employee_name,
        v2.mobile
        from sfa_background_real_time_data_four_v2_daily_retained v2
        LEFT JOIN ods.hp_ceo_business_organization_view v on v.organization_id = v2.biz_organization_id
        where v2.the_date = #{params.endDate} and v2.the_year_mon = DATE_FORMAT(#{params.endDate},'%Y-%m') and v2.position_type_id = 3
        and v.business_group = #{params.businessGroup}
        <if test="params.organizationId != null and params.organizationId != '' and params.organizationId.indexOf('ZB') != 0">
            AND (v.organization_id3 = #{params.organizationId} or v.virtual_area_id  = #{params.organizationId} or v.province_id = #{params.organizationId}
            or v.organization_id2 = #{params.organizationId} or v.department_id = #{params.organizationId})
        </if>
        <choose>
            <when test="type == 1">
                and v2.itemsSupplyTotal_cm_all > 0
            </when>
            <when test="type == 2">
                and (v2.itemsSupplyTotal_cm_all &lt;= 0 or v2.itemsSupplyTotal_cm_all is null)
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="selectWorkReportRealTimeData" resultType="com.wantwant.sfa.backend.workReport.DO.WorkReportRealTimeDataDO">
        SELECT
            ifnull(pjyjmb_378,0) as saleGoal,
            ifnull(zwbppjyj_past_30,0) as itemsSupplyTotal,
            ifnull(pjyjmbdcl_32,0) as saleGoalAchievementRate,
            round(wjbyjzb_dq_506*100,1) as wantGoldDiscountRatio,
            ifnull(hhrrzrs_295,0) as developCustomerCount,
            ifnull(zwbppjyj_cur_405_hb,0) as monthRingRatio,
            ifnull(zwbppjyj_cur_405_tb,0) as monthAchievement
        FROM
            ads_bigtable_organization
        where date_type_id = '10'
          and the_year_month = #{yearMonth}
          and organization_id = #{organizationId}
    </select>

    <select id="selectWorkReportData" resultType="com.wantwant.sfa.backend.workReport.DO.WorkReportDataDO">
        select
            abo.pjyjmb_378 as saleGoal,
            abo.zwbppjyj_cur_405 as itemsSupplyTotal,
            abo.pjyjmbdcl_32 as saleGoalAchievementRate,
            abo.zwbppjyj_cur_405_hb as monthRingRatio,
            srt.zwPerformance as itemSupplyTotalCday
        from ads_bigtable_organization abo
        left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id organization_id,
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
        FROM
            ads.sfa_background_real_time_performance_trends
        WHERE
            filter_type = '线别'
            and  the_date = #{date}
        group by 1,2,3
        ) srt on abo.organization_id = srt.organization_id
        where abo.date_type_id = '10'
          and abo.the_year_month = #{yearMonth}
          and abo.organization_id = #{organizationId}
    </select>

<!--    <select id="selectWorkReportData" resultType="com.wantwant.sfa.backend.workReport.DO.WorkReportDataDO">-->
<!--        SELECT-->
<!--            ifnull(saleGoal,0) as saleGoal,-->
<!--            ifnull(itemsSupplyTotal_cm,0) as itemsSupplyTotal,-->
<!--            ifnull(saleGoal_achievement_rate,0) as saleGoalAchievementRate,-->
<!--            ifnull(month_ring_ratio,0) as monthRingRatio,-->
<!--            itemsSupplyTotal_cday as itemSupplyTotalCday-->
<!--        FROM-->
<!--            sfa_background_real_time_data_four_daily_retained-->
<!--        where the_date = #{date}-->
<!--          and the_year_mon = #{yearMonth}-->
<!--          and biz_organization_id = #{organizationId}-->
<!--        limit 1-->
<!--    </select>-->

    <select id="selectWorkReportDataAdditional" resultType="com.wantwant.sfa.backend.workReport.DO.WorkReportDataAdditionalDO">
        SELECT
            ifnull(itemsSupplyTotal_cm_marketable,0) as itemSupplyTotalCmMarketable,
            ifnull(itemsSupplyTotal_cm_all,0) as itemsSupplyTotalCmAll,
            ifnull(itemsSupplyTotal_year_on_year,0) as monthAchievement
        FROM
            sfa_background_real_time_data_four_v2_daily_retained
        where the_date = #{date}
        and the_year_mon = #{yearMonth}
        and biz_organization_id = #{organizationId}
    </select>

    <select id="selDisplayskuTotal" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.SpecialChenVo">
        select
        total_quota as totalDisbursementCost,
        total_quota_qoq as totalDisbursementCostMonOnMoh,
        total_quota_yoy as totalDisbursementCostYearOnYear,
        total_performance as totalPerformance,
        total_performance_qoq as totalPerformanceMonOnMoh,
        total_performance_yoy as totalPerformanceYearOnYear,
        total_quota_rate as totalExpenseRate,
        total_quota_rate_qoq as totalExpenseRateMonOnMoh,
        total_quota_rate_yoy as totalExpenseRateYearOnYear
        <if test="businessGroup!=99">
            from ads_sfa_realtime_displaysku_total
        </if>
        <if test="businessGroup==99">
            from ads_sfa_realtime_displaysku_total_allbusinessgroup
        </if>
        where the_year_month=#{yearMonth}
        <if test="null != positionType and positionType== 7">
            and zb_name='总部'
            and business_group_id=#{businessGroup}
            and data_flag='month_zb'
        </if>
        <if test="null != positionType and positionType== 1">
            and region_id=#{organizationId}
            and data_flag='month_region'
        </if>
        <if test="null != positionType and positionType== 2">
            and company_id=#{organizationId}
            and data_flag='month_company'
        </if>
        <if test="null != positionType and positionType== 10">
            and department_id=#{organizationId}
            and data_flag='month_dept'
        </if>
        <if test="null != positionType and positionType== 11">
            and province_id=#{organizationId}
            and data_flag='month_province'
        </if>
        <if test="null != positionType and positionType== 12">
            and varea_id=#{organizationId}
            and data_flag='month_varea'
        </if>
        limit 1
    </select>

    <select id="selDisplayskuDetail" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.SpecialChenProductVo">
        select
        sku as sku,
        CONCAT(sku_name,'_',pack_spec,'_',flavour) as commodityName,
        line_name as line,
        sku_quota as issueDisplayCost,
        Round(quota_Percentage*100,2) as displayCostRate,
        performance as performance,
        Round(quota_rate*100,2) as expenseRate
        <if test="businessGroup!=99">
            from ads_sfa_realtime_displaysku_detail
        </if>
        <if test="businessGroup==99">
            from ads_sfa_realtime_displaysku_detail_allbusinessgroup
        </if>
        where the_year_month=#{yearMonth}
        <if test="null != positionType and positionType== 7">
            and zb_name='总部'
            and business_group_id=#{businessGroup}
            and data_flag ='month_zb_sku'
        </if>
        <if test="null != positionType and positionType== 1">
            and region_id=#{organizationId}
            and data_flag='month_region_sku'
        </if>
        <if test="null != positionType and positionType== 2">
            and company_id=#{organizationId}
            and data_flag='month_company_sku'
        </if>
        <if test="null != positionType and positionType== 10">
            and department_id=#{organizationId}
            and data_flag='month_dept_sku'
        </if>
        <if test="null != positionType and positionType== 11">
            and province_id=#{organizationId}
            and data_flag='month_province_sku'
        </if>
        <if test="null != positionType and positionType== 12">
            and varea_id=#{organizationId}
            and data_flag='month_varea_sku'
        </if>
        order by quota_rate desc
        <if test="null != isPage and isPage== 1">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="getEmpRanking" resultType="com.wantwant.sfa.backend.realData.vo.EmpRankingVO">
        select
        d.region_name,d.branch_name,d.department_name,d.employee_name,v2.position_name,v2.mobile,d.onboard_time,
        d.onboard_days,ifnull(d.rownum,0) as rowNum,d.itemsSupplyTotal_cm,d.saleGoal,
        round(d.saleGoal_achievement_rate * 100,2) as saleGoalAchievementRate,
        round(d.month_ring_ratio * 100,2) as monthRingRatio,
        round(v2.itemsSupplyTotal_year_on_year * 100,2) as itemsSupplyTotalYear,
        d.itemsSupplyTotal_cm_all,d.position_id,d.itemsSupplyTotal_cm_marketable
        from sfa_background_real_time_data_four_v2_daily_retained v2
        LEFT JOIN sfa_background_real_time_data_four_daily_retained v1 on v1.biz_organization_id = v2.biz_organization_id
        AND v1.the_year_mon = v2.the_year_mon AND v1.the_date = v2.the_date and v1.position_type_id = 3
        LEFT JOIN dash_board_details_record_daily_retained d on d.position_id = v2.position_id AND d.the_date = v2.the_date
        AND d.the_year_mon = v2.the_year_mon and d.position_type_id = 3
        where v2.the_year_mon = DATE_FORMAT(#{params.endDate},'%Y-%m') and v2.the_date = #{params.endDate}
        and v2.department_id = #{params.organizationId}
        and v2.position_type_id = 3 and v2.off_time is null
        order by rowNum
    </select>

    <select id="selectOrganizationAchievementMonth" resultType="com.wantwant.sfa.backend.realData.vo.OrganizationMonthItemVO">
        select
        biz_organization_id,
        the_quarter as theQuarter,
        onboard_partners as partners,
        round(onboard_partners_rate * 100,2) as partnersRate,
        onboard_partners_preformance_avg as performanceAvg,
        preformance_median as performanceMedian,
        position_type_id as positionTypeId
        from sfa_background_real_time_data_four_organization_contrast_trend
        <where>
            <if test="organizationId != 'ZB_Z'">
                and (region_organization_id = #{organizationId}
                or branch_organization_id = #{organizationId}
                or department_id = #{organizationId}
                )and biz_organization_id != #{organizationId}
            </if>
            and the_quarter = #{quarter} and position_type_id != 4
            <if test="partnerType != null">and partner_type = #{partnerType}</if>
        </where>
    </select>

    <select id="getRealtimeValue" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.RealtimeOrganizationVo">
        select
            m3.mobile as mobile,
            m3.organizationName as organizationName,
            m3.postionName as postionName,
            m3.name as name,
            m3.memberKey as memberKey,
            m3.organizationId as organizationId
        from (
            SELECT
                m1.mobile as mobile,
                m1.organization_name as organizationName,
                m1.pos_role_name as postionName,
                m1.employee_name as name,
                '' as memberKey,
                m1.organization_code as organizationId
            FROM
                ads_member_personal_month m1
            WHERE
                m1.the_year_month = #{yearMonth}

            <if test="organizationType==1">
                and  m1.area_code = #{organizationId}
            </if>
            <if test="organizationType==12">
                and  m1.varea_code = #{organizationId}
            </if>
            <if test="organizationType==11">
                and  m1.province_code = #{organizationId}
            </if>
            <if test="organizationType==2">
                and  m1.company_code = #{organizationId}
            </if>
            <if test="organizationType==10">
                and  m1.department_code = #{organizationId}
            </if>
            <if test="null != businessGroup">
                and m1.business_group_id = #{businessGroup}
            </if>
            <if test="null != value and value!=''">
                and
                ( m1.employee_name like CONCAT('%',#{value},'%')
                or m1.mobile like CONCAT('%',#{value},'%') )
            </if>
            union all
            SELECT
                m2.mobile as mobile,
                m2.department_name as organizationName,
                m2.pos_role_name as postionName,
                m2.employee_name as name,
                m2.member_key as memberKey,
                m2.organization_code as organizationId
            FROM
                dim_emp_pos_role_org_mon_partner m2
                inner join ads_bigtable_partner abp
                    on abp.member_key =m2.member_key
                           and abp.the_year_month = m2.the_year_month
                <if test="null != businessGroup">
                    and abp.business_group_id =#{businessGroup}
                </if>

            WHERE
                m2.the_year_month = #{yearMonth}
                and m2.job_type_id = 1
            <!--
            <if test="null != businessGroup">
                and m2.business_group =#{businessGroup}
            </if>
            -->


            <if test="organizationType==1">
                and  abp.area_id = #{organizationId}
            </if>
            <if test="organizationType==12">
                and  abp.varea_id = #{organizationId}
            </if>
            <if test="organizationType==11">
                and  abp.province_id = #{organizationId}
            </if>
            <if test="organizationType==2">
                and  abp.company_id = #{organizationId}
            </if>
            <if test="organizationType==10">
                and  abp.department_id = #{organizationId}
            </if>

            <if test="null != value and value!=''">
                and
                ( m2.employee_name like CONCAT('%',#{value},'%')
                or m2.mobile like CONCAT('%',#{value},'%') )
            </if>


        ) m3 group by 1,2,3,4,5,6


    </select>


    <select id="getReatimeValueYear" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.RealtimeOrganizationVo">
        SELECT
        mobile ,
        biz_office_name as organizationName,
        position_name as postionName,
        employee_name as name,
        biz_organization_id as organizationId
        FROM
        <if test="businessGroup!=99">
            ads.sfa_background_real_time_data_four_annual
        </if>
        <if test="businessGroup==99">
            ads.sfa_background_real_time_data_four_annual_group
        </if>
        WHERE
        the_year = #{year}
        <if test="organizationType==4">
            and  position_type_id in (1,12,11,2,10,3)
        </if>
        <if test="organizationType==1">
            and  position_type_id in (12,11,2,10,3)
        </if>
        <if test="organizationType==12">
            and  position_type_id in (11,2,10,3)
        </if>
        <if test="organizationType==11">
            and  position_type_id in (2,10,3)
        </if>
        <if test="organizationType==2">
            and  position_type_id in (10,3)
        </if>
        <if test="organizationType==10">
            and  position_type_id in (3)
        </if>
        <if test="null != value and value!=''">
            and
            ( employee_name like CONCAT('%',#{value},'%')
            or mobile like CONCAT('%',#{value},'%') )
        </if>
        <if test="null != businessGroup and businessGroup != ''">
            and business_group =#{businessGroup}
        </if>
    </select>
    <select id="getMainProductTemplateNew" resultType="com.wantwant.sfa.backend.realData.vo.MainProductVo">
        select
        main_products_name as mainProductName,
        0 as biddingPerformance,
        0  as goal,
        0  as goalAchievementRate,
        0  as sequentialGrowthRate,
        0  as yearGrowthRate,
        0  as invoicingPartnersNumber,
        0  as partnerBillingRate,
        0  as billingRateSequentialIncreaseRate,
        0  as perCapitaPerformance,
        0  as perCapitaPerformanceGrowthRate,
        0  as tradingClients,
        0  as numberOfDisplayCustomers
        from
        <if test="businessGroup !=99">
            sfa_background_real_time_data_four_main_products
        </if>
        <if test="businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup
        </if>
        where
        the_year_mon  = #{yearMonth}
        and date_type_id  = #{dateTypeId}
        and business_group_id=#{businessGroup}

        group by main_products_name
    </select>
    <select id="getMainProductMonthTemplate" resultType="com.wantwant.sfa.backend.realData.vo.MainProductVo">
        select
        main_products_name as mainProductName,
        0 as biddingPerformance,
        0  as goal,
        0  as goalAchievementRate,
        0  as sequentialGrowthRate,
        0  as yearGrowthRate,
        0  as invoicingPartnersNumber,
        0  as partnerBillingRate,
        0  as billingRateSequentialIncreaseRate,
        0  as perCapitaPerformance,
        0  as perCapitaPerformanceGrowthRate,
        0  as tradingClients,
        0  as numberOfDisplayCustomers
        from
        <if test="type ==0">
            <include refid="selMainProducts"></include>
            the_year_mon  = #{date}
            and business_group_id=#{businessGroup}
        </if>
        <if test="type !=0">
            <if test="businessGroup !=99">
                sfa_background_real_time_four_main_products_annual
            </if>
            <if test="businessGroup ==99">
                sfa_background_four_main_products_annual_allbusinessgroup
            </if>
            where  the_year_mon  = #{date} and time_type_id=#{type}
            and business_group=#{businessGroup}
        </if>
        group by main_products_name
    </select>

    <select id="getMainProductNew" resultMap="mainResultMonthQuarter">
        select
        mp.region_name  as areaName,
        mp.province_name as provinceName,
        mp.branch_name as companyName,
        mp.department_name as departmentName,
        mp.biz_organization_id as organizationId,
        mp.position_type_id as positionTypeId,
        mp.position_name as post,
        mp.pic_url as avatar,
        mp.employee_name as name,
        mp.onboard_days as onboardDays,
        mp.main_products_name as mainProductName,
        mp.virtual_area_name as virtualAreaName,

        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        CONCAT_WS('/', mp.region_name ,mp.virtual_area_name, mp.province_name,mp.branch_name,mp.department_name) as fullOrganizationName,

        round(mp.main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
        mp.main_products_itemsSupplyTotal as biddingPerformance,
        mp.main_products_salegoal as goal,
        round(mp.main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
        round(mp.main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,

        mp.billing_partner_nums as invoicingPartnersNumber,
        round(mp.billing_rate_cm*100,2) as partnerBillingRate,
        round(mp.billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        mp.avg_performance as perCapitaPerformance,
        round(mp.avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        mp.cus_nums as tradingClients,
        round(mp.main_products_cash_performance,2) as mainProductsCashPerformance,

        mp.customers_unit_price as guestUnitPrice,

        ifnull(mp.display_customer_nums,0) as numberOfDisplayCustomers
        from
        <if test="businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{yearMonth}
        and mp.date_type_id  = #{dateTypeId}
        and mp.biz_organization_id = #{organizationId}
        limit 1
    </select>

    <select id="getMainProductMonth" resultMap="mainResultMonthQuarter">
        select
        biz_organization_id as organizationId,
        position_type_id as positionTypeId,
        region_name  as areaName,
        province_name as provinceName,
        branch_name as companyName,
        department_name as departmentName,
        biz_organization_id as organizationId,
        position_type_id as positionTypeId,
        position_name as post,
        pic_url as avatar,
        employee_name as name,
        onboard_days as onboardDays,
        main_products_name as mainProductName,
        virtual_area_name as virtualAreaName,
        <if test="type ==0">
            round(main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
            main_products_itemsSupplyTotal as biddingPerformance,
            main_products_salegoal as goal,
            round(main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
            round(main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,
        </if>
        <if test="type !=0">
            round(main_products_saleGoal_achievement_annual_rate*100,2) as goalAchievementRate,
            main_products_itemsSupplyTotal_cumulative as biddingPerformance,
            main_products_salegoal_cumulative as goal,
            round(main_products_itemsSupplyTotal_cumulative_ring_ratio*100,2) as sequentialGrowthRate,
            round(main_products_itemsSupplyTotal_cumulative_yoy_growth_rate*100,2) as yearGrowthRate,
        </if>
        billing_partner_nums as invoicingPartnersNumber,
        round(billing_rate_cm*100,2) as partnerBillingRate,
        round(billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        avg_performance as perCapitaPerformance,
        round(avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        cus_nums as tradingClients,
        <if test="type ==0">
            <if test="monthlyType==0 ">
                customers_unit_price as guestUnitPrice,
            </if>
        </if>
        ifnull(display_customer_nums,0) as numberOfDisplayCustomers
        from
        <if test="type ==0">
            <include refid="selMainProducts"></include>
            the_year_mon  = #{date}
            and business_group_id=#{businessGroup}
        </if>
        <if test="type !=0">
            <if test="businessGroup !=99">
                sfa_background_real_time_four_main_products_annual
            </if>
            <if test="businessGroup ==99">
                sfa_background_four_main_products_annual_allbusinessgroup
            </if>
            where  the_year_mon  = #{date} and time_type_id=#{type}
            and business_group=#{businessGroup}
        </if>
        <if test="organizationType == 4 ">
            and position_type_id=4 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 1 ">
            and  position_type_id=1 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=12 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=11 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=2 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=10 and biz_organization_id = #{organizationId}
        </if>
    </select>


    <select id="getMainProductNextNew" resultMap="mainResultMonthQuarter">
        select
        if(mp.position_type_id != 3,1,0) as isNextRealtime,
        mp.region_name  as areaName,
        mp.province_name as provinceName,
        mp.branch_name as companyName,
        mp.department_name as departmentName,
        mp.biz_organization_id as organizationId,
        mp.position_type_id as positionTypeId,
        mp.position_name as post,
        mp.pic_url as avatar,
        mp.employee_name as name,
        mp.onboard_days as onboardDays,
        mp.main_products_name as mainProductName,
        mp.virtual_area_name as virtualAreaName,
        CONCAT_WS('/', mp.region_name ,mp.virtual_area_name, mp.province_name,mp.branch_name,mp.department_name) as fullOrganizationName,

        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        round(mp.main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
        mp.main_products_itemsSupplyTotal as biddingPerformance,
        mp.main_products_salegoal as goal,
        round(mp.main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
        round(mp.main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,


        mp.billing_partner_nums as invoicingPartnersNumber,
        round(mp.billing_rate_cm*100,2) as partnerBillingRate,
        round(mp.billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        mp.avg_performance as perCapitaPerformance,
        round(mp.avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        mp.cus_nums as tradingClients,
        round(mp.main_products_cash_performance,2) as mainProductsCashPerformance,

        mp.customers_unit_price as guestUnitPrice,

        ifnull(mp.display_customer_nums,0) as numberOfDisplayCustomers
        from

        <if test="businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{yearMonth}
        and mp.date_type_id  = #{dateTypeId}
        and mp.business_group_id=#{businessGroup}


        <if test="organizationType == 4 ">
            and mp.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and mp.position_type_id=12 and mp.region_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12">
            and mp.position_type_id=11 and mp.virtual_area_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and mp.position_type_id=2 and mp.province_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and mp.position_type_id=10 and mp.branch_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and mp.position_type_id=3 and mp.department_id = #{organizationId}
        </if>

    </select>


    <sql id="searchAllWhere">
        and mp.biz_organization_id in (
        select cbo.organization_id
            from ods.hp_ceo_business_organization cbo
            inner join ods.hp_ceo_business_organization_tree t
            on t.organization_id = cbo.organization_id and t.organization_parent_id = #{request.organizationId}
            <if test="request.organizationIds!=null and request.organizationIds.size>0">
                <foreach collection="request.organizationIds" open="and cbo.organization_id in (" close=")" item="item" separator="," index="index">
                    #{item}
                </foreach>
            </if>
        )

        <!--0:全部下级 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人-->
        <!--岗位类型id,1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区-->
        <if test="request.organizationIds !=null and request.organizationIds.size>0">
            and (
            mp.biz_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.region_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.virtual_area_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.province_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.branch_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.department_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            )
        </if>

    </sql>

    <select id="getMainProductTileListSearchAll" resultMap="mainResultMonthQuarter">
        select
        mp.region_name  as areaName,
        mp.province_name as provinceName,
        mp.branch_name as companyName,
        mp.department_name as departmentName,
        mp.biz_organization_id as organizationId,
        mp.position_type_id as positionTypeId,
        mp.position_name as post,
        mp.pic_url as avatar,
        mp.employee_name as name,
        mp.onboard_days as onboardDays,
        mp.main_products_name as mainProductName,
        mp.virtual_area_name as virtualAreaName,
        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        CONCAT_WS('/', mp.region_name ,mp.virtual_area_name, mp.province_name,mp.branch_name,mp.department_name) as fullOrganizationName,

        round(mp.main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
        mp.main_products_itemsSupplyTotal as biddingPerformance,
        mp.main_products_salegoal as goal,
        round(mp.main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
        round(mp.main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,
        round(mp.main_products_cash_performance,2) as mainProductsCashPerformance,

        mp.billing_partner_nums as invoicingPartnersNumber,
        round(mp.billing_rate_cm*100,2) as partnerBillingRate,
        round(mp.billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        mp.avg_performance as perCapitaPerformance,
        round(mp.avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        mp.cus_nums as tradingClients,

        mp.customers_unit_price as guestUnitPrice,

        ifnull(mp.display_customer_nums,0) as numberOfDisplayCustomers
        from

        <if test="request.businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="request.businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{request.yearMonth}
        and mp.date_type_id  = #{request.dateTypeId}
        and mp.business_group_id=#{request.businessGroup}

        <include refid="searchAllWhere"></include>
        limit #{startNumber},#{pageSize}
    </select>

    <select id="getMainProductTileListSearchAllCount" resultType="java.lang.Long">
        select
            count(*)
        from
        <if test="request.businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="request.businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{request.yearMonth}
        and mp.date_type_id  = #{request.dateTypeId}
        and mp.business_group_id=#{request.businessGroup}

        <include refid="searchAllWhere"></include>

    </select>




    <select id="getMainProductTileList" resultMap="mainResultMonthQuarter">
        select
        mp.region_name  as areaName,
        mp.province_name as provinceName,
        mp.branch_name as companyName,
        mp.department_name as departmentName,
        mp.biz_organization_id as organizationId,
        mp.position_type_id as positionTypeId,
        mp.position_name as post,
        mp.pic_url as avatar,
        mp.employee_name as name,
        mp.onboard_days as onboardDays,
        mp.main_products_name as mainProductName,
        mp.virtual_area_name as virtualAreaName,
        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        CONCAT_WS('/', mp.region_name ,mp.virtual_area_name, mp.province_name,mp.branch_name,mp.department_name) as fullOrganizationName,

        round(mp.main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
        mp.main_products_itemsSupplyTotal as biddingPerformance,
        mp.main_products_salegoal as goal,
        round(mp.main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
        round(mp.main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,
        round(mp.main_products_cash_performance,2) as mainProductsCashPerformance,

        mp.billing_partner_nums as invoicingPartnersNumber,
        round(mp.billing_rate_cm*100,2) as partnerBillingRate,
        round(mp.billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        mp.avg_performance as perCapitaPerformance,
        round(mp.avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        mp.cus_nums as tradingClients,

        mp.customers_unit_price as guestUnitPrice,

        ifnull(mp.display_customer_nums,0) as numberOfDisplayCustomers
        from

        <if test="request.businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="request.businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{request.yearMonth}
        and mp.date_type_id  = #{request.dateTypeId}
        and mp.business_group_id=#{request.businessGroup}

        <!--0:全部下级 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人-->
        <!--岗位类型id,1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区-->
        <if test="request.organizationIds !=null and request.organizationIds.size>0">
            and (
            mp.biz_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.region_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.virtual_area_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.province_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.branch_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.department_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            )
        </if>
        <choose>
            <when test="request.searchType == 0 and request.organizationType != 'zb'">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <when test="request.searchType == 1">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="request.organizationType == 'area'">
                        and mp.region_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and mp.virtual_area_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'province'">
                        and mp.province_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'company'">
                        and mp.branch_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'department'">
                        and mp.department_id = #{request.organizationId}
                    </when>
                </choose>
                <choose>
                    <when test="request.searchType == 2">
                        and mp.position_type_id = 1
                    </when>
                    <when test="request.searchType == 3">
                        and mp.position_type_id = 12
                    </when>
                    <when test="request.searchType == 4">
                        and mp.position_type_id = 11
                    </when>
                    <when test="request.searchType == 5">
                        and mp.position_type_id = 2
                    </when>
                    <when test="request.searchType == 6">
                        and mp.position_type_id = 10
                    </when>
                    <when test="request.searchType == 7">
                        and mp.position_type_id = 3
                    </when>
                </choose>
            </otherwise>
        </choose>


    </select>


    <select id="getMainProductTileListLimitCount" resultType="java.lang.Long">
        select
            count(1)
        from

        <if test="request.businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="request.businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{request.yearMonth}
        and mp.date_type_id  = #{request.dateTypeId}
        and mp.business_group_id=#{request.businessGroup}

        <if test="request.organizationIds !=null and request.organizationIds.size>0">
            and (
            mp.biz_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.region_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.virtual_area_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.province_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.branch_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.department_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            )
        </if>
        <!--0:全部下级 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人-->
        <!--岗位类型id,1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区-->

        <choose>
            <when test="request.searchType == 0 and request.organizationType != 'zb'">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <when test="request.searchType == 1">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="request.organizationType == 'area'">
                        and mp.region_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and mp.virtual_area_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'province'">
                        and mp.province_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'company'">
                        and mp.branch_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'department'">
                        and mp.department_id = #{request.organizationId}
                    </when>
                </choose>
                <choose>
                    <when test="request.searchType == 2">
                        and mp.position_type_id = 1
                    </when>
                    <when test="request.searchType == 3">
                        and mp.position_type_id = 12
                    </when>
                    <when test="request.searchType == 4">
                        and mp.position_type_id = 11
                    </when>
                    <when test="request.searchType == 5">
                        and mp.position_type_id = 2
                    </when>
                    <when test="request.searchType == 6">
                        and mp.position_type_id = 10
                    </when>
                    <when test="request.searchType == 7">
                        and mp.position_type_id = 3
                    </when>
                </choose>
            </otherwise>
        </choose>

    </select>

    <select id="getMainProductTileListLimit" resultMap="mainResultMonthQuarter">
        select
        mp.region_name  as areaName,
        mp.province_name as provinceName,
        mp.branch_name as companyName,
        mp.department_name as departmentName,
        mp.biz_organization_id as organizationId,
        mp.position_type_id as positionTypeId,
        mp.position_name as post,
        mp.pic_url as avatar,
        mp.employee_name as name,
        mp.onboard_days as onboardDays,
        mp.main_products_name as mainProductName,
        mp.virtual_area_name as virtualAreaName,
        case abo.position_type_id
        when 4 then abo.glgzzrs_329
        when 1 then abo.glgzzrs_329
        when 12 then abo.glgzzrs_329
        when 11 then abo.glgzzrs_329
        when 2 then abo.glgzzrs_329
        when 10 then abo.khs_61
        else '-'
        end as memberCount,

        CONCAT_WS('/', mp.region_name ,mp.virtual_area_name, mp.province_name,mp.branch_name,mp.department_name) as fullOrganizationName,

        round(mp.main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
        mp.main_products_itemsSupplyTotal as biddingPerformance,
        mp.main_products_salegoal as goal,
        round(mp.main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
        round(mp.main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,
        round(mp.main_products_cash_performance,2) as mainProductsCashPerformance,

        mp.billing_partner_nums as invoicingPartnersNumber,
        round(mp.billing_rate_cm*100,2) as partnerBillingRate,
        round(mp.billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        mp.avg_performance as perCapitaPerformance,
        round(mp.avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        mp.cus_nums as tradingClients,

        mp.customers_unit_price as guestUnitPrice,

        ifnull(mp.display_customer_nums,0) as numberOfDisplayCustomers
        from

        <if test="request.businessGroup !=99">
            sfa_background_real_time_data_four_main_products mp
        </if>
        <if test="request.businessGroup ==99">
            sfa_background_data_four_main_products_allbusinessgroup mp
        </if>
        left join ads_bigtable_organization abo on abo.the_year_month = mp.the_year_mon and mp.biz_organization_id = abo.organization_id
        where
        mp.the_year_mon  = #{request.yearMonth}
        and mp.date_type_id  = #{request.dateTypeId}
        and mp.business_group_id=#{request.businessGroup}

        <if test="request.organizationIds !=null and request.organizationIds.size>0">
            and (
            mp.biz_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.region_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.virtual_area_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.province_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.branch_organization_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            or mp.department_id in
            <foreach collection="request.organizationIds" item="item" open=" (" close=")" separator=",">
                #{item}
            </foreach>

            )
        </if>
        <!--0:全部下级 1.直属下级 2.按总督导 3.按大区总监 4.按省区总监 5.按区域总监 6.按区域经理 7.按合伙人-->
        <!--岗位类型id,1:战区，2：分公司，3：合伙人，10：区域经理，11：省区，12：大区-->

        <choose>
            <when test="request.searchType == 0 and request.organizationType != 'zb'">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <when test="request.searchType == 1">
                <foreach collection="request.nextOrgCodes" item="item" open="and mp.biz_organization_id in (" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <choose>
                    <when test="request.organizationType == 'area'">
                        and mp.region_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'varea'">
                        and mp.virtual_area_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'province'">
                        and mp.province_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'company'">
                        and mp.branch_organization_id = #{request.organizationId}
                    </when>
                    <when test="request.organizationType == 'department'">
                        and mp.department_id = #{request.organizationId}
                    </when>
                </choose>
                <choose>
                    <when test="request.searchType == 2">
                        and mp.position_type_id = 1
                    </when>
                    <when test="request.searchType == 3">
                        and mp.position_type_id = 12
                    </when>
                    <when test="request.searchType == 4">
                        and mp.position_type_id = 11
                    </when>
                    <when test="request.searchType == 5">
                        and mp.position_type_id = 2
                    </when>
                    <when test="request.searchType == 6">
                        and mp.position_type_id = 10
                    </when>
                    <when test="request.searchType == 7">
                        and mp.position_type_id = 3
                    </when>
                </choose>
            </otherwise>
        </choose>
        order by mp.biz_organization_id  desc
        <if test=" startNumber !=null">
            limit #{startNumber},#{pageSize}
        </if>

    </select>


    <select id="getMainProductMonthNext" resultMap="mainResultMonthQuarter">
        select
        <if test="organizationType!=10">
            1 as isNextRealtime,
        </if>
        region_name  as areaName,
        province_name as provinceName,
        branch_name as companyName,
        department_name as departmentName,
        biz_organization_id as organizationId,
        position_type_id as positionTypeId,
        position_name as post,
        pic_url as avatar,
        employee_name as name,
        onboard_days as onboardDays,
        main_products_name as mainProductName,
        virtual_area_name as virtualAreaName,
        <if test="type ==0">
            round(main_products_saleGoal_achievement_rate*100,2) as goalAchievementRate,
            main_products_itemsSupplyTotal as biddingPerformance,
            main_products_salegoal as goal,
            round(main_products_itemsSupplyTotal_ring_ratio*100,2) as sequentialGrowthRate,
            round(main_products_itemsSupplyTotal_year_on_year*100,2) as yearGrowthRate,
        </if>
        <if test="type !=0">
            round(main_products_saleGoal_achievement_annual_rate*100,2) as goalAchievementRate,
            main_products_itemsSupplyTotal_cumulative as biddingPerformance,
            main_products_salegoal_cumulative as goal,
            round(main_products_itemsSupplyTotal_cumulative_ring_ratio*100,2) as sequentialGrowthRate,
            round(main_products_itemsSupplyTotal_cumulative_yoy_growth_rate*100,2) as yearGrowthRate,
        </if>
        billing_partner_nums as invoicingPartnersNumber,
        round(billing_rate_cm*100,2) as partnerBillingRate,
        round(billing_rate_cm_ring_ratio*100,2) as billingRateSequentialIncreaseRate,
        avg_performance as perCapitaPerformance,
        round(avg_performance_ring_ratio*100,2) as perCapitaPerformanceGrowthRate,
        cus_nums as tradingClients,
        <if test="type ==0">
            <if test="monthlyType==0 ">
                customers_unit_price as guestUnitPrice,
            </if>
        </if>
        ifnull(display_customer_nums,0) as numberOfDisplayCustomers
        from
        <if test="type ==0">
            <include refid="selMainProducts"></include>
            the_year_mon  = #{date}
            and business_group_id=#{businessGroup}
        </if>
        <if test="type !=0">
            <if test="businessGroup !=99">
                sfa_background_real_time_four_main_products_annual
            </if>
            <if test="businessGroup ==99">
                sfa_background_four_main_products_annual_allbusinessgroup
            </if>
            where  the_year_mon  = #{date} and time_type_id=#{type}
            and business_group=#{businessGroup}
        </if>
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and region_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12">
            and position_type_id=11 and virtual_area_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and province_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and branch_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3 and department_id = #{organizationId}
        </if>

    </select>

    <sql id="selMainProducts">
        <if test="monthlyType==0 ">
            <if test="businessGroup !=99">
                sfa_background_real_time_data_four_main_products where
            </if>
            <if test="businessGroup ==99">
                sfa_background_data_four_main_products_allbusinessgroup where
            </if>
        </if>
        <if test="monthlyType==1">
            sfa_background_four_main_products_month_report where
        </if>
        <if test="monthlyType==2">
            sfa_background_four_main_products_quarter_report where
            is_quarter=1 and
        </if>
        <if test="monthlyType==3">
            sfa_background_four_main_products_quarter_report where
            is_quarter=0 and
        </if>
    </sql>

    <select id="getMainProductYear" resultType="com.wantwant.sfa.backend.realData.vo.RealtimeMainProductVo">

    </select>


    <select id="selectDataFourV1" resultType="com.wantwant.sfa.backend.workReport.vo.WeeklyCompletionDetailVO">
<!--        SELECT-->
<!--        v1.employee_name AS employeeName,-->
<!--        v1.biz_organization_id AS organizationId,-->
<!--        v1.biz_office_name AS organizationName,-->
<!--        ifnull( v1.itemsSupplyTotal_cm, 0 ) AS itemsSupplyTotalCm,-->
<!--        ifnull( v1.saleGoal, 0 ) AS saleGoal,-->
<!--        ROUND(ifnull( v1.saleGoal_achievement_rate * 100,0),2) AS saleGoalAchievementRate,-->
<!--        ROUND(ifnull( v2.director_all, 0 ),0) AS directorAll,-->
<!--        ROUND(IFNULL( v2.city_manager_all, 0 ),0) AS cityManagerAll-->
<!--        from sfa_background_real_time_data_four_daily_retained v1-->
<!--        LEFT JOIN sfa_background_real_time_data_four_v2_daily_retained v2 ON v1.biz_organization_id = v2.biz_organization_id-->
<!--        AND v1.the_year_mon = v2.the_year_mon AND v1.the_date = v2.the_date-->
<!--        where v1.the_date = #{endDate}-->
<!--        <if test="param.organizationType == 'zb' ">-->
<!--            and v1.position_type_id = 1 and v1.business_group = #{param.businessGroup}-->
<!--        </if>-->
<!--        <if test="param.organizationType == 'area' ">-->
<!--            and v1.position_type_id = 12 and v1.department_id = #{organizationId}-->
<!--        </if>-->
<!--        and v1.the_year_mon = DATE_FORMAT(#{endDate},'%Y-%m')-->

        SELECT
            ampm.employee_name AS employeeName,
            abo.organization_id AS organizationId,
            abo.organization_name AS organizationName,
            ifnull(abo.zwbppjyj_cur_405, 0 ) AS itemsSupplyTotalCm,
            ifnull( abo.pjyjmb_378, 0 ) AS saleGoal,
            ROUND(ifnull( abo.pjyjmbdcl_32 * 100,0),2) AS saleGoalAchievementRate,
            ROUND(ifnull( abo.qyzjzzrs_301, 0 ),0) AS directorAll,
            ROUND(IFNULL( abo.csjlzzrs_297, 0 ),0) AS cityManagerAll
        from ads_bigtable_organization abo
        LEFT JOIN ads_member_personal_month ampm
            ON abo.organization_id = ampm.organization_id and ampm.the_year_month = DATE_FORMAT(#{endDate},'%Y-%m')
        where abo.the_year_month = DATE_FORMAT(#{endDate},'%Y-%m')
        <if test="param.organizationType == 'zb' ">
            and abo.position_type_id = 1 and abo.business_group_id = #{param.businessGroup}
        </if>
        <if test="param.organizationType == 'area' ">
            and abo.position_type_id = 12 and abo.area_id = #{organizationId}
        </if>
    </select>

    <select id="selectDataFourV2" resultType="com.wantwant.sfa.backend.workReport.vo.WeeklyReportInfoVO">
        select
            ampm.employee_name as employeeName,
            abo.organization_id as organizationId,
            CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name) AS organizationName,
            ROUND(ifnull(abo.zwbppjyj_cur_405, 0),2) AS itemsSupplyTotalCm,
            ROUND(ifnull(abo.pjyjmb_378, 0),2) AS saleGoal,
            ROUND(ifnull(abo.pjyjmbdcl_32 * 100,0),2) AS saleGoalAchievementRate,
            ampm.pic_url as picUrl,
            ampm.pos_role_name as positionName
        from
            ads_bigtable_organization abo
        left join ads_member_personal_month ampm
            on abo.organization_id = ampm.organization_code
                and ampm.the_year_month = #{personalSearchDate}
        where abo.date_type_id = #{dateType} and abo.the_year_month = DATE_FORMAT(#{searchDate},'%Y-%m')
        <if test="param.organizationType == 'zb' ">
            and abo.position_type_id = 1 and abo.business_group_id  = #{param.businessGroup}
        </if>
        <if test="param.organizationType == 'area' ">
            and abo.position_type_id = 12 and abo.area_id = #{organizationId}
        </if>
        <if test="param.organizationType == 'varea' ">
            and abo.position_type_id = 11 and abo.varea_id = #{organizationId}
        </if>
        <if test="param.organizationType == 'province' ">
            and abo.position_type_id = 2 and abo.province_id = #{organizationId}
        </if>
        <if test="param.organizationType == 'company' ">
            and abo.position_type_id = 10 and abo.company_id = #{organizationId}
        </if>
    </select>
<!--    <select id="selectDataFourV2" resultType="com.wantwant.sfa.backend.workReport.vo.WeeklyReportInfoVO">-->
<!--        SELECT-->
<!--        v1.employee_name AS employeeName,-->
<!--        v1.biz_organization_id AS organizationId,-->
<!--        SUBSTRING(CONCAT_WS('/',v1.region_name,v1.virtual_area_name,v1.province_name,v1.branch_name,v1.department_name,v1.biz_office_name),4) AS organizationName,-->
<!--        ifnull( v1.itemsSupplyTotal_cm, 0 ) AS itemsSupplyTotalCm,-->
<!--        ifnull( v1.saleGoal, 0 ) AS saleGoal,-->
<!--        ROUND(ifnull( v1.saleGoal_achievement_rate * 100,0),2) AS saleGoalAchievementRate,-->
<!--        v1.pic_url as picUrl,-->
<!--        v1.position_name-->
<!--        from sfa_background_real_time_data_four_daily_retained v1-->
<!--        where v1.the_date = #{endDate}-->
<!--        <if test="param.organizationType == 'zb' ">-->
<!--            and v1.position_type_id = 1 and v1.business_group = #{param.businessGroup}-->
<!--        </if>-->
<!--        <if test="param.organizationType == 'area' ">-->
<!--            and v1.position_type_id = 12 and v1.department_id = #{organizationId}-->
<!--        </if>-->
<!--        <if test="param.organizationType == 'varea' ">-->
<!--            and v1.position_type_id = 11 and v1.department_id = #{organizationId}-->
<!--        </if>-->
<!--        <if test="param.organizationType == 'province' ">-->
<!--            and v1.position_type_id = 2 and v1.department_id = #{organizationId}-->
<!--        </if>-->
<!--        <if test="param.organizationType == 'company' ">-->
<!--            and v1.position_type_id = 10 and v1.department_id = #{organizationId}-->
<!--        </if>-->
<!--        and v1.the_year_mon = DATE_FORMAT(#{endDate},'%Y-%m')-->
<!--    </select>-->

    <select id="selectSubDataFourV2" resultType="com.wantwant.sfa.backend.workReport.vo.WeeklyReportInfoVO">
        SELECT
        v1.employee_name AS employeeName,
        v1.biz_organization_id AS organizationId,
        SUBSTRING(CONCAT_WS('/',v1.region_name,v1.virtual_area_name,v1.province_name,v1.branch_name,v1.department_name,v1.biz_office_name),4) AS organizationName,
        ifnull( v1.itemsSupplyTotal_cm, 0 ) AS itemsSupplyTotalCm,
        ifnull( v1.saleGoal, 0 ) AS saleGoal,
        ROUND(ifnull( v1.saleGoal_achievement_rate * 100,0),2) AS saleGoalAchievementRate,
        v1.pic_url as picUrl,
        v1.position_name,
        v1.the_date
        from sfa_background_real_time_data_four_daily_retained v1
        where v1.the_date = #{endDate}
        and v1.department_id = #{organizationId}
        and v1.the_year_mon = DATE_FORMAT(#{endDate},'%Y-%m') and v1.position_type_id != 3
    </select>

    <select id="selectSkuAnalyseGmvCm" resultType="java.math.BigDecimal">
        select
        ifnull(ctdyj_380,0) as gmvCmNormal
        from ads_bigtable_organization_sku
        where
        the_year_month = #{yearMonth} and organization_id = #{organizationId} and sku_id = #{sku} limit 1
    </select>

    <select id="selectBigDataInfo" resultType="com.wantwant.sfa.backend.workReport.DO.WorkReportDataDO">
        select
            round(ifnull(abo2.zwbppjyj_past_30, 0), 2) as itemsSupplyTotal,
            round(ifnull(abo1.pjyjmb_378, 0), 2) as saleGoal,
            round(ifnull(abo1.zwbppjyj_past_30, 0), 2) as itemsSupplyTotalCm
        from
            ads_bigtable_organization abo1
                left join ads_bigtable_organization abo2
                    on
                    abo1.organization_id = abo2.organization_id
                    and abo1.date_type_id = abo2.date_type_id
                    and abo2.the_year_month = DATE_FORMAT(DATE_ADD(STR_TO_DATE(CONCAT(#{yearMonth}, '-01'),'%Y-%m-%d') , INTERVAL -1 MONTH), '%Y-%m')
        where
            abo1.organization_id = #{organizationId}
          and abo1.the_year_month = #{yearMonth}
          and abo1.date_type_id = '10'
    </select>

    <select id="getResultsDateMonthlyPostionType" parameterType="com.wantwant.sfa.backend.realData.vo.TeamResultsVo"
            resultType="int">
        <if test="monthlyType==1">
            SELECT
            position_type_id as positionType
            from
            ads_sfa_background_month_report as sbqr
            where
            current_organization_id = #{organizationId}
            and the_year_mon = #{yearMonth}
            and is_quarter = 0
        </if>
        <if test="monthlyType==2">
            SELECT
            position_type_id as positionType
            from
            ads_sfa_background_quarter_report as sbqr
            where
            current_organization_id = #{organizationId}
            and the_year_mon = #{yearMonth}
            and is_quarter = 1
        </if>
    </select>

    <select id="getResultsDateMonthlyMonth" parameterType="com.wantwant.sfa.backend.realData.vo.TeamResultsVo"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        sbqr.position_type_id as positionType,
        sbqr.up_four_level_organization_name as area,
        sbqr.up_three_level_organization_name as varea,
        sbqr.up_two_level_organization_name as province,
        sbqr.up_one_level_organization_name as company,
        sbqr.current_organization_name as departmentName,
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqrThree.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
    from
        ads_sfa_background_month_report as sbqr
    left join (
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(sale_goal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from
            ads_sfa_background_month_report
        where
            current_organization_id = #{organizationId}
            and the_year_mon = #{yearMonth}
            and is_quarter = 0 ) as sbqrTwo ON
        sbqrTwo.organization = sbqr.current_organization_id
        left join (
        select
        current_organization_id as organization,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from
            ads_sfa_background_month_report
        where
            current_organization_id = #{organizationId}
            and the_year_mon = #{lastTowMonth}
            and is_quarter = 0 ) as sbqrThree ON
        sbqrThree.organization = sbqr.current_organization_id
    where
        sbqr.current_organization_id = #{organizationId}
        and sbqr.the_year_mon = #{yearMonth}
        and sbqr.is_quarter = 0
    </select>


    <select id="getResultsDateNextComapnyMonth" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        <!-- sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv, -->
        sbqr.up_three_level_organization_name as area,
        sbqr.up_two_level_organization_name as varea,
        sbqr.up_one_level_organization_name as province,
        sbqr.current_organization_name as company,
        1 as isNextRealtime,
        sbqr.up_four_level_organization_name as area,
        sbqr.up_three_level_organization_name as varea,
        sbqr.up_two_level_organization_name as province,
        sbqr.up_one_level_organization_name as company,
        sbqr.current_organization_name as departmentName,
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from
        ads_sfa_background_month_report as sbqr
        left join (
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(sale_goal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate<!--
        ,ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        -->
        from
        ads_sfa_background_month_report
        where
        the_year_mon = #{yearMonth}
        <if test="organizationType == 1 ">
            and up_three_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and up_two_level_organization_id = #{organizationId}
        </if>
        and position_type_id=2
        and business_group=#{businessGroup}
        ) as sbqrTwo ON
        sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.the_year_mon = #{yearMonth}
        <if test="organizationType == 1 ">
            and up_three_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and up_two_level_organization_id = #{organizationId}
        </if>
        and position_type_id=2
        and sbqr.business_group=#{businessGroup}
    </select>

    <select id="getResultsDateNextComapnyQuarter" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        <!-- sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv, -->
        sbqr.up_three_level_organization_name as area,
        sbqr.up_two_level_organization_name as varea,
        sbqr.up_one_level_organization_name as province,
        sbqr.current_organization_name as company,
        1 as isNextRealtime,
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate<!--
        ,ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        -->
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 1 ">
            and up_three_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and up_two_level_organization_id = #{organizationId}
        </if>
        and position_type_id=2
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 1 ">
            and up_three_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and up_two_level_organization_id = #{organizationId}
        </if>
        and position_type_id=2
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.is_quarter  = 1  and sbqr.the_year_mon = #{yearMonth}
        <if test="organizationType == 1 ">
            and up_three_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and up_two_level_organization_id = #{organizationId}
        </if>
        and position_type_id=2
        and sbqr.business_group=#{businessGroup}
    </select>

    <select id="getResultsDateNextMonthlyMonth" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        sbqr.position_type_id as positionType,
        <if test="organizationType==4">
            sbqr.current_organization_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            sbqr.up_four_level_organization_name as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.titleMonth,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqrThree.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from
        ads_sfa_background_month_report as sbqr
        left join (
        select
        current_organization_id as organization,
        the_year_mon as title,
        '月度' as titleMonth,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(sale_goal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from
        ads_sfa_background_month_report
        where
        the_year_mon = #{yearMonth}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id in (12,11,2) and (up_one_level_organization_id = #{organizationId} or up_three_level_organization_id = #{organizationId} or up_four_level_organization_id  = #{organizationId})
        </if>
        <if test="organizationType == 12 ">
            and position_type_id in (11,2) and (up_one_level_organization_id = #{organizationId}  or up_two_level_organization_id  = #{organizationId})
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3 and up_one_level_organization_id = #{organizationId}
        </if>
        and business_group=#{businessGroup}
        and is_quarter=0
        ) as sbqrTwo ON
        sbqrTwo.organization = sbqr.current_organization_id
        left join (
        select
        current_organization_id as organization,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from
        ads_sfa_background_month_report
        where
        the_year_mon = #{lastTowMonth}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3 and up_one_level_organization_id = #{organizationId}
        </if>
        and business_group=#{businessGroup}
        and is_quarter=0
        ) as sbqrThree ON
        sbqrThree.organization = sbqr.current_organization_id
        where
        sbqr.the_year_mon = #{yearMonth}
        <if test="organizationType == 4 ">
            and sbqr.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and sbqr.position_type_id=12 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and sbqr.position_type_id=11 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and sbqr.position_type_id=2 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and sbqr.position_type_id=10 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and sbqr.position_type_id=3 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        and sbqr.business_group=#{businessGroup}
        and sbqr.is_quarter=0
    </select>

    <select id="getResultsDateBusinessMonthlyTileMode"  resultMap="TeamResultMonthly">
        SELECT
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        sbqr.position_type_id as positionType,
        <if test="req.positionTypeId==1">
            sbqr.current_organization_name as area,

        </if>
        <if test="req.positionTypeId==12">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,

        </if>
        <if test="req.positionTypeId==11">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,

        </if>

        <if test="req.positionTypeId==2">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,

        </if>
        <if test="req.positionTypeId==10">
            sbqr.up_four_level_organization_name  as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
        </if>
        <if test="req.positionTypeId==3">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        <if test="req.positionTypeId == 99">
            case position_type_id when 1 then current_organization_name
            when 12 then up_one_level_organization_name
            when 11 then up_two_level_organization_name
            when 2 then up_three_level_organization_name
            when 10 then up_four_level_organization_name
            end as area,
            case position_type_id when 12 then current_organization_name
            when 11 then up_one_level_organization_name
            when 2 then up_two_level_organization_name
            when 10 then up_three_level_organization_name
            end as varea,
            case position_type_id when 11 then current_organization_name
            when 2 then up_one_level_organization_name
            when 10 then up_two_level_organization_name
            end as province,
            case position_type_id when 2 then current_organization_name
            when 10 then up_one_level_organization_name
            end as company,
            case position_type_id when 10 then current_organization_name
            end as departmentName,
        </if>
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.titleMonth,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqrThree.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from
        <choose>
            <when test="req.monthlyType == 1">
                ads_sfa_background_month_report sbqr
            </when>
            <otherwise>
                ads_sfa_background_quarter_report sbqr
            </otherwise>
        </choose>
        inner join ods.hp_ceo_business_organization_view v on v.organization_id = sbqr.current_organization_id
        left join (
        select
        current_organization_id as organization,
        the_year_mon as title,
        <choose>
            <when test="req.monthlyType == 1">
                '月度' as titleMonth,
                ifnull(sale_goal,0) as goal,
            </when>
            <otherwise>
                '季度' as titleMonth,
                ifnull(saleGoal,0) as goal,
            </otherwise>
        </choose>

        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from
        <choose>
            <when test="req.monthlyType == 1">
                ads_sfa_background_month_report
            </when>
            <otherwise>
                ads_sfa_background_quarter_report
            </otherwise>
        </choose>

        where
        the_year_mon = #{req.yearMonth}


        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        and business_group=#{req.businessGroup}
        <choose>
            <when test="req.monthlyType == 1">
                and is_quarter= 0
            </when>
            <otherwise>
                and is_quarter=1
            </otherwise>
        </choose>

        ) as sbqrTwo ON
        sbqrTwo.organization = sbqr.current_organization_id
        left join (
        select
        current_organization_id as organization,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from
        <choose>
            <when test="req.monthlyType == 1">
                ads_sfa_background_month_report
            </when>
            <otherwise>
                ads_sfa_background_quarter_report
            </otherwise>
        </choose>
        where
        the_year_mon = #{req.lastTowMonth}
        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        and business_group=#{req.businessGroup}
        <choose>
            <when test="req.monthlyType == 1">
                and is_quarter= 0
            </when>
            <otherwise>
                and is_quarter=1
            </otherwise>
        </choose>
        ) as sbqrThree ON
        sbqrThree.organization = sbqr.current_organization_id
        where
        sbqr.the_year_mon = #{req.yearMonth}

        <choose>
            <when test="req.orgType == 'area'">
                and v.organization_id3 = #{req.organizationId}
            </when>
            <when test="req.orgType == 'varea'">
                and v.virtual_area_id = #{req.organizationId}
            </when>
            <when test="req.orgType == 'province'">
                and v.province_id = #{req.organizationId}
            </when>
            <when test="req.orgType == 'company'">
                and v.organization_id2 = #{req.organizationId}
            </when>
            <when test="req.orgType == 'department'">
                and v.department_id = #{req.organizationId}
            </when>
        </choose>

        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        and sbqr.business_group=#{req.businessGroup}
        and sbqr.is_quarter=0
        order by  sbqr.itemsSupplyTotal_cm_budget_avg_gmv
    </select>

    <select id="getResultsDateMonthlyQuarterOld" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
     SELECT
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        sbqr.up_four_level_organization_name as area,
        sbqr.up_three_level_organization_name as varea,
        sbqr.up_two_level_organization_name as province,
        sbqr.up_one_level_organization_name as company,
        sbqr.current_organization_name as departmentName,
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
            (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(saleGoal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and current_organization_id = #{organizationId} and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and current_organization_id = #{organizationId} and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where  sbqr.is_quarter  = 1 and sbqr.current_organization_id = #{organizationId} and sbqr.the_year_mon = #{yearMonth} and sbqr.business_group=#{businessGroup}
        </select>

    <select id="getResultsDateMonthlyQuarter" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        sbqr.up_four_level_organization_name as area,
        sbqr.up_three_level_organization_name as varea,
        sbqr.up_two_level_organization_name as province,
        sbqr.up_one_level_organization_name as company,
        sbqr.current_organization_name as departmentName,
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and current_organization_id = #{organizationId} and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and current_organization_id = #{organizationId} and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
        union all
        select
        current_organization_id as organization,
        CONCAT(the_year_mon,'财年') as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 3 and current_organization_id = #{organizationId} and the_year_mon = #{year} and business_group=#{businessGroup}
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where  sbqr.is_quarter  = 1 and sbqr.current_organization_id = #{organizationId} and sbqr.the_year_mon = #{yearMonth} and sbqr.business_group=#{businessGroup}
    </select>


    <select id="getResultsDateMonthlyZB" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultType="com.wantwant.sfa.backend.realData.vo.TeamPerformanceVo">
         select
        '季度' as title,
        0 as biddingPerformance,
        0 as goal,
        0 as goalAchievementRate,
        0 as performanceAchievementRate,
        0 as yearAchievementRate,
        0 as managementPositionPerPerformance,
        0 as employmentRate
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and current_organization_id = 'ZB_Z_A' and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        union all
        select
        the_year_mon as title,
        0 as biddingPerformance,
        0 as goal,
        0 as goalAchievementRate,
        0 as performanceAchievementRate,
        0 as yearAchievementRate,
        0 as managementPositionPerPerformance,
        0 as employmentRate
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and current_organization_id = 'ZB_Z_A' and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
    </select>

    <select id="getResultsDateNextMonthlyQuarterOld" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.position_type_id as positionType,
        <if test="organizationType==4">
            sbqr.current_organization_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            sbqr.up_four_level_organization_name as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and up_one_level_organization_id = #{organizationId}
        </if>
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and up_one_level_organization_id = #{organizationId}
        </if>
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.is_quarter  = 1  and sbqr.the_year_mon = #{yearMonth}
        <if test="organizationType == 4 ">
            and sbqr.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and sbqr.position_type_id=12 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and sbqr.position_type_id=11 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and sbqr.position_type_id=2 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and sbqr.position_type_id=10 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and sbqr.position_type_id=3 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        and sbqr.business_group=#{businessGroup}
    </select>

    <select id="getResultsDateNextMonthlyQuarter" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.position_type_id as positionType,
        <if test="organizationType==4">
            sbqr.current_organization_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            sbqr.up_four_level_organization_name as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and up_one_level_organization_id = #{organizationId}
        </if>
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and up_one_level_organization_id = #{organizationId}
        </if>
        union all
        select
        current_organization_id as organization,
        CONCAT(the_year_mon,'财年') as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 3 and the_year_mon = #{year} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and up_one_level_organization_id = #{organizationId}
        </if>
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.is_quarter  = 1  and sbqr.the_year_mon = #{yearMonth}
        <if test="organizationType == 4 ">
            and sbqr.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and sbqr.position_type_id=12 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and sbqr.position_type_id=11 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and sbqr.position_type_id=2 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and sbqr.position_type_id=10 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and sbqr.position_type_id=3 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        and sbqr.business_group=#{businessGroup}
    </select>



    <select id="getResultsDateNextMonthlyQuarterTileMode" resultMap="TeamResultMonthly">
        SELECT
        sbqr.position_type_id as positionType,
        <if test="req.positionTypeId==1">
            sbqr.current_organization_name as area,

        </if>
        <if test="req.positionTypeId==12">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,

        </if>
        <if test="req.positionTypeId==11">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,

        </if>

        <if test="req.positionTypeId==2">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,

        </if>
        <if test="req.positionTypeId==10">
            sbqr.up_four_level_organization_name  as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
        </if>
        <if test="req.positionTypeId==3">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        <if test="req.positionTypeId == 99">
            case position_type_id when 1 then current_organization_name
            when 12 then up_one_level_organization_name
            when 11 then up_two_level_organization_name
            when 2 then up_three_level_organization_name
            when 10 then up_four_level_organization_name
            end as area,
            case position_type_id when 12 then current_organization_name
            when 11 then up_one_level_organization_name
            when 2 then up_two_level_organization_name
            when 10 then up_three_level_organization_name
            end as varea,
            case position_type_id when 11 then current_organization_name
            when 2 then up_one_level_organization_name
            when 10 then up_two_level_organization_name
            end as province,
            case position_type_id when 2 then current_organization_name
            when 10 then up_one_level_organization_name
            end as company,
            case position_type_id when 10 then current_organization_name
            end as departmentName,
        </if>

        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm,
        sbqr.itemsSupplyTotal_cm_budget_avg_gmv as itemsSupplyTotalCmBudgetAvgGmv,
        round(ifnull(sbqr.sale_goal_achievement_rate,0)*100,2) as saleGoalAchievementRate,
        round(ifnull(sbqr.itemsSupplyTotal_year_on_year,0)*100,2) as itemSupplyTotalYearOnYear
        from ads_sfa_background_quarter_report as sbqr
        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where  is_quarter  = 1 and the_year_mon = #{req.yearMonth} and business_group=#{req.businessGroup}
        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        union all
        select
        current_organization_id as organization,
        the_year_mon as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 0 and concat(substr(the_year_mon,1,4),'-0',quarter(concat(the_year_mon,'-01'))) = #{req.yearMonth} and business_group=#{req.businessGroup}
        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        union all
        select
        current_organization_id as organization,
        CONCAT(the_year_mon,'财年') as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        ifnull(saleGoal,0) as goal,
        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from ads_sfa_background_quarter_report
        where is_quarter  = 3 and the_year_mon = #{req.year} and business_group=#{req.businessGroup}
        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.is_quarter  = 1  and sbqr.the_year_mon = #{req.yearMonth}
        <choose>
            <when test="req.positionTypeId  != 99">
                and position_type_id=#{req.positionTypeId}
            </when>
            <otherwise>
                <foreach collection="req.organizationIds" separator="," open=" and current_organization_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </otherwise>
        </choose>
        and sbqr.business_group=#{req.businessGroup}
    </select>


    <select id="getResultsDateNextMonthlyQuarterDimission" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodReq"
            resultMap="TeamResultMonthly">
        SELECT
        sbqr.position_type_id as positionType,
        <if test="organizationType==4">
            sbqr.current_organization_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            sbqr.up_one_level_organization_name as area,
            sbqr.current_organization_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            sbqr.up_two_level_organization_name as area,
            sbqr.up_one_level_organization_name as varea,
            sbqr.current_organization_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            sbqr.up_three_level_organization_name as area,
            sbqr.up_two_level_organization_name as varea,
            sbqr.up_one_level_organization_name as province,
            sbqr.current_organization_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            sbqr.up_four_level_organization_name as area,
            sbqr.up_three_level_organization_name as varea,
            sbqr.up_two_level_organization_name as province,
            sbqr.up_one_level_organization_name as company,
            sbqr.current_organization_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            sbqr.up_five_level_organization_name  as area,
            sbqr.up_four_level_organization_name as varea,
            sbqr.up_three_level_organization_name as province,
            sbqr.up_two_level_organization_name as company,
            sbqr.up_one_level_organization_name as departmentName,
        </if>
        sbqr.up_one_level_organization_name as positionTypeId,
        sbqr.current_organization_name as organizationName,
        sbqr.current_organization_id as organizationId,
        sbqr.position_name as positionName,
        sbqr.pic_url as url,
        sbqr.employee_name as employeeName,
        sbqr.onboard_days as workingDays,
        sbqr.itemsSupplyTotal_cm_budget as population,
        round(sbqr.itemsSupplyTotal_cm_budget_achievement_rate*100,2) as populationProportion,
        sbqr.onboard_days as workingDays,
        sbqr.contract_partner_onboard_nums as contractNum,
        sbqr.all_nums_cm as onJobNum,
        sbqr.city_manager_all as departmentNumsCm,
        sbqr.city_manager_all as cityManagerHeadCount,
        round(sbqr.city_manager_onboard_rate*100,2) as cityManagerOnBoardRate,
        sbqr.all_nums_cm as onBoardNumsCm,
        sbqr.business_partner_onboard_part_time_nums as partTimeNum,
        sbqr.business_partner_onboard_full_time_nums as fullTimeNum,
        sbqr.enterprise_partner_onboard_nums as enterpriseNum,
        sbqr.partner_onboard_nums_cm as inductionNum,
        sbqr.partner_off_nums_cm as departureNum,
        round(sbqr.partner_off_nums_rate*100,2) as departureRate,
        round(sbqr.employment_expenses_total_rate_quarter*100,2) as employmentRateSeason,
        round(sbqr.employment_expenses_total_rate_year*100,2) as employmentRateYears,
        sbqrTwo.title,
        sbqrTwo.biddingPerformance,
        sbqrTwo.goal,
        sbqrTwo.goalAchievementRate,
        sbqrTwo.performanceAchievementRate,
        sbqrTwo.yearAchievementRate,
        sbqrTwo.managementPositionPerPerformance,
        sbqrTwo.employmentRate,
        sbqrTwo.managementPositionTarget,
        sbqrTwo.managementPositionGoalRate,
        sbqrTwo.managementPositionChainRatio,
        sbqrTwo.managementPositionYearOnYear,
        sbqr.cur_itemsSupplyTotal_cm as curItemsSupplyTotalCm
        from
        <choose>
            <when test="monthlyType == 1">
                ads_sfa_background_month_report sbqr
            </when>
            <otherwise>
                ads_sfa_background_quarter_report sbqr
            </otherwise>
        </choose>

        left join
        (
        select
        current_organization_id as organization,
        '季度' as title,
        ifnull(itemsSupplyTotal_cm,0) as biddingPerformance,
        <choose>
            <when test="monthlyType == 1">
                ifnull(sale_goal,0) as goal,
            </when>
            <otherwise>
                ifnull(saleGoal,0) as goal,
            </otherwise>
        </choose>

        ifnull(Round(sale_goal_achievement_rate*100,2),0) as goalAchievementRate,
        ifnull(Round(month_ring_ratio*100,2),0) as performanceAchievementRate,
        ifnull(Round(itemsSupplyTotal_year_on_year*100,2),0) as yearAchievementRate,
        ifnull(management_avg_gmv,0) as managementPositionPerPerformance,
        ifnull(Round(employment_expenses_total_rate*100,2),0) as employmentRate,
        ifnull(management_avg_gmv_target,0) as managementPositionTarget,
        ifnull(Round(management_avg_gmv_rate*100,2),0) as managementPositionGoalRate,
        ifnull(Round(management_avg_gmv_mom*100,2),0) as managementPositionChainRatio,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPositionYearOnYear
        from
        <choose>
            <when test="monthlyType == 1">
                ads_sfa_background_month_report
            </when>
            <otherwise>
                ads_sfa_background_quarter_report
            </otherwise>
        </choose>


        where
        <choose>
            <when test="monthlyType == 1">
                is_quarter  = 0
            </when>
            <otherwise>
                is_quarter  = 1
            </otherwise>
        </choose>
        and the_year_mon = #{yearMonth} and business_group=#{businessGroup}
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2 and up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and up_one_level_organization_id = #{organizationId}
        </if>
        ) as sbqrTwo ON sbqrTwo.organization = sbqr.current_organization_id
        where
        sbqr.the_year_mon = #{yearMonth}
        <choose>
            <when test="monthlyType == 1">
                and sbqr.is_quarter  = 0
            </when>
            <otherwise>
                and sbqr.is_quarter  = 1
            </otherwise>
        </choose>

        and sbqr.the_year_mon = #{yearMonth}

        <if test="organizationType == 4 ">
            and sbqr.position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and sbqr.position_type_id=12 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and sbqr.position_type_id=11 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and sbqr.position_type_id=2 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and sbqr.position_type_id=10 and sbqr.up_one_level_organization_id = #{organizationId}
        </if>
        and sbqr.business_group=#{businessGroup}
    </select>

    <select id="queryGoodsProductLineDataYear" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.CommodityYearSkuLineVo">
        SELECT
        sku_images as picture,
        sku_spu_line as productNmae,
        sku_name as name,
        classic_item as isSutraItem,
        sku_spec as specification,
        line as productLine,
        line as spuLine,
        flavour as taste,
        round(gmv_cm,2) as cumulativePerformance,
        round(gmv_cm_rate*100,2) as performanceRatio,
        round(gmv_sm,2) as contemporaneousPerformance,
        round(gmv_sm_rate*100,2) as concurrentProportion,
        round(gmv_sm_growth_rate*100,2) as yearOnYear,
        round(management_avg_gmv,2) as managePerPerformance,
        round(management_avg_gmv_yoy*100,2) as managePerPerformanceYear,
        round(billing_partner_nums,2) as billingNumber,
        round(billing_partner_grate_yoy*100,2) as billingNumberYear,
        round(avg_performance,2) as perPerformance,
        round(avg_performance_grate_yoy*100,2) as perPerformanceYear,
        round(gmv_sm_growth_rate_all*100,2) as gmvSmGrowthRateAll,
        round(management_avg_gmv_sm,2) as managementAvgGmvSm,
        round(management_avg_gmv_yoy_all*100,2) as managementAvgGmvYoyAll,
        round(billing_partner_nums_sm,2) as billingPartnerNumsSm,
        round(billing_partner_grate_yoy_all*100,2) as billingPartnerGrateYoyAll,
        round(avg_performance_sm,2) as avgPerformanceSm,
        round(avg_performance_grate_yoy_all*100,2) as avgPerformanceGrateYoyAll,
        round(passed_gmv_rate_sm*100,2) as passedGmvRateSm,
        round(passed_gmv_sm,2) as passedGmvRateSmPerformance
        FROM
        <if test="businessGroup != 99">
            annual_real_time_product_analyse
        </if>
        <if test="businessGroup == 99">
            annual_real_time_product_analyse_allbusinessgroup
        </if>
        where type_id=#{productType}
        <if test="null != organizationId and organizationId !=''">
            and organization_id=#{organizationId}
        </if>
        <if test="null != year and year !=''">
            and the_year=#{year}
        </if>
        <if test="null != productName and productName !=''">
            and sku_name like CONCAT('%',#{productName},'%')
        </if>
        <if test="null != businessGroup">
            and business_group=#{businessGroup}
        </if>
        order by
        <if test="(null == orderName or orderName== '') and (null == orderType or orderType== '')">
            gmv_cm desc
        </if>
        <if test="null != orderName and orderName !=''">
            CAST (${orderName} as decimal)
        </if>
        <if test="null != orderType and orderType!= '' and orderType=='asc'">
            asc
        </if>
        <if test="null != orderType and orderType!= '' and orderType=='desc'">
            desc
        </if>
        <if test="null != page and null != rows">
            limit #{offset},#{limit}
        </if>
    </select>

    <select id="queryGoodsProductLineDataYearCount" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="int">
        SELECT
        count(1)
        FROM
        <if test="businessGroup != 99">
            annual_real_time_product_analyse
        </if>
        <if test="businessGroup == 99">
            annual_real_time_product_analyse_allbusinessgroup
        </if>
        where type_id=#{productType}
        <if test="null != organizationId and organizationId !=''">
            and organization_id=#{organizationId}
        </if>
        <if test="null != year and year !=''">
            and the_year=#{year}
        </if>
    </select>

    <select id="getResultsYear" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.NextResultsYearVo">
        select
        virtual_area_id as area,
        province_name as varea,
        branch_name as province,
        department_name as company,
        biz_office_name as departmentName,
        biz_office_name as organizationName,
        biz_organization_id as organizationId,
        biz_office_name as organizationName,
        position_type_id as positionTypeId,
        position_name as post,
        pic_url as url,
        employee_name as employeeName,
        onboard_days as workingDays,
        ifnull(Round(itemsSupplyTotal_cm,2),0) as cumulativePerformance,
        ifnull(Round(itemsSupplyTotal_ly,2),0) as contemporaneousPerformance,
        ifnull(Round(itemsSupplyTotal_cm_yoy_intact_rate*100,2),0) as itemsSupplyTotalCmYoyIntactRate,
        ifnull(Round(itemsSupplyTotal_cm_yoy_rate*100,2),0) as yearOnYear,
        ifnull(Round(management_avg_gmv,2),0) as managementPerPerformance,
        ifnull(management_avg_gmv_ly,0) as managementAvgGmvLy,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPerPerformanceYear,
        ifnull(Round(management_avg_gmv_intact_yoy*100,2),0) as managementAvgGmvIntactYoy,
        ifnull(Round(partner_billing_nums,2),0) as billingNumber,
        ifnull(Round(partner_billing_nums_ly,2),0) as partnerBillingNumsLy,
        ifnull(Round(partner_billing_nums_year_on_year*100,2),0) as billingNumberYear,
        ifnull(Round(partner_billing_nums_intact_year_on_year*100,2),0) as partnerBillingNumsIntactYearOnYear,
        ifnull(Round(customer_price_total,2),0) as billingPerPerformance,
        ifnull(Round(customer_price_total_ly,2),0) as customerPriceTotalLy,
        ifnull(Round(customer_price_total_year_on_year*100,2),0) as billingPerPerformanceYear,
        ifnull(Round(customer_price_intact_year_on_year*100,2),0) as customerPriceIntactYearOnYar
        from
        <if test="businessGroup != 99">
            sfa_background_real_time_data_four_annual
        </if>
        <if test="businessGroup == 99">
            sfa_background_real_time_data_four_annual_group
        </if>
        where the_year = #{year}
        <if test="organizationType == 4 ">
            and position_type_id=4 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=1 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=12 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=11 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=2 and biz_organization_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=10 and biz_organization_id = #{organizationId}
        </if>
        limit 1
    </select>

    <select id="getResultsNextYear" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.NextResultsYearVo">
        SELECT
        <if test="organizationType==4">
            biz_office_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            department_name as area,
            biz_office_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            branch_name as area,
            department_name as varea,
            biz_office_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            province_name as area,
            branch_name as varea,
            department_name as province,
            biz_office_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            virtual_area_name  as area,
            province_name as varea,
            branch_name as province,
            department_name as company,
            biz_office_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            region_name  as area,
            virtual_area_name as varea,
            province_name as province,
            branch_name as company,
            department_name as departmentName,
        </if>
        position_name as post,
        position_type_id as positionTypeId,
        biz_office_name as organizationName,
        biz_organization_id as organizationId,
        biz_office_name as organizationName,
        position_name as post,
        pic_url as url,
        employee_name as employeeName,
        onboard_days as workingDays,
        ifnull(Round(itemsSupplyTotal_cm,2),0) as cumulativePerformance,
        ifnull(Round(itemsSupplyTotal_ly,2),0) as contemporaneousPerformance,
        ifnull(Round(itemsSupplyTotal_cm_yoy_intact_rate*100,2),0) as itemsSupplyTotalCmYoyIntactRate,
        ifnull(Round(itemsSupplyTotal_cm_yoy_rate*100,2),0) as yearOnYear,
        ifnull(Round(management_avg_gmv,2),0) as managementPerPerformance,
        ifnull(management_avg_gmv_ly,0) as managementAvgGmvLy,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPerPerformanceYear,
        ifnull(Round(management_avg_gmv_intact_yoy*100,2),0) as managementAvgGmvIntactYoy,
        ifnull(Round(partner_billing_nums,2),0) as billingNumber,
        ifnull(Round(partner_billing_nums_ly,2),0) as partnerBillingNumsLy,
        ifnull(Round(partner_billing_nums_year_on_year*100,2),0) as billingNumberYear,
        ifnull(Round(partner_billing_nums_intact_year_on_year*100,2),0) as partnerBillingNumsIntactYearOnYear,
        ifnull(Round(customer_price_total,2),0) as billingPerPerformance,
        ifnull(Round(customer_price_total_ly,2),0) as customerPriceTotalLy,
        ifnull(Round(customer_price_total_year_on_year*100,2),0) as billingPerPerformanceYear,
        ifnull(Round(customer_price_intact_year_on_year*100,2),0) as customerPriceIntactYearOnYar
        from
        <if test="businessGroup != 99">
            sfa_background_real_time_data_four_annual
        </if>
        <if test="businessGroup == 99">
            sfa_background_real_time_data_four_annual_group
        </if>
        where
        the_year = #{year}
        <if test="null != businessGroup">
            and business_group=#{businessGroup}
        </if>
        <if test="organizationType == 4 ">
            and position_type_id=1
        </if>
        <if test="organizationType == 1 ">
            and position_type_id=12 and department_id = #{organizationId}
        </if>
        <if test="organizationType == 12 ">
            and position_type_id=11 and department_id = #{organizationId}
        </if>
        <if test="organizationType == 11 ">
            and position_type_id=2  and department_id = #{organizationId}
        </if>
        <if test="organizationType == 2 ">
            and position_type_id=10 and department_id = #{organizationId}
        </if>
        <if test="organizationType == 10 ">
            and position_type_id=3  and  department_id = #{organizationId}
        </if>
    </select>

    <select id="getResultsNextYearByPositionTypeId" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.NextResultsYearVo">
        SELECT
        <if test="organizationType==4">
            biz_office_name as area,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==1">
            department_name as area,
            biz_office_name as varea,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==12">
            branch_name as area,
            department_name as varea,
            biz_office_name as province,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==11">
            province_name as area,
            branch_name as varea,
            department_name as province,
            biz_office_name as company,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==2">
            virtual_area_name  as area,
            province_name as varea,
            branch_name as province,
            department_name as company,
            biz_office_name as departmentName,
            1 as isNextRealtime,
        </if>
        <if test="organizationType==10">
            region_name  as area,
            virtual_area_name as varea,
            province_name as province,
            branch_name as company,
            department_name as departmentName,
        </if>
        <if test="positionTypeId != 3">
            biz_office_name as organizationName,
        </if>
        <if test="positionTypeId == 3">
            department_name AS organizationName,
        </if>
        position_name as post,
        position_type_id as positionTypeId,

        biz_organization_id as organizationId,
        biz_office_name as organizationName,
        position_name as post,
        pic_url as url,
        employee_name as employeeName,
        onboard_days as workingDays,
        ifnull(Round(itemsSupplyTotal_cm,2),0) as cumulativePerformance,
        ifnull(Round(itemsSupplyTotal_ly,2),0) as contemporaneousPerformance,
        ifnull(Round(itemsSupplyTotal_cm_yoy_intact_rate*100,2),0) as itemsSupplyTotalCmYoyIntactRate,
        ifnull(Round(itemsSupplyTotal_cm_yoy_rate*100,2),0) as yearOnYear,
        ifnull(Round(management_avg_gmv,2),0) as managementPerPerformance,
        ifnull(management_avg_gmv_ly,0) as managementAvgGmvLy,
        ifnull(Round(management_avg_gmv_yoy*100,2),0) as managementPerPerformanceYear,
        ifnull(Round(management_avg_gmv_intact_yoy*100,2),0) as managementAvgGmvIntactYoy,
        ifnull(Round(partner_billing_nums,2),0) as billingNumber,
        ifnull(Round(partner_billing_nums_ly,2),0) as partnerBillingNumsLy,
        ifnull(Round(partner_billing_nums_year_on_year*100,2),0) as billingNumberYear,
        ifnull(Round(partner_billing_nums_intact_year_on_year*100,2),0) as partnerBillingNumsIntactYearOnYear,
        ifnull(Round(customer_price_total,2),0) as billingPerPerformance,
        ifnull(Round(customer_price_total_ly,2),0) as customerPriceTotalLy,
        ifnull(Round(customer_price_total_year_on_year*100,2),0) as billingPerPerformanceYear,
        ifnull(Round(customer_price_intact_year_on_year*100,2),0) as customerPriceIntactYearOnYar
        from
        <if test="businessGroup != 99">
            sfa_background_real_time_data_four_annual
        </if>
        <if test="businessGroup == 99">
            sfa_background_real_time_data_four_annual_group
        </if>
        where
        the_year = #{year}
        <if test="organizationId.contains('ZB'.toString())">
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 1 and positionTypeId == 12"> <!--战区组织-->
            and department_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 1 and positionTypeId == 11">
            and branch_organization_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 1 and positionTypeId == 2">
            and province_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 1 and positionTypeId == 10">
            and virtual_area_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 1 and positionTypeId == 3">
            and region_organization_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 12 and positionTypeId == 11"> <!--大区组织-->
            and department_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 12 and positionTypeId == 2">
            and branch_organization_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 12 and positionTypeId == 10">
            and province_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 12 and positionTypeId == 3">
            and virtual_area_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 11 and positionTypeId == 2"><!--省区组织-->
            and department_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 11 and positionTypeId == 10">
            and branch_organization_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 11 and positionTypeId == 3">
            and province_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 2 and positionTypeId == 10"><!--分公司组织-->
            and department_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 2 and positionTypeId == 3">
            and branch_organization_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="!organizationId.contains('ZB'.toString()) and organizationType == 10 and positionTypeId == 3"><!--区域经理-->
            and department_id = #{organizationId}
            and position_type_id = #{positionTypeId}
        </if>
        <if test="null != businessGroup">
            and business_group=#{businessGroup}
        </if>
    </select>

    <select id="getResultsPerformanceYear" parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.RealTimeYearVo">
        select
        position_name as postionName,
        mobile as mobile,
        position_type_id as positionTypeId,
        pic_url as picUrl,
        employee_name as employeeName,
        organization_id as organizationId,
        biz_office_name as organizationName,
        resume_url as curriculumVitae,
        birth_date as birthDate,
        pos_role_name as partnerType,
        employee_status as partnerTypeStatus,
        joining_company as contractingCompany,
        onboard_time as onboardTime,
        onboard_days as onboardDays,
        itemsSupplyTotal_cm as yearTotalPerformance,
        saleGoal as yearTotalGoal,
        saleGoal_achievement_rate as goalAchievementRate,
        itemsSupplyTotal_cm_yoy_rate as monthAchievement,
        itemsSupplyTotal_cm_at_that_ime as itemsSupplyTotalCmAtThatIme,
        itemsSupplyTotal_cm as itemsSupplyTotalCm,
        itemsSupplyTotal_cm_yoy_rate,
        itemsSupplyTotal_ly,
        itemsSupplyTotal_cm_yoy_intact_rate
        from
        <if test="businessGroup != 99">
            sfa_background_real_time_data_four_annual
        </if>
        <if test="businessGroup == 99">
            sfa_background_real_time_data_four_annual_group
        </if>
        where the_year = #{year}
        <if test="null != organizationId and '' != organizationId">
            and organization_id=#{organizationId}
        </if>
        <if test="null != information and '' != information">
            and (mobile like CONCAT('%',#{information},'%') or  employee_name like CONCAT('%',#{information},'%'))
        </if>
        <if test="null != businessGroup and '' != businessGroup">
            and business_group=#{businessGroup}
        </if>
        limit 1
    </select>


    <select id="getYearPerformanceAchievedTrendsTotal"
            resultType="com.wantwant.sfa.backend.productSynchronization.vo.YearPerformanceAchievedTrendsVo"
            parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest">
        SELECT
        thisYear.theDate,
        thisYear.numberingDate,
        thisYear.annualItemsSupplyTotalAchieved,
        thisYear.annualItemsSupplyTotalAchievedRate,
        thisYear.dropValue,
        thisYear.yoyRate,
        sum(annualItemsSupplyTotal) over (order by thisYear.numberingDate ) as annualItemsSupplyTotal,
        sum(annualItemsSupplyTotalLast) over (order by lastYear.numberingDate ) as annualItemsSupplyTotalLast
        from
        (
        SELECT
        the_date as theDate,
        numbering_date as numberingDate,
        annual_itemsSupplyTotal as annualItemsSupplyTotal,
        annual_itemsSupplyTotal_goal as annualItemsSupplyTotalAchieved,
        annual_itemsSupplyTotal_achieved_rate as annualItemsSupplyTotalAchievedRate,
        ROUND(cumulative_itemsSupplyTotal_difference,2) as dropValue,
        ROUND(cumulative_itemsSupplyTotal_yoy * 100 , 2) as yoyRate
        FROM
        sfa_background_real_time_performance_trends_annual
        <where>
            <if test="null != year and '' != year">
                the_year=#{year}
            </if>
            <if test="null != organizationId and '' != organizationId">
                and organization_id=#{organizationId}
            </if>
            <if test="null != businessGroup and '' != businessGroup">
                and business_group=#{businessGroup}
            </if>
        </where>
        ) as thisYear
        left join (
        SELECT
        numbering_date as numberingDate,
        annual_itemsSupplyTotal as annualItemsSupplyTotallast
        FROM
        sfa_background_real_time_performance_trends_annual
        <where>
            <if test="null != lastYear and '' != lastYear">
                the_year=#{lastYear}
            </if>
            <if test="null != organizationId and '' != organizationId">
                and organization_id=#{organizationId}
            </if>
            <if test="null != businessGroup and '' != businessGroup">
                and business_group=#{businessGroup}
            </if>
        </where>
        ) AS lastYear on thisYear.numberingDate=lastYear.numberingDate
        order by thisYear.numberingDate asc
        limit 200
    </select>

    <select id="getYearPerformanceAchievedTrends"
            resultType="com.wantwant.sfa.backend.productSynchronization.vo.YearPerformanceAchievedTrendsVo"
            parameterType="com.wantwant.sfa.backend.realData.request.RealTimeYearRequest">
        SELECT
        thisYear.numberingDate,
        thisYear.annualItemsSupplyTotalAchieved,
        thisYear.annualItemsSupplyTotalAchievedRate,
        thisYear.annualItemsSupplyTotal,
        thisYear.dropValue,
        thisYear.yoyRate,
        lastYear.annualItemsSupplyTotalLast
        from
        (
        SELECT
        numbering_date as numberingDate,
        annual_itemsSupplyTotal as annualItemsSupplyTotal,
        annual_itemsSupplyTotal_goal as annualItemsSupplyTotalAchieved,
        annual_itemsSupplyTotal_achieved_rate as annualItemsSupplyTotalAchievedRate,
        ROUND(cumulative_itemsSupplyTotal_difference,2) as dropValue,
        cumulative_itemsSupplyTotal_yoy as yoyRate
        FROM
        sfa_background_real_time_performance_trends_annual
        <where>
            <if test="null != year and '' != year">
                the_year=#{year}
            </if>
            <if test="null != organizationId and '' != organizationId">
                and organization_id=#{organizationId}
            </if>
            <if test="null != businessGroup and '' != businessGroup">
                and business_group=#{businessGroup}
            </if>
        </where>
        ) as thisYear
        left join (
        SELECT
        numbering_date as numberingDate,
        annual_itemsSupplyTotal as annualItemsSupplyTotallast
        FROM
        sfa_background_real_time_performance_trends_annual
        <where>
            <if test="null != lastYear and '' != lastYear">
                the_year=#{lastYear}
            </if>
            <if test="null != organizationId and '' != organizationId">
                and organization_id=#{organizationId}
            </if>
            <if test="null != businessGroup and '' != businessGroup">
                and business_group=#{businessGroup}
            </if>
        </where>
        ) AS lastYear on thisYear.numberingDate=lastYear.numberingDate
        order by thisYear.numberingDate asc
        limit 200
    </select>

    <select id="selectClassification" parameterType="string" resultType="com.wantwant.sfa.backend.realData.vo.SkuDataStockVo">
        SELECT
        channel_name as channelName,
        normal_inventory as inventoryNum
        FROM
        production_sale_inventory_second_edition
        <if test="null != yaar and yaar=='2022'">
            WHERE data_date = '2022-01-31'
        </if>
        <if test="null != yaar and yaar=='2023'">
            WHERE data_date = '2023-01-21'
        </if>
        <if test="null != yaar and yaar=='2024'">
            WHERE data_date = DATE_FORMAT(NOW(), '%Y-%m-%d')
        </if>
        <if test="null != sku and sku !='' ">
            and  sku = #{sku}
        </if>
    </select>



    <select id="querySamePostPerformance" resultType="com.wantwant.sfa.backend.realData.vo.QuerySamePostPerformanceVo">
        SELECT
            abo.area_name AS areaName,
            abo.varea_name AS vareaName,
            abo.province_name AS provinceName,
            abo.company_name AS companyName,
            abo.department_name AS departmentName,
            abo.organization_id as organizationId,
            sbr.pic_url as url,
            sbr.employee_info_id as employeeInfoId,
            sbr.employee_name as employeeName,
            abo.organization_name as organizationName,

            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,

            sbr.onboard_time as onboardTime,
            sbr.off_time as dischargeDate,
            sbr.zw_onboard_days as onboardDays,
            sbr.position_id as positionId,
            <!--
                成员数量：取人员数据，按照产品组+组织+岗位
                1、总部（合计）～区域总监岗位的成员数据取：管理岗汇总在职人数
                2、区域经理的成员数量取：合伙人—客户数
                岗位类型id,4-总部,1-总督导,12-大区总监,11-省区总监,2-区域总监,10-区域经理,3-合伙人
            -->
            case abo.position_type_id
                when 4 then abo.glgzzrs_329
                when 1 then abo.glgzzrs_329
                when 12 then abo.glgzzrs_329
                when 11 then abo.glgzzrs_329
                when 2 then abo.glgzzrs_329
                when 10 then abo.khs_61
                else '-'
                end as memberCount,
            case
                abo.position_type_id when 4 then 7
                 else abo.position_type_id
                end as positionTypeId,
            sbr.employee_status as employeeStatus,
            sbr.pos_role_name as positionName,

            abo.zwbppjyj_cur_405 as performance,
            abo.pjyjmb_378 as goal,
            round(abo.pjyjmbdcl_32 * 100, 1) as performanceAchievementRate,
            round(abo.zwbppjyj_cur_405_hb * 100, 1) as performanceChainRatio,
            round(abo.zwbppjyj_cur_405_tb * 100, 1) as performanceYearRatio,

            abo.yczye_319 as preStoredValuePerformance,
            abo.yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
            abo.yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

            abo.rks_466 as population,
            abo.rksrjyj_320 as performancePerPopulation,

            abo.glgzzrs_329 as managementOnJobNum,
            abo.glgrjyj_cur_483 as managementPerPerformance,

            round(abo.glgrjjykhs_486, 1) as managementPositionCustomerNum,
            abo.kdj_37 as perCustomer,
            round(abo.khfgl_145 * 100, 1) as repurchaseRate,

            round(abo.glgrjkfkhs_488, 1) as managementPositionAverDevelopCustomerCount,
            round(ifnull(abo.hhrlzl_65, 0)* 100, 1) as closeRate,

            round(abo.bfl_460 * 100, 2) as visitRate,
            round(abo.csjlrrjbfs_510, 1) as departmentVisitCount,
            round(abo.qyzjrrjbfs_511, 1) as companyVisitCount,
            round(abo.dqzjrrjbfs_512, 1) as vareaVisitCount
        from
            ads_bigtable_organization abo
                left join ads_member_personal_month sbr on
                        sbr.organization_code = abo.organization_id
                    and sbr.the_year_month = #{request.relationMonth}
        <where>
            abo.the_year_month = #{request.yearMonth}
            and abo.date_type_id = #{request.dateTypeId}
            and abo.business_group_id = #{request.businessGroup}
            <choose>
                <when test="request.ownOrganizationType == 'zb'">
                    and abo.position_type_id = #{request.readPositionTypeId}
                </when>
                <when test="request.ownOrganizationType != 'zb'">
                    and abo.organization_id in
                    <foreach collection="request.samePostOrganizationIds" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </when>
            </choose>

            <if test="request.organizationIds !=null and request.organizationIds.size > 0">
                and (abo.organization_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>

                or abo.area_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>

                or abo.varea_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>

                or abo.province_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>

                or abo.company_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>


                or abo.department_id in
                <foreach collection="request.organizationIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
                )
            </if>
        </where>

        <choose>
            <when test="request.sortName !=null and request.sortName != '' ">
                order by
                <choose>
                    <when test="request.sortName == 'performance'">
                        abo.zwbppjyj_cur_405
                    </when>
                    <when test="request.sortName == 'goal'">
                        abo.pjyjmb_378
                    </when>
                    <when test="request.sortName == 'performanceAchievementRate'">
                        abo.pjyjmbdcl_32
                    </when>
                    <when test="request.sortName == 'performanceChainRatio'">
                        abo.zwbppjyj_cur_405_hb
                    </when>
                    <when test="request.sortName == 'performanceYearRatio'">
                        abo.zwbppjyj_cur_405_tb
                    </when>
                    <when test="request.sortName == 'preStoredValuePerformance'">
                        abo.yczye_319
                    </when>
                    <when test="request.sortName == 'advanceOrderUnshippedPerformanceThis'">
                        abo.yddwfhyj_byfh_past_421
                    </when>
                    <when test="request.sortName == 'oldCustomerPerformanceDealerYearRatioNext'">
                        abo.yddwfhyj_cyfh_past_422
                    </when>
                    <when test="request.sortName == 'population'">
                        abo.rks_466
                    </when>
                    <when test="request.sortName == 'performancePerPopulation'">
                        abo.rksrjyj_320
                    </when>
                    <when test="request.sortName == 'managementOnJobNum'">
                        abo.glgzzrs_329
                    </when>
                    <when test="request.sortName == 'managementPerPerformance'">
                        abo.glgrjyj_cur_483
                    </when>
                    <when test="request.sortName == 'managementPositionCustomerNum'">
                        abo.glgrjjykhs_486
                    </when>
                    <when test="request.sortName == 'perCustomer'">
                        abo.kdj_37
                    </when>
                    <when test="request.sortName == 'repurchaseRate'">
                        abo.khfgl_145
                    </when>
                    <when test="request.sortName == 'managementPositionAverDevelopCustomerCount'">
                        abo.glgrjkfkhs_488
                    </when>
                    <when test="request.sortName == 'closeRate'">
                        abo.hhrlzl_65
                    </when>
                    <when test="request.sortName == 'visitRate'">
                        abo.bfl_460
                    </when>
                    <when test="request.sortName == 'departmentVisitRate'">
                        abo.csjlrrjbfs_510
                    </when>
                    <when test="request.sortName == 'companyVisitRate'">
                        abo.qyzjrrjbfs_511
                    </when>
                    <when test="request.sortName == 'vareaVisitRate'">
                        abo.dqzjrrjbfs_512
                    </when>
                    <otherwise>
                        abo.zwbppjyj_cur_405
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by abo.zwbppjyj_cur_405
            </otherwise>
        </choose>
        <choose>
            <when test="request.sortOrder !=null and request.sortOrder != '' ">
                <choose>
                    <when test="'ASC' == request.sortOrder ">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                desc
            </otherwise>
        </choose>

    </select>

    <select id="queryAllGroupPerformanceDate" parameterType="com.wantwant.sfa.backend.realData.request.AllGroupPerformanceRequest"
            resultType="com.wantwant.sfa.backend.realData.vo.GroupPerformanceVo">
        SELECT
        abo.business_group_id as businessGroupId,
        hsbg.business_group_name as businessGroupName,
        hsbg.sort as sort,
        hsbg.small_icon as smallIcon,
        hsbg.large_icon as largeIcon,
        hsbg.color as color,
        abo.organization_id as organizationId,
        abo.zwbppjyj_cur_405 as performance,
        abo.pjyjmb_378 as goal,
        round(abo.pjyjmbdcl_32*100,2) as performanceAchievementRate,
        round(abo.zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
        round(abo.zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,
        round(abo.wjbyjzb_dq_506*100,2) as performanceWantGoldDiscountRatio,
        abo.glgzzrs_329 as managementOnJobNum,
        round(abo.glgzzrs_329_hb*100,2) as managementOnJobNumChainRatio,
        round(abo.glgzzrs_329_tb*100,2) as managementOnJobNumYearRatio,
        round(xjyjzb_dq_525*100,2) as cashPerformanceRate,
        abo.ywrjyj_cur_590 as businessPersonnelPerPerformance,
        round(abo.ywrjyj_cur_590_hb*100,2) as businessPersonnelPerPerformanceChainRatio,
        round(abo.ywrjyj_cur_590_tb*100,2) as businessPersonnelPerPerformanceYearRatio,
        abo.glgrjyj_cur_483 as managementPerPerformance,
        round(abo.glgrjyj_cur_483_hb*100,2) as managementPerPerformanceChainRatio,
        round(abo.glgrjyj_cur_483_tb*100,2) as managementPerPerformanceYearRatio
        <if test="dateTypeId==10">
            ,
            coalesce(srt.zwPerformance,0) as dayPerformance
        </if>
        from ads_bigtable_organization abo
        inner join ods.hp_sfa_business_group hsbg on abo.business_group_id = hsbg.id and hsbg.delete_flag = 0 and hsbg.forbidden_sign_up = 0  and hsbg.status = 1
        <if test="dateTypeId==10">
            left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            ads.sfa_background_real_time_performance_trends
            WHERE
            filter_type = '线别'
            and  the_date = #{theDate}
            group by
            1,2,3
            ) srt on abo.organization_id = srt.biz_organization_id
        </if>
        WHERE abo.the_year_month=#{yearMonth}
        and abo.organization_id like CONCAT('',#{organizationId},'%')
        and abo.business_group_id !='99'
        and abo.date_type_id = #{dateTypeId}
        order by hsbg.sort
    </select>

    <select id="queryPerformanceDateSnapshot" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceDateVo">
        SELECT
        round(wxpwjbyj_657,1) as promotionalMaterialsGoldenCoinPerformance,
        round(wxpwjbyjzb_658*100,1) as promotionalMaterialsGoldenCoinPerformanceRate,
        round(yskje_659,1) as needReceivableAmount,

        round(wjbyjzb_dq_506*100,2) as goldenCoinPerformanceRate,
        round(wjbyjzb_dq_506*100,1) as performanceWantGoldDiscountRatio,
        ifnull(xjyj_dq_505,0) as cashPerformance,
        round(xjyjzb_dq_525*100,2) as cashPerformanceRate,

        jhxkhs_472 as outTimeactivatedCustomerWithInThirtyNum,
        round(jhxkhs_472_hb*100,2) as outTimeactivatedCustomerWithInThirtyRatio,
        round(jhxkhs_472_tb*100,2) as outTimeactivatedCustomerWithInThirtyYearRatio,

        jhxkhs_thirty_more_494 as outTimeactivatedCustomerAboveThirtyNum,
        round(jhxkhs_thirty_more_494_hb*100,2) as outTimeactivatedCustomerAboveThirtyRatio,
        round(jhxkhs_thirty_more_494_tb*100,2) as outTimeactivatedCustomerAboveThirtyYearRatio,

        kfxkhs_473 as inTimeactivatedCustomerNum,
        round(kfxkhs_473_hb*100,2) as inTimeactivatedCustomerRatio,
        round(kfxkhs_473_tb*100,2) as inTimeactivatedCustomerYearRatio,

        fgkhs_44 as repeatPurchasesCustomerNum,
        round(fgkhs_44_hb*100,2) as repeatPurchasesCustomerRatio,
        round(fgkhs_44_tb*100,2) as repeatPurchasesCustomerYearRatio,

        zhkhs_471 as recallCustomerNum,
        round(zhkhs_471_hb*100,2) as recallCustomerRatio,
        round(zhkhs_471_tb*100,2) as recallCustomerYearRatio,

        khs_61 as customerCount,
        hhrrzrs_295 as developCustomerCount,
        hhrlzrs_296 as closeCustomerCount,
        round(hhrlzl_65*100,2) as closeRate,
        yxkhs_447 as intentionCustomerCount,
        qzkhs_470 as latentCustomerCount,
        bfkhs_543 as visitCustomerCount,
        round(bfl_460*100,1) as visitCustomerRate,
        round(pdl_54*100,1) as takeStockRate,
        round(hhrjyl_50*100,1) as transactionRate,
        round(glgrjbfkhs_544,1) as managementPositionAverVisitCustomerCount,


        round(glgrjkhs_487,1) as managementPositionAverCustomerCount,
        round(glgrjkfkhs_488,1) as managementPositionAverDevelopCustomerCount,
        round(glgrjgbkhs_489,1) as managementPositionAverCloseCustomerCount,
        round(glgrjyxkhs_490,1) as managementPositionAverIntentionCustomerCount,
        round(glgrjqzkhs_491,1) as managementPositionAverLatentCustomerCount,
        round(yxwxdkhs_495,1) as intentionNotOrderCustomerCount,
        round(glgrjyxwxdkhs_496,1) as managementPositionAverIntentionNotOrderCustomerCount,

        round(yxxgdkhs_578,1) as intentionOrderCustomerCount,
        round(glgrjyxxgdkhs_579,1) as managementPositionAverIntentionOrderCustomerCount,

        sbr.pic_url as url,
        sbr.employee_name as employeeName,
        abo.organization_name as organizationName,
        abo.organization_name as organizationName,
        sbr.onboard_time as onboardTime,
        sbr.zw_onboard_days as onboardDays,
        sbr.position_id as positionId,
        case abo.position_type_id when 4 then 7 else abo.position_type_id end as positionTypeId,
        zwbppjyj_cur_405 as performance,
        pjyjmb_378 as goal,
        round(pjyjmbdcl_32*100,2) as performanceAchievementRate,
        round(zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,
        glgzzrs_329 as managementOnJobNum,
        round(glgzzrs_329_hb*100,2) as managementOnJobNumChainRatio,
        round(glgzzrs_329_tb*100,2) as managementOnJobNumYearRatio,
        glgrjyj_cur_483 as managementPerPerformance,
        round(glgrjyj_cur_483_hb*100,2) as managementPerPerformanceChainRatio,
        round(glgrjyj_cur_483_tb*100,2) as managementPerPerformanceYearRatio,
        yddyj_past_379 as bookingPerformance,
        round(yddyjzb_377*100,2) as bookingPerformanceRate,

        ctdyj_380 as normalOrderPerformance,
        round(ctdyjzb_381*100,2) as normalOrderPerformanceRate,
        round(ctdyj_380_hb*100,1) as normalOrderPerformanceRatio,
        round(ctdyj_380_tb*100,1) as normalOrderPerformanceYearRatio,
        round(ctdxjyj_649,1) as  normalOrderCashPerformance,
        round(ctdzkl_652*100,1) as normalOrderWantGoldDiscountRatio,

        ttdyj_382 as specialOrderPerformance,
        round(ttdyjzb_383*100,2) as specialOrderPerformanceRate,
        round(ttdyj_382_hb*100,1) as specialOrderPerformanceRatio,
        round(ttdyj_382_tb*100,1) as specialOrderPerformanceYearRatio,
        round(ttdxjyj_651,1) as specialOrderCashPerformance,
        round(ttdzkl_654*100,1) as specialOrderWantGoldDiscountRatio,


        abo.yddyj_cur_407 as currentBookingPerformance,
        round(abo.yddyjzb_cur_533*100,1) as currentBookingPerformanceRate,
        round(abo.yddyj_cur_407_hb*100,1) as currentBookingPerformanceRatio,
        round(abo.yddyj_cur_407_tb*100,1) as currentBookingPerformanceYearRatio,
        round(abo.yddxjyj_650,1) as currentBookingCashPerformance,
        round(abo.yddzkl_653*100,1) as currentBookingWantGoldDiscountRatio,

        wjbyj_dq_409 as goldenCoinPerformance,
        jdpxpjyj_141 as classicItemPerformance,
        zhzpxyj_390 as synthesisGroupPerformance,
        yddwfhyj_past_318 as advanceOrderUnshippedPerformance,
        yczye_319 as preStoredValuePerformance,
        cwsrhs_423 as financialReceipts,
        ywsrhs_446 as businessIncome,
        fywsrhs_444 as nonBusinessIncome,
        scsrhs_445 as tasteMallIncome,

        glgrjxkhyj_484 as managementPositionNewCustomerPerformance,
        round(glgrjxkhyj_484_hb*100,2) as managementPositionPerformanceNewCustomerRatio,
        round(glgrjxkhyj_484_tb*100,2) as managementPositionPerformanceNewCustomerYearRatio,

        glgrjlkhyj_485 as managementPositionOldCustomerPerformance,
        round(glgrjlkhyj_485_hb*100,2) as managementPositionPerformanceOldCustomerRatio,
        round(glgrjlkhyj_485_tb*100,2) as managementPositionPerformanceOldCustomerYearRatio,

        glgrjyj_cur_483 as managementPositionCustomerPerformance,
        round(glgrjyj_cur_483_hb*100,2) as managementPositionPerformanceCustomerRatio,
        round(glgrjyj_cur_483_tb*100,2) as managementPositionPerformanceCustomerYearRatio,


        yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
        yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

        zwbppjyj_cur_405 as performancePer,
        round(zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,
        jykhs_36 as tradingCustomerNum,
        round(jykhs_36_hb*100,2) as tradingCustomerNumRateRatio,
        round(jykhs_36_tb*100,2) as tradingCustomerNumRateYearRatio,
        round(hhrjyl_50*100,2) as partnerTransactionRate,
        kdj_37 as perCustomer,
        round(kdj_37_hb*100,2) as perCustomerRatio,
        round(kdj_37_tb*100,2) as perCustomerYearRatio,

        round(zwbppjyj_cur_405_hb*100,2) as performancePerRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performancePerYearRatio,
        xkhyj_397 as newCustomerPerformanceDealer,
        round(xkhyj_397_hb*100,2) as newCustomerPerformanceDealerRatio,
        round(xkhyj_397_tb*100,2) as newCustomerPerformanceDealerYearRatio,
        round(xkhwjbzkl_541*100,1) as newCustomerPerformanceDealerWantGoldDiscountRatio,
        xkhs_41 as newTradingCustomerNumDealer,
        round(xkhs_41_hb*100,2) as newTradingCustomerNumDealerRatio,
        round(xkhs_41_tb*100,2) as newTradingCustomerNumDealerYearRatio,
        xkhkdj_344 as newPerCustomerDealer,
        round(xkhkdj_344_hb*100,2) as newPerCustomerDealerRatio,
        round(xkhkdj_344_tb*100,2) as newPerCustomerDealerYearRatio,
        lkhyj_400 as oldCustomerPerformanceDealer,
        round(lkhyj_400_hb*100,2) as oldCustomerPerformanceDealerRatio,
        round(lkhyj_400_tb*100,2) as oldCustomerPerformanceDealerYearRatio,
        round(lkhwjbzkl_542*100,1) as oldCustomerPerformanceDealerWantGoldDiscountRatio,
        lkhs_42 as oldTradingCustomerNumDealer,
        round(lkhs_42_hb*100,2) as oldTradingCustomerNumDealerRatio,
        round(lkhs_42_tb*100,2) as oldTradingCustomerNumDealerYearRatio,
        lkhkdj_343 as oldPerCustomerDealer,
        round(lkhkdj_343_hb*100,2) as oldPerCustomerDealerRatio,
        round(lkhkdj_343_tb*100,2) as oldPerCustomerDealerYearRatio,


        ynxdkhs_9001 as withinAMonthTradingCustomerNum,
        round(ynxdkhszb_9005*100,2) as withinAMonthTradingCustomerRatio,
        ynxdkhs_9002 as withinTwoMonthTradingCustomerNum,
        round(ynxdkhszb_9006*100,2) as withinTwoMonthTradingCustomerRatio,
        ynxdkhs_9003 as withinThreeMonthTradingCustomerNum,
        round(ynxdkhszb_9007*100,2) as withinThreeMonthTradingCustomerRatio,
        ynxdkhs_9004 as beforeThreeMonthTradingCustomerNum,
        round(ynxdkhszb_9008*100,2) as beforeThreeMonthTradingCustomerRatio,


        round(glgrjjykhs_486,1) as managementPositionCustomerNum,
        round(glgrjjykhs_486_hb*100,2) as managementPositionCustomerNumRatio,
        round(glgrjjykhs_486_tb*100,2) as managementPositionCustomerNumYearRatio,

        round(glgrjxkhs_451,1)   as managementPositionNewCustomerNum,
        round(glgrjxkhs_451_hb*100,2) as managementPositionNewCustomerNumRatio,
        round(glgrjxkhs_451_tb*100,2) as managementPositionNewCustomerNumYearRatio,

        round(glgrjlkhs_456,1) as managementPositionOldCustomerNum,
        round(glgrjlkhs_456_hb*100,2) as managementPositionOldCustomerNumRatio,
        round(glgrjlkhs_456_tb*100,2) as managementPositionOldCustomerNumYearRatio,

        round(zazckhyj_573,1) as projectPolicyCustomerPerformance,
        round(zazckhyj_573_hb*100,2) as projectPolicyCustomerPerformanceChainRatio,
        round(zazckhyj_573_tb*100,2) as projectPolicyCustomerPerformanceYearRatio,
        round(zazckhyjzb_660*100,2) as projectPolicyCustomerPerformanceRate,


        round(ywbdzzrs_497,1)  as onJobNumBusinessDevelopment,
        round(ywbdzzrs_497_hb*100,1) as onJobNumRatioBusinessDevelopment,
        round(ywbdzzrs_497_tb*100,1)  as onJobNumYearRatioBusinessDevelopment,
        round(ywbdrzrs_498,1) as entryNumBusinessDevelopment,
        round(ywbdrzrs_498_hb*100,1) as entryNumRatioBusinessDevelopment,
        round(ywbdrzrs_498_tb*100,1) as entryNumYearRatioBusinessDevelopment,
        round(ywbdlzrs_499,1) as dimissionNumBusinessDevelopment,
        round(ywbdlzrs_499_hb*100,1) as dimissionNumRatioBusinessDevelopment,
        round(ywbdlzrs_499_tb*100,1) as dimissionNumYearRatioBusinessDevelopment,
        round(ywbdlzl_500*100,1) as quitRateBusinessDevelopment,
        round(ywbdlzl_500_hb*100,1) as quitRateRatioBusinessDevelopment,
        round(ywbdlzl_500_tb*100,1) as quitRateYearRatioBusinessDevelopment,
        round(ywbdbzs_502,1) as establishmentNumBusinessDevelopment,
        round(ywbdbzzgl_501*100,2) as establishmentOnGuardNumBusinessDevelopment,
        round(qzywbdzzrs_583,1) as fullTimeBusinessDevelopmentNum,
        round(qzywbdzzrszb_586*100,1) as fullTimeRateBusinessDevelopment,
        round(clywbdzzrs_584,1) as undertakeBusinessDevelopmentNum,
        round(clywbdzzrszb_587*100,1) as undertakeRateBusinessDevelopment,
        round(jzywbdzzrs_585,1) as partTimeBusinessDevelopmentNum,
        round(jzywbdzzrszb_588*100,1) as partTimeRateBusinessDevelopment,

        glgzzrs_329 as onJobNumManagement,
        glgzgzzrs_633 as onJobMainNumManagement,
        glgjgzzrs_634 as onJobPartNumManagement,
        round(glgzzrs_329_hb*100,1) as onJobNumManagementRatio,
        round(glgzzrs_329_tb*100,1) as onJobNumManagementYearRatio,
        glgrzrs_509 as entryNumManagement,
        round(glgrzrs_509_hb*100,1) as entryNumManagementRatio,
        round(glgrzrs_509_tb*100,1) as entryNumManagementYearRatio,
        glglzrs_507 as dimissionNumManagement,
        round(glglzrs_507_hb*100,1) as dimissionNumManagementRatio,
        round(glglzrs_507_tb*100,1) as dimissionNumManagementYearRatio,
        round(glglzl_508*100,2) as quitRateManagement,
        round(glglzl_508_hb*100,1) as quitRateManagementRatio,
        round(glglzl_508_tb*100,1)as quitRateManagementYearRatio,

        glgbzs_387 as establishmentNumManagement,
        round(glgbzzgl_330*100,2) as establishmentOnGuardNumManagement,
        round(glgzgzzrs_zb_645 * 100,2) as onJobMainRatioManagement,
        round(glgjgzzrs_zb_646 * 100,2) as onJobPartRatioManagement,

        lthhrs_515 as circulatingPartnerCount,
        kflthhrs_516 as openCirculatingPartnerCount,
        gblthhrs_517 as closeCirculatingPartnerCount,
        round(lthhrgbl_518 *100,1) as closeCirculatingPartnerRate,
        yxkhslt_545 as circulatingPartnerIntentionCustomerCount,
        yxwxdkhslt_546 as circulatingPartnerIntentionNotOrderCustomerCount,
        qzkhslt_547 as circulatingPartnerLatentCustomerCount,
        round(glgrjyxkhslt_548,1) as circulatingPartnerManagementPositionAverIntentionCustomerCount,
        round(glgrjyxwxdkhslt_549,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCount,
        round(glgrjqzkhslt_550,1) as circulatingPartnerManagementPositionAverLatentCustomerCount,
        round(glgrjkfkhslt_551,1) as circulatingPartnerManagementPositionAverDevelopCustomerCount,
        round(glgrjgbkhslt_552,1) as circulatingPartnerManagementPositionAverCloseCustomerCount,
        round(hhrjyllt_553*100,1) as circulatingPartnerTransactionRate,
        round(pdllt_554*100,1) as circulatingPartnerTakeStockRate,
        round(bfllt_555*100,1) as circulatingPartnerVisitCustomerRate,
        bfkhslt_556 as circulatingPartnerVisitCustomerCount,
        round(glgrjbfkhslt_557,1) as circulatingPartnerManagementPositionAverVisitCustomerCount,


        zyhhs_519 as directPartnerCount,
        kfzyhhrs_520 as openDirectPartnerCount,
        gbzyhhrs_521 as closeDirectPartnerCount,
        round(zyhhrgbl_522 * 100,1) as closeDirectPartnerRate,
        yxkhszy_558 as directPartnerIntentionCustomerCount,
        yxwxdkhszy_559 as directPartnerIntentionNotOrderCustomerCount,
        qzkhszy_560 as directPartnerLatentCustomerCount,
        round(glgrjyxkhszy_561,1) as directPartnerManagementPositionAverIntentionCustomerCount,
        round(glgrjyxwxdkhszy_562,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCount,
        round(glgrjqzkhszy_563,1) as directPartnerManagementPositionAverLatentCustomerCount,
        round(glgrjkfkhszy_564,1) as directPartnerManagementPositionAverDevelopCustomerCount,
        round(glgrjgbkhszy_565,1) as directPartnerManagementPositionAverCloseCustomerCount,
        round(hhrjylzy_566*100,1) as directPartnerTransactionRate,
        round(pdlzy_567*100,1) as directPartnerTakeStockRate,
        round(bflzy_568*100,1) as directPartnerVisitCustomerRate,
        bfkhszy_569 as directPartnerVisitCustomerCount,
        round(glgrjbfkhszy_570,1) as directPartnerManagementPositionAverVisitCustomerCount,

        dgzzrs_523 as onJobShoppingGuideCount,
        dggbrs_524 as closeShoppingGuideCount,
        dgrzrs_526 as entryShoppingGuideCount,

        ywzzrs_589 as salesmanOnJobNum,
        round(ywzzrs_589_hb*100,1) as salesmanOnJobNumChainRatio,
        round(ywzzrs_589_tb*100,1) as salesmanOnJobNumYearRatio,
        ywrjyj_cur_590 as salesmanPerPerformance,
        round(ywrjyj_cur_590_hb*100,1) as salesmanPerPerformanceChainRatio,
        round(ywrjyj_cur_590_tb*100,1) as salesmanPerPerformanceYearRatio,
        round(ztfyzkl_577*100,1) as overallDiscountRate,
        jykhsmb_537 as tradingCustomerGoal,
        round(jykhsmbdcl_538*100,1) as tradingCustomerGoalAchievementRate,
        round(xkhszb_593*100,1) as newTradingCustomerNumDealerProportion,
        round(lkhszb_594*100,1) as oldTradingCustomerNumDealerProportion,
        round(jsjhxkhszb_595*100,1) as inTimeActivatedCustomerProportion,
        round(wjsjhxkhszb_596*100,1) as outTimeActivatedCustomerWithInThirtyProportion,
        round(fgkhszb_597*100,1) as repeatPurchasesCustomerProportion,
        round(glgrjfgkhs_592,1) as managementPositionAverRepeatPurchasesCustomerNum,
        round(zhkhszb_598*100,1) as recallCustomerProportion,
        round(glgrjzhkhs_591,1) as managementPositionAverRecallCustomerNum,

        round(yxkhs_447_hb*100,1) as intentionCustomerCountChainRatio,
        round(yxkhs_447_tb*100,1) as intentionCustomerCountYearRatio,
        round(yxwxdkhs_495_hb*100,1) as intentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhs_495_tb*100,1) as intentionNotOrderCustomerCountYearRatio,
        round(qzkhs_470_hb*100,1) as latentCustomerCountChainRatio,
        round(qzkhs_470_tb*100,1) as latentCustomerCountYearRatio,
        round(glgrjyxkhs_490_hb*100,1) as managementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhs_490_tb*100,1) as managementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjqzkhs_491_hb*100,1) as managementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhs_491_tb*100,1) as managementPositionAverLatentCustomerCountYearRatio,
        round(glgrjyxwxdkhs_496_hb*100,1) as managementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhs_496_tb*100,1) as managementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(yxkhslt_545_hb*100,1) as circulatingPartnerIntentionCustomerCountChainRatio,
        round(yxkhslt_545_tb*100,1) as circulatingPartnerIntentionCustomerCountYearRatio,
        round(yxwxdkhslt_546_hb*100,1) as circulatingPartnerIntentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhslt_546_tb*100,1) as circulatingPartnerIntentionNotOrderCustomerCountYearRatio,
        round(qzkhslt_547_hb*100,1) as circulatingPartnerLatentCustomerCountChainRatio,
        round(qzkhslt_547_tb*100,1) as circulatingPartnerLatentCustomerCountYearRatio,
        round(glgrjyxkhslt_548_hb*100,1) as circulatingPartnerManagementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhslt_548_tb*100,1) as circulatingPartnerManagementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjyxwxdkhslt_549_hb*100,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhslt_549_tb*100,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(glgrjqzkhslt_550_hb*100,1) as circulatingPartnerManagementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhslt_550_tb*100,1) as circulatingPartnerManagementPositionAverLatentCustomerCountYearRatio,
        round(yxkhszy_558_hb*100,1) as directPartnerIntentionCustomerCountChainRatio,
        round(yxkhszy_558_tb*100,1) as directPartnerIntentionCustomerCountYearRatio,
        round(yxwxdkhszy_559_hb*100,1) as directPartnerIntentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhszy_559_tb*100,1) as directPartnerIntentionNotOrderCustomerCountYearRatio,
        round(qzkhszy_560_hb*100,1) as directPartnerLatentCustomerCountChainRatio,
        round(qzkhszy_560_tb*100,1) as directPartnerLatentCustomerCountYearRatio,
        round(glgrjyxkhszy_561_hb*100,1) as directPartnerManagementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhszy_561_tb*100,1) as directPartnerManagementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjyxwxdkhszy_562_hb*100,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhszy_562_tb*100,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(glgrjqzkhszy_563_hb*100,1) as directPartnerManagementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhszy_563_tb*100,1) as directPartnerManagementPositionAverLatentCustomerCountYearRatio,


        zddzzrs_314 as onJobNumArea,
        round(zddzzrs_314_hb*100,1) as onJobNumAreaRatio,
        round(zddzzrs_314_tb*100,1) as onJobNumAreaYearRatio,
        zddrzrs_315 as entryNumArea,
        round(zddrzrs_315_hb*100,1) as entryNumAreaRatio,
        round(zddrzrs_315_tb*100,1) as entryNumAreaYearRatio,
        zddlzrs_316 as dimissionNumArea,
        round(zddlzrs_316_hb*100,1) as dimissionNumAreaRatio,
        round(zddlzrs_316_tb*100,1) as dimissionNumAreaYearRatio,
        round(zddlzl_317*100,2) as quitRateArea,
        round(zddlzl_317_hb*100,1) as quitRateAreaRatio,
        round(zddlzl_317_tb*100,1)as quitRateAreaYearRatio,

        zddbzs_411 as establishmentNumArea,
        round(zddbzzgl_386*100,2) as establishmentOnGuardNumArea,
        round(zddzgzzrs_zb_643 * 100,2) as onJobMainRatioArea,
        round(zddjgzzrs_zb_644 * 100,2) as onJobPartRatioArea,

        dqzjzzrs_310 as onJobNumVrea,
        dqzjzgzzrs_629 as onJobMainNumVrea,
        dqzjjgzzrs_630 as onJobPartNumVrea,
        round(dqzjzzrs_310_hb*100,1) as onJobNumVreaRatio,
        round(dqzjzzrs_310_tb*100,1) as onJobNumVreaYearRatio,
        dqzjrzrs_311 as entryNumVrea,
        round(dqzjrzrs_311_hb*100,1) as entryNumVreaRatio,
        round(dqzjrzrs_311_tb*100,1) as entryNumVreaYearRatio,
        dqzjlzrs_312 as dimissionNumVrea,
        round(dqzjlzrs_312_hb*100,1) as dimissionNumVreaRatio,
        round(dqzjlzrs_312_tb*100,1) as dimissionNumVreaYearRatio,
        round(dqzjlzl_313*100,2) as quitRateVrea,
        round(dqzjlzl_313_hb*100,1) as quitRateVreaRatio,
        round(dqzjlzl_313_tb*100,1) as quitRateVreaYearRatio,
        dqzjbzs_412 as establishmentNumVrea,
        round(dqzjbzzgl_385*100,2) as establishmentOnGuardNumVrea,
        round(dqzjzgzzrs_zb_641 * 100,2) as onJobMainRatioVrea,
        round(dqzjjgzzrs_zb_642 * 100,2) as onJobPartRatioVrea,


        sqzjzzrs_306 as onJobNumProvince,
        sqzjzgzzrs_627 as onJobMainNumProvince,
        sqzjjgzzrs_628 as onJobPartNumProvince,
        round(sqzjzzrs_306_hb*100,1) as onJobNumProvinceRatio,
        round(sqzjzzrs_306_tb*100,1) as onJobNumProvinceYearRatio,
        sqzjrzrs_307 as entryNumProvince,
        round(sqzjrzrs_307_hb*100,1) as entryNumProvinceRatio,
        round(sqzjrzrs_307_tb*100,1) as entryNumProvinceYearRatio,
        sqzjlzrs_308 as dimissionNumProvince,
        round(sqzjlzrs_308_hb*100,1) as dimissionNumProvinceRatio,
        round(sqzjlzrs_308_tb*100,1) as dimissionNumProvinceYearRatio,
        round(sqzjlzl_309*100,2) as quitRateProvince,
        round(sqzjlzl_309_hb*100,1) as quitRateProvinceRatio,
        round(sqzjlzl_309_tb*100,1) as quitRateProvinceYearRatio,
        sqzjbzs_413 as establishmentNumProvince,
        round(sqzjbzzgl_384*100,2) as establishmentOnGuardNumProvince,
        round(sqzjzgzzrs_zb_639 * 100,2) as onJobMainRatioProvince,
        round(sqzjjgzzrs_zb_640 * 100,2) as onJobPartRatioProvince,


        qyzjzzrs_301 as onJobNumCompany,
        qyzjzgzzrs_625 as onJobMainNumCompany,
        qyzjjgzzrs_626 as onJobPartNumCompany,
        round(qyzjzzrs_301_hb*100,1) as onJobNumCompanyRatio,
        round(qyzjzzrs_301_tb*100,1) as onJobNumCompanyYearRatio,
        qyzjrzrs_302 as entryNumCompany,
        round(qyzjrzrs_302_hb*100,1) as entryNumCompanyRatio,
        round(qyzjrzrs_302_tb*100,1) as entryNumCompanyYearRatio,
        qyzjlzrs_303 as dimissionNumCompany,
        round(qyzjlzrs_303_hb*100,1) as dimissionNumCompanyRatio,
        round(qyzjlzrs_303_tb*100,1) as dimissionNumCompanyYearRatio,
        round(qyzjlzl_304*100,2) as quitRateCompany,
        round(qyzjlzl_304_hb*100,1) as quitRateCompanyRatio,
        round(qyzjlzl_304_tb*100,1) as quitRateCompanyYearRatio,
        qyzjbzs_414 as establishmentNumCompany,
        round(qyzjbzzgl_305*100,2) as establishmentOnGuardNumCompany,
        round(qyzjzgzzrs_zb_637 * 100,2) as onJobMainRatioCompany,
        round(qyzjjgzzrs_zb_638 * 100,2) as onJobPartRatioCompany,

        csjlzzrs_297 as onJobNumDepartment,
        csjlzgzzrs_623 as onJobMainNumDepartment,
        csjljgzzrs_624 as onJobPartNumDepartment,
        round(csjlzzrs_297_hb*100,1) as onJobNumDepartmentRatio,
        round(csjlzzrs_297_tb*100,1) as onJobNumDepartmentYearRatio,
        csjlrzrs_298 as entryNumDepartment,
        round(csjlrzrs_298_hb*100,1) as entryNumDepartmentRatio,
        round(csjlrzrs_298_tb*100,1) as entryNumDepartmentYearRatio,
        csjllzrs_299 as dimissionNumDepartment,
        round(csjllzrs_299_hb*100,1) as dimissionNumDepartmentRatio,
        round(csjllzrs_299_tb*100,1) as dimissionNumDepartmentYearRatio,
        round(csjllzl_126*100,2) as quitRateDepartment,
        round(csjllzl_126_hb*100,1) as quitRateDepartmentRatio,
        round(csjllzl_126_tb*100,1) as quitRateDepartmentYearRatio,
        csjlbzs_415 as establishmentNumDepartment,
        round(csjlbzzgl_300*100,2) as establishmentOnGuardNumDepartment,
        round(csjlzgzzrs_zb_635 * 100,2) as onJobMainRatioDepartment,
        round(csjljgzzrs_zb_636 * 100,2) as onJobPartRatioDepartment,

        abm.xjscs_464 as countryMarketCount,
        round(abm.xjscfgl_465*100,1) as countryMarketCoverage,

        abo.organization_id as organizationId
        <if test="dateTypeId==10">
            ,
            coalesce(srt.zwPerformance,0) as dayPerformance
        </if>
        from ads_bigtable_organization_month_report abo
        left join ads_member_personal_month sbr on sbr.organization_code = abo.organization_id and sbr.the_year_month = #{relationMonth}
        left join ads_bigtable_market abm
            on abm.date_type_id = abo.date_type_id
            and abm.the_year_month = abo.the_year_month
            and abm.organization_id = abo.organization_id
            and abm.county_name = '合计'
        <if test="dateTypeId==10">
            left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id organization_id,
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup!=99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup==99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            filter_type = '线别'
            and  the_date = #{theDate}
            group by
            1,2,3
            ) srt on abo.organization_id = srt.organization_id
        </if>
        WHERE abo.the_year_month=#{yearMonth}
        and abo.organization_id=#{organizationId}
        and abo.date_type_id = #{dateTypeId}
        and abo.etl_date like concat(#{etlDate},'%')
    </select>


    <select id="queryPerformanceDate" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceDateVo">
        SELECT
        round(wxpwjbyj_657,1) as promotionalMaterialsGoldenCoinPerformance,
        round(wxpwjbyjzb_658*100,1) as promotionalMaterialsGoldenCoinPerformanceRate,
        round(yskje_659,1) as needReceivableAmount,

        round(wjbyjzb_dq_506*100,1) as goldenCoinPerformanceRate,
        round(wjbyjzb_dq_506*100,1) as performanceWantGoldDiscountRatio,
        xjyj_dq_505 as cashPerformance,
        round(xjyjzb_dq_525*100,2) as cashPerformanceRate,

        jhxkhs_472 as outTimeactivatedCustomerWithInThirtyNum,
        round(jhxkhs_472_hb*100,2) as outTimeactivatedCustomerWithInThirtyRatio,
        round(jhxkhs_472_tb*100,2) as outTimeactivatedCustomerWithInThirtyYearRatio,

        jhxkhs_thirty_more_494 as outTimeactivatedCustomerAboveThirtyNum,
        round(jhxkhs_thirty_more_494_hb*100,2) as outTimeactivatedCustomerAboveThirtyRatio,
        round(jhxkhs_thirty_more_494_tb*100,2) as outTimeactivatedCustomerAboveThirtyYearRatio,

        kfxkhs_473 as inTimeactivatedCustomerNum,
        round(kfxkhs_473_hb*100,2) as inTimeactivatedCustomerRatio,
        round(kfxkhs_473_tb*100,2) as inTimeactivatedCustomerYearRatio,

        fgkhs_44 as repeatPurchasesCustomerNum,
        round(fgkhs_44_hb*100,2) as repeatPurchasesCustomerRatio,
        round(fgkhs_44_tb*100,2) as repeatPurchasesCustomerYearRatio,

        zhkhs_471 as recallCustomerNum,
        round(zhkhs_471_hb*100,2) as recallCustomerRatio,
        round(zhkhs_471_tb*100,2) as recallCustomerYearRatio,

        khs_61 as customerCount,
        hhrrzrs_295 as developCustomerCount,
        hhrlzrs_296 as closeCustomerCount,
        round(hhrlzl_65*100,2) as closeRate,
        yxkhs_447 as intentionCustomerCount,
        qzkhs_470 as latentCustomerCount,
        bfkhs_543 as visitCustomerCount,
        round(bfl_460*100,1) as visitCustomerRate,
        round(pdl_54*100,1) as takeStockRate,
        round(hhrjyl_50*100,1) as transactionRate,
        round(glgrjbfkhs_544,1) as managementPositionAverVisitCustomerCount,

        round(glgrjkhs_487,1) as managementPositionAverCustomerCount,
        round(glgrjkfkhs_488,1) as managementPositionAverDevelopCustomerCount,
        round(glgrjgbkhs_489,1) as managementPositionAverCloseCustomerCount,
        round(glgrjyxkhs_490,1) as managementPositionAverIntentionCustomerCount,
        round(glgrjqzkhs_491,1) as managementPositionAverLatentCustomerCount,
        round(yxwxdkhs_495,1) as intentionNotOrderCustomerCount,
        round(glgrjyxwxdkhs_496,1) as managementPositionAverIntentionNotOrderCustomerCount,

        round(yxxgdkhs_578,1) as intentionOrderCustomerCount,
        round(glgrjyxxgdkhs_579,1) as managementPositionAverIntentionOrderCustomerCount,

        sbr.pic_url as url,
        sbr.employee_info_id as employeeInfoId,
        sbr.employee_name as employeeName,
        abo.organization_name as organizationName,
        sbr.onboard_time as onboardTime,
        sbr.zw_onboard_days as onboardDays,
        sbr.position_id as positionId,
        case abo.position_type_id when 4 then 7 else abo.position_type_id end as positionTypeId,
        sbr.employee_status as employeeStatus,
        sbr.pos_role_name as positionName,

        zwbppjyj_cur_405 as performance,
        zwbppjyj_past_30 as thenPerformance,
        pjyjmb_378 as goal,
        round(pjyjmbdcl_32*100,2) as performanceAchievementRate,
        round(zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,
        glgzzrs_329 as managementOnJobNum,
        round(glgzzrs_329_hb*100,2) as managementOnJobNumChainRatio,
        round(glgzzrs_329_tb*100,2) as managementOnJobNumYearRatio,
        glgrjyj_cur_483 as managementPerPerformance,
        round(glgrjyj_cur_483_hb*100,2) as managementPerPerformanceChainRatio,
        round(glgrjyj_cur_483_tb*100,2) as managementPerPerformanceYearRatio,



        ctdyj_380 as normalOrderPerformance,
        round(ctdyjzb_381*100,2) as normalOrderPerformanceRate,
        round(ctdyj_380_hb*100,2) as normalOrderPerformanceRatio,
        round(ctdyj_380_tb*100,2) as normalOrderPerformanceYearRatio,
        round(ctdxjyj_649,1) as  normalOrderCashPerformance,
        round(ctdzkl_652*100,1) as normalOrderWantGoldDiscountRatio,


        ttdyj_382 as specialOrderPerformance,
        round(ttdyjzb_383*100,2) as specialOrderPerformanceRate,
        round(ttdyj_382_hb*100,2) as specialOrderPerformanceRatio,
        round(ttdyj_382_tb*100,2) as specialOrderPerformanceYearRatio,
        round(ttdxjyj_651,1) as specialOrderCashPerformance,
        round(ttdzkl_654*100,1) as specialOrderWantGoldDiscountRatio,

        wjbyj_dq_409 as goldenCoinPerformance,
        jdpxpjyj_141 as classicItemPerformance,
        zhzpxyj_390 as synthesisGroupPerformance,
        yddwfhyj_past_318 as advanceOrderUnshippedPerformance,
        yczye_319 as preStoredValuePerformance,
        cwsrhs_423 as financialReceipts,
        ywsrhs_446 as businessIncome,
        fywsrhs_444 as nonBusinessIncome,
        scsrhs_445 as tasteMallIncome,

        glgrjxkhyj_484 as managementPositionNewCustomerPerformance,
        round(glgrjxkhyj_484_hb*100,2) as managementPositionPerformanceNewCustomerRatio,
        round(glgrjxkhyj_484_tb*100,2) as managementPositionPerformanceNewCustomerYearRatio,

        glgrjlkhyj_485 as managementPositionOldCustomerPerformance,
        round(glgrjlkhyj_485_hb*100,2) as managementPositionPerformanceOldCustomerRatio,
        round(glgrjlkhyj_485_tb*100,2) as managementPositionPerformanceOldCustomerYearRatio,

        glgrjyj_cur_483 as managementPositionCustomerPerformance,
        round(glgrjyj_cur_483_hb*100,2) as managementPositionPerformanceCustomerRatio,
        round(glgrjyj_cur_483_tb*100,2) as managementPositionPerformanceCustomerYearRatio,


        yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
        yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

        zwbppjyj_cur_405 as performancePer,
        round(zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,
        jykhs_36 as tradingCustomerNum,
        round(jykhs_36_hb*100,2) as tradingCustomerNumRateRatio,
        round(jykhs_36_tb*100,2) as tradingCustomerNumRateYearRatio,
        round(hhrjyl_50*100,2) as partnerTransactionRate,
        kdj_37 as perCustomer,
        round(kdj_37_hb*100,2) as perCustomerRatio,
        round(kdj_37_tb*100,2) as perCustomerYearRatio,

        round(zwbppjyj_cur_405_hb*100,2) as performancePerRatio,
        round(zwbppjyj_cur_405_tb*100,2) as performancePerYearRatio,
        xkhyj_397 as newCustomerPerformanceDealer,
        round(xkhyj_397_hb*100,2) as newCustomerPerformanceDealerRatio,
        round(xkhyj_397_tb*100,2) as newCustomerPerformanceDealerYearRatio,
        round(xkhwjbzkl_541*100,1) as newCustomerPerformanceDealerWantGoldDiscountRatio,
        xkhs_41 as newTradingCustomerNumDealer,
        round(xkhs_41_hb*100,2) as newTradingCustomerNumDealerRatio,
        round(xkhs_41_tb*100,2) as newTradingCustomerNumDealerYearRatio,
        xkhkdj_344 as newPerCustomerDealer,
        round(xkhkdj_344_hb*100,2) as newPerCustomerDealerRatio,
        round(xkhkdj_344_tb*100,2) as newPerCustomerDealerYearRatio,
        lkhyj_400 as oldCustomerPerformanceDealer,
        round(lkhyj_400_hb*100,2) as oldCustomerPerformanceDealerRatio,
        round(lkhyj_400_tb*100,2) as oldCustomerPerformanceDealerYearRatio,
        round(lkhwjbzkl_542*100,1) as oldCustomerPerformanceDealerWantGoldDiscountRatio,
        lkhs_42 as oldTradingCustomerNumDealer,
        round(lkhs_42_hb*100,2) as oldTradingCustomerNumDealerRatio,
        round(lkhs_42_tb*100,2) as oldTradingCustomerNumDealerYearRatio,
        lkhkdj_343 as oldPerCustomerDealer,
        round(lkhkdj_343_hb*100,2) as oldPerCustomerDealerRatio,
        round(lkhkdj_343_tb*100,2) as oldPerCustomerDealerYearRatio,


        ynxdkhs_9001 as withinAMonthTradingCustomerNum,
        round(ynxdkhszb_9005*100,2) as withinAMonthTradingCustomerRatio,
        ynxdkhs_9002 as withinTwoMonthTradingCustomerNum,
        round(ynxdkhszb_9006*100,2) as withinTwoMonthTradingCustomerRatio,
        ynxdkhs_9003 as withinThreeMonthTradingCustomerNum,
        round(ynxdkhszb_9007*100,2) as withinThreeMonthTradingCustomerRatio,
        ynxdkhs_9004 as beforeThreeMonthTradingCustomerNum,
        round(ynxdkhszb_9008*100,2) as beforeThreeMonthTradingCustomerRatio,


        round(glgrjjykhs_486,1) as managementPositionCustomerNum,
        round(glgrjjykhs_486_hb*100,2) as managementPositionCustomerNumRatio,
        round(glgrjjykhs_486_tb*100,2) as managementPositionCustomerNumYearRatio,

        round(glgrjxkhs_451,1)   as managementPositionNewCustomerNum,
        round(glgrjxkhs_451_hb*100,2) as managementPositionNewCustomerNumRatio,
        round(glgrjxkhs_451_tb*100,2) as managementPositionNewCustomerNumYearRatio,

        round(glgrjlkhs_456,1) as managementPositionOldCustomerNum,
        round(glgrjlkhs_456_hb*100,2) as managementPositionOldCustomerNumRatio,
        round(glgrjlkhs_456_tb*100,2) as managementPositionOldCustomerNumYearRatio,


        lthhrs_515 as circulatingPartnerCount,
        kflthhrs_516 as openCirculatingPartnerCount,
        gblthhrs_517 as closeCirculatingPartnerCount,
        round(lthhrgbl_518 *100,1) as closeCirculatingPartnerRate,
        yxkhslt_545 as circulatingPartnerIntentionCustomerCount,
        yxwxdkhslt_546 as circulatingPartnerIntentionNotOrderCustomerCount,
        qzkhslt_547 as circulatingPartnerLatentCustomerCount,
        round(glgrjyxkhslt_548,1) as circulatingPartnerManagementPositionAverIntentionCustomerCount,
        round(glgrjyxwxdkhslt_549,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCount,
        round(glgrjqzkhslt_550,1) as circulatingPartnerManagementPositionAverLatentCustomerCount,
        round(glgrjkfkhslt_551,1) as circulatingPartnerManagementPositionAverDevelopCustomerCount,
        round(glgrjgbkhslt_552,1) as circulatingPartnerManagementPositionAverCloseCustomerCount,
        round(hhrjyllt_553*100,1) as circulatingPartnerTransactionRate,
        round(pdllt_554*100,1) as circulatingPartnerTakeStockRate,
        round(bfllt_555*100,1) as circulatingPartnerVisitCustomerRate,
        bfkhslt_556 as circulatingPartnerVisitCustomerCount,
        round(glgrjbfkhslt_557,1) as circulatingPartnerManagementPositionAverVisitCustomerCount,

        zyhhs_519 as directPartnerCount,
        kfzyhhrs_520 as openDirectPartnerCount,
        gbzyhhrs_521 as closeDirectPartnerCount,
        round(zyhhrgbl_522 * 100,1) as closeDirectPartnerRate,
        yxkhszy_558 as directPartnerIntentionCustomerCount,
        yxwxdkhszy_559 as directPartnerIntentionNotOrderCustomerCount,
        qzkhszy_560 as directPartnerLatentCustomerCount,
        round(glgrjyxkhszy_561,1) as directPartnerManagementPositionAverIntentionCustomerCount,
        round(glgrjyxwxdkhszy_562,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCount,
        round(glgrjqzkhszy_563,1) as directPartnerManagementPositionAverLatentCustomerCount,
        round(glgrjkfkhszy_564,1) as directPartnerManagementPositionAverDevelopCustomerCount,
        round(glgrjgbkhszy_565,1) as directPartnerManagementPositionAverCloseCustomerCount,
        round(hhrjylzy_566*100,1) as directPartnerTransactionRate,
        round(pdlzy_567*100,1) as directPartnerTakeStockRate,
        round(bflzy_568*100,1) as directPartnerVisitCustomerRate,
        bfkhszy_569 as directPartnerVisitCustomerCount,
        round(glgrjbfkhszy_570,1) as directPartnerManagementPositionAverVisitCustomerCount,


        dgzzrs_523 as onJobShoppingGuideCount,
        dggbrs_524 as closeShoppingGuideCount,
        dgrzrs_526 as entryShoppingGuideCount,



        ywzzrs_589 as salesmanOnJobNum,
        round(ywzzrs_589_hb*100,1) as salesmanOnJobNumChainRatio,
        round(ywzzrs_589_tb*100,1) as salesmanOnJobNumYearRatio,
        ywrjyj_cur_590 as salesmanPerPerformance,
        round(ywrjyj_cur_590_hb*100,1) as salesmanPerPerformanceChainRatio,
        round(ywrjyj_cur_590_tb*100,1) as salesmanPerPerformanceYearRatio,
        round(ztfyzkl_577*100,1) as overallDiscountRate,
        jykhsmb_537 as tradingCustomerGoal,
        round(jykhsmbdcl_538*100,1) as tradingCustomerGoalAchievementRate,
        round(xkhszb_593*100,1) as newTradingCustomerNumDealerProportion,
        round(lkhszb_594*100,1) as oldTradingCustomerNumDealerProportion,
        round(jsjhxkhszb_595*100,1) as inTimeActivatedCustomerProportion,
        round(wjsjhxkhszb_596*100,1) as outTimeActivatedCustomerWithInThirtyProportion,
        round(fgkhszb_597*100,1) as repeatPurchasesCustomerProportion,
        round(glgrjfgkhs_592,1) as managementPositionAverRepeatPurchasesCustomerNum,
        round(zhkhszb_598*100,1) as recallCustomerProportion,
        round(glgrjzhkhs_591,1) as managementPositionAverRecallCustomerNum,

        round(yxkhs_447_hb*100,1) as intentionCustomerCountChainRatio,
        round(yxkhs_447_tb*100,1) as intentionCustomerCountYearRatio,
        round(yxwxdkhs_495_hb*100,1) as intentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhs_495_tb*100,1) as intentionNotOrderCustomerCountYearRatio,
        round(qzkhs_470_hb*100,1) as latentCustomerCountChainRatio,
        round(qzkhs_470_tb*100,1) as latentCustomerCountYearRatio,
        round(glgrjyxkhs_490_hb*100,1) as managementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhs_490_tb*100,1) as managementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjqzkhs_491_hb*100,1) as managementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhs_491_tb*100,1) as managementPositionAverLatentCustomerCountYearRatio,
        round(glgrjyxwxdkhs_496_hb*100,1) as managementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhs_496_tb*100,1) as managementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(yxkhslt_545_hb*100,1) as circulatingPartnerIntentionCustomerCountChainRatio,
        round(yxkhslt_545_tb*100,1) as circulatingPartnerIntentionCustomerCountYearRatio,
        round(yxwxdkhslt_546_hb*100,1) as circulatingPartnerIntentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhslt_546_tb*100,1) as circulatingPartnerIntentionNotOrderCustomerCountYearRatio,
        round(qzkhslt_547_hb*100,1) as circulatingPartnerLatentCustomerCountChainRatio,
        round(qzkhslt_547_tb*100,1) as circulatingPartnerLatentCustomerCountYearRatio,
        round(glgrjyxkhslt_548_hb*100,1) as circulatingPartnerManagementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhslt_548_tb*100,1) as circulatingPartnerManagementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjyxwxdkhslt_549_hb*100,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhslt_549_tb*100,1) as circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(glgrjqzkhslt_550_hb*100,1) as circulatingPartnerManagementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhslt_550_tb*100,1) as circulatingPartnerManagementPositionAverLatentCustomerCountYearRatio,
        round(yxkhszy_558_hb*100,1) as directPartnerIntentionCustomerCountChainRatio,
        round(yxkhszy_558_tb*100,1) as directPartnerIntentionCustomerCountYearRatio,
        round(yxwxdkhszy_559_hb*100,1) as directPartnerIntentionNotOrderCustomerCountChainRatio,
        round(yxwxdkhszy_559_tb*100,1) as directPartnerIntentionNotOrderCustomerCountYearRatio,
        round(qzkhszy_560_hb*100,1) as directPartnerLatentCustomerCountChainRatio,
        round(qzkhszy_560_tb*100,1) as directPartnerLatentCustomerCountYearRatio,
        round(glgrjyxkhszy_561_hb*100,1) as directPartnerManagementPositionAverIntentionCustomerCountChainRatio,
        round(glgrjyxkhszy_561_tb*100,1) as directPartnerManagementPositionAverIntentionCustomerCountYearRatio,
        round(glgrjyxwxdkhszy_562_hb*100,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio,
        round(glgrjyxwxdkhszy_562_tb*100,1) as directPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio,
        round(glgrjqzkhszy_563_hb*100,1) as directPartnerManagementPositionAverLatentCustomerCountChainRatio,
        round(glgrjqzkhszy_563_tb*100,1) as directPartnerManagementPositionAverLatentCustomerCountYearRatio,

        round(ywbdzzrs_497,1)  as onJobNumBusinessDevelopment,
        round(ywbdzzrs_497_hb*100,1) as onJobNumRatioBusinessDevelopment,
        round(ywbdzzrs_497_tb*100,1)  as onJobNumYearRatioBusinessDevelopment,
        round(ywbdrzrs_498,1) as entryNumBusinessDevelopment,
        round(ywbdrzrs_498_hb*100,1) as entryNumRatioBusinessDevelopment,
        round(ywbdrzrs_498_tb*100,1) as entryNumYearRatioBusinessDevelopment,
        round(ywbdlzrs_499,1) as dimissionNumBusinessDevelopment,
        round(ywbdlzrs_499_hb*100,1) as dimissionNumRatioBusinessDevelopment,
        round(ywbdlzrs_499_tb*100,1) as dimissionNumYearRatioBusinessDevelopment,
        round(ywbdlzl_500*100,1) as quitRateBusinessDevelopment,
        round(ywbdlzl_500_hb*100,1) as quitRateRatioBusinessDevelopment,
        round(ywbdlzl_500_tb*100,1) as quitRateYearRatioBusinessDevelopment,
        round(ywbdbzs_502,1) as establishmentNumBusinessDevelopment,
        round(ywbdbzzgl_501*100,2) as establishmentOnGuardNumBusinessDevelopment,
        round(qzywbdzzrs_583,1) as fullTimeBusinessDevelopmentNum,
        round(qzywbdzzrszb_586*100,1) as fullTimeRateBusinessDevelopment,
        round(clywbdzzrs_584,1) as undertakeBusinessDevelopmentNum,
        round(clywbdzzrszb_587*100,1) as undertakeRateBusinessDevelopment,
        round(jzywbdzzrs_585,1) as partTimeBusinessDevelopmentNum,
        round(jzywbdzzrszb_588*100,1) as partTimeRateBusinessDevelopment,


        glgzzrs_329 as onJobNumManagement,
        glgzgzzrs_633 as onJobMainNumManagement,
        glgjgzzrs_634 as onJobPartNumManagement,
        round(glgzzrs_329_hb*100,1) as onJobNumManagementRatio,
        round(glgzzrs_329_tb*100,1) as onJobNumManagementYearRatio,
        glgrzrs_509 as entryNumManagement,
        round(glgrzrs_509_hb*100,1) as entryNumManagementRatio,
        round(glgrzrs_509_tb*100,1) as entryNumManagementYearRatio,
        glglzrs_507 as dimissionNumManagement,
        round(glglzrs_507_hb*100,1) as dimissionNumManagementRatio,
        round(glglzrs_507_tb*100,1) as dimissionNumManagementYearRatio,
        round(glglzl_508*100,2) as quitRateManagement,
        round(glglzl_508_hb*100,1) as quitRateManagementRatio,
        round(glglzl_508_tb*100,1)as quitRateManagementYearRatio,

        glgbzs_387 as establishmentNumManagement,
        round(glgbzzgl_330*100,2) as establishmentOnGuardNumManagement,
        round(glgzgzzrs_zb_645 * 100,2) as onJobMainRatioManagement,
        round(glgjgzzrs_zb_646 * 100,2) as onJobPartRatioManagement,

        abo.yddyj_cur_407 as currentBookingPerformance,
        round(abo.yddyjzb_cur_533*100,1) as currentBookingPerformanceRate,
        round(abo.yddyj_cur_407_hb*100,1) as currentBookingPerformanceRatio,
        round(abo.yddyj_cur_407_tb*100,1) as currentBookingPerformanceYearRatio,
        round(abo.yddxjyj_650,1) as currentBookingCashPerformance,
        round(abo.yddzkl_653*100,1) as currentBookingWantGoldDiscountRatio,

        abo.yddyjfhyj_cur_ydby_527 as curMonthAdvanceOrderCurMonthOrderPerformance,
        abo.yddyj_past_379 as curMonthAdvanceOrderCurrentMonthDeliveredPerformance,
        abo.yddwfhyj_byfh_past_421 as curMonthAdvanceOrderCurMonthNotDeliveredPerformance,

        abo.yddyjfhyj_cur_ydcy_528 as lastMonthAdvanceOrderCurMonthOrderPerformance,
        abo.yddyjydcy_past_504 as lastMonthAdvanceOrderCurrentMonthDeliveredPerformance,
        abo.yddwfhyj_cur_cyfh_529 as lastMonthAdvanceOrderCurMonthNotDeliveredPerformance,

        abo.ycyddyjfhyj_cur_530 as delayAdvanceOrderOrderPerformance,
        abo.ycyddyj_cur_531 as delayAdvanceOrderDeliveredPerformance,
        abo.ycyddwfhyj_cur_532 as delayAdvanceOrderNotDeliveredPerformance,

        abo.yddwfhyj_cyfh_past_422 as nextMonthAdvanceOrderCurMonthOrderPerformance,

        round(abo.ywyrfyl_514*100,1) as businessStaffingExpenseRate,


        round(zazckhyj_573,1) as projectPolicyCustomerPerformance,
        round(zazckhyj_573_hb*100,2) as projectPolicyCustomerPerformanceChainRatio,
        round(zazckhyj_573_tb*100,2) as projectPolicyCustomerPerformanceYearRatio,
        round(zazckhyjzb_660*100,2) as projectPolicyCustomerPerformanceRate,
        round(zazcjykhs_574,1) as projectPolicyTradingCustomerCount,
        round(zazckhkdj_575,1) as projectPolicyPerCustomer,
        round(zazckhwjbzkl_576*100,1) projectPolicyWantGoldDiscountRatio,

        zddzzrs_314 as onJobNumArea,
        zddzgzzrs_631 as onJobMainNumArea,
        zddjgzzrs_632 as onJobPartNumArea,
        round(zddzzrs_314_hb*100,1) as onJobNumAreaRatio,
        round(zddzzrs_314_tb*100,1) as onJobNumAreaYearRatio,
        zddrzrs_315 as entryNumArea,
        round(zddrzrs_315_hb*100,1) as entryNumAreaRatio,
        round(zddrzrs_315_tb*100,1) as entryNumAreaYearRatio,
        zddlzrs_316 as dimissionNumArea,
        round(zddlzrs_316_hb*100,1) as dimissionNumAreaRatio,
        round(zddlzrs_316_tb*100,1) as dimissionNumAreaYearRatio,
        round(zddlzl_317*100,2) as quitRateArea,
        round(zddlzl_317_hb*100,1) as quitRateAreaRatio,
        round(zddlzl_317_tb*100,1)as quitRateAreaYearRatio,

        zddbzs_411 as establishmentNumArea,
        round(zddbzzgl_386*100,2) as establishmentOnGuardNumArea,
        round(zddzgzzrs_zb_643 * 100,2) as onJobMainRatioArea,
        round(zddjgzzrs_zb_644 * 100,2) as onJobPartRatioArea,

        dqzjzzrs_310 as onJobNumVrea,
        dqzjzgzzrs_629 as onJobMainNumVrea,
        dqzjjgzzrs_630 as onJobPartNumVrea,
        round(dqzjzzrs_310_hb*100,1) as onJobNumVreaRatio,
        round(dqzjzzrs_310_tb*100,1) as onJobNumVreaYearRatio,
        dqzjrzrs_311 as entryNumVrea,
        round(dqzjrzrs_311_hb*100,1) as entryNumVreaRatio,
        round(dqzjrzrs_311_tb*100,1) as entryNumVreaYearRatio,
        dqzjlzrs_312 as dimissionNumVrea,
        round(dqzjlzrs_312_hb*100,1) as dimissionNumVreaRatio,
        round(dqzjlzrs_312_tb*100,1) as dimissionNumVreaYearRatio,
        round(dqzjlzl_313*100,2) as quitRateVrea,
        round(dqzjlzl_313_hb*100,1) as quitRateVreaRatio,
        round(dqzjlzl_313_tb*100,1) as quitRateVreaYearRatio,
        dqzjbzs_412 as establishmentNumVrea,
        round(dqzjbzzgl_385*100,2) as establishmentOnGuardNumVrea,
        round(dqzjzgzzrs_zb_641 * 100,2) as onJobMainRatioVrea,
        round(dqzjjgzzrs_zb_642 * 100,2) as onJobPartRatioVrea,

        sqzjzzrs_306 as onJobNumProvince,
        sqzjzgzzrs_627 as onJobMainNumProvince,
        sqzjjgzzrs_628 as onJobPartNumProvince,
        round(sqzjzzrs_306_hb*100,1) as onJobNumProvinceRatio,
        round(sqzjzzrs_306_tb*100,1) as onJobNumProvinceYearRatio,
        sqzjrzrs_307 as entryNumProvince,
        round(sqzjrzrs_307_hb*100,1) as entryNumProvinceRatio,
        round(sqzjrzrs_307_tb*100,1) as entryNumProvinceYearRatio,
        sqzjlzrs_308 as dimissionNumProvince,
        round(sqzjlzrs_308_hb*100,1) as dimissionNumProvinceRatio,
        round(sqzjlzrs_308_tb*100,1) as dimissionNumProvinceYearRatio,
        round(sqzjlzl_309*100,2) as quitRateProvince,
        round(sqzjlzl_309_hb*100,1) as quitRateProvinceRatio,
        round(sqzjlzl_309_tb*100,1) as quitRateProvinceYearRatio,
        sqzjbzs_413 as establishmentNumProvince,
        round(sqzjbzzgl_384*100,2) as establishmentOnGuardNumProvince,
        round(sqzjzgzzrs_zb_639 * 100,2) as onJobMainRatioProvince,
        round(sqzjjgzzrs_zb_640 * 100,2) as onJobPartRatioProvince,

        qyzjzzrs_301 as onJobNumCompany,
        qyzjzgzzrs_625 as onJobMainNumCompany,
        qyzjjgzzrs_626 as onJobPartNumCompany,
        round(qyzjzzrs_301_hb*100,1) as onJobNumCompanyRatio,
        round(qyzjzzrs_301_tb*100,1) as onJobNumCompanyYearRatio,
        qyzjrzrs_302 as entryNumCompany,
        round(qyzjrzrs_302_hb*100,1) as entryNumCompanyRatio,
        round(qyzjrzrs_302_tb*100,1) as entryNumCompanyYearRatio,
        qyzjlzrs_303 as dimissionNumCompany,
        round(qyzjlzrs_303_hb*100,1) as dimissionNumCompanyRatio,
        round(qyzjlzrs_303_tb*100,1) as dimissionNumCompanyYearRatio,
        round(qyzjlzl_304*100,2) as quitRateCompany,
        round(qyzjlzl_304_hb*100,1) as quitRateCompanyRatio,
        round(qyzjlzl_304_tb*100,1) as quitRateCompanyYearRatio,
        qyzjbzs_414 as establishmentNumCompany,
        round(qyzjbzzgl_305*100,2) as establishmentOnGuardNumCompany,
        round(qyzjzgzzrs_zb_637 * 100,2) as onJobMainRatioCompany,
        round(qyzjjgzzrs_zb_638 * 100,2) as onJobPartRatioCompany,

        csjlzzrs_297 as onJobNumDepartment,
        csjlzgzzrs_623 as onJobMainNumDepartment,
        csjljgzzrs_624 as onJobPartNumDepartment,
        round(csjlzzrs_297_hb*100,1) as onJobNumDepartmentRatio,
        round(csjlzzrs_297_tb*100,1) as onJobNumDepartmentYearRatio,
        csjlrzrs_298 as entryNumDepartment,
        round(csjlrzrs_298_hb*100,1) as entryNumDepartmentRatio,
        round(csjlrzrs_298_tb*100,1) as entryNumDepartmentYearRatio,
        csjllzrs_299 as dimissionNumDepartment,
        round(csjllzrs_299_hb*100,1) as dimissionNumDepartmentRatio,
        round(csjllzrs_299_tb*100,1) as dimissionNumDepartmentYearRatio,
        round(csjllzl_126*100,2) as quitRateDepartment,
        round(csjllzl_126_hb*100,1) as quitRateDepartmentRatio,
        round(csjllzl_126_tb*100,1) as quitRateDepartmentYearRatio,
        csjlbzs_415 as establishmentNumDepartment,
        round(csjlbzzgl_300*100,2) as establishmentOnGuardNumDepartment,
        round(csjlzgzzrs_zb_635 * 100,2) as onJobMainRatioDepartment,
        round(csjljgzzrs_zb_636 * 100,2) as onJobPartRatioDepartment,
        DATE_FORMAT(abo.etl_date, '%Y-%m-%d %H:%i:%s') as etlDate,

        abm.xjscs_464 as countryMarketCount,
        round(abm.xjscfgl_465*100,1) as countryMarketCoverage,

        abo.organization_id as organizationId
        <if test="dateTypeId==10">
            ,
            coalesce(srt.zwPerformance,0) as dayPerformance
        </if>
        from ads_bigtable_organization abo
        left join ads_member_personal_month sbr on sbr.organization_code = abo.organization_id and sbr.the_year_month = #{relationMonth}
        left join ads_bigtable_market abm
            on abm.date_type_id = abo.date_type_id
            and abm.the_year_month = abo.the_year_month
            and abm.organization_id = abo.organization_id
            and abm.county_name = '合计'
        <if test="dateTypeId==10">
            left join    (  SELECT
            the_date,
            the_year_month,
            biz_organization_id organization_id,
            '当月业绩' as name,
            Round(sum(zw_performance),1) as zwPerformance,
            max(the_date) as theYearDate
            FROM
            <if test="businessGroup!=99">
                ads.sfa_background_real_time_performance_trends
            </if>
            <if test="businessGroup==99">
                ads.sfa_background_real_time_performance_trends_allbusinessgroup
            </if>
            WHERE
            filter_type = '线别'
            and  the_date = #{TheDate}
            group by
            1,2,3
            ) srt on abo.organization_id = srt.organization_id
        </if>
        WHERE abo.the_year_month=#{yearMonth}
        and abo.organization_id=#{organizationId}
        and abo.date_type_id = #{dateTypeId}
    </select>

    <select id="queryPerformanceDatePartner" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.PerformanceDateVo">
         SELECT
         abp.member_key as memberKey,
         abp.wjbyj_dq_409 as goldenCoinPerformance,
         round(abp.wjbyjzb_dq_506*100,2) as goldenCoinPerformanceRate,
         abp.xjyj_dq_505 as cashPerformance,
         round(abp.xjyjzb_dq_525*100,2) as cashPerformanceRate,
         abp.organization_id as organizationId,
         abp.department_name as organizationName,
         di.pos_role_name as type,
         di.employee_status as employeeStatusEnum,
         di.pic_url as url,
         di.employee_name as employeeName,

         di.zw_onboard_time as onboardTime,
         cast(di.zw_onboard_days as char) as onboardDays,
         di.employee_info_id as employeeInfoId,

         di.joining_company as joiningCompany,
         abp.zwbppjyj_past_30 as thenPerformance,
         abp.zwbppjyj_cur_405 as performance,
         abp.pjyjmb_378 as goal,
         round(abp.pjyjmbdcl_32*100,2) as performanceAchievementRate,
         round(abp.zwbppjyj_cur_405_hb*100,2) as performanceChainRatio,
         round(abp.zwbppjyj_cur_405_tb*100,2) as performanceYearRatio,



         abp.yddyj_cur_407 as currentBookingPerformance,
         round(abp.yddyjzb_cur_533*100,1) as currentBookingPerformanceRate,
         round(abp.yddyj_cur_407_hb*100,1) as currentBookingPerformanceRatio,
         round(abp.yddyj_cur_407_tb*100,1) as currentBookingPerformanceYearRatio,
         round(abp.yddxjyj_650,1) as currentBookingCashPerformance,
         round(abp.yddzkl_653*100,1) as currentBookingWantGoldDiscountRatio,

         abp.yddyjfhyj_cur_ydby_527 as curMonthAdvanceOrderCurMonthOrderPerformance,
         abp.yddyj_past_379 as curMonthAdvanceOrderCurrentMonthDeliveredPerformance,
         abp.yddwfhyj_byfh_past_421 as curMonthAdvanceOrderCurMonthNotDeliveredPerformance,

         abp.yddyjfhyj_cur_ydcy_528 as lastMonthAdvanceOrderCurMonthOrderPerformance,
         abp.yddyjydcy_past_504 as lastMonthAdvanceOrderCurrentMonthDeliveredPerformance,
         abp.yddwfhyj_cur_cyfh_529 as lastMonthAdvanceOrderCurMonthNotDeliveredPerformance,

         abp.ycyddyjfhyj_cur_530 as delayAdvanceOrderOrderPerformance,
         abp.ycyddyj_cur_531 as delayAdvanceOrderDeliveredPerformance,
         abp.ycyddwfhyj_cur_532 as delayAdvanceOrderNotDeliveredPerformance,

         abp.yddwfhyj_cyfh_past_422 as nextMonthAdvanceOrderCurMonthOrderPerformance,


         abp.ctdyj_380 as normalOrderPerformance,
         round(abp.ctdyjzb_381*100,2) as normalOrderPerformanceRate,
         round(abp.ctdyj_380_hb*100,1) as normalOrderPerformanceRatio,
         round(abp.ctdyj_380_tb*100,1) as normalOrderPerformanceYearRatio,
         round(abp.ctdxjyj_649,1) as  normalOrderCashPerformance,
         round(abp.ctdzkl_652*100,1) as normalOrderWantGoldDiscountRatio,

         abp.ttdyj_382 as specialOrderPerformance,
         round(abp.ttdyjzb_383*100,2) as specialOrderPerformanceRate,
         round(abp.ttdyj_382_hb*100,1) as specialOrderPerformanceRatio,
         round(abp.ttdyj_382_tb*100,1) as specialOrderPerformanceYearRatio,
         round(abp.ttdxjyj_651,1) as specialOrderCashPerformance,
         round(abp.ttdzkl_654*100,1) as specialOrderWantGoldDiscountRatio,

         abp.wjbyj_dq_409 as goldenCoinPerformance,
         abp.jdpxpjyj_141 as classicItemPerformance,
         abp.zhzpxyj_390 as synthesisGroupPerformance,
         abp.yddwfhyj_past_318 as advanceOrderUnshippedPerformance,
         abp.yczye_319 as PrestoredValuePerformance,
         abp.yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
         abp.yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

         round(abp.ywbdzzrs_497,1)  as onJobNumBusinessDevelopment,
         round(abp.ywbdzzrs_497_hb*100,1) as onJobNumRatioBusinessDevelopment,
         round(abp.ywbdzzrs_497_tb*100,1)  as onJobNumYearRatioBusinessDevelopment,
         round(abp.ywbdrzrs_498,1) as entryNumBusinessDevelopment,
         round(abp.ywbdrzrs_498_hb*100,1) as entryNumRatioBusinessDevelopment,
         round(abp.ywbdrzrs_498_tb*100,1) as entryNumYearRatioBusinessDevelopment,
         round(abp.ywbdlzrs_499,1) as dimissionNumBusinessDevelopment,
         round(abp.ywbdlzrs_499_hb*100,1) as dimissionNumRatioBusinessDevelopment,
         round(abp.ywbdlzrs_499_tb*100,1) as dimissionNumYearRatioBusinessDevelopment,
         round(abp.ywbdlzl_500*100,1) as quitRateBusinessDevelopment,
         round(abp.ywbdlzl_500_hb*100,1) as quitRateRatioBusinessDevelopment,
         round(abp.ywbdlzl_500_tb*100,1) as quitRateYearRatioBusinessDevelopment,

         round(abp.qzywbdzzrs_583,1) as fullTimeBusinessDevelopmentNum,
         round(abp.qzywbdzzrszb_586*100,1) as fullTimeRateBusinessDevelopment,
         round(abp.clywbdzzrs_584,1) as undertakeBusinessDevelopmentNum,
         round(abp.clywbdzzrszb_587*100,1) as undertakeRateBusinessDevelopment,
         round(abp.jzywbdzzrs_585,1) as partTimeBusinessDevelopmentNum,
         round(abp.jzywbdzzrszb_588*100,1) as partTimeRateBusinessDevelopment

         from dim_emp_pos_role_org_mon_partner di
         left join ads_bigtable_partner abp
         on di.member_key  = abp.member_key
                and abp.the_year_month=#{yearMonth}
                and abp.business_group_id = #{businessGroup}
                and abp.date_type_id = #{dateTypeId}
         where
             di.job_type_id = 1
             and di.the_year_month = #{relationMonth}
             and di.member_key = #{memberKey}
          order by abp.zwbppjyj_cur_405 desc
          limit 1
    </select>


    <select id="queryGoodsProductSku" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.CommodityDataNewVo">
        <if test="isPartner==0">
            SELECT
            dssbi.line_name line,
            dssbi.sku_images productPicture,
            dssbi.sku_id sku,
            dssbi.sku_name skuName,
            dssbi.sku_spec specification,
            dssbi.flavor  flavor,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            abos.wjbyj_cur_409 as goldenCoinPerformance,
            abos.jykhs_36 as tradingClients,
            round(abos.jykhs_36_hb*100,2) as tradingClientsRatio,
            round(abos.jykhs_36_tb*100,2) as tradingClientsYearRatio,
            abos.kdj_37 as perCustomer,
            round(abos.kdj_37_hb*100,2) as perCustomerRatio,
            round(abos.kdj_37_tb*100,2) as perCustomerYearRatio,
            abos.xsygje_410 as estimatedSalesAmount,
            round(abos.wjbzkl_cur_506*100,2) as wantGoldDiscountRatio,
            round(abos.fgl_145*100,2) as customerRepurchaseRatio,
            round(abos.xsygdcl_51*100,2) as achievingRate
            from ads_bigtable_organization_sku abos
            left join ads_bigtable_organization_sku abosyoy
            on abos.sku_id = abosyoy.sku_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.organization_id = abos.organization_id
            left join dim_store_sku_base_info dssbi
            on abos.sku_id = dssbi.sku_id
            where abos.sku_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.organization_id=#{organizationId}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and (dssbi.sku_id like CONCAT('%',#{goodName},'%')  or dssbi.sku_name like CONCAT('%',#{goodName},'%'))
            </if>
            <include refid="sort"></include>

        </if>
        <if test="isPartner==1">
            SELECT
            dssbi.line_name line,
            dssbi.sku_images productPicture,
            dssbi.sku_id sku,
            dssbi.sku_name skuName,
            dssbi.sku_spec specification,
            dssbi.flavor  flavor,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,1) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,1) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,1) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            round(abos.wjbzkl_cur_506*100,2) as wantGoldDiscountRatio,
            abos.wjbyj_cur_409 as goldenCoinPerformance
            from ads_bigtable_partner_sku abos
            left join ads_bigtable_partner_sku abosyoy
            on abos.sku_id = abosyoy.sku_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.member_key = abos.member_key
            left join dim_store_sku_base_info dssbi
            on abos.sku_id = dssbi.sku_id
            where abos.sku_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.member_key=#{memberKey}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and (dssbi.sku_id like CONCAT('%',#{goodName},'%') or dssbi.sku_name like CONCAT('%',#{goodName},'%'))
            </if>
            <include refid="sort"></include>

        </if>
    </select>

    <select id="queryGoodsProductSkuCount" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="int">
        <if test="isPartner==0">
            SELECT
            count(1)
            from ads_bigtable_organization_sku abos
            left join dim_store_sku_base_info dssbi
            on abos.sku_id = dssbi.sku_id
            where abos.sku_id is not null
            and abos.the_year_month = #{yearMonth}
            and organization_id=#{organizationId}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and (dssbi.sku_id like CONCAT('%',#{goodName},'%') or dssbi.sku_name like CONCAT('%',#{goodName},'%'))
            </if>
        </if>
        <if test="isPartner==1">
            SELECT
            count(1)
            from ads_bigtable_partner_sku abos
            left join dim_store_sku_base_info dssbi
            on abos.sku_id = dssbi.sku_id
            where abos.sku_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.member_key=#{memberKey}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and (dssbi.sku_id like CONCAT('%',#{goodName},'%') or dssbi.sku_name like CONCAT('%',#{goodName},'%'))
            </if>
        </if>
    </select>
    <select id="queryGoodsProductInfos" resultType="com.wantwant.sfa.backend.realData.vo.CommodityDataNewVo">
        SELECT
            sku_id as sku,
            spu_id as spuId,
            spu_name as spuName,
            line_id as lineId,
            line_name as lineName,
            round(zwbppjyj_cur_405,1) as monthlyPerformance,
            round(zwbppjyj_cur_405_hb*100,1) as monthlyPerformanceRatio,
            round(zwbppjyj_cur_405_tb*100,1) as monthlyPerformanceYearRatio,
            round(cpyjzb_cur_406*100,1) as monthlyPerformanceRate,
            yddyj_cur_407 as bookingPerformance,
            yddwfhyj_cur_408 as bookingUnshippedPerformance,
            wjbyj_cur_409 as goldenCoinPerformance,
            jykhs_36 as tradingClients,
            round(wjbzkl_cur_506*100,1) as wantGoldDiscountRatio,
            xjyj_dq_505 as cashPerformance,
            round(jykhs_36_hb*100,1) as tradingClientsRatio,
            round(jykhs_36_tb*100,1) as tradingClientsYearRatio,
            kdj_37 as perCustomer,
            round(kdj_37_hb*100,1) as perCustomerRatio,
            round(kdj_37_tb*100,1) as perCustomerYearRatio,
            xsygje_410 as estimatedSalesAmount,
            round(xsygdcl_51*100,1) as achievingRate,
            xsygxs_580 as productionMarketingCoordinationEstimateNum,
            fpxs_581 as productionMarketingCoordinationAssignNum,
            sjchxs_582 as productionMarketingCoordinationActualNum,
            round(xsygdcl_51*100,1) as  productionMarketingCoordinationAchievementRate
        from ads_bigtable_organization_sku
        where
        the_year_month = #{yearMonth}
          and organization_id=#{organizationId}
          and date_type_id=#{dateTypeId}
          <if test="type == 'sku'">
              and sku_id in
              <foreach collection="productInfos" open="(" close=")" item="item" separator=",">
                  #{item}
              </foreach>
          </if>
        <if test="type == 'spu'">
            and spu_id in
            <foreach collection="productInfos" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="type == 'line'">
            and line_id in
            <foreach collection="productInfos" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="queryGoodsProductSpu" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.CommodityDataNewVo">
        <if test="isPartner==0">
            SELECT
            abos.spu_name as spuName,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            abos.wjbyj_cur_409 as goldenCoinPerformance,
            abos.jykhs_36 as tradingClients,
            round(abos.jykhs_36_hb*100,2) as tradingClientsRatio,
            round(abos.jykhs_36_tb*100,2) as tradingClientsYearRatio,
            abos.kdj_37 as perCustomer,
            round(abos.kdj_37_hb*100,2) as perCustomerRatio,
            round(abos.kdj_37_tb*100,2) as perCustomerYearRatio,
            abos.xsygje_410 as estimatedSalesAmount,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio,
            round(abos.fgl_145*100,1) as customerRepurchaseRatio,
            round(abos.xsygdcl_51*100,2) as achievingRate
            from ads_bigtable_organization_sku abos
            left join ads_bigtable_organization_sku abosyoy
            on abos.spu_id = abosyoy.spu_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.organization_id = abos.organization_id
            where abos.spu_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.organization_id=#{organizationId}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and abos.spu_name like CONCAT('%',#{goodName},'%')
            </if>
            <include refid="sort"></include>

        </if>
        <if test="isPartner==1">
            SELECT
            abos.spu_name as spuName,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio,
            abos.wjbyj_cur_409 as goldenCoinPerformance
            from ads_bigtable_partner_sku abos
            left join ads_bigtable_partner_sku abosyoy
            on abos.spu_id = abosyoy.spu_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.member_key = abos.member_key
            where abos.spu_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.member_key=#{memberKey}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and abos.spu_name like CONCAT('%',#{goodName},'%')
            </if>
            <include refid="sort"></include>

        </if>
    </select>

    <select id="queryGoodsProductSpuCount" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="int">
        <if test="isPartner==0">
            SELECT
            count(1)
            from ads_bigtable_organization_sku
            where spu_id is not null
            and the_year_month = #{yearMonth}
            and organization_id=#{organizationId}
            and business_group_id=#{businessGroup}
            and date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and spu_name like CONCAT('%',#{goodName},'%')
            </if>
        </if>
        <if test="isPartner==1">
            SELECT
            count(1)
            from ads_bigtable_partner_sku
            where spu_id is not null
            and the_year_month = #{yearMonth}
            and member_key=#{memberKey}
            and business_group_id=#{businessGroup}
            and date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and spu_name like CONCAT('%',#{goodName},'%')
            </if>
        </if>
    </select>


    <select id="queryGoodsProductLine" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="com.wantwant.sfa.backend.realData.vo.CommodityDataNewVo">
        <if test="isPartner==0">
            SELECT
            abos.line_name as lineName,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            abos.wjbyj_cur_409 as goldenCoinPerformance,
            abos.jykhs_36 as tradingClients,
            round(abos.jykhs_36_hb*100,2) as tradingClientsRatio,
            round(abos.jykhs_36_tb*100,2) as tradingClientsYearRatio,
            abos.kdj_37 as perCustomer,
            round(abos.kdj_37_hb*100,2) as perCustomerRatio,
            round(abos.kdj_37_tb*100,2) as perCustomerYearRatio,
            abos.xsygje_410 as estimatedSalesAmount,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio,
            round(abos.fgl_145*100,1) as customerRepurchaseRatio,
            round(abos.xsygdcl_51*100,2) as achievingRate
            from ads_bigtable_organization_sku abos
            left join ads_bigtable_organization_sku abosyoy
            on abos.line_id = abosyoy.line_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.organization_id = abos.organization_id
            where abos.line_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.organization_id=#{organizationId}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and abos.line_name like CONCAT('%',#{goodName},'%')
            </if>
            <include refid="sort"></include>

        </if>
        <if test="isPartner==1">
            SELECT
            abos.line_name as lineName,
            abos.zwbppjyj_cur_405 as monthlyPerformance,
            abosyoy.zwbppjyj_cur_405 as performanceYoy,
            abos.xjyj_dq_505 as cashPerformance,
            round(abos.zwbppjyj_cur_405_hb*100,2) as monthlyPerformanceRatio,
            round(abos.zwbppjyj_cur_405_tb*100,2) as monthlyPerformanceYearRatio,
            round(abos.cpyjzb_cur_406*100,2) as monthlyPerformanceRate,
            abos.yddyj_cur_407 as bookingPerformance,
            abos.yddwfhyj_cur_408 as bookingUnshippedPerformance,
            round(abos.wjbzkl_cur_506*100,1) as wantGoldDiscountRatio,
            abos.wjbyj_cur_409 as goldenCoinPerformance
            from ads_bigtable_partner_sku abos
            left join ads_bigtable_partner_sku abosyoy
            on abos.line_id = abosyoy.line_id
                and abosyoy.the_year_month = #{yearMonthYoy}
                and abosyoy.business_group_id = abos.business_group_id
                and abosyoy.date_type_id = abos.date_type_id
                and abosyoy.member_key = abos.member_key
            where abos.line_id is not null
            and abos.the_year_month = #{yearMonth}
            and abos.member_key=#{memberKey}
            and abos.business_group_id=#{businessGroup}
            and abos.date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and abos.line_name like CONCAT('%',#{goodName},'%')
            </if>
            <include refid="sort"></include>

        </if>
    </select>


    <select id="queryGoodsProductLineCount" parameterType="com.wantwant.sfa.backend.realData.request.TradeGoodNewReq"
            resultType="int">
        <if test="isPartner==0">
            SELECT
            count(1)
            from ads_bigtable_organization_sku
            where line_id is not null
            and the_year_month = #{yearMonth}
            and organization_id=#{organizationId}
            and business_group_id=#{businessGroup}
            and date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and line_name like CONCAT('%',#{goodName},'%')
            </if>
        </if>
        <if test="isPartner==1">
            SELECT
            count(1)
            from ads_bigtable_partner_sku
            where line_id is not null
            and the_year_month = #{yearMonth}
            and member_key=#{memberKey}
            and business_group_id=#{businessGroup}
            and date_type_id=#{dateTypeId}
            <if test="null != goodName and goodName != ''">
                and line_name like CONCAT('%',#{goodName},'%')
            </if>
        </if>
    </select>


    <sql id="sort">
        <choose>
            <when test="sortName == 'monthlyPerformance'">
                order by abos.zwbppjyj_cur_405
            </when>
            <when test="sortName == 'monthlyPerformanceRatio'">
                order by abos.zwbppjyj_cur_405_hb
            </when>
            <when test="sortName == 'monthlyPerformanceYearRatio'">
                order by abos.zwbppjyj_cur_405_tb
            </when>
            <when test="sortName == 'monthlyPerformanceRate'">
                order by abos.cpyjzb_cur_406
            </when>
            <when test="sortName == 'performanceYoy'">
                order by abosyoy.zwbppjyj_cur_405
            </when>
            <otherwise>
                order by abos.zwbppjyj_cur_405
            </otherwise>
        </choose>
        <choose>
            <when test=" '' != sortOrder and  sortOrder=='DESC'">
                DESC
            </when>
            <when test=" '' != sortOrder and  sortOrder=='ASC'">
                ASC
            </when>
            <otherwise>
                DESC
            </otherwise>
        </choose>
    </sql>


    <select id="getPersonnelTendencyAnalyze" resultType="com.wantwant.sfa.backend.realData.vo.PerformancePersonnelDateVo">
        select
        the_year_month as month,

        glgzzrs_329 as onJobNumManage,
        round(glgzzrs_329_hb*100,1) as ringRatioOnJobNumManage,
        round(glgzzrs_329_tb*100,1) as yearOnYearOnJobNumManage,

        round(ywyrfyl_514*100,1) as businessEmploymentCostRate,
        round(ywyrfyl_514_hb*100,1) as ringRatioBusinessEmploymentCostRate,
        round(ywyrfyl_514_tb*100,1) as yearOnYearBusinessEmploymentCostRate,

        zddzzrs_314 as onJobNumArea,
        zddzgzzrs_631 as onJobMainNumArea,
        zddjgzzrs_632 as onJobPartNumArea,
        zddrzrs_315 as entryNumArea,
        zddlzrs_316 as dimissionNumArea,
        round(zddlzl_317*100,1) as quitRateArea,
        zddbzs_411 as establishmentNumArea,
        round(zddbzzgl_386*100,1) as establishmentOnGuardNumArea,
        round(zddyrfyl_617*100,1) as employmentCostRateArea,

        dqzjzzrs_310 as onJobNumVrea,
        dqzjrzrs_311 as entryNumVrea,
        dqzjlzrs_312 as dimissionNumVrea,
        round(dqzjlzl_313*100,1) as quitRateVrea,
        dqzjbzs_412 as establishmentNumVrea,
        round(dqzjbzzgl_385*100,1) as establishmentOnGuardNumVrea,
        round(dqzjyrfyl_618*100,1) as employmentCostRateVrea,

        sqzjzzrs_306 as onJobNumProvince,
        sqzjrzrs_307 as entryNumProvince,
        sqzjlzrs_308 as dimissionNumProvince,
        round(sqzjlzl_309*100,1) as quitRateProvince,
        sqzjbzs_413 as establishmentNumProvince,
        round(sqzjbzzgl_384*100,1) as establishmentOnGuardNumProvince,
        round(sqzjyrfyl_619*100,1) as employmentCostRateProvince,

        qyzjzzrs_301 as onJobNumCompany,
        qyzjrzrs_302 as entryNumCompany,
        qyzjlzrs_303 as dimissionNumCompany,
        round(qyzjlzl_304*100,1) as quitRateCompany,
        qyzjbzs_414 as establishmentNumCompany,
        round(qyzjbzzgl_305*100,1) as establishmentOnGuardNumCompany,
        round(qyzjyrfyl_620*100,1) as employmentCostRateCompany,

        csjlzzrs_297 as onJobNumDepartment,
        csjlrzrs_298 as entryNumDepartment,
        csjllzrs_299 as dimissionNumDepartment,
        round(csjllzl_126*100,1) as quitRateDepartment,
        csjlbzs_415 as establishmentNumDepartment,
        round(csjlbzzgl_300*100,1) as establishmentOnGuardNumDepartment,
        round(csjlyrfyl_621*100,1) as employmentCostRateDepartment
        from
        ads_bigtable_organization
        where organization_id = #{organizationId}
        and date_type_id = #{dateTypeId}
        <if test="startYearMonth !=null and startYearMonth != ''">
            and the_year_month &gt;= #{startYearMonth}
        </if>
        <if test="endYearMonth !=null and endYearMonth != ''">
            and the_year_month &lt;= #{endYearMonth}
        </if>
        order by
        <choose>
            <when test="null!= sortName and sortName!=''">${sortName}</when>
            <otherwise>the_year_month</otherwise>
        </choose>
        <choose>
            <when test="null!= sortType and sortType!=''">${sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>



    <select id="getOperateTendencyAnalyzeSnapshot" resultType="com.wantwant.sfa.backend.realData.vo.PerformanceOperateDateVo">
        select
            the_year_month as month,
            organization_id as organizationId,
            zwbppjyj_cur_405 as performance,
            xjyj_dq_505 as cashPerformance,
            round(zwbppjyj_cur_405_hb*100,1) as performanceChainRatio,
            round(zwbppjyj_cur_405_tb*100,1) as performanceYearRatio,
            pjyjmb_378 as performanceGoal,
            round(pjyjmb_378_hb*100,1) as performanceGoalRatio,
            round(pjyjmb_378_tb*100,1) as performanceGoalYearRatio,
            round(pjyjmbdcl_32*100,1) as performanceAchievementRate,
            round(pjyjmbdcl_32_hb*100,1) as performanceAchievementRateRatio,
            round(pjyjmbdcl_32_tb*100,1) as performanceAchievementRateYearRatio,
            round(wjbyjzb_dq_506*100,1) as wantGoldDiscountRatio,
            round(wjbyjzb_dq_506_hb*100,1) as wantGoldDiscountRatioChainRatio,
            round(wjbyjzb_dq_506_tb*100,1) as wantGoldDiscountRatioYearRatio,
            round(ztfyzkl_577*100,1) as overallDiscountRatio,
            round(ztfyzkl_577_hb*100,1) as overallDiscountRatioChainRatio,
            round(ztfyzkl_577_tb*100,1) as overallDiscountRatioYearRatio,

            glgrjyj_cur_483 as managementPerPerformance,
            round(glgrjyj_cur_483_hb*100,1) as managementPerPerformanceChainRatio,
            round(glgrjyj_cur_483_tb*100,1) as managementPerPerformanceYearRatio,
            glgzzrs_329 onJobNumManagement,
            round(glgzzrs_329_hb*100,1) onJobNumManagementChainRatio,
            round(glgzzrs_329_tb*100,1) onJobNumManagementYearRatio,
            glglzrs_507 dimissionNumManagement,
            round(glglzrs_507_hb*100,1) dimissionNumManagementChainRatio,
            round(glglzrs_507_tb*100,1) dimissionNumManagementYearRatio,
            round(glglzl_508*100,1) as quitRateManagement,
            round(glglzl_508_hb*100,1) as quitRateManagementChainRatio,
            round(glglzl_508_tb*100,1) as quitRateManagementYearRatio,

            csjlzzrs_297 as onJobNumDepartment,
            round(csjlzzrs_297_hb*100,1) as onJobNumDepartmentRatio,
            round(csjlzzrs_297_tb*100,1) as onJobNumDepartmentYearRatio,
            csjllzrs_299 as dimissionNumDepartment,
            round(csjllzl_126*100,1) as quitRateDepartment,

            jykhs_36 as tradingCustomerNum,
            round(jykhs_36_hb*100,1) as tradingCustomerNumRateRatio,
            round(jykhs_36_tb*100,1) as tradingCustomerNumRateYearRatio,
            xkhs_41 as newTradingCustomerNumDealer,
            round(xkhs_41_hb*100,1) as newTradingCustomerNumDealerRatio,
            round(xkhs_41_tb*100,1) as newTradingCustomerNumDealerYearRatio,
            lkhs_42 as oldTradingCustomerNumDealer,
            round(lkhs_42_hb*100,1) as oldTradingCustomerNumDealerRatio,
            round(lkhs_42_tb*100,1) as oldTradingCustomerNumDealerYearRatio,

            kdj_37 as perCustomer,
            round(kdj_37_hb*100,1) as perCustomerRatio,
            round(kdj_37_tb*100,1) as perCustomerYearRatio,
            xkhkdj_344 as newPerCustomerDealer,
            round(xkhkdj_344_hb*100,1) as newPerCustomerDealerRatio,
            round(xkhkdj_344_tb*100,1) as newPerCustomerDealerYearRatio,
            lkhkdj_343 as oldPerCustomerDealer,
            round(lkhkdj_343_hb*100,1) as oldPerCustomerDealerRatio,
            round(lkhkdj_343_tb*100,1) as oldPerCustomerDealerYearRatio,

            xjscs_464 as countyMarketNum,
            round(xjscs_464_hb*100,1) as countyMarketNumRatio,
            round(xjscs_464_tb*100,1) as countyMarketNumYearRatio,
            xjscfgs_616 as countyMarketCoverNum,
            round(xjscfgs_616_hb*100,1) as countyMarketCoverNumRatio,
            round(xjscfgs_616_tb*100,1) as countyMarketCoverNumYearRatio,
            round(xjscfgl_465*100,1) as countyMarketCoverRate,
            round(xjscfgl_465_hb*100,1) as countyMarketCoverRateRatio,
            round(xjscfgl_465_tb*100,1) as countyMarketCoverRateYearRatio,
            hhrrzrs_295 as branchEntryNum,
            round(hhrrzrs_295_hb*100,1) as branchEntryNumRatio,
            round(hhrrzrs_295_tb*100,1) as branchEntryNumYearRatio,
            hhrlzrs_296 as branchDepartmentNum,
            round(hhrlzrs_296_hb*100,1) as branchDepartmentNumRatio,
            round(hhrlzrs_296_tb*100,1) as branchDepartmentNumYearRatio,
            round(hhrlzl_65*100,1) as branchDepartmentRate,
            round(hhrlzl_65_hb*100,1) as branchDepartmentRateRatio,
            round(hhrlzl_65_tb*100,1) as branchDepartmentYearRatio,
            fgkhs_44 as repurchaseCustomerNum,
            round(fgkhs_44_hb*100,1) as repurchaseCustomerNumRatio,
            round(fgkhs_44_tb*100,1) as repurchaseCustomerNumYearRatio,
            round(khfgl_145*100,1) as repurchaseRate,
            round(khfgl_145_hb*100,1) as repurchaseRateRatio,
            round(khfgl_145_tb*100,1) as repurchaseRateYearRatio,
            lskhs_448 as lossCustomerNum,
            round(lskhs_448_hb*100,1) as lossCustomerNumRatio,
            round(lskhs_448_tb*100,1) as lossCustomerNumYearRatio,
            round(khlsl_143*100,1) as lossRate,
            round(khlsl_143_hb*100,1) as lossRateRatio,
            round(khlsl_143_tb*100,1) as lossRateYearRatio
    from
        ads_bigtable_organization_month_report
        where organization_id = #{organizationId}
        and date_type_id = #{dateTypeId}
        and etl_date like concat(#{etlDate},'%')
        and the_year_month <![CDATA[ <= ]]> #{theYearMonth}
        and the_year_month <![CDATA[ >= ]]> DATE_FORMAT(DATE_SUB(STR_TO_DATE(#{theYearMonth}, '%Y-%m'), INTERVAL 1 YEAR), '%Y-%m')
        order by
        <choose>
            <when test="null!= sortName and sortName!=''">${sortName}</when>
            <otherwise>the_year_month</otherwise>
        </choose>
        <choose>
            <when test="null!= sortType and sortType!=''">${sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>


    <select id="getOperateTendencyAnalyze" resultType="com.wantwant.sfa.backend.realData.vo.PerformanceOperateDateVo">
        select
        the_year_month as month,
        organization_id as organizationId,
        zwbppjyj_cur_405 as performance,
        xjyj_dq_505 as cashPerformance,
        round(zwbppjyj_cur_405_hb*100,1) as performanceChainRatio,
        round(zwbppjyj_cur_405_tb*100,1) as performanceYearRatio,
        pjyjmb_378 as performanceGoal,
        round(pjyjmb_378_hb*100,1) as performanceGoalRatio,
        round(pjyjmb_378_tb*100,1) as performanceGoalYearRatio,
        round(pjyjmbdcl_32*100,1) as performanceAchievementRate,
        round(pjyjmbdcl_32_hb*100,1) as performanceAchievementRateRatio,
        round(pjyjmbdcl_32_tb*100,1) as performanceAchievementRateYearRatio,
        round(wjbyjzb_dq_506*100,1) as wantGoldDiscountRatio,
        round(wjbyjzb_dq_506_hb*100,1) as wantGoldDiscountRatioChainRatio,
        round(wjbyjzb_dq_506_tb*100,1) as wantGoldDiscountRatioYearRatio,
        round(ztfyzkl_577*100,1) as overallDiscountRatio,
        round(ztfyzkl_577_hb*100,1) as overallDiscountRatioChainRatio,
        round(ztfyzkl_577_tb*100,1) as overallDiscountRatioYearRatio,

        glgrjyj_cur_483 as managementPerPerformance,
        round(glgrjyj_cur_483_hb*100,1) as managementPerPerformanceChainRatio,
        round(glgrjyj_cur_483_tb*100,1) as managementPerPerformanceYearRatio,
        glgzzrs_329 onJobNumManagement,
        round(glgzzrs_329_hb*100,1) onJobNumManagementChainRatio,
        round(glgzzrs_329_tb*100,1) onJobNumManagementYearRatio,
        glglzrs_507 dimissionNumManagement,
        round(glglzrs_507_hb*100,1) dimissionNumManagementChainRatio,
        round(glglzrs_507_tb*100,1) dimissionNumManagementYearRatio,
        round(glglzl_508*100,1) as quitRateManagement,
        round(glglzl_508_hb*100,1) as quitRateManagementChainRatio,
        round(glglzl_508_tb*100,1) as quitRateManagementYearRatio,

        csjlzzrs_297 as onJobNumDepartment,
        round(csjlzzrs_297_hb*100,1) as onJobNumDepartmentRatio,
        round(csjlzzrs_297_tb*100,1) as onJobNumDepartmentYearRatio,
        csjllzrs_299 as dimissionNumDepartment,
        round(csjllzl_126*100,1) as quitRateDepartment,

        jykhs_36 as tradingCustomerNum,
        round(jykhs_36_hb*100,1) as tradingCustomerNumRateRatio,
        round(jykhs_36_tb*100,1) as tradingCustomerNumRateYearRatio,
        xkhs_41 as newTradingCustomerNumDealer,
        round(xkhs_41_hb*100,1) as newTradingCustomerNumDealerRatio,
        round(xkhs_41_tb*100,1) as newTradingCustomerNumDealerYearRatio,
        lkhs_42 as oldTradingCustomerNumDealer,
        round(lkhs_42_hb*100,1) as oldTradingCustomerNumDealerRatio,
        round(lkhs_42_tb*100,1) as oldTradingCustomerNumDealerYearRatio,

        kdj_37 as perCustomer,
        round(kdj_37_hb*100,1) as perCustomerRatio,
        round(kdj_37_tb*100,1) as perCustomerYearRatio,
        xkhkdj_344 as newPerCustomerDealer,
        round(xkhkdj_344_hb*100,1) as newPerCustomerDealerRatio,
        round(xkhkdj_344_tb*100,1) as newPerCustomerDealerYearRatio,
        lkhkdj_343 as oldPerCustomerDealer,
        round(lkhkdj_343_hb*100,1) as oldPerCustomerDealerRatio,
        round(lkhkdj_343_tb*100,1) as oldPerCustomerDealerYearRatio,

        xjscs_464 as countyMarketNum,
        round(xjscs_464_hb*100,1) as countyMarketNumRatio,
        round(xjscs_464_tb*100,1) as countyMarketNumYearRatio,
        xjscfgs_616 as countyMarketCoverNum,
        round(xjscfgs_616_hb*100,1) as countyMarketCoverNumRatio,
        round(xjscfgs_616_tb*100,1) as countyMarketCoverNumYearRatio,
        round(xjscfgl_465*100,1) as countyMarketCoverRate,
        round(xjscfgl_465_hb*100,1) as countyMarketCoverRateRatio,
        round(xjscfgl_465_tb*100,1) as countyMarketCoverRateYearRatio,
        hhrrzrs_295 as branchEntryNum,
        round(hhrrzrs_295_hb*100,1) as branchEntryNumRatio,
        round(hhrrzrs_295_tb*100,1) as branchEntryNumYearRatio,
        hhrlzrs_296 as branchDepartmentNum,
        round(hhrlzrs_296_hb*100,1) as branchDepartmentNumRatio,
        round(hhrlzrs_296_tb*100,1) as branchDepartmentNumYearRatio,
        round(hhrlzl_65*100,1) as branchDepartmentRate,
        round(hhrlzl_65_hb*100,1) as branchDepartmentRateRatio,
        round(hhrlzl_65_tb*100,1) as branchDepartmentYearRatio,
        fgkhs_44 as repurchaseCustomerNum,
        round(fgkhs_44_hb*100,1) as repurchaseCustomerNumRatio,
        round(fgkhs_44_tb*100,1) as repurchaseCustomerNumYearRatio,
        round(khfgl_145*100,1) as repurchaseRate,
        round(khfgl_145_hb*100,1) as repurchaseRateRatio,
        round(khfgl_145_tb*100,1) as repurchaseRateYearRatio,
        lskhs_448 as lossCustomerNum,
        round(lskhs_448_hb*100,1) as lossCustomerNumRatio,
        round(lskhs_448_tb*100,1) as lossCustomerNumYearRatio,
        round(khlsl_143*100,1) as lossRate,
        round(khlsl_143_hb*100,1) as lossRateRatio,
        round(khlsl_143_tb*100,1) as lossRateYearRatio
        from
        ads_bigtable_organization
        where organization_id = #{organizationId}
        and date_type_id = #{dateTypeId}
        <if test="startYearMonth !=null and startYearMonth != ''">
            and the_year_month &gt;= #{startYearMonth}
        </if>
        <if test="endYearMonth !=null and endYearMonth != ''">
            and the_year_month &lt;= #{endYearMonth}
        </if>
        order by
        <choose>
            <when test="null!= sortName and sortName!=''">${sortName}</when>
            <otherwise>the_year_month</otherwise>
        </choose>
        <choose>
            <when test="null!= sortType and sortType!=''">${sortType}</when>
            <otherwise>desc</otherwise>
        </choose>
    </select>




    <select id="selectCeoTrend" resultType="com.wantwant.sfa.backend.realData.dto.CeoTrendDTO">
        select
        abp.the_year_month as theYearMonth,
        abp.zwbppjyj_cur_405 as performance,
        round(ifnull(abp.zwbppjyj_cur_405_hb,0)*100,2) as performanceChainRatio,
        round(ifnull(abp.zwbppjyj_cur_405_tb,0)*100,2) as performanceYearRatio
        from ads_bigtable_partner abp
        where abp.member_key = #{memberKey}
        and abp.date_type_id = #{dateTypeId}
        and abp.business_group_id = #{businessGroup}
        <if test="dateTypeId != 2">
            and abp.the_year_month in
            <foreach collection="yearMonthList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dateTypeId == 2">
            and abp.the_year_month <![CDATA[ <= ]]> #{yearMonth}
        </if>
        order by abp.the_year_month
    </select>


    <select id="queryCeoPerformanceDate" resultType="com.wantwant.sfa.backend.realData.vo.ServiceDataDetailVo">
        SELECT
        abp.member_key as memberKey,
        abp.business_group_id AS `group`,
        abp.area_name AS area,
        abp.varea_name AS varea,
        abp.province_name AS province,
        abp.company_name AS company,
        abp.department_name AS departmentName,
        abp.member_key as organizationId,
        coalesce(NULLIF(CONCAT_WS('/', abp.area_name ,abp.varea_name, abp.province_name,abp.company_name,abp.department_name),'' ),abp.organization_name) as fullOrganizationName,

        di.position_type_id AS positionTypeId,
        di.pic_url AS url,
        '合伙人' AS postName,
        di.employee_name AS name,
        di.onboard_time AS onboardTime,
        di.off_time as dischargeDate,
        di.zw_onboard_days as onboardDays,
        di.employee_info_id as employeeInfoId,

        abp.zwbppjyj_cur_405 AS overallPerformance,
        abp2.zwbppjyj_cur_405 as performanceYoy,
        abp.rksrjyj_320 as performancePerPopulation,
        abp.pjyjmb_378 AS overallPerformanceGoal,
        round( abp.pjyjmbdcl_32 * 100, 2 ) AS overallPerformanceRate,
        round( abp.zwbppjyj_cur_405_tb * 100, 2 ) AS overallPerformanceYearRatio,
        round(abp.zwbppjyj_cur_405_hb*100,2) as overallPerformanceChainRatio,
        abp.yddyj_past_379 AS bookingPerformance,

        abp.yczye_319 as preStoredValuePerformance,
        abp.yddwfhyj_byfh_past_421 as advanceOrderUnshippedPerformanceThis,
        abp.yddwfhyj_cyfh_past_422 as oldCustomerPerformanceDealerYearRatioNext,

        round( abp.yddyjzb_377 * 100, 2 ) AS bookingPerformanceRate,
        abp.yddwfhyj_past_318 AS bookingUnshippedPerformance
        FROM
        ads.ads_bigtable_partner abp
        left join ads_bigtable_partner abp2
        on abp2.member_key = abp.member_key
            and abp2.date_type_id = abp.date_type_id
            and abp2.the_year_month = #{yearMonthYoy}
            and abp2.business_group_id = abp.business_group_id
            and abp2.department_id = abp.department_id
        left join dim_emp_pos_role_org_mon_partner di
        on di.member_key  = abp.member_key and di.job_type_id = 1 and di.the_year_month = #{relationMonth}


        <where>
            and abp.business_group_id = #{businessGroup}
            and abp.the_year_month = #{yearMonth}
            and abp.date_type_id=#{dateTypeId}

            <if test="organizationIds !=null and organizationIds.size != 0">
                and (

                <foreach collection="organizationIds" open="abp.area_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abp.varea_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abp.province_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abp.company_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="organizationIds" open="abp.department_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>

            <choose>
                <when test="organizationType == 'area'">
                    and abp.area_id = #{organizationId}
                </when>
                <when test="organizationType == 'varea'">
                    and abp.varea_id = #{organizationId}
                </when>
                <when test="organizationType == 'province'">
                    and abp.province_id = #{organizationId}
                </when>
                <when test="organizationType == 'company'">
                    and abp.company_id = #{organizationId}
                </when>
                <when test="organizationType == 'department'">
                    and abp.department_id = #{organizationId}
                </when>
            </choose>
        </where>

        <choose>
            <when test="sortName != null and sortName != ''">
                order by
                <choose>
                    <when test="sortName == 'overallPerformance'">
                        abp.zwbppjyj_cur_405
                    </when>
                    <when test="sortName == 'fiscalYearPerformance'">
                        abp.zwbppjyj_past_cn_30
                    </when>
                    <when test="sortName == 'fiscalYearGoal'">
                        abp.pjyjmb_cn_378
                    </when>
                    <when test="sortName == 'fiscalYearRate'">
                        abp.pjyjmbdcl_cn_32
                    </when>
                    <when test="sortName == 'fiscalYearRatio'">
                        abp.zwbppjyj_cur_cn_405_tb
                    </when>
                    <when test="sortName == 'quarterPerformance'">
                        abp.zwbppjyj_past_jd_30
                    </when>
                    <when test="sortName == 'quarterGoal'">
                        abp.pjyjmb_jd_378
                    </when>
                    <when test="sortName == 'quarterRate'">
                        abp.pjyjmbdcl_jd_32
                    </when>
                    <when test="sortName == 'quarterYearRatio'">
                        abp.zwbppjyj_cur_jd_405_tb
                    </when>
                    <when test="sortName == 'overallPerformanceGoal'">
                        abp.pjyjmb_378
                    </when>
                    <when test="sortName == 'overallPerformanceRate'">
                        abp.pjyjmbdcl_32
                    </when>
                    <when test="sortName == 'overallPerformanceYearRatio'">
                        abp.zwbppjyj_cur_405_tb
                    </when>
                    <when test="sortName == 'overallMainProductPerformance'">
                        abp.ztpztyj_past_416
                    </when>
                    <when test="sortName == 'overallMainProductGoal'">
                        abp.ztpztyjmb_417
                    </when>
                    <when test="sortName == 'overallMainProductRate'">
                        abp.ztpdcl_52
                    </when>
                    <when test="sortName == 'overallMainProductYearRatio'">
                        abp.ztpztyj_cur_418_tb
                    </when>
                    <when test="sortName == 'bookingPerformance'">
                        abp.yddyj_past_379
                    </when>
                    <when test="sortName == 'bookingPerformanceRate'">
                        abp.yddyjzb_377
                    </when>
                    <when test="sortName == 'bookingUnshippedPerformance'">
                        abp.yddwfhyj_past_318
                    </when>
                    <when test="sortName == 'managementOnJobNum'">
                        abp.glgzzrs_329
                    </when>
                    <when test="sortName == 'managementPerPerformance'">
                        abp.glgrjyj_cur_483
                    </when>
                    <when test="sortName == 'tradingClientNum'">
                        abp.jykhs_36
                    </when>
                    <when test="sortName == 'perCustomer'">
                        abp.kdj_37
                    </when>
                    <when test="sortName == 'dealerNewCustomerNum'">
                        abp.xkhs_41
                    </when>
                    <when test="sortName == 'dealerNewPerCustomer'">
                        abp.xkhkdj_344
                    </when>
                    <when test="sortName == 'dealerOldCustomerNum'">
                        abp.lkhs_42
                    </when>
                    <when test="sortName == 'nextQuarterRepurchaseRate'">
                        abp.cjdfgljxs_388
                    </when>
                    <when test="sortName == 'repurchaseRate'">
                        abp.khfgl_145
                    </when>
                    <when test="sortName == 'performanceYoy'">
                        abp2.zwbppjyj_cur_405
                    </when>
                    <otherwise>
                        abp.zwbppjyj_cur_405
                    </otherwise>
                </choose>
                <choose>
                    <when test="sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by abp.zwbppjyj_cur_405 desc
            </otherwise>
        </choose>

    </select>


    <select id="queryBusinessDevelopment" resultType="com.wantwant.sfa.backend.realData.vo.BusinessDevelopmentVo">
        select

        coalesce(NULLIF(CONCAT_WS('/', abdr.area_name ,abdr.varea_name, abdr.province_name,abdr.company_name,abdr.department_name),'' ),abdr.organization_name) as fullOrganizationName,

        abdr.department_id as organizationId,
        abdr.area_name as areaName,
        abdr.varea_name as vareaName,
        abdr.province_name as provinceName,
        abdr.company_name as companyName,
        abdr.department_name as departmentName,
        abdr.pic_url as partnerPicUrl,
        abdr.employee_name as partnerName,
        abdr.partner_member_key as partnerMemberKey,
        mon.pos_role_name as postName,
        mon.pic_url as picUrl,
        mon.employee_name as name,
        mon.onboard_time as entryDate,
        mon.off_time as dischargeDate,
        mon.employee_info_id as employeeInfoId,
        round(ifnull(abdr.bfzds_average_day_place,0),2) as visitTerminalAverageDaily,
        round(ifnull(abdr.clzds_average_day_place,0),2) as displayTerminalAverageDaily,
        round(ifnull(abdr.dds_average_day_place,0),2) as copyOrderCountAverageDaily,
        round(ifnull(abdr.ddzds_average_day_place,0),2) as copyOrderTerminalCountAverageDaily,
        round(ifnull(abdr.ddje_average_day_place,0),2) as copyOrderAmountAverageDaily,
        round(ifnull(abdr.zdkdj_average_day_place,0),2) as copyPerTransactionTerminalAverageDaily,
        round(ifnull(abdr.dds_average_day_refund,0),2) as chargebackOrderCountAverageDaily,
        round(ifnull(abdr.ddzds_average_day_refund,0),2) as chargebackOrderTerminalCountAverageDaily,
        round(ifnull(abdr.ddje_average_day_refund,0),2) as chargebackOrderAmountAverageDaily,
        round(ifnull(abdr.zdkdj_average_day_refund,0),2) as chargebackPerTransactionTerminalAverageDaily
        from ads_bigtable_business_bd_report abdr
        left join dim_emp_pos_role_org_mon_partner mon
        on mon.member_key = abdr.member_key and mon.job_type_id = 2 and mon.the_year_month = #{request.theDate}
        where
        abdr.the_year_month =  #{request.yearMonth}
        and abdr.date_type_id =  #{request.dateTypeId}
        and abdr.business_group = #{request.businessGroup}

        <if test="request.organizationIds !=null and request.organizationIds.size != 0">
            and (

                <foreach collection="request.organizationIds" open="abdr.area_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="request.organizationIds" open="abdr.varea_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="request.organizationIds" open="abdr.province_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="request.organizationIds" open="abdr.company_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>

                or
                <foreach collection="request.organizationIds" open="abdr.department_id in (" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            )
        </if>
        <choose>
            <when test="request.organizationType == 'area'">
                and abdr.area_id = #{request.organizationId}
            </when>
            <when test="request.organizationType == 'varea'">
                and abdr.varea_id = #{request.organizationId}
            </when>
            <when test="request.organizationType == 'province'">
                and abdr.province_id = #{request.organizationId}
            </when>
            <when test="request.organizationType == 'company'">
                and abdr.company_id = #{request.organizationId}
            </when>
            <when test="request.organizationType == 'department'">
                and abdr.department_id = #{request.organizationId}
            </when>
            <when test="request.organizationType == 'branch'">
                and abdr.partner_member_key = #{request.memberKey}
            </when>
        </choose>

        <choose>
            <when test="request.sortName != null and request.sortName != ''">
                order by
                <choose>
                    <when test="request.sortName == 'entryDate'">
                        mon.onboard_time
                    </when>
                    <when test="request.sortName == 'dischargeDate'">
                        mon.off_time
                    </when>
                    <when test="request.sortName == 'visitTerminalAverageDaily'">
                        abdr.bfzds_average_day_place
                    </when>
                    <when test="request.sortName == 'displayTerminalAverageDaily'">
                        abdr.clzds_average_day_place
                    </when>
                    <when test="request.sortName == 'copyOrderCountAverageDaily'">
                        abdr.dds_average_day_place
                    </when>
                    <when test="request.sortName == 'copyOrderAmountAverageDaily'">
                        abdr.ddje_average_day_place
                    </when>
                    <when test="request.sortName == 'chargebackOrderCountAverageDaily'">
                        abdr.dds_average_day_refund
                    </when>
                    <when test="request.sortName == 'chargebackOrderAmountAverageDaily'">
                        abdr.ddje_average_day_refund
                    </when>
                    <otherwise>
                        abdr.bfzds_average_day_place
                    </otherwise>
                </choose>
                <choose>
                    <when test="request.sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by abdr.bfzds_average_day_place desc
            </otherwise>
        </choose>


    </select>

    <select id="advanceOrderBoardSkuNames" resultType="java.util.Map">

        select DISTINCT sku_id as skuId,concat_ws('_',sku_name,sku_full_case_num,flavor) as skuInfo from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
              and aodbt.the_year_month = #{req.yearMonth}
              and aodbt.organization_id = #{req.organizationId}
    </select>

    <select id="advanceOrderBoardLineNames" resultType="java.lang.String">
        select distinct line_name from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
          and aodbt.the_year_month = #{req.yearMonth}
          and aodbt.organization_id = #{req.organizationId}
    </select>

    <select id="advanceOrderBoardTagNames" resultType="java.lang.String">
        <!--null转换为横杠-->
        select distinct coalesce(tag_name,'-') from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
          and aodbt.the_year_month = #{req.yearMonth}
          and aodbt.organization_id = #{req.organizationId}
    </select>

    <select id="advanceOrderBoardAttributeTypes" resultType="java.lang.String">
        select distinct attributeType from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
          and aodbt.the_year_month = #{req.yearMonth}
          and aodbt.organization_id = #{req.organizationId}
    </select>

    <select id="advanceOrderBoardChannelNames" resultType="java.lang.String">
        select distinct channel_name from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
          and aodbt.the_year_month = #{req.yearMonth}
          and aodbt.organization_id = #{req.organizationId}
    </select>

    <resultMap id="advanceOrderPageMap" type="com.wantwant.sfa.backend.realData.vo.AdvanceOrderBoardPageListVo">
        <id column="advancedHashKey" property="advancedHashKey"></id>
        <result column="skuImages" property="skuImages"></result>
        <result column="skuId" property="skuId"></result>
        <result column="skuName" property="skuName"></result>
        <result column="skuFullCaseNum" property="skuFullCaseNum"></result>
        <result column="flavor" property="flavor"></result>
        <result column="lineName" property="lineName"></result>
        <result column="tagName" property="tagName"></result>
        <result column="attributeType" property="attributeType"></result>
        <result column="channelName" property="channelName"></result>

        <result column="estimateDeliverPerformance_byfhydd" property="estimateDeliverPerformance_byfhydd"></result>
        <result column="estimateDeliverBox_byfhydd" property="estimateDeliverBox_byfhydd"></result>
        <result column="actualDeliverPerformance_byfhydd" property="actualDeliverPerformance_byfhydd"></result>
        <result column="actualDeliverBox_byfhydd" property="actualDeliverBox_byfhydd"></result>
        <result column="notDeliverPerformance_byfhydd" property="notDeliverPerformance_byfhydd"></result>
        <result column="notDeliverBox_byfhydd" property="notDeliverBox_byfhydd"></result>
        <result column="deliverSatisfyRate_byfhydd" property="deliverSatisfyRate_byfhydd"></result>


        <result column="estimateDeliverPerformance_bysyyddbyfh" property="estimateDeliverPerformance_bysyyddbyfh"></result>
        <result column="estimateDeliverBox_bysyyddbyfh" property="estimateDeliverBox_bysyyddbyfh"></result>
        <result column="actualDeliverPerformance_bysyyddbyfh" property="actualDeliverPerformance_bysyyddbyfh"></result>
        <result column="actualDeliverBox_bysyyddbyfh" property="actualDeliverBox_bysyyddbyfh"></result>
        <result column="notDeliverPerformance_bysyyddbyfh" property="notDeliverPerformance_bysyyddbyfh"></result>
        <result column="notDeliverBox_bysyyddbyfh" property="notDeliverBox_bysyyddbyfh"></result>
        <result column="deliverSatisfyRate_bysyyddbyfh" property="deliverSatisfyRate_bysyyddbyfh"></result>
        <result column="promiseActualDeliverBox_bysyyddbyfh" property="promiseActualDeliverBox_bysyyddbyfh"></result>
        <result column="promiseDeliverSatisfyRate_bysyyddbyfh" property="promiseDeliverSatisfyRate_bysyyddbyfh"></result>


        <result column="estimateDeliverPerformance_byydd_byfh" property="estimateDeliverPerformance_byydd_byfh"></result>
        <result column="estimateDeliverBox_byydd_byfh" property="estimateDeliverBox_byydd_byfh"></result>
        <result column="actualDeliverPerformance_byydd_byfh" property="actualDeliverPerformance_byydd_byfh"></result>
        <result column="actualDeliverBox_byydd_byfh" property="actualDeliverBox_byydd_byfh"></result>
        <result column="notDeliverPerformance_byydd_byfh" property="notDeliverPerformance_byydd_byfh"></result>
        <result column="notDeliverBox_byydd_byfh" property="notDeliverBox_byydd_byfh"></result>
        <result column="deliverSatisfyRate_byydd_byfh" property="deliverSatisfyRate_byydd_byfh"></result>

        <result column="estimateDeliverPerformance_syydd_byfh" property="estimateDeliverPerformance_syydd_byfh"></result>
        <result column="estimateDeliverBox_syydd_byfh" property="estimateDeliverBox_syydd_byfh"></result>
        <result column="actualDeliverPerformance_syydd_byfh" property="actualDeliverPerformance_syydd_byfh"></result>
        <result column="actualDeliverBox_syydd_byfh" property="actualDeliverBox_syydd_byfh"></result>
        <result column="notDeliverPerformance_syydd_byfh" property="notDeliverPerformance_syydd_byfh"></result>
        <result column="notDeliverBox_syydd_byfh" property="notDeliverBox_syydd_byfh"></result>
        <result column="deliverSatisfyRate_syydd_byfh" property="deliverSatisfyRate_syydd_byfh"></result>

        <result column="estimateDeliverPerformance_ycydd_byfh" property="estimateDeliverPerformance_ycydd_byfh"></result>
        <result column="estimateDeliverBox_ycydd_byfh" property="estimateDeliverBox_ycydd_byfh"></result>
        <result column="actualDeliverPerformance_ycydd_byfh" property="actualDeliverPerformance_ycydd_byfh"></result>
        <result column="actualDeliverBox_ycydd_byfh" property="actualDeliverBox_ycydd_byfh"></result>
        <result column="notDeliverPerformance_ycydd_byfh" property="notDeliverPerformance_ycydd_byfh"></result>
        <result column="notDeliverBox_ycydd_byfh" property="notDeliverBox_ycydd_byfh"></result>
        <result column="deliverSatisfyRate_ycydd_byfh" property="deliverSatisfyRate_ycydd_byfh"></result>

        <result column="advancedOrderUnshippedPerformance_cyfh" property="advancedOrderUnshippedPerformance_cyfh"></result>
        <result column="advancedOrderUnshippedBox_cyfh" property="advancedOrderUnshippedBox_cyfh"></result>


    </resultMap>

    <select id="queryAdvanceOrderPageList" resultMap="advanceOrderPageMap">
        select
            aodbt.sku_images as skuImages,
            aodbt.sku_id as skuId,
            aodbt.sku_name as skuName,
            aodbt.sku_full_case_num as skuFullCaseNum,
            aodbt.flavor as flavor,
            aodbt.line_name as lineName,
            aodbt.tag_name as tagName,
            aodbt.attributeType as attributeType,
            aodbt.channel_name as channelName,
            aodbt.advanced_hash_key as advancedHashKey,
            round(aodbt.estimate_deliver_performance_ydd,1) as estimateDeliverPerformance_byfhydd,
            round(aodbt.estimate_deliver_box_ydd,1) as estimateDeliverBox_byfhydd,
            round(aodbt.actual_deliver_performance_ydd,1) as actualDeliverPerformance_byfhydd,
            round(aodbt.actual_deliver_box_ydd,1) as actualDeliverBox_byfhydd,
            round(aodbt.not_deliver_performance_ydd,1) as notDeliverPerformance_byfhydd,
            round(aodbt.not_deliver_box_ydd,1) as notDeliverBox_byfhydd,
            round(aodbt.deliver_satisfy_rate_ydd*100,1) as deliverSatisfyRate_byfhydd,

            round(aodbt.estimate_deliver_performance_bysyyddbyfh,1) as estimateDeliverPerformance_bysyyddbyfh,
            round(aodbt.estimate_deliver_box_bysyyddbyfh,1)  as estimateDeliverBox_bysyyddbyfh,
            round(aodbt.actual_deliver_performance_bysyyddbyfh,1)  as actualDeliverPerformance_bysyyddbyfh,
            round(aodbt.actual_deliver_box_bysyyddbyfh,1) as actualDeliverBox_bysyyddbyfh,
            round(aodbt.not_deliver_performance_bysyyddbyfh,1)  as notDeliverPerformance_bysyyddbyfh,
            round(aodbt.not_deliver_box_bysyyddbyfh,1)  as notDeliverBox_bysyyddbyfh,
            round(aodbt.deliver_satisfy_rate_bysyyddbyfh*100,1) as deliverSatisfyRate_bysyyddbyfh,
            round(aodbt.promise_actual_deliver_box_bysyyddbyfh,1)  as promiseActualDeliverBox_bysyyddbyfh,
            round(aodbt.promise_deliver_satisfy_rate_bysyyddbyfh*100,1) as promiseDeliverSatisfyRate_bysyyddbyfh,

            round(aodbt.estimate_deliver_performance_byyddbyfh,1) as estimateDeliverPerformance_byydd_byfh,
            round(aodbt.estimate_deliver_box_byyddbyfh,1) as estimateDeliverBox_byydd_byfh,
            round(aodbt.actual_deliver_performance_byyddbyfh,1) as actualDeliverPerformance_byydd_byfh,
            round(aodbt.actual_deliver_box_byyddbyfh,1) as actualDeliverBox_byydd_byfh,
            round(aodbt.not_deliver_performance_byyddbyfh,1) as notDeliverPerformance_byydd_byfh,
            round(aodbt.not_deliver_box_byyddbyfh,1) as notDeliverBox_byydd_byfh,
            round(aodbt.deliver_satisfy_rate_byyddbyfh*100,1) as deliverSatisfyRate_byydd_byfh,


            round(aodbt.estimate_deliver_performance_syyddbyfh,1) as estimateDeliverPerformance_syydd_byfh,
            round(aodbt.estimate_deliver_box_syyddbyfh,1) as estimateDeliverBox_syydd_byfh,
            round(aodbt.actual_deliver_performance_syyddbyfh,1) as actualDeliverPerformance_syydd_byfh,
            round(aodbt.actual_deliver_box_syyddbyfh,1) as actualDeliverBox_syydd_byfh,
            round(aodbt.not_deliver_performance_syyddbyfh,1) as notDeliverPerformance_syydd_byfh,
            round(aodbt.not_deliver_box_syyddbyfh,1) as notDeliverBox_syydd_byfh,
            round(aodbt.deliver_satisfy_rate_syyddbyfh*100,1) as deliverSatisfyRate_syydd_byfh,

            round(aodbt.estimate_deliver_performance_ycyddbyfh,1) as estimateDeliverPerformance_ycydd_byfh,
            round(aodbt.estimate_deliver_box_ycyddbyfh,1) as estimateDeliverBox_ycydd_byfh,
            round(aodbt.actual_deliver_performance_ycyddbyfh,1) as actualDeliverPerformance_ycydd_byfh,
            round(aodbt.actual_deliver_box_ycyddbyfh,1) as actualDeliverBox_ycydd_byfh,
            round(aodbt.not_deliver_performance_ycyddbyfh,1) as notDeliverPerformance_ycydd_byfh,
            round(aodbt.not_deliver_box_ycyddbyfh,1) as notDeliverBox_ycydd_byfh,
            round(aodbt.deliver_satisfy_rate_ycyddbyfh*100,1) as deliverSatisfyRate_ycydd_byfh,

            round(aodbt.advanced_order_unshipped_performance_cyfh,1) as advancedOrderUnshippedPerformance_cyfh,
            round(aodbt.advanced_order_unshipped_box_cyfh,1) as advancedOrderUnshippedBox_cyfh

        from advanced_order_data_board_total aodbt
        where aodbt.business_group_id = #{req.businessGroup}
          and aodbt.the_year_month = #{req.yearMonth}

         <choose>
             <when test="req.positionTypeId !=null and req.positionTypeId !=''">
                 and
                <choose>

                    <when test="req.positionTypeId == 1">
                        aodbt.area_id = #{req.organizationId}
                    </when>
                    <when test="req.positionTypeId == 12">
                        aodbt.varea_id = #{req.organizationId}
                    </when>
                    <when test="req.positionTypeId == 11">
                        aodbt.province_id = #{req.organizationId}
                    </when>
                    <when test="req.positionTypeId == 2">
                        aodbt.company_id = #{req.organizationId}
                    </when>
                    <when test="req.positionTypeId == 10">
                        aodbt.department_id = #{req.organizationId}
                    </when>
                    <otherwise>
                        aodbt.organization_id = #{req.organizationId}
                    </otherwise>
                </choose>


             </when>
             <otherwise>
                 and aodbt.organization_id = #{req.organizationId}
             </otherwise>
         </choose>
        <if test="req.skuId !=null and req.skuId !=''">
            and aodbt.sku_id = #{req.skuId}
        </if>
        <if test="req.lineNames !=null and req.lineNames.size>0 ">
            <foreach collection="req.lineNames" item="item" separator="," open="and aodbt.line_name in(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.tagNames !=null and req.tagNames.size>0 ">
            and (
            aodbt.tag_name in
            <foreach collection="req.tagNames" item="item" separator="," open=" (" close=")">
                #{item}
            </foreach>

            <if test="req.tagNameFlag !=null and req.tagNameFlag ==0 ">
                or aodbt.tag_name is null
            </if>
            )
        </if>
        <if test="req.attributeTypes !=null and req.attributeTypes.size>0 ">
            <foreach collection="req.attributeTypes" item="item" separator="," open="and aodbt.attributeType in(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="req.channelNames !=null and req.channelNames.size>0 ">
            <foreach collection="req.channelNames" item="item" separator="," open="and aodbt.channel_name in(" close=")">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="req.sortName != null and req.sortName != ''">
                order by
                <choose>
                    <when test="req.sortName == 'estimateDeliverPerformance_byfhydd'">
                        aodbt.estimate_deliver_performance_ydd
                    </when>
                    <when test="req.sortName == 'actualDeliverPerformance_byfhydd'">
                        aodbt.actual_deliver_performance_ydd
                    </when>
                    <when test="req.sortName == 'notDeliverPerformance_byfhydd'">
                        aodbt.not_deliver_performance_ydd
                    </when>

                    <when test="req.sortName == 'estimateDeliverPerformance_bysyyddbyfh'">
                        aodbt.estimate_deliver_performance_bysyyddbyfh
                    </when>
                    <when test="req.sortName == 'actualDeliverPerformance_bysyyddbyfh'">
                        aodbt.actual_deliver_performance_bysyyddbyfh
                    </when>
                    <when test="req.sortName == 'notDeliverPerformance_bysyyddbyfh'">
                        aodbt.not_deliver_performance_bysyyddbyfh
                    </when>

                    <when test="req.sortName == 'estimateDeliverPerformance_byydd_byfh'">
                        aodbt.estimate_deliver_performance_byyddbyfh
                    </when>
                    <when test="req.sortName == 'actualDeliverPerformance_byydd_byfh'">
                        aodbt.actual_deliver_performance_byyddbyfh
                    </when>
                    <when test="req.sortName == 'notDeliverPerformance_byydd_byfh'">
                        aodbt.not_deliver_performance_byyddbyfh
                    </when>

                    <when test="req.sortName == 'estimateDeliverPerformance_syydd_byfh'">
                        aodbt.estimate_deliver_performance_syyddbyfh
                    </when>
                    <when test="req.sortName == 'actualDeliverPerformance_syydd_byfh'">
                        aodbt.actual_deliver_performance_syyddbyfh
                    </when>
                    <when test="req.sortName == 'notDeliverPerformance_syydd_byfh'">
                        aodbt.not_deliver_performance_syyddbyfh
                    </when>

                    <when test="req.sortName == 'estimateDeliverPerformance_ycydd_byfh'">
                        aodbt.estimate_deliver_performance_ycyddbyfh
                    </when>
                    <when test="req.sortName == 'actualDeliverPerformance_ycydd_byfh'">
                        aodbt.actual_deliver_performance_ycyddbyfh
                    </when>
                    <when test="req.sortName == 'notDeliverPerformance_ycydd_byfh'">
                        aodbt.not_deliver_performance_ycyddbyfh
                    </when>
                    <when test="req.sortName == 'advancedOrderUnshippedPerformance_cyfh'">
                        aodbt.advanced_order_unshipped_performance_cyfh
                    </when>
                    <otherwise>
                        aodbt.estimate_deliver_performance_ydd
                    </otherwise>

                </choose>
                <choose>
                    <when test="req.sortType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by aodbt.sku_id,aodbt.estimate_deliver_performance_ydd desc,aodbt.channel_name
            </otherwise>
        </choose>
    </select>


    <select id="queryAdvanceOrderDetailPageList" resultType="com.wantwant.sfa.backend.realData.vo.AdvanceOrderBoardPageDetailListVo">
        select
            di.employee_name as person,
            CONCAT_WS('/', aodbd.area_name ,aodbd.varea_name, aodbd.province_name,aodbd.company_name,aodbd.department_name) as fullOrganizationName,
            aodbd.code as code,
            aodbd.status as status,
            round(aodbd.estimate_deliver_box,1) as estimateDeliverBox,
            round(aodbd.estimate_deliver_performance,1) as estimateDeliverPerformance,

            DATE_FORMAT(aodbd.placedat, '%Y-%m-%d %H:%i:%s') as orderAt,
                <!--
                @ApiModelProperty("预计发货时间")
                private String estimateDeliveryAt,
                -->
            DATE_FORMAT(aodbd.sentToWMSAt,'%Y-%m-%d %H:%i:%s') as sentToWMSAt,
            DATE_FORMAT(aodbd.deliveringat,'%Y-%m-%d %H:%i:%s') as deliveryAt,
            DATE_FORMAT(hohei.arrival_time,'%Y-%m-%d') as estimateArrivalAt,
            DATE_FORMAT(aodbd.receivedAt,'%Y-%m-%d %H:%i:%s')  as receivedAt,
            DATE_FORMAT(aodbd.completeat,'%Y-%m-%d %H:%i:%s')  as completeAt
        from advanced_order_data_board_total aodbt
        left join advanced_order_data_board_detail aodbd
            <!--
                1.分组
                1）分仓：时间+产品组+sku+组织ID【不同层级不同字段，总部为全部】+仓
                2）全国仓：时间+产品组+sku+组织ID【不同层级不同字段，总部为全部】
                2.全组
                1）分仓：时间+sku+组织ID【不同层级不同字段，全组ID需和分组ID关联】+仓
                2）全国仓：时间+sku+组织ID【不同层级不同字段，全组ID需和分组ID关联】

                岗位类型id,4-总部,1-总督导,12-大区总监,11-省区总监,2-区域总监,10-区域经理,3-合伙人
            -->
            on aodbt.sku_id = aodbd.sku_id
                and aodbt.the_year_month = aodbd.the_year_month
                and (case when aodbt.channel_id = '0' then 1 = 1 else aodbt.channel_id = aodbd.channel_id end)
            <choose>
                <when test="req.businessGroup == 99">
                    and
                        (case
                        when aodbt.position_type_id = 1 then aodbd.area_id like CONCAT(aodbt.organization_id,'%')
                        when aodbt.position_type_id = 12 then aodbd.varea_id like CONCAT(aodbt.organization_id,'%')
                        when aodbt.position_type_id = 11 then aodbd.province_id like CONCAT(aodbt.organization_id,'%')
                        when aodbt.position_type_id = 2 then aodbd.company_id like CONCAT(aodbt.organization_id,'%')
                        when aodbt.position_type_id = 10 then aodbd.department_id like CONCAT(aodbt.organization_id,'%')
                        else 1 = 1 end)
                </when>
                <otherwise>
                    and aodbt.business_group_id = aodbd.business_group_id
                    and
                        (case
                            when aodbt.position_type_id = 1 then aodbt.organization_id = aodbd.area_id
                            when aodbt.position_type_id = 12 then aodbt.organization_id = aodbd.varea_id
                            when aodbt.position_type_id = 11 then aodbt.organization_id = aodbd.province_id
                            when aodbt.position_type_id = 2 then aodbt.organization_id = aodbd.company_id
                            when aodbt.position_type_id = 10 then aodbt.organization_id = aodbd.department_id
                            else 1 = 1 end)
                </otherwise>
            </choose>
        left join dim_emp_pos_role_org_mon_partner di
            on aodbd.the_year_month = di.the_year_month
                   and aodbd.memberkey = di.member_key
                    and di.job_type_id = 1
        left join ods.hp_order_header_extend_info hohei
            on aodbd.code = hohei.order_header_key
        where aodbt.advanced_hash_key = #{req.advancedHashKey}
        <if test="req.statusList !=null and req.statusList.size>0 ">
            <foreach collection="req.statusList" item="item" separator="," open="and aodbd.status in(" close=")">
                #{item}
            </foreach>
        </if>

        <choose>
            <when test="req.dataType == null or req.dataType == '' or req.dataType == 0">
                and aodbd.data_type in (1,2,3)
            </when>
            <when test="req.dataType == null or req.dataType == '' or req.dataType == 5">
                and aodbd.data_type in (1,2)
            </when>
            <otherwise>
                and aodbd.data_type = #{req.dataType}
            </otherwise>
        </choose>
        <choose>
        <!--
            aodbd.deliveringat
            发货时间判断，发货时间为本月（实际发货），发货时间为本月之后或为空（未发货）
            预计发货业绩:1 实际发货业绩:2  未发货业绩:3

            req.searchType ==1 不限制
        -->
            <when test="req.searchType ==2 ">
                and DATE_FORMAT(aodbd.deliveringat,'%Y-%m') = aodbd.the_year_month
            </when>
            <when test="req.searchType ==3 ">
                and (
                    aodbd.deliveringat is null or DATE_FORMAT(aodbd.deliveringat,'%Y-%m') > aodbd.the_year_month
                )
            </when>
        </choose>

        <choose>
            <when test="req.sortName != null and req.sortName != ''">
                order by
                <choose>
                    <when test="req.sortName == 'orderAt'">
                        aodbd.placedat
                    </when>
                    <when test="req.sortName == 'sentToWMSAt'">
                        aodbd.sentToWMSAt
                    </when>
                    <when test="req.sortName == 'deliveryAt'">
                        aodbd.deliveringat
                    </when>
                    <when test="req.sortName == 'estimateArrivalAt'">
                        hohei.arrival_time
                    </when>
                    <when test="req.sortName == 'receivedAt'">
                        aodbd.receivedAt
                    </when>
                    <when test="req.sortName == 'completeAt'">
                        aodbd.completeat
                    </when>
                    <otherwise>
                        aodbd.placedat
                    </otherwise>
                </choose>
                <choose>
                    <when test="req.sortType == 'asc'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by aodbd.placedat desc
            </otherwise>
        </choose>


    </select>


    <select id="queryPartnerInventoryCheckTrendInfo" resultType="com.wantwant.sfa.backend.realData.vo.PartnerInventoryCheckTrendQueryInfoVo">
        SELECT
            psict.product_granularity_value as queryId,
            CONCAT_WS('',sbi.sku_name,sbi.sku_spec,sbi.flavor) as queryInfo
        FROM ads_bigtable_partner_sku_inventory_check_trend psict
         inner join dim_store_sku_base_info sbi
             on psict.product_granularity_value = sbi.sku_id
         where psict.member_key = #{req.memberKey}
            and psict.the_year_month <![CDATA[ <= ]]> #{req.endDate}
            and psict.the_year_month >= #{req.startDate}
        <choose>
            <when test="req.queryType =='spu'">
                and psict.product_granularity = 2
            </when>
            <when test="req.queryType =='line'">
                and psict.product_granularity =3
            </when>
            <otherwise>
                and psict.product_granularity = 1
            </otherwise>
        </choose>
        group by 1,2
    </select>

    <select id="queryPartnerInventoryCheckTrendList" resultType="com.wantwant.sfa.backend.realData.vo.PartnerInventoryCheckTrendVo">
        select
            psict.the_year_month as yearMonth,
            psict.product_granularity_value as skuId,
            sbi.sku_name as skuName,
            psict.check_time as checkTime,
            round(psict.check_nums,1) as checkNums,
            round(psict.check_amount,1) as checkAmount
        from ads_bigtable_partner_sku_inventory_check_trend psict
        inner join dim_store_sku_base_info sbi
        on psict.product_granularity_value = sbi.sku_id
        where psict.product_granularity_value = #{req.queryInfo}
            and psict.member_key = #{req.memberKey}
            and psict.the_year_month <![CDATA[ <= ]]> #{req.endDate}
            and psict.the_year_month >= #{req.startDate}
            <choose>
                <when test="req.queryType =='spu'">
                    and psict.product_granularity = 2
                </when>
                <when test="req.queryType =='line'">
                    and psict.product_granularity =3
                </when>
                <otherwise>
                    and psict.product_granularity = 1
                </otherwise>
            </choose>
        order by psict.the_year_month

    </select>

    <select id="queryPartnerInventoryCheckList" resultType="com.wantwant.sfa.backend.realData.vo.PartnerInventoryCheckListVo">
        SELECT
            sbi.sku_images as skuImages,
            psici.product_granularity_value as skuId,
            sbi.sku_name as skuName,
            sbi.sku_spec as skuSpec,
            sbi.flavor as flavor,
            sbi.line_name as lineName,
            psici.this_check_time as thisCheckTime,
            round(psici.this_check_nums,1) as thisCheckNums,
            round(psici.this_check_amount,1) as  thisCheckAmount,
            psici.last_check_time as  lastCheckTime,
            round(psici.last_check_nums,1) as lastCheckNums,
            round(psici.last_check_amount,1) as lastCheckAmount
        FROM ads_bigtable_partner_sku_inventory_check_info psici
                 inner join dim_store_sku_base_info sbi
                            on psici.product_granularity_value = sbi.sku_id
        where psici.the_year_month = #{req.nearMonth}
          and psici.member_key = #{req.memberKey}
        <choose>
            <when test="req.queryType =='spu'">
                 and psici.product_granularity =2
            </when>
            <when test="req.queryType =='line'">
                 and psici.product_granularity =3
            </when>
            <otherwise>
                and psici.product_granularity = 1
            </otherwise>
        </choose>
        <choose>
            <when test="req.sortName != null and req.sortName != ''">
                order by
                <choose>
                    <when test="req.sortName == 'thisCheckNums'">
                        psici.this_check_nums
                    </when>
                    <when test="req.sortName == 'thisCheckAmount'">
                        psici.this_check_amount
                    </when>
                    <when test="req.sortName == 'lastCheckTime'">
                        psici.last_check_time
                    </when>
                    <when test="req.sortName == 'lastCheckNums'">
                        psici.last_check_nums
                    </when>
                    <otherwise>
                        psici.this_check_amount
                    </otherwise>
                </choose>
                <choose>
                    <when test="req.sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by psici.this_check_amount desc
            </otherwise>
        </choose>
    </select>
    <select id="getFiscalYearQuarters" resultType="java.lang.String">
        SELECT DISTINCT REPLACE(dtd.the_year_quarter, '-0', '-Q') as quarter FROM dim_td_date dtd where dtd.financial_year = #{yearMonth}
        order by quarter
    </select>
    <select id="queryPartnerGoalAttainmentList"
            resultType="com.wantwant.sfa.backend.realData.vo.PartnerGoalAttainmentVo">
        select
            abp.member_key as memberKey,
            abo.area_name as areaName,
            abo.varea_name as vareaName,
            abo.province_name as provinceName,
            abo.company_name as companyName,
            abo.department_name as departmentName,
            coalesce(NULLIF(CONCAT_WS('/', abo.area_name ,abo.varea_name, abo.province_name,abo.company_name,abo.department_name),'' ),abo.organization_name) as fullOrganizationName,
            dimp.pos_role_name as partnerType,
            dimp.pic_url as picUrl,
            dimp.employee_name as partnerName,
            abm.county_names as coverRegion,
            abp.rks_466 as population,
            abp.rksrjyj_320 as populationPerformanceAvg,

            round(abp.zwbppjyj_cur_405,1) as performance,
            round(abp.pjyjmb_378,1) as goal,
            round(abp.pjyjmbdcl_32*100,1) as performanceAchievementRate,
            round(abp.wjbyjzb_dq_506*100,1) as performanceWantGoldDiscountRatio,
            round(abp.zwbppjyj_cur_405_hb*100,1) as performanceChainRatio,
            round(abp.zwbppjyj_cur_405_tb*100,1) as performanceYearRatio,

            abp.dds_38 as orderQuantity,
            abp.bbfcs_571 as beingVisitedRecord,
            psici.this_check_time as thisCheckTime,
            psici.this_check_amount as thisCheckAmount,
            round(psici.sales_turnover_rate*100,1) as salesTurnoverRate

        from ads_bigtable_organization abo

        inner join ads_bigtable_partner abp
            on abp.department_id = abo.organization_id
            and abp.the_year_month = abo.the_year_month
            and abp.date_type_id = abo.date_type_id
        left join (
            SELECT
                mt.member_key,
                GROUP_CONCAT(mt.first_market_name)  as first_market_names,
                GROUP_CONCAT(mt.second_market_name) as second_market_names,
                GROUP_CONCAT(mt.market_name) as county_names
            FROM
                dim_market_partner_three_scope_mapping mt
            GROUP BY
                member_key
        )abm on abm.member_key = abp.member_key
        left join dim_emp_pos_role_org_mon_partner dimp
             on dimp.member_key = abp.member_key
             and dimp.the_year_month = #{req.nearMonth}
        left join ads_bigtable_partner_sku_inventory_check_info psici
             on psici.member_key  = abp.member_key
             and psici.product_granularity = 4
             and psici.the_year_month = #{req.nearMonth}
        where abo.date_type_id = #{req.dateTypeId}
          and abo.the_year_month = #{req.yearMonth}
          and abo.business_group_id = #{req.businessGroup}

          and abo.position_type_id = 10

        <if test="req.coverRegion !=null and req.coverRegion !=''">
            and (
                    abm.county_names like CONCAT('%',#{req.coverRegion},'%')
                or abm.first_market_names like CONCAT('%',#{req.coverRegion},'%')
                or abm.second_market_names like CONCAT('%',#{req.coverRegion},'%')

                )
        </if>
        <if test="req.partnerType !=null and req.partnerType !=''">
            and dimp.pos_role_name = #{req.partnerType}
        </if>

        <if test="req.organizationIds !=null and req.organizationIds.size != 0">
            and (

            <foreach collection="req.organizationIds" open="abo.area_id in (" close=")" item="item" separator=",">
                #{item}
            </foreach>

            or
            <foreach collection="req.organizationIds" open="abo.varea_id in (" close=")" item="item" separator=",">
                #{item}
            </foreach>

            or
            <foreach collection="req.organizationIds" open="abo.province_id in (" close=")" item="item" separator=",">
                #{item}
            </foreach>

            or
            <foreach collection="req.organizationIds" open="abo.company_id in (" close=")" item="item" separator=",">
                #{item}
            </foreach>

            or
            <foreach collection="req.organizationIds" open="abo.department_id in (" close=")" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <choose>
            <when test="req.organizationType=='department'">
                and abo.organization_id = #{req.organizationId}
            </when>
            <when test="req.organizationType=='company'">
                and abo.company_id = #{req.organizationId}
            </when>
            <when test="req.organizationType=='province'">
                and abo.province_id = #{req.organizationId}
            </when>
            <when test="req.organizationType=='varea'">
                and abo.varea_id = #{req.organizationId}
            </when>
            <when test="req.organizationType=='area'">
                and abo.area_id = #{req.organizationId}
            </when>
        </choose>

        <choose>
            <when test="req.sortName != null and req.sortName != ''">
                order by
                <choose>
                    <when test="req.sortName == 'performance'">
                        abp.zwbppjyj_cur_405
                    </when>

                    <otherwise>
                        abp.zwbppjyj_cur_405
                    </otherwise>
                </choose>
                <choose>
                    <when test="req.sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by abp.zwbppjyj_cur_405 desc
            </otherwise>
        </choose>



    </select>
    <select id="partnerGoalAttainmentOrderList"
            resultType="com.wantwant.sfa.backend.realData.vo.PartnerGoalAttainmentOrderVo">

        select
            osdc.memberKey,
            osdc.code as orderNo,
            case when osdc.status = 'COMPLETE' then '已完成'
                 when osdc.status = 'RECEIVED' then '已收货'
                 when osdc.status = 'DELIVERING' then '待收货'
                 when osdc.status = 'PROCESSING' then '待发货'
            end as orderStatus,
            round(sum(osdc.supply_price_total),1) as performance,
            round(sum(case when type = 3 then osdc.supply_price_total else 0 end),1) as normalOrderPerformance,
            round(sum(case when type = 999 then osdc.supply_price_total else 0 end),1) as bookingPerformance,
            round(sum(case when type = 90 then osdc.supply_price_total else 0 end),1) as specialOrderPerformance,
            round(sum(osdc.free_total_amount) / sum(osdc.supply_price_total)*100,1) as performanceWantGoldDiscountRatio
        from dws.order_sku_detail_cur osdc
        where osdc.supply_price_total >0
          and osdc.type in (3,999,88,90)
          and itemType = 0
          and osdc.memberKey = #{req.memberKey}
          and osdc.the_year_month BETWEEN #{req.startMonth} and #{req.endMonth}
          <if test="req.orderStatus !=null and req.orderStatus!=''">
              and osdc.status = #{req.orderStatus}
          </if>
        group by 1,2,3
        <choose>
            <when test="req.sortName != null and req.sortName != ''">
                order by
                <choose>
                    <when test="req.sortName == 'performance'">
                        performance
                    </when>
                    <when test="req.sortName == 'normalOrderPerformance'">
                        normalOrderPerformance
                    </when>
                    <when test="req.sortName == 'bookingPerformance'">
                        bookingPerformance
                    </when>
                    <when test="req.sortName == 'specialOrderPerformance'">
                        specialOrderPerformance
                    </when>
                    <when test="req.sortName == 'performanceWantGoldDiscountRatio'">
                        performanceWantGoldDiscountRatio
                    </when>
                    <otherwise>
                        performance
                    </otherwise>
                </choose>
                <choose>
                    <when test="req.sortOrder == 'ASC'">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by performance desc
            </otherwise>
        </choose>
    </select>
    <select id="queryLastWeekJobConditionInfoVo"
            resultType="com.wantwant.sfa.backend.workReport.vo.WorkReportLastWeekJobConditionInfoVo">
        select
            round(own_pmws.check_rate*100,1) as attendanceAchieving,
            round(other_pmws.check_rate*100,1) as attendanceSamePostAvg,
            round((own_pmws.check_rate - other_pmws.check_rate)*100,1) as attendanceDifference,

            round(own_pmws.complete_rate*100,1) as completeAchieving,
            round(other_pmws.complete_rate*100,1) as completeSamePostAvg,
            round((own_pmws.complete_rate-other_pmws.complete_rate)*100,1) as completeDifference,

            round(own_pmws.meet_hold_rate*100,1) as dailyMeetingAchieving,
            round(other_pmws.meet_hold_rate*100,1) as dailyMeetingSamePostAvg,
            round((own_pmws.meet_hold_rate - other_pmws.meet_hold_rate)*100,1) as dailyMeetingDifference,

            own_pmws.vis_par_nums_avg as visitPartnerAchieving,
            other_pmws.vis_par_nums_avg as visitPartnerSamePostAvg,
            round(own_pmws.vis_par_nums_avg - other_pmws.vis_par_nums_avg,1) as visitPartnerDifference,

            own_pmws.vis_cus_nums_avg as visitCustomerAchieving,
            other_pmws.vis_cus_nums_avg as visitCustomerSamePostAvg,
            round(own_pmws.vis_cus_nums_avg-other_pmws.vis_cus_nums_avg,1) as visitCustomerDifference

        from ads_sfa_process_management_week_summary own_pmws
                 left join ads_sfa_process_management_week_summary other_pmws
                           on own_pmws.the_year_month = other_pmws.the_year_month
                               and own_pmws.weeks  = other_pmws.weeks
                               and  own_pmws.`type` = 1 and other_pmws.`type` = 2
                               and own_pmws.pos_role_id  = other_pmws.pos_role_id
                               and own_pmws.business_group_id = other_pmws.business_group_id
        where
            own_pmws.the_year_month = #{yearMonth}
          and own_pmws.organization_code = #{organizationId}
          and own_pmws.weeks = #{weeks}
          and own_pmws.employee_info_id = #{employeeInfoId}

    </select>

    <select id="queryReportPeriodJobConditionInfo"
            resultType="com.wantwant.sfa.backend.review.vo.ReportPeriodJobConditionInfoVo">
        select
            round(own_pmws.attendance_rate*100,1) as attendanceAchieving,
            round(other_pmws.attendance_rate*100,1) as attendanceSamePostAvg,
            round((own_pmws.attendance_rate - other_pmws.attendance_rate)*100,1) as attendanceDifference,

            round(own_pmws.through_rate*100,1) as completeAchieving,
            round(other_pmws.through_rate*100,1) as completeSamePostAvg,
            round((own_pmws.through_rate-other_pmws.through_rate)*100,1) as completeDifference,

            round(own_pmws.hold_mmeet_rate*100,1) as meetingAchieving,
            round(other_pmws.hold_mmeet_rate*100,1) as meetingSamePostAvg,
            round((own_pmws.hold_mmeet_rate - other_pmws.hold_mmeet_rate)*100,1) as meetingDifference,

            own_pmws.visit_par_nums as visitPartnerAchieving,
            other_pmws.visit_par_nums as visitPartnerSamePostAvg,
            round(own_pmws.visit_par_nums - other_pmws.visit_par_nums,1) as visitPartnerDifference,

            own_pmws.visit_cus_nums as visitCustomerAchieving,
            other_pmws.visit_cus_nums as visitCustomerSamePostAvg,
            round(own_pmws.visit_cus_nums-other_pmws.visit_cus_nums,1) as visitCustomerDifference

        from ads_sfa_process_management_month_report own_pmws
                 left join ads_sfa_process_management_month_report other_pmws
                           on own_pmws.the_year_month = other_pmws.the_year_month
                               and  own_pmws.`data_type` = 1 and other_pmws.`data_type` = 2
                               and own_pmws.pos_role_id  = other_pmws.pos_role_id
                               and own_pmws.business_group_id = other_pmws.business_group_id
        where
            date_format(own_pmws.the_date, '%Y-%m') = #{yearMonth}
          and own_pmws.organization_code = #{organizationId}
          and own_pmws.employee_info_id = #{employeeInfoId}
    </select>

    <select id="queryWholeOrganizationInfo"
            resultType="com.wantwant.sfa.backend.realData.dto.WholeOrganizationInfoDto">

        select

        from ads_bigtable_organization abo
        where abo.organization_id in
        <foreach collection="organizationIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and abo.the_year_month = #{yearMonth}
        and abo.date_type_id = #{dateTypeId}
    </select>


</mapper>



