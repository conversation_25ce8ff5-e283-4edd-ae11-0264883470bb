<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.wantwant</groupId>
	<artifactId>sfa-backend</artifactId>
	<packaging>pom</packaging>
	<version>1.0.0-SNAPSHOT</version>

	<modules>
		<module>wantwant-${application.name}-api</module>
		<module>wantwant-${application.name}-webapi</module>
		<module>wantwant-${application.name}-service</module>
	</modules>

	<!-- Maven 仓库 -->
	<distributionManagement>
		<repository>
			<id>central</id>
			<name>shqdg001-releases</name>
			<url>https://monitor.hotkidceo.com/artifactory/wantwant-snapshots</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>shqdg001-snapshots</name>
			<url>https://monitor.hotkidceo.com/artifactory/wantwant-snapshots</url>
		</snapshotRepository>
	</distributionManagement>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.2.RELEASE</version>
	</parent>

	<!-- 依赖版本管理 -->
	<properties>
		<application.name>sfa-backend</application.name>
		<package.name>sfa.backend</package.name>

		<sfa.common.version>2.4.1-RELEASE</sfa.common.version>

		<internal.depend.version>1.0.0-SNAPSHOT</internal.depend.version>
		<commons.core.version>1.5.1</commons.core.version>
		<sfa.log.version>1.1.0</sfa.log.version>
		<org.mapstruct.version>1.3.1.Final</org.mapstruct.version>
		<hutool.version>5.8.16</hutool.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<spring-boot.version>2.2.5.RELEASE</spring-boot.version>
		<spring.version>5.1.18.RELEASE</spring.version>
		<mybatisplus.version>3.0.7.1</mybatisplus.version>
		<mysql.version>5.1.38</mysql.version>
		<mssql.version>4.0</mssql.version>
		<druid.version>1.1.13</druid.version>
		<quartz.version>2.3.0</quartz.version>
		<commons.lang.version>2.6</commons.lang.version>
		<commons.fileupload.version>1.3.3</commons.fileupload.version>
		<commons.io.version>2.5</commons.io.version>
		<commons.codec.version>1.10</commons.codec.version>
		<commons.configuration.version>1.10</commons.configuration.version>
		<swagger.version>2.6.1</swagger.version>
		<swagger.ui.version>1.9.6</swagger.ui.version>
		<swagger.annotations.version>1.5.20</swagger.annotations.version>
		<joda.time.version>2.9.9</joda.time.version>
		<fastjson.version>1.2.69</fastjson.version>
		<lombok.version>1.18.4</lombok.version>
		<pagehelper.version>1.2.5</pagehelper.version>
		<common-util.version>1.0.3</common-util.version>
		<commons-lang3.version>3.8.1</commons-lang3.version>
		<rocketmq.version>4.3.0</rocketmq.version>
		<shard-jdbc.version>3.1.0</shard-jdbc.version>
		<redis-client.version>2.9.1</redis-client.version>
		<mail.version>1.5.0-b01</mail.version>
		<commons.text.version>1.6</commons.text.version>
		<commons.collections4.version>4.2</commons.collections4.version>
		<mapstruct.version>1.3.1.Final</mapstruct.version>
		<jackson.version>2.10.0</jackson.version>
	</properties>

	<!-- 依赖管理 -->
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-web</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-data-redis</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-aop</artifactId>
				<version>${spring-boot.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-openfeign</artifactId>
				<version>2.1.3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-netflix-zuul</artifactId>
				<version>2.1.3.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context-support</artifactId>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-configuration-processor</artifactId>
				<optional>true</optional>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatisplus.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.baomidou</groupId>
						<artifactId>mybatis-plus-generator</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus</artifactId>
				<version>${mybatisplus.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper-spring-boot-starter</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid-spring-boot-starter</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>org.quartz-scheduler</groupId>
				<artifactId>quartz</artifactId>
				<version>${quartz.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.mchange</groupId>
						<artifactId>c3p0</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>commons-lang</groupId>
				<artifactId>commons-lang</artifactId>
				<version>${commons.lang.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons.fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-codec</groupId>
				<artifactId>commons-codec</artifactId>
				<version>${commons.codec.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-configuration</groupId>
				<artifactId>commons-configuration</artifactId>
				<version>${commons.configuration.version}</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger.version}</version>
			</dependency>
			<dependency>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-annotations</artifactId>
				<version>${swagger.annotations.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>swagger-bootstrap-ui</artifactId>
				<version>${swagger.ui.version}</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>${joda.time.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons-lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-text</artifactId>
				<version>${commons.text.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons.collections4.version}</version>
			</dependency>

			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct-processor</artifactId>
				<version>${mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>com.codingapi.txlcn</groupId>
				<artifactId>txlcn-tm</artifactId>
				<version>5.0.2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.codingapi.txlcn</groupId>
				<artifactId>txlcn-tc</artifactId>
				<version>5.0.2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.codingapi.txlcn</groupId>
				<artifactId>txlcn-txmsg-netty</artifactId>
				<version>5.0.2.RELEASE</version>
			</dependency>
			<!--com.alibaba.cloud -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>2.1.2.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<!-- 插件管理 -->
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>