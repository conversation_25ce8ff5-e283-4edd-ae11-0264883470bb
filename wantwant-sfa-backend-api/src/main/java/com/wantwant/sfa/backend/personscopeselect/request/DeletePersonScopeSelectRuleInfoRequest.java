package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 14:26
 */
@Data
public class DeletePersonScopeSelectRuleInfoRequest {
    @ApiModelProperty("id")
    @NotNull(message = "PERSON_SELECTOR_RULE_ID_MUST_NOT_NULL")
    private Long id;

    @ApiModelProperty("用户id")
    @NotNull(message = "COMMON_PERSON_MUST_NOT_NULL")
    private String employeeId;

    @ApiModelProperty("用户姓名：前端无可不传")
    private String employeeName;
}
