package com.wantwant.sfa.backend.order.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel(value = "订单详情")
public class OrderDetailNewRequest implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;
    @ApiModelProperty(value = "业务组", hidden = true)
    private Integer personBusinessGroup;

    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String code;

    @ApiModelProperty(value = "时区", hidden = true)
    private String timeZone;
}
