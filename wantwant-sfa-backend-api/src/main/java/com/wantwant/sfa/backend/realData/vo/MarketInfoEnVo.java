package com.wantwant.sfa.backend.realData.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2024/04/23/下午3:09
 */
@Data
@ApiModel("市场信息VO")
public class MarketInfoEnVo {
    @ExcelIgnore
    private String id;

    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ApiModelProperty("战区")
    @Excel(name = "Campaign Zone")
    private String areaName;

    @ApiModelProperty("组织ID")
    @ExcelIgnore
    private String organizationId;

    @ApiModelProperty("大区")
    @Excel(name = "Area")
    private String vareaName;

    @ApiModelProperty("省区")
    @Excel(name = "Province Area")
    private String provinceName;

    @ApiModelProperty("分公司")
    @Excel(name = "Branch Company")
    private String companyName;

    @ApiModelProperty("营业所")
    @Excel(name = "Business Office")
    private String departmentName;

    @ApiModelProperty("成员数量")
    @Excel(name = "Member Count")
    private String memberCount;

    @ApiModelProperty("岗位")
    @Excel(name = "Position")
    private String position;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("名称")
    @Excel(name = "Name")
    private String employeeName;

    @ApiModelProperty(value = "人员状态")
    @ExcelIgnore
    private String employeeStatus;

    @ApiModelProperty("在职天数")
    @Excel(name = "Working Days")
    private Long onBoardDate;

    @ApiModelProperty(value = "入职日期")
    @ExcelIgnore
    private String onboardTime;

    @ApiModelProperty(value = "离职日期")
    @ExcelIgnore
    private String dischargeDate;

    @ApiModelProperty(value = "员工表ID")
    @ExcelIgnore
    private Integer employeeInfoId;

    @ApiModelProperty("县级市场数")
    @Excel(name = "County Market Count", numFormat = "0.0")
    private BigDecimal countryMarketCount;

    @ApiModelProperty("区/县")
    @Excel(name = "District/County")
    private String country;

    @ApiModelProperty("人口数")
    @Excel(name = "Population")
    private BigDecimal population;

    @ApiModelProperty("县级市场覆盖率")
    @Excel(name = "Country Market Coverage Rate", suffix = "%", numFormat = "0.0")
    private BigDecimal countryMarketCoverage;

    @ApiModelProperty("人口数人均业绩")
    @ExcelIgnore
    private BigDecimal populationPerformanceAvg;

    @ApiModelProperty("业绩")
    @Excel(name = "Performance", numFormat = "0.0")
    private BigDecimal supplyPriceTotal;

    @ApiModelProperty("业绩同比")
    @Excel(name = "Performance YoY", suffix = "%", numFormat = "0.0")
    private BigDecimal supplyPriceTotalYoy;

    @ApiModelProperty(value = "业绩目标-月目标")
    @Excel(name = "Performance Target", numFormat = "0.0")
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    @Excel(name = "Performance Target Achievement Rate", suffix = "%", numFormat = "0.0")
    private BigDecimal performanceAchievementRate;

    @ApiModelProperty("交易客户数")
    @Excel(name = "Number of Trading Customers", numFormat = "0")
    private BigDecimal tradCus;

    @ApiModelProperty("交易客户数同比")
    @Excel(name = "Number of Trading Customers YoY", suffix = "%", numFormat = "0.0")
    private BigDecimal tradCusYoy;

    @ApiModelProperty("客单价")
    @Excel(name = "Average Order Value", numFormat = "0.0")
    private BigDecimal cusPrice;


    @ApiModelProperty("客单价同比")
    @Excel(name = "Average Order Value YoY", suffix = "%", numFormat = "0.0")
    private BigDecimal cusPriceYoy;

    @ApiModelProperty("是否有子集")
    @ExcelIgnore
    private Boolean hasChildren;

    @ExcelIgnore
    @ApiModelProperty(value = "总计信息标示", hidden = true)
    private Boolean totalInfo;


    @ApiModelProperty(value = "面试流程ID")
    @ExcelIgnore
    private Integer interviewRecordId;
    @ApiModelProperty(value = "岗位类型ID")
    @ExcelIgnore
    private Integer positionTypeId;

    @ApiModelProperty(value = "业绩达成率颜色标识:performanceAchievementRate")
    @ExcelIgnore
    private String saleGoalAchievementColor;
}
