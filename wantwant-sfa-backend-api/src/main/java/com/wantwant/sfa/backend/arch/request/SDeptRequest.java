package com.wantwant.sfa.backend.arch.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/09/19/下午3:56
 */
@Data
@ApiModel("部门查询request")
public class SDeptRequest {
    @ApiModelProperty("部门名称")
    private String deptName;
    @ApiModelProperty("是否停用:1.正常 0.停用")
    private Integer status;
    @ApiModelProperty("创建开始日期：格式yyyy-MM-dd")
    private String createStartDate;
    @ApiModelProperty("创建结束日期：格式yyyy-MM-dd")
    private String createEndDate;
}
