package com.wantwant.sfa.backend.complete.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("通关列表返回")
public class CompleteListEnVo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("通关日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    @Excel(name = "Date", replace = {"-_null"}, exportFormat = "yyyy-MM-dd")
    private LocalDate completeDate;

    @ApiModelProperty("组别")
    private Integer businessGroup;

    @ApiModelProperty("组织Code")
    private String organizationId;

    @ApiModelProperty("组织Code")
    private String organizationCode;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("战区名称")
    @Excel(name = "Compaign Zone", replace = {"-_null"})
    private String areaName;

    @ApiModelProperty("大区名称")
    @Excel(name = "Area", replace = {"-_null"})
    private String vareaName;

    @ApiModelProperty("省区名称")
    @Excel(name = "Province Area", replace = {"-_null"})
    private String provinceName;

    @ApiModelProperty("分公司名称")
    @Excel(name = "Branch Company", replace = {"-_null"})
    private String companyName;

    @ApiModelProperty("营业所名称")
    @Excel(name = "Branch Office", replace = {"-_null"})
    private String departmentName;

    @ApiModelProperty("岗位")
    private Long positionTypeId;
    @ApiModelProperty("岗位名称")
    @Excel(name = "Position", replace = {"-_null"})
    private String positionName;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("姓名")
    @Excel(name = "Name", replace = {"-_null"})
    private String employeeName;

    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("员工表id")
    private Integer employeeInfoId;

    @ApiModelProperty("入职日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    @Excel(name = "Onboarding Date", replace = {"-_null"}, exportFormat = "yyyy-MM-dd")
    private LocalDate onboardTime;

    @ApiModelProperty("通关规则ID")
    private Long ruleId;

    @ApiModelProperty("通关编号")
    @Excel(name = "Pass Number", replace = {"-_null"})
    private String completeNum;

    @ApiModelProperty("通关开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime completeStartTime;

    @ApiModelProperty("通关结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime completeEndTime;

    @ApiModelProperty("通关间隔小时数")
    private long intervalHours;

    @ApiModelProperty("通关间隔分钟数")
    private long intervalMinutes;

    @ApiModelProperty("通关时间段")
    @Excel(name = "Customs Clearance Time Period", replace = {"-_null"})
    private String completeTimePeriod;

    @ApiModelProperty("通关打卡时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @Excel(name = "Business time check in", replace = {"-_null"}, exportFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    private String longitude;

    private String latitude;

    private String address;

    @ApiModelProperty("通关状态")
    private Integer completeStatus;
    @ApiModelProperty("通关状态名称")
    @Excel(name = "Pass Status", replace = {"-_null"})
    private String completeStatusName;

    @ApiModelProperty("当时人员状态")
    private Integer employeeStatus;
    @ApiModelProperty("当时人员状态名称")
    @Excel(name = "Current Status", replace = {"-_null"})
    private String employeeStatusName;

    private String image;

    private String imageName;

    @ApiModelProperty("稽核状态")
    private Integer auditStatus;
    @ApiModelProperty("稽核状态名称")
    @Excel(name = "Audit Status", replace = {"-_null"})
    private String auditStatusName;

    @ApiModelProperty("稽核异常原因")
    @Excel(name = "Exception Reason", replace = {"-_null"})
    private String reason;

    @ApiModelProperty("稽核人工号")
    private String auditEmployeeId;
    @ApiModelProperty("稽核人")
    @Excel(name = "Operator", replace = {"-_null"})
    private String auditEmployeeName;

    @ApiModelProperty("组别")
    @Excel(name = "Business Group", replace = {"-_null"})
    private String businessGroupName;

    // 按钮权限
    @ApiModelProperty("稽核按钮是否高亮")
    private boolean bShowBtn1 = false;

    @ApiModelProperty("是否出差")
    private Boolean isTrip;


    @ApiModelProperty("对比相似度照片")
    private String signUpPicUrl;

    @ApiModelProperty("对比度")
    private BigDecimal faceSimilarScore;

    @ApiModelProperty("0-主岗 1-兼岗")
    private Integer partTime;

}
