package com.wantwant.sfa.backend.taskManagement.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 驳回
 * LEO的待确认完结、项目管理（杜颖、孙帆）的待审核，新增驳回按钮
 */
@Data
@ApiModel("任务驳回request")
@ToString
public class TaskRefuseRequest extends TaskOperatorRequest {

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;
}
