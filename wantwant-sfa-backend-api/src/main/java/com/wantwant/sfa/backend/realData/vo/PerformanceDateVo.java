package com.wantwant.sfa.backend.realData.vo;

import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel(value = "新实时数据 业绩数据返回")
public class PerformanceDateVo {

    @ApiModelProperty(value = "是否展示薪资(0.否；1.是)APP用")
    private int isPay;

    @ApiModelProperty(value = "头像")
    private String url;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "员工表ID")
    private Integer employeeInfoId;

    @ApiModelProperty(value = "业务名称")
    private String employeeName;

    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    @ApiModelProperty(value = "简历")
    private String resumeUrl;

    @ApiModelProperty(value = "出生日期")
    private String birthDate;

    @ApiModelProperty(value = "合伙人类型")
    private String type;

    @ApiModelProperty(value = "人员状态")
    private Integer employeeStatusEnum;

    @ApiModelProperty(value = "人员状态")
    private String employeeStatus;

    @ApiModelProperty(value = "签约公司")
    private String joiningCompany;

    @ApiModelProperty(value = "入职日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate onboardTime;

    @ApiModelProperty(value = "在职天数")
    private String onboardDays;

    @ApiModelProperty(value = "岗位id")
    private String positionId;

    @ApiModelProperty(value = "岗位类型")
    private Integer positionTypeId;

    @ApiModelProperty(value = "岗位类型描述")
    private String positionName;

    @ApiModelProperty(value = "当时业绩")
    @PerformanceValue(serialNumber = "30")
    private BigDecimal thenPerformance;

    @ApiModelProperty(value = "业绩")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performance;

    @ApiModelProperty("业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal performanceWantGoldDiscountRatio;

    @ApiModelProperty(value = "当日业绩")
    private BigDecimal dayPerformance;

    @ApiModelProperty(value = "业绩目标-月目标")
    @PerformanceValue(serialNumber = "378")
    private BigDecimal goal;

    @ApiModelProperty(value = "业绩达成率")
    @PerformanceValue(serialNumber = "32")
    private BigDecimal performanceAchievementRate;

    @ApiModelProperty(value = "业绩环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceChainRatio;

    @ApiModelProperty(value = "业绩同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performanceYearRatio;

    @ApiModelProperty(value = "管理岗在职人数")
    @PerformanceValue(serialNumber = "329")
    private Integer managementOnJobNum;

    @ApiModelProperty(value = "管理岗在职人数环比")
    @PerformanceValue(serialNumber = "329")
    private BigDecimal managementOnJobNumChainRatio;

    @ApiModelProperty(value = "管理岗在职人数同比")
    @PerformanceValue(serialNumber = "329")
    private BigDecimal managementOnJobNumYearRatio;

    @ApiModelProperty(value = "管理岗人均业绩")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPerPerformance;

    @ApiModelProperty(value = "管理岗人均业绩环比")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPerPerformanceChainRatio;

    @ApiModelProperty(value = "管理岗人均业绩同比")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPerPerformanceYearRatio;

    @ApiModelProperty(value = "业务在职人数")
    @PerformanceValue(serialNumber = "589")
    private Integer salesmanOnJobNum;

    @ApiModelProperty(value = "业务在职人数-环比")
    @PerformanceValue(serialNumber = "589")
    private BigDecimal salesmanOnJobNumChainRatio;

    @ApiModelProperty(value = "业务在职人数-同比")
    @PerformanceValue(serialNumber = "589")
    private BigDecimal salesmanOnJobNumYearRatio;

    @ApiModelProperty(value = "业务人均业绩")
    @PerformanceValue(serialNumber = "590")
    private BigDecimal salesmanPerPerformance;

    @ApiModelProperty(value = "业务人均业绩-环比")
    @PerformanceValue(serialNumber = "590")
    private BigDecimal salesmanPerPerformanceChainRatio;

    @ApiModelProperty(value = "业务人均业绩-同比")
    @PerformanceValue(serialNumber = "590")
    private BigDecimal salesmanPerPerformanceYearRatio;


    /**
     * 本月预订单(本月发货)
     * 下单业绩【527】、已发货业绩【379】、未发货业绩【421】
     */
    @ApiModelProperty(value = "本月预订单(本月发货)-下单业绩")
    @PerformanceValue(serialNumber = "527")
    private BigDecimal curMonthAdvanceOrderCurMonthOrderPerformance;

    @ApiModelProperty(value = "本月预订单(本月发货)-已发货业绩")
    @PerformanceValue(serialNumber = "379")
    private BigDecimal curMonthAdvanceOrderCurrentMonthDeliveredPerformance;

    @ApiModelProperty(value = "本月预订单(本月发货)-未发货业绩")
    @PerformanceValue(serialNumber = "421")
    private BigDecimal curMonthAdvanceOrderCurMonthNotDeliveredPerformance;

    /**
     * 上月预定单(本月发货)
     * 下单业绩【528】、已发货业绩【504】、未发货业绩【529】
     */
    @ApiModelProperty(value = "上月预定单(本月发货)-下单业绩")
    @PerformanceValue(serialNumber = "528")
    private BigDecimal lastMonthAdvanceOrderCurMonthOrderPerformance;

    @ApiModelProperty(value = "上月预定单(本月发货)-已发货业绩")
    @PerformanceValue(serialNumber = "504")
    private BigDecimal lastMonthAdvanceOrderCurrentMonthDeliveredPerformance;

    @ApiModelProperty(value = "上月预定单(本月发货)-未发货业绩")
    @PerformanceValue(serialNumber = "529")
    private BigDecimal lastMonthAdvanceOrderCurMonthNotDeliveredPerformance;

    /**
     * 延迟预定单
     * 下单业绩【530】、已发货业绩【531】、未发货业绩【532】
     */
    @ApiModelProperty(value = "延迟预定单-下单业绩")
    @PerformanceValue(serialNumber = "530")
    private BigDecimal delayAdvanceOrderOrderPerformance;

    @ApiModelProperty(value = "延迟预定单-已发货业绩")
    @PerformanceValue(serialNumber = "531")
    private BigDecimal delayAdvanceOrderDeliveredPerformance;

    @ApiModelProperty(value = "延迟预定单-未发货业绩")
    @PerformanceValue(serialNumber = "532")
    private BigDecimal delayAdvanceOrderNotDeliveredPerformance;

    /**
     * 次月预定单(本月下单)
     * 下单业绩【422】
     */
    @ApiModelProperty(value = "次月预定单(本月下单)-下单业绩")
    @PerformanceValue(serialNumber = "422")
    private BigDecimal nextMonthAdvanceOrderCurMonthOrderPerformance;

    /**
     * 财务指标
     */

    @ApiModelProperty("财务指标-业务用人费用率")
    @PerformanceValue(serialNumber = "514")
    private BigDecimal businessStaffingExpenseRate;


    @ApiModelProperty("财务指标-整体折扣率")
    @PerformanceValue(serialNumber = "577")
    private BigDecimal overallDiscountRate;

    /**
     * 订单结构
     */


    @ApiModelProperty(value = "常态单业绩")
    @PerformanceValue(serialNumber = "380")
    private BigDecimal normalOrderPerformance;

    @ApiModelProperty(value = "常态单业绩--环比")
    @PerformanceValue(serialNumber = "380")
    private BigDecimal normalOrderPerformanceRatio;

    @ApiModelProperty(value = "常态单业绩--同比")
    @PerformanceValue(serialNumber = "380")
    private BigDecimal normalOrderPerformanceYearRatio;

    @ApiModelProperty(value = "常态单业绩占比")
    @PerformanceValue(serialNumber = "381")
    private BigDecimal normalOrderPerformanceRate;

    @ApiModelProperty(value = "常态单现金业绩")
    @PerformanceValue(serialNumber = "649")
    private BigDecimal normalOrderCashPerformance;

    @ApiModelProperty(value = "常态单旺金币折扣率")
    @PerformanceValue(serialNumber = "652")
    private BigDecimal normalOrderWantGoldDiscountRatio;



    @ApiModelProperty(value = "特通单业绩")
    @PerformanceValue(serialNumber = "382")
    private BigDecimal specialOrderPerformance;

    @ApiModelProperty(value = "特通单业绩--环比")
    @PerformanceValue(serialNumber = "382")
    private BigDecimal specialOrderPerformanceRatio;

    @ApiModelProperty(value = "特通单业绩--同比")
    @PerformanceValue(serialNumber = "382")
    private BigDecimal specialOrderPerformanceYearRatio;

    @ApiModelProperty(value = "特通单业绩占比")
    @PerformanceValue(serialNumber = "383")
    private BigDecimal specialOrderPerformanceRate;


    @ApiModelProperty(value = "特通单现金业绩")
    @PerformanceValue(serialNumber = "651")
    private BigDecimal specialOrderCashPerformance;

    @ApiModelProperty(value = "特通单旺金币折扣率")
    @PerformanceValue(serialNumber = "654")
    private BigDecimal specialOrderWantGoldDiscountRatio;


    @ApiModelProperty(value = "预订单业绩(当前)")
    @PerformanceValue(serialNumber = "407")
    private BigDecimal currentBookingPerformance;

    @ApiModelProperty(value = "预订单业绩(当前)-环比")
    @PerformanceValue(serialNumber = "407")
    private BigDecimal currentBookingPerformanceRatio;

    @ApiModelProperty(value = "预订单业绩(当前)-环比")
    @PerformanceValue(serialNumber = "407")
    private BigDecimal currentBookingPerformanceYearRatio;

    @ApiModelProperty(value = "预订单业绩占比(当前)")
    @PerformanceValue(serialNumber = "533")
    private BigDecimal currentBookingPerformanceRate;

    @ApiModelProperty(value = "预订单现金业绩")
    @PerformanceValue(serialNumber = "650")
    private BigDecimal currentBookingCashPerformance;

    @ApiModelProperty(value = "预订单旺金币折扣率")
    @PerformanceValue(serialNumber = "653")
    private BigDecimal currentBookingWantGoldDiscountRatio;


    @ApiModelProperty(value = "文宣品-旺金币业绩")
    @PerformanceValue(serialNumber = "657")
    private BigDecimal promotionalMaterialsGoldenCoinPerformance;

    @ApiModelProperty(value = "文宣品-旺金币业绩占比")
    @PerformanceValue(serialNumber = "658")
    private BigDecimal promotionalMaterialsGoldenCoinPerformanceRate;

    @ApiModelProperty(value = "应收款金额")
    @PerformanceValue(serialNumber = "659")
    private BigDecimal needReceivableAmount;

    @ApiModelProperty(value = "旺金币业绩")
    @PerformanceValue(serialNumber = "409")
    private BigDecimal goldenCoinPerformance;

    @ApiModelProperty(value = "旺金币业绩占比")
    @PerformanceValue(serialNumber = "506")
    private BigDecimal goldenCoinPerformanceRate;

    @ApiModelProperty(value = "现金业绩")
    @PerformanceValue(serialNumber = "505")
    private BigDecimal cashPerformance;


    @ApiModelProperty(value = "现金业绩占比")
    @PerformanceValue(serialNumber = "525")
    private BigDecimal cashPerformanceRate;

    @ApiModelProperty(value = "经典品项业绩")
    @PerformanceValue(serialNumber = "141")
    private BigDecimal classicItemPerformance;

    @ApiModelProperty(value = "综合组品项业绩")
    @PerformanceValue(serialNumber = "390")
    private BigDecimal synthesisGroupPerformance;

    @ApiModelProperty(value = "预订单未发货业绩")
    @PerformanceValue(serialNumber = "318")
    private BigDecimal advanceOrderUnshippedPerformance;

    @ApiModelProperty(value = "预储值金额")
    @PerformanceValue(serialNumber = "319")
    private BigDecimal preStoredValuePerformance;

    @ApiModelProperty(value = "财务收入")
    private BigDecimal financialReceipts;

    @ApiModelProperty(value = "业务收入")
    private BigDecimal businessIncome;

    @ApiModelProperty(value = "非业务收入")
    private BigDecimal nonBusinessIncome;

    @ApiModelProperty(value = "试吃商城收入")
    private BigDecimal tasteMallIncome;


    /**
     * 客户数据-业绩
     */
    @ApiModelProperty(value = "业绩-业绩-405")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performancePer;

    @ApiModelProperty(value = "业绩-环比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performancePerRatio;

    @ApiModelProperty(value = "业绩-同比")
    @PerformanceValue(serialNumber = "405")
    private BigDecimal performancePerYearRatio;

    @ApiModelProperty(value = "新客户业绩(经销商)-业绩")
    @PerformanceValue(serialNumber = "397")
    private BigDecimal newCustomerPerformanceDealer;

    @ApiModelProperty(value = "新客户业绩(经销商)-业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "541")
    private BigDecimal newCustomerPerformanceDealerWantGoldDiscountRatio;

    @ApiModelProperty(value = "新客户业绩(经销商)-环比")
    @PerformanceValue(serialNumber = "397")
    private BigDecimal newCustomerPerformanceDealerRatio;

    @ApiModelProperty(value = "新客户业绩(经销商)-同比")
    @PerformanceValue(serialNumber = "397")
    private BigDecimal newCustomerPerformanceDealerYearRatio;

    @ApiModelProperty(value = "老客户业绩(经销商)-业绩")
    @PerformanceValue(serialNumber = "400")
    private BigDecimal oldCustomerPerformanceDealer;

    @ApiModelProperty(value = "老客户业绩(经销商)-业绩-旺金币折扣率")
    @PerformanceValue(serialNumber = "542")
    private BigDecimal oldCustomerPerformanceDealerWantGoldDiscountRatio;

    @ApiModelProperty(value = "老客户业绩(经销商)-环比")
    @PerformanceValue(serialNumber = "400")
    private BigDecimal oldCustomerPerformanceDealerRatio;

    @ApiModelProperty(value = "老客户业绩(经销商)-同比")
    @PerformanceValue(serialNumber = "400")
    private BigDecimal oldCustomerPerformanceDealerYearRatio;

    @ApiModelProperty(value = "预订单未发货业绩（本月发货）--废弃")
    private BigDecimal advanceOrderUnshippedPerformanceThis;

    @ApiModelProperty(value = "预订单未发货业绩（次月发货）--废弃")
    private BigDecimal oldCustomerPerformanceDealerYearRatioNext;


    @ApiModelProperty(value = "管理岗人均新客户业绩")
    @PerformanceValue(serialNumber = "484")
    private BigDecimal managementPositionNewCustomerPerformance;
    @ApiModelProperty("管理岗人均新客户业绩环比")
    @PerformanceValue(serialNumber = "484")
    private BigDecimal managementPositionPerformanceNewCustomerRatio;
    @ApiModelProperty("管理岗人均新客户业绩同比")
    @PerformanceValue(serialNumber = "484")
    private BigDecimal managementPositionPerformanceNewCustomerYearRatio;


    @ApiModelProperty(value = "管理岗人均老客户业绩")
    @PerformanceValue(serialNumber = "485")
    private BigDecimal managementPositionOldCustomerPerformance;
    @ApiModelProperty("管理岗人均老客户业绩环比")
    @PerformanceValue(serialNumber = "485")
    private BigDecimal managementPositionPerformanceOldCustomerRatio;
    @ApiModelProperty("管理岗人均老客户业绩同比")
    @PerformanceValue(serialNumber = "485")
    private BigDecimal managementPositionPerformanceOldCustomerYearRatio;


    @ApiModelProperty(value = "管理岗人均客户业绩")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPositionCustomerPerformance;
    @ApiModelProperty("管理岗人均客户业绩环比")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPositionPerformanceCustomerRatio;
    @ApiModelProperty("管理岗人均客户业绩同比")
    @PerformanceValue(serialNumber = "483")
    private BigDecimal managementPositionPerformanceCustomerYearRatio;

    /**
     * 客户数据-交易客户数
     */
    @ApiModelProperty(value = "交易客户数-36")
    @PerformanceValue(serialNumber = "36")
    private Integer tradingCustomerNum;

    @ApiModelProperty(value = "交易客户数-环比-36")
    @PerformanceValue(serialNumber = "36")
    private BigDecimal tradingCustomerNumRateRatio;

    @ApiModelProperty(value = "交易客户数-同比-36")
    @PerformanceValue(serialNumber = "36")
    private BigDecimal tradingCustomerNumRateYearRatio;

    @ApiModelProperty(value = "合伙人交易率`")
    @PerformanceValue(serialNumber = "50")
    private BigDecimal partnerTransactionRate;

    @ApiModelProperty(value = "交易客户目标")
    @PerformanceValue(serialNumber = "537")
    private Integer tradingCustomerGoal;

    @ApiModelProperty(value = "交易客户目标达成率")
    @PerformanceValue(serialNumber = "538")
    private BigDecimal tradingCustomerGoalAchievementRate;

    @ApiModelProperty(value = "新客户(交易客户数)-业绩")
    @PerformanceValue(serialNumber = "41")
    private Integer newTradingCustomerNumDealer;

    @ApiModelProperty(value = "新客户(交易客户数)-环比")
    @PerformanceValue(serialNumber = "41")
    private BigDecimal newTradingCustomerNumDealerRatio;

    @ApiModelProperty(value = "新客户(交易客户数)-同比")
    @PerformanceValue(serialNumber = "41")
    private BigDecimal newTradingCustomerNumDealerYearRatio;


    @ApiModelProperty(value = "新客户(交易客户数)-占比")
    @PerformanceValue(serialNumber = "593")
    private BigDecimal newTradingCustomerNumDealerProportion;

    @ApiModelProperty(value = "老客户(交易客户数)-业绩")
    @PerformanceValue(serialNumber = "42")
    private BigDecimal oldTradingCustomerNumDealer;

    @ApiModelProperty(value = "老客户(交易客户数)-环比")
    @PerformanceValue(serialNumber = "42")
    private BigDecimal oldTradingCustomerNumDealerRatio;

    @ApiModelProperty(value = "老客户(交易客户数)-同比")
    @PerformanceValue(serialNumber = "42")
    private BigDecimal oldTradingCustomerNumDealerYearRatio;

    @ApiModelProperty(value = "老客户(交易客户数)-占比")
    @PerformanceValue(serialNumber = "594")
    private BigDecimal oldTradingCustomerNumDealerProportion;

    @ApiModelProperty(value = "未及时激活新客户数30天内")
    @PerformanceValue(serialNumber = "472")
    private BigDecimal outTimeactivatedCustomerWithInThirtyNum;

    @ApiModelProperty(value = "未及时激活新客户数30天内-环比")
    @PerformanceValue(serialNumber = "472")
    private BigDecimal outTimeactivatedCustomerWithInThirtyRatio;

    @ApiModelProperty(value = "未及时激活新客户数30天内--同比")
    @PerformanceValue(serialNumber = "472")
    private BigDecimal outTimeactivatedCustomerWithInThirtyYearRatio;


    @ApiModelProperty(value = "未及时激活新客户数30天内--占比")
    @PerformanceValue(serialNumber = "596")
    private BigDecimal outTimeActivatedCustomerWithInThirtyProportion;


    @ApiModelProperty(value = "未及时激活新客户数30天以上")
    @PerformanceValue(serialNumber = "494")
    private BigDecimal outTimeactivatedCustomerAboveThirtyNum;

    @ApiModelProperty(value = "未及时激活新客户数30天以上-环比")
    @PerformanceValue(serialNumber = "494")
    private BigDecimal outTimeactivatedCustomerAboveThirtyRatio;

    @ApiModelProperty(value = "未及时激活新客户数30天以上--同比")
    @PerformanceValue(serialNumber = "494")
    private BigDecimal outTimeactivatedCustomerAboveThirtyYearRatio;


    @ApiModelProperty(value = "及时激活新客户数")
    @PerformanceValue(serialNumber = "473")
    private BigDecimal inTimeactivatedCustomerNum;

    @ApiModelProperty(value = "及时激活新客户数--环比")
    @PerformanceValue(serialNumber = "473")
    private BigDecimal inTimeactivatedCustomerRatio;

    @ApiModelProperty(value = "及时激活新客户数--同比")
    @PerformanceValue(serialNumber = "473")
    private BigDecimal inTimeactivatedCustomerYearRatio;

    @ApiModelProperty(value = "及时激活新客户数--占比")
    @PerformanceValue(serialNumber = "595")
    private BigDecimal inTimeActivatedCustomerProportion;

    @ApiModelProperty(value = "开发客户数")
    @PerformanceValue(serialNumber = "474")
    private BigDecimal developCustomerNum;


    @ApiModelProperty(value = "开发客户数转化率")
    @PerformanceValue(serialNumber = "44")
    private BigDecimal developCustomerConversionRate;

    @ApiModelProperty(value = "复购客户数")
    @PerformanceValue(serialNumber = "44")
    private BigDecimal repeatPurchasesCustomerNum;

    @ApiModelProperty(value = "复购客户数--环比")
    @PerformanceValue(serialNumber = "44")
    private BigDecimal repeatPurchasesCustomerRatio;

    @ApiModelProperty(value = "复购客户数--同比")
    @PerformanceValue(serialNumber = "44")
    private BigDecimal repeatPurchasesCustomerYearRatio;

    @ApiModelProperty(value = "复购客户数--占比")
    @PerformanceValue(serialNumber = "597")
    private BigDecimal repeatPurchasesCustomerProportion;

    @PerformanceValue(serialNumber = "592")
    @ApiModelProperty(value = "复购客户数--管理岗人均复购客户数")
    private BigDecimal managementPositionAverRepeatPurchasesCustomerNum;


    @ApiModelProperty(value = "召回客户数")
    @PerformanceValue(serialNumber = "471")
    private BigDecimal recallCustomerNum;

    @ApiModelProperty(value = "召回客户数--环比")
    @PerformanceValue(serialNumber = "471")
    private BigDecimal recallCustomerRatio;

    @ApiModelProperty(value = "召回客户数--同比")
    @PerformanceValue(serialNumber = "471")
    private BigDecimal recallCustomerYearRatio;

    @ApiModelProperty(value = "召回客户数--占比")
    @PerformanceValue(serialNumber = "598")
    private BigDecimal recallCustomerProportion;

    @PerformanceValue(serialNumber = "591")
    @ApiModelProperty(value = "召回客户数--管理岗人均召回客户数")
    private BigDecimal managementPositionAverRecallCustomerNum;

    @ApiModelProperty(value = "1个月内下单客户数")
    private BigDecimal withinAMonthTradingCustomerNum;

    @ApiModelProperty(value = "1个月内下单客户数占比")
    private BigDecimal withinAMonthTradingCustomerRatio;

    @ApiModelProperty(value = "2个月内下单客户数")
    private BigDecimal withinTwoMonthTradingCustomerNum;

    @ApiModelProperty(value = "2个月内下单客户数占比")
    private BigDecimal withinTwoMonthTradingCustomerRatio;

    @ApiModelProperty(value = "3个月内下单客户数")
    private BigDecimal withinThreeMonthTradingCustomerNum;

    @ApiModelProperty(value = "3个月内下单客户数占比")
    private BigDecimal withinThreeMonthTradingCustomerRatio;

    @ApiModelProperty(value = "3个月前下单客户数")
    private BigDecimal beforeThreeMonthTradingCustomerNum;

    @ApiModelProperty(value = "3个月前下单客户数占比")
    private BigDecimal beforeThreeMonthTradingCustomerRatio;


    @ApiModelProperty(value = "管理岗人均交易客户数")
    @PerformanceValue(serialNumber = "486")
    private BigDecimal managementPositionCustomerNum;
    @ApiModelProperty("管理岗人均交易客户数环比")
    @PerformanceValue(serialNumber = "486")
    private BigDecimal managementPositionCustomerNumRatio;
    @ApiModelProperty("管理岗人均交易客户数同比")
    @PerformanceValue(serialNumber = "486")
    private BigDecimal managementPositionCustomerNumYearRatio;

    @ApiModelProperty(value = "管理岗人均新客户数")
    @PerformanceValue(serialNumber = "451")
    private BigDecimal managementPositionNewCustomerNum;
    @ApiModelProperty("管理岗人均新客户数环比")
    @PerformanceValue(serialNumber = "451")
    private BigDecimal managementPositionNewCustomerNumRatio;
    @ApiModelProperty("管理岗人均新客户数同比")
    @PerformanceValue(serialNumber = "451")
    private BigDecimal managementPositionNewCustomerNumYearRatio;

    @ApiModelProperty(value = "管理岗人均老客户数")
    @PerformanceValue(serialNumber = "456")
    private BigDecimal managementPositionOldCustomerNum;
    @ApiModelProperty("管理岗人均老客户数环比")
    @PerformanceValue(serialNumber = "456")
    private BigDecimal managementPositionOldCustomerNumRatio;
    @ApiModelProperty("管理岗人均老客户数同比")
    @PerformanceValue(serialNumber = "456")
    private BigDecimal managementPositionOldCustomerNumYearRatio;

    /**
     * 客户数据-客单价
     */
    @ApiModelProperty(value = "客单价")
    @PerformanceValue(serialNumber = "37")
    private BigDecimal perCustomer;

    @ApiModelProperty(value = "客单价-环比")
    @PerformanceValue(serialNumber = "37")
    private BigDecimal perCustomerRatio;

    @ApiModelProperty(value = "客单价-同比")
    @PerformanceValue(serialNumber = "37")
    private BigDecimal perCustomerYearRatio;

    @ApiModelProperty(value = "新客户客单价-业绩")
    @PerformanceValue(serialNumber = "344")
    private BigDecimal newPerCustomerDealer;

    @ApiModelProperty(value = "新客户客单价-环比")
    @PerformanceValue(serialNumber = "344")
    private BigDecimal newPerCustomerDealerRatio;

    @ApiModelProperty(value = "新客户客单价-同比")
    @PerformanceValue(serialNumber = "344")
    private BigDecimal newPerCustomerDealerYearRatio;

    @ApiModelProperty(value = "老客户客单价-业绩")
    @PerformanceValue(serialNumber = "343")
    private BigDecimal oldPerCustomerDealer;

    @ApiModelProperty(value = "老客户客单价-环比")
    @PerformanceValue(serialNumber = "343")
    private BigDecimal oldPerCustomerDealerRatio;

    @ApiModelProperty(value = "老客户客单价-同比")
    @PerformanceValue(serialNumber = "343")
    private BigDecimal oldPerCustomerDealerYearRatio;

    /**
     * 人员数据-管理岗汇总
     */
    @ApiModelProperty(value = "管理岗汇总-已使用额度")
    private BigDecimal managementUsedQuota;
    @ApiModelProperty(value = "管理岗汇总-剩余额度")
    private BigDecimal managementSurplus;

    @ApiModelProperty(value = "在职人数-管理岗汇总")
    @PerformanceValue(serialNumber = "329")
    private Integer onJobNumManagement;

    @ApiModelProperty(value = "在职主岗人数-管理岗汇总")
    private Integer onJobMainNumManagement;
    @ApiModelProperty(value = "在职兼岗人数-管理岗汇总")
    private Integer onJobPartNumManagement;

    @ApiModelProperty(value = "在职人数环比-管理岗汇总")
    @PerformanceValue(serialNumber = "329")
    private BigDecimal onJobNumManagementRatio;

    @ApiModelProperty(value = "在职人数同比-管理岗汇总")
    @PerformanceValue(serialNumber = "329")
    private BigDecimal onJobNumManagementYearRatio;


    @ApiModelProperty(value = "入职人数-管理岗汇总")
    @PerformanceValue(serialNumber = "509")
    private Integer entryNumManagement;

    @ApiModelProperty(value = "入职人数环比-管理岗汇总")
    @PerformanceValue(serialNumber = "509")
    private BigDecimal entryNumManagementRatio;

    @ApiModelProperty(value = "入职人数同比-管理岗汇总")
    @PerformanceValue(serialNumber = "509")
    private BigDecimal entryNumManagementYearRatio;


    @ApiModelProperty(value = "离职人数-管理岗汇总")
    @PerformanceValue(serialNumber = "507")
    private Integer dimissionNumManagement;

    @ApiModelProperty(value = "离职人数环比-管理岗汇总")
    @PerformanceValue(serialNumber = "507")
    private BigDecimal dimissionNumManagementRatio;

    @ApiModelProperty(value = "离职人数同比-管理岗汇总")
    @PerformanceValue(serialNumber = "507")
    private BigDecimal dimissionNumManagementYearRatio;

    @ApiModelProperty(value = "离职率-管理岗汇总")
    @PerformanceValue(serialNumber = "508")
    private BigDecimal quitRateManagement;

    @ApiModelProperty(value = "离职率环比-管理岗汇总")
    @PerformanceValue(serialNumber = "508")
    private BigDecimal quitRateManagementRatio;

    @ApiModelProperty(value = "离职率同比-管理岗汇总")
    @PerformanceValue(serialNumber = "508")
    private BigDecimal quitRateManagementYearRatio;

    @ApiModelProperty(value = "编制数-管理岗汇总")
    @PerformanceValue(serialNumber = "387")
    private Integer establishmentNumManagement;

    @ApiModelProperty(value = "编制在岗率-管理岗汇总")
    @PerformanceValue(serialNumber = "330")
    private BigDecimal establishmentOnGuardNumManagement;

    @ApiModelProperty(value = " 在职主岗占比-管理岗汇总")
    private BigDecimal onJobMainRatioManagement;
    @ApiModelProperty(value = " 在职兼岗占比-管理岗汇总")
    private BigDecimal onJobPartRatioManagement;

    /**
     * 人员数据-总督导
     */
    @ApiModelProperty("总督导模块展示标识")
    private Boolean areaShowFlag;

    @ApiModelProperty(value = "总督导-已使用额度")
    private BigDecimal areaUsedQuota;

    @ApiModelProperty(value = "总督导-剩余额度预计可用人数")
    private Integer areaSurplusQuotaExpectedAvailabilityCount;

    @ApiModelProperty(value = "在职人数-总督导")
    @PerformanceValue(serialNumber = "314")
    private Integer onJobNumArea;

    @ApiModelProperty(value = "在职主岗人数-总督导")
    private Integer onJobMainNumArea;
    @ApiModelProperty(value = "在职兼岗人数-总督导")
    private Integer onJobPartNumArea;

    @ApiModelProperty(value = "在职人数环比-总督导")
    @PerformanceValue(serialNumber = "314")
    private BigDecimal onJobNumAreaRatio;

    @ApiModelProperty(value = "在职人数同比-总督导")
    @PerformanceValue(serialNumber = "314")
    private BigDecimal onJobNumAreaYearRatio;

    @ApiModelProperty(value = "入职人数-总督导")
    @PerformanceValue(serialNumber = "315")
    private Integer entryNumArea;

    @ApiModelProperty(value = "入职人数环比-总督导")
    @PerformanceValue(serialNumber = "315")
    private BigDecimal entryNumAreaRatio;

    @ApiModelProperty(value = "入职人数同比-总督导")
    @PerformanceValue(serialNumber = "315")
    private BigDecimal entryNumAreaYearRatio;

    @ApiModelProperty(value = "离职人数-总督导")
    @PerformanceValue(serialNumber = "316")
    private Integer dimissionNumArea;


    @ApiModelProperty(value = "离职人数环比-总督导")
    @PerformanceValue(serialNumber = "316")
    private BigDecimal dimissionNumAreaRatio;

    @ApiModelProperty(value = "离职人数同比-总督导")
    @PerformanceValue(serialNumber = "316")
    private BigDecimal dimissionNumAreaYearRatio;


    @ApiModelProperty(value = "离职率-总督导")
    @PerformanceValue(serialNumber = "317")
    private BigDecimal quitRateArea;

    @ApiModelProperty(value = "离职率环比-总督导")
    @PerformanceValue(serialNumber = "317")
    private BigDecimal quitRateAreaRatio;

    @ApiModelProperty(value = "离职率同比-总督导")
    @PerformanceValue(serialNumber = "317")
    private BigDecimal quitRateAreaYearRatio;

    @ApiModelProperty(value = "编制数-总督导")
    @PerformanceValue(serialNumber = "411")
    private Integer establishmentNumArea;

    @ApiModelProperty(value = "编制在岗率-总督导")
    @PerformanceValue(serialNumber = "386")
    private BigDecimal establishmentOnGuardNumArea;

    @ApiModelProperty(value = " 在职主岗占比-总督导")
    private BigDecimal onJobMainRatioArea;
    @ApiModelProperty(value = " 在职兼岗占比-总督导")
    private BigDecimal onJobPartRatioArea;

    /**
     * 人员数据-大区总监
     */

    @ApiModelProperty("大区总监模块展示标识")
    private Boolean vareaShowFlag;

    @ApiModelProperty(value = "大区总监-已使用额度")
    private BigDecimal vareaUsedQuota;

    @ApiModelProperty(value = "大区总监-剩余额度预计可用人数")
    private Integer vareaSurplusQuotaExpectedAvailabilityCount;

    @ApiModelProperty(value = "在职人数-大区总监")
    @PerformanceValue(serialNumber = "310")
    private Integer onJobNumVrea;

    @ApiModelProperty(value = "在职主岗人数-大区总监")
    private Integer onJobMainNumVrea;
    @ApiModelProperty(value = "在职兼岗人数-大区总监")
    private Integer onJobPartNumVrea;

    @ApiModelProperty(value = "在职人数环比-大区总监")
    @PerformanceValue(serialNumber = "310")
    private BigDecimal onJobNumVreaRatio;

    @ApiModelProperty(value = "在职人数同比-大区总监")
    @PerformanceValue(serialNumber = "310")
    private BigDecimal onJobNumVreaYearRatio;

    @ApiModelProperty(value = "入职人数-大区总监")
    @PerformanceValue(serialNumber = "311")
    private Integer entryNumVrea;

    @ApiModelProperty(value = "入职人数环比-大区总监")
    @PerformanceValue(serialNumber = "311")
    private BigDecimal entryNumVreaRatio;

    @ApiModelProperty(value = "入职人数同比-大区总监")
    @PerformanceValue(serialNumber = "311")
    private BigDecimal entryNumVreaYearRatio;

    @ApiModelProperty(value = "离职人数-大区总监")
    @PerformanceValue(serialNumber = "312")
    private Integer dimissionNumVrea;

    @ApiModelProperty(value = "离职人数环比-大区总监")
    @PerformanceValue(serialNumber = "312")
    private BigDecimal dimissionNumVreaRatio;

    @ApiModelProperty(value = "离职人数同比-大区总监")
    @PerformanceValue(serialNumber = "312")
    private BigDecimal dimissionNumVreaYearRatio;

    @ApiModelProperty(value = "离职率-大区总监")
    @PerformanceValue(serialNumber = "313")
    private BigDecimal quitRateVrea;

    @ApiModelProperty(value = "离职率环比-大区总监")
    @PerformanceValue(serialNumber = "313")
    private BigDecimal quitRateVreaRatio;

    @ApiModelProperty(value = "离职率同比-大区总监")
    @PerformanceValue(serialNumber = "313")
    private BigDecimal quitRateVreaYearRatio;

    @ApiModelProperty(value = "编制数-大区总监")
    private Integer establishmentNumVrea;

    @ApiModelProperty(value = "编制在岗率-大区总监")
    private BigDecimal establishmentOnGuardNumVrea;

    @ApiModelProperty(value = " 在职主岗占比-大区总监")
    private BigDecimal onJobMainRatioVrea;
    @ApiModelProperty(value = " 在职兼岗占比-大区总监")
    private BigDecimal onJobPartRatioVrea;

    /**
     * 人员数据-省区总监
     */

    @ApiModelProperty("省区总监模块展示标识")
    private Boolean provinceShowFlag;

    @ApiModelProperty(value = "省区总监-已使用额度")
    private BigDecimal provinceUsedQuota;

    @ApiModelProperty(value = "省区总监-剩余额度预计可用人数")
    private Integer provinceSurplusQuotaExpectedAvailabilityCount;

    @ApiModelProperty(value = "在职人数-省区总监")
    @PerformanceValue(serialNumber = "306")
    private Integer onJobNumProvince;

    @ApiModelProperty(value = "在职主岗人数-省区总监")
    private Integer onJobMainNumProvince;
    @ApiModelProperty(value = "在职兼岗人数-省区总监")
    private Integer onJobPartNumProvince;

    @ApiModelProperty(value = "在职人数环比-省区总监")
    @PerformanceValue(serialNumber = "306")
    private BigDecimal onJobNumProvinceRatio;

    @ApiModelProperty(value = "在职人数同比-省区总监")
    @PerformanceValue(serialNumber = "306")
    private BigDecimal onJobNumProvinceYearRatio;

    @ApiModelProperty(value = "入职人数-省区总监")
    @PerformanceValue(serialNumber = "307")
    private Integer entryNumProvince;

    @ApiModelProperty(value = "入职人数环比-省区总监")
    @PerformanceValue(serialNumber = "307")
    private BigDecimal entryNumProvinceRatio;

    @ApiModelProperty(value = "入职人数同比-省区总监")
    @PerformanceValue(serialNumber = "307")
    private BigDecimal entryNumProvinceYearRatio;

    @ApiModelProperty(value = "离职人数-省区总监")
    @PerformanceValue(serialNumber = "308")
    private Integer dimissionNumProvince;

    @ApiModelProperty(value = "离职人数环比-省区总监")
    @PerformanceValue(serialNumber = "308")
    private BigDecimal dimissionNumProvinceRatio;

    @ApiModelProperty(value = "离职人数同比-省区总监")
    @PerformanceValue(serialNumber = "308")
    private BigDecimal dimissionNumProvinceYearRatio;

    @ApiModelProperty(value = "离职率-省区总监")
    @PerformanceValue(serialNumber = "309")
    private BigDecimal quitRateProvince;

    @ApiModelProperty(value = "离职率环比-省区总监")
    @PerformanceValue(serialNumber = "309")
    private BigDecimal quitRateProvinceRatio;

    @ApiModelProperty(value = "离职率同比-省区总监")
    @PerformanceValue(serialNumber = "309")
    private BigDecimal quitRateProvinceYearRatio;

    @ApiModelProperty(value = "编制数-省区总监")
    private Integer establishmentNumProvince;

    @ApiModelProperty(value = "编制在岗率-省区总监")
    private BigDecimal establishmentOnGuardNumProvince;

    @ApiModelProperty(value = " 在职主岗占比-省区总监")
    private BigDecimal onJobMainRatioProvince;
    @ApiModelProperty(value = " 在职兼岗占比-省区总监")
    private BigDecimal onJobPartRatioProvince;

    /**
     * 人员数据-区域总监
     */

    @ApiModelProperty("区域总监模块展示标识")
    private Boolean companyShowFlag;

    @ApiModelProperty(value = "区域总监-已使用额度")
    private BigDecimal companyUsedQuota;

    @ApiModelProperty(value = "区域总监-剩余额度预计可用人数")
    private Integer companySurplusQuotaExpectedAvailabilityCount;

    @ApiModelProperty(value = "在职人数-区域总监")
    @PerformanceValue(serialNumber = "301")
    private Integer onJobNumCompany;

    @ApiModelProperty(value = "在职主岗人数-区域总监")
    private Integer onJobMainNumCompany;
    @ApiModelProperty(value = "在职兼岗人数-区域总监")
    private Integer onJobPartNumCompany;

    @ApiModelProperty(value = "在职人数环比-区域总监")
    @PerformanceValue(serialNumber = "301")
    private BigDecimal onJobNumCompanyRatio;

    @ApiModelProperty(value = "在职人数同比-区域总监")
    @PerformanceValue(serialNumber = "301")
    private BigDecimal onJobNumCompanyYearRatio;

    @ApiModelProperty(value = "入职人数-区域总监")
    @PerformanceValue(serialNumber = "302")
    private Integer entryNumCompany;

    @ApiModelProperty(value = "入职人数环比-区域总监")
    @PerformanceValue(serialNumber = "302")
    private BigDecimal entryNumCompanyRatio;

    @ApiModelProperty(value = "入职人数同比-区域总监")
    @PerformanceValue(serialNumber = "302")
    private BigDecimal entryNumCompanyYearRatio;

    @ApiModelProperty(value = "离职人数-区域总监")
    @PerformanceValue(serialNumber = "303")
    private Integer dimissionNumCompany;

    @ApiModelProperty(value = "离职人数环比-区域总监")
    @PerformanceValue(serialNumber = "303")
    private BigDecimal dimissionNumCompanyRatio;

    @ApiModelProperty(value = "离职人数同比-区域总监")
    @PerformanceValue(serialNumber = "303")
    private BigDecimal dimissionNumCompanyYearRatio;

    @ApiModelProperty(value = "离职率-区域总监")
    @PerformanceValue(serialNumber = "304")
    private BigDecimal quitRateCompany;

    @ApiModelProperty(value = "离职率环比-区域总监")
    @PerformanceValue(serialNumber = "304")
    private BigDecimal quitRateCompanyRatio;

    @ApiModelProperty(value = "离职率同比-区域总监")
    @PerformanceValue(serialNumber = "304")
    private BigDecimal quitRateCompanyYearRatio;


    @ApiModelProperty(value = "编制数-区域总监")
    private Integer establishmentNumCompany;

    @ApiModelProperty(value = "编制在岗率-区域总监")
    private BigDecimal establishmentOnGuardNumCompany;

    @ApiModelProperty(value = " 在职主岗占比-区域总监")
    private BigDecimal onJobMainRatioCompany;
    @ApiModelProperty(value = " 在职兼岗占比-区域总监")
    private BigDecimal onJobPartRatioCompany;

    /**
     * 人员数据-区域经理
     */
    @ApiModelProperty("区域经理模块展示标识")
    private Boolean departmentShowFlag;

    @ApiModelProperty(value = "区域经理-已使用额度")
    private BigDecimal departmentUsedQuota;

    @ApiModelProperty(value = "区域经理-剩余额度预计可用人数")
    private Integer departmentSurplusQuotaExpectedAvailabilityCount;

    @ApiModelProperty(value = "在职人数-区域经理")
    @PerformanceValue(serialNumber = "297")
    private Integer onJobNumDepartment;

    @ApiModelProperty(value = "在职主岗人数-区域经理")
    private Integer onJobMainNumDepartment;
    @ApiModelProperty(value = "在职兼岗人数-区域经理")
    private Integer onJobPartNumDepartment;

    @ApiModelProperty(value = "在职人数环比-区域经理")
    @PerformanceValue(serialNumber = "297")
    private BigDecimal onJobNumDepartmentRatio;

    @ApiModelProperty(value = "在职人数同比-区域经理")
    @PerformanceValue(serialNumber = "297")
    private BigDecimal onJobNumDepartmentYearRatio;


    @ApiModelProperty(value = "入职人数-区域经理")
    @PerformanceValue(serialNumber = "298")
    private Integer entryNumDepartment;

    @ApiModelProperty(value = "入职人数环比-区域经理")
    @PerformanceValue(serialNumber = "298")
    private BigDecimal entryNumDepartmentRatio;

    @ApiModelProperty(value = "入职人数同比-区域经理")
    @PerformanceValue(serialNumber = "298")
    private BigDecimal entryNumDepartmentYearRatio;

    @ApiModelProperty(value = "离职人数-区域经理")
    @PerformanceValue(serialNumber = "299")
    private Integer dimissionNumDepartment;

    @ApiModelProperty(value = "离职人数环比-区域经理")
    @PerformanceValue(serialNumber = "299")
    private BigDecimal dimissionNumDepartmentRatio;

    @ApiModelProperty(value = "离职人数同比-区域经理")
    @PerformanceValue(serialNumber = "299")
    private BigDecimal dimissionNumDepartmentYearRatio;

    @ApiModelProperty(value = "离职率-区域经理")
    @PerformanceValue(serialNumber = "126")
    private BigDecimal quitRateDepartment;

    @ApiModelProperty(value = "离职率环比-区域经理")
    @PerformanceValue(serialNumber = "126")
    private BigDecimal quitRateDepartmentRatio;

    @ApiModelProperty(value = "离职率同比-区域经理")
    @PerformanceValue(serialNumber = "126")
    private BigDecimal quitRateDepartmentYearRatio;

    @ApiModelProperty(value = "编制数-区域经理")
    private Integer establishmentNumDepartment;

    @ApiModelProperty(value = "编制在岗率-区域经理")
    private BigDecimal establishmentOnGuardNumDepartment;

    @ApiModelProperty(value = " 在职主岗占比-区域经理")
    private BigDecimal onJobMainRatioDepartment;
    @ApiModelProperty(value = " 在职兼岗占比-区域经理")
    private BigDecimal onJobPartRatioDepartment;


    /**
     * 人员数据-合伙人
     */
//    @ApiModelProperty(value = "客户数")
//    private BigDecimal customerCount;
    @ApiModelProperty("拜访客户数")
    @PerformanceValue(serialNumber = "543")
    private BigDecimal visitCustomerCount;

    @ApiModelProperty("拜访率")
    @PerformanceValue(serialNumber = "460")
    private BigDecimal visitCustomerRate;

    @ApiModelProperty("盘点率")
    @PerformanceValue(serialNumber = "54")
    private BigDecimal takeStockRate;

    @ApiModelProperty("交易率")
    @PerformanceValue(serialNumber = "50")
    private BigDecimal transactionRate;

    @PerformanceValue(serialNumber = "544")
    @ApiModelProperty(value = "管理岗人均拜访客户数")
    private BigDecimal managementPositionAverVisitCustomerCount;

    @ApiModelProperty(value = "开发客户数")
    private Integer developCustomerCount;

    @ApiModelProperty(value = "关闭客户数")
    private Integer closeCustomerCount;

    @ApiModelProperty(value = "关闭率")
    private BigDecimal closeRate;

    @ApiModelProperty(value = "意向客户数")
    @PerformanceValue(serialNumber = "447")
    private Integer intentionCustomerCount;

    @ApiModelProperty(value = "意向客户数-环比")
    @PerformanceValue(serialNumber = "447")
    private BigDecimal intentionCustomerCountChainRatio;

    @ApiModelProperty(value = "意向客户数-同比")
    @PerformanceValue(serialNumber = "447")
    private BigDecimal intentionCustomerCountYearRatio;

    @ApiModelProperty(value = "意向未下单客户数")
    @PerformanceValue(serialNumber = "495")
    private Integer intentionNotOrderCustomerCount;

    @ApiModelProperty(value = "意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "495")
    private BigDecimal intentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "495")
    private BigDecimal intentionNotOrderCustomerCountYearRatio;

    @ApiModelProperty(value = "潜在客户数")
    @PerformanceValue(serialNumber = "470")
    private Integer latentCustomerCount;

    @ApiModelProperty(value = "潜在客户数-环比")
    @PerformanceValue(serialNumber = "470")
    private BigDecimal latentCustomerCountChainRatio;

    @ApiModelProperty(value = "潜在客户数-同比")
    @PerformanceValue(serialNumber = "470")
    private BigDecimal latentCustomerCountYearRatio;

//    @PerformanceValue(serialNumber = "487")
//    @ApiModelProperty(value = "管理岗人均客户数")
//    private BigDecimal managementPositionAverCustomerCount;
    @PerformanceValue(serialNumber = "488")
    @ApiModelProperty(value = "管理岗人均开发客户数")
    private BigDecimal managementPositionAverDevelopCustomerCount;
    @PerformanceValue(serialNumber = "489")
    @ApiModelProperty(value = "管理岗人均关闭客户数")
    private BigDecimal managementPositionAverCloseCustomerCount;


    @PerformanceValue(serialNumber = "490")
    @ApiModelProperty(value = "管理岗人均意向客户数")
    private BigDecimal managementPositionAverIntentionCustomerCount;

    @PerformanceValue(serialNumber = "490")
    @ApiModelProperty(value = "管理岗人均意向客户数-环比")
    private BigDecimal managementPositionAverIntentionCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "490")
    @ApiModelProperty(value = "管理岗人均意向客户数-同比")
    private BigDecimal managementPositionAverIntentionCustomerCountYearRatio;

    @PerformanceValue(serialNumber = "491")
    @ApiModelProperty(value = "管理岗人均潜在客户数")
    private BigDecimal managementPositionAverLatentCustomerCount;

    @PerformanceValue(serialNumber = "491")
    @ApiModelProperty(value = "管理岗人均潜在客户数-环比")
    private BigDecimal managementPositionAverLatentCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "491")
    @ApiModelProperty(value = "管理岗人均潜在客户数-同比")
    private BigDecimal managementPositionAverLatentCustomerCountYearRatio;

    @ApiModelProperty(value = "管理岗人均意向未下单客户数")
    @PerformanceValue(serialNumber = "496")
    private BigDecimal managementPositionAverIntentionNotOrderCustomerCount;

    @ApiModelProperty(value = "管理岗人均意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "496")
    private BigDecimal managementPositionAverIntentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "管理岗人均意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "496")
    private BigDecimal managementPositionAverIntentionNotOrderCustomerCountYearRatio;

    /**
     * 系统客户-与合伙人共用部分字段
     */
    @ApiModelProperty(value = "意向下过单客户数")
    @PerformanceValue(serialNumber = "578")
    private Integer intentionOrderCustomerCount;
    @ApiModelProperty(value = "管理岗人均意向下单客户数")
    @PerformanceValue(serialNumber = "579")
    private BigDecimal managementPositionAverIntentionOrderCustomerCount;
    /**
     * 业务BD
     */

    @ApiModelProperty("业务BD模块展示标识")
    private Boolean businessDevelopmentShowFlag;

    @ApiModelProperty(value = "在职人数-业务BD")
    @PerformanceValue(serialNumber = "497")
    private Integer onJobNumBusinessDevelopment;

    @ApiModelProperty(value = "在职人数环比-业务BD")
    @PerformanceValue(serialNumber = "497")
    private BigDecimal onJobNumRatioBusinessDevelopment;

    @ApiModelProperty(value = "在职人数同比-业务BD")
    @PerformanceValue(serialNumber = "497")
    private BigDecimal onJobNumYearRatioBusinessDevelopment;

    @ApiModelProperty(value = "入职人数-业务BD")
    @PerformanceValue(serialNumber = "498")
    private Integer entryNumBusinessDevelopment;

    @ApiModelProperty(value = "入职人数环比-业务BD")
    @PerformanceValue(serialNumber = "498")
    private Integer entryNumRatioBusinessDevelopment;

    @ApiModelProperty(value = "入职人数同比-业务BD")
    @PerformanceValue(serialNumber = "498")
    private Integer entryNumYearRatioBusinessDevelopment;

    @ApiModelProperty(value = "离职人数-业务BD")
    @PerformanceValue(serialNumber = "499")
    private Integer dimissionNumBusinessDevelopment;

    @ApiModelProperty(value = "离职人数环比-业务BD")
    @PerformanceValue(serialNumber = "499")
    private Integer dimissionNumRatioBusinessDevelopment;

    @ApiModelProperty(value = "离职人数同比-业务BD")
    @PerformanceValue(serialNumber = "499")
    private Integer dimissionNumYearRatioBusinessDevelopment;

    @ApiModelProperty(value = "离职率-业务BD")
    @PerformanceValue(serialNumber = "500")
    private BigDecimal quitRateBusinessDevelopment;

    @ApiModelProperty(value = "离职率环比-业务BD")
    @PerformanceValue(serialNumber = "500")
    private BigDecimal quitRateRatioBusinessDevelopment;

    @ApiModelProperty(value = "离职率同比-业务BD")
    @PerformanceValue(serialNumber = "500")
    private BigDecimal quitRateYearRatioBusinessDevelopment;

    @ApiModelProperty(value = "编制数-业务BD")
    @PerformanceValue(serialNumber = "502")
    private Integer establishmentNumBusinessDevelopment;

    @ApiModelProperty(value = "编制在岗率-业务BD")
    @PerformanceValue(serialNumber = "501")
    private BigDecimal establishmentOnGuardNumBusinessDevelopment;


    @ApiModelProperty(value = "全职人数-业务BD")
    @PerformanceValue(serialNumber = "583")
    private Integer fullTimeBusinessDevelopmentNum;

    @ApiModelProperty(value = "全职人数占比-业务BD")
    @PerformanceValue(serialNumber = "586")
    private BigDecimal fullTimeRateBusinessDevelopment;

    @ApiModelProperty(value = "承揽人数-业务BD")
    @PerformanceValue(serialNumber = "584")
    private Integer undertakeBusinessDevelopmentNum;

    @ApiModelProperty(value = "承揽人数占比-业务BD")
    @PerformanceValue(serialNumber = "587")
    private BigDecimal undertakeRateBusinessDevelopment;

    @ApiModelProperty(value = "兼职人数占比-业务BD")
    @PerformanceValue(serialNumber = "585")
    private Integer partTimeBusinessDevelopmentNum;

    @ApiModelProperty(value = "兼职人数占比-业务BD")
    @PerformanceValue(serialNumber = "588")
    private BigDecimal partTimeRateBusinessDevelopment;

    /**
     * 流通合伙人
     */

//    @ApiModelProperty(value = "流通合伙人数")
//    @PerformanceValue(serialNumber = "515")
//    private BigDecimal circulatingPartnerCount;
    @ApiModelProperty(value = "开发流通合伙人数")
    @PerformanceValue(serialNumber = "516")
    private Integer openCirculatingPartnerCount;
    @ApiModelProperty(value = "关闭流通合伙人数")
    @PerformanceValue(serialNumber = "517")
    private Integer closeCirculatingPartnerCount;
    @ApiModelProperty(value = "流通合伙人关闭率")
    @PerformanceValue(serialNumber = "518")
    private BigDecimal closeCirculatingPartnerRate;


    @ApiModelProperty(value = "流通合伙人-意向客户数")
    @PerformanceValue(serialNumber = "545")
    private Integer circulatingPartnerIntentionCustomerCount;

    @ApiModelProperty(value = "流通合伙人-意向客户数-环比")
    @PerformanceValue(serialNumber = "545")
    private BigDecimal circulatingPartnerIntentionCustomerCountChainRatio;

    @ApiModelProperty(value = "流通合伙人-意向客户数-同比")
    @PerformanceValue(serialNumber = "545")
    private BigDecimal circulatingPartnerIntentionCustomerCountYearRatio;

    @ApiModelProperty(value = "流通合伙人-意向未下单客户数")
    @PerformanceValue(serialNumber = "546")
    private Integer circulatingPartnerIntentionNotOrderCustomerCount;

    @ApiModelProperty(value = "流通合伙人-意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "546")
    private BigDecimal circulatingPartnerIntentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "流通合伙人-意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "546")
    private BigDecimal circulatingPartnerIntentionNotOrderCustomerCountYearRatio;

    @ApiModelProperty(value = "流通合伙人-潜在客户数")
    @PerformanceValue(serialNumber = "547")
    private Integer circulatingPartnerLatentCustomerCount;

    @ApiModelProperty(value = "流通合伙人-潜在客户数-环比")
    @PerformanceValue(serialNumber = "547")
    private BigDecimal circulatingPartnerLatentCustomerCountChainRatio;

    @ApiModelProperty(value = "流通合伙人-潜在客户数-同比")
    @PerformanceValue(serialNumber = "547")
    private BigDecimal circulatingPartnerLatentCustomerCountYearRatio;


    @PerformanceValue(serialNumber = "548")
    @ApiModelProperty(value = "流通合伙人-管理岗人均意向客户数")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionCustomerCount;

    @PerformanceValue(serialNumber = "548")
    @ApiModelProperty(value = "流通合伙人-管理岗人均意向客户数-环比")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "548")
    @ApiModelProperty(value = "流通合伙人-管理岗人均意向客户数-同比")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionCustomerCountYearRatio;

    @ApiModelProperty(value = "流通合伙人-管理岗人均意向未下单客户数")
    @PerformanceValue(serialNumber = "549")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCount;

    @ApiModelProperty(value = "流通合伙人-管理岗人均意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "549")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "流通合伙人-管理岗人均意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "549")
    private BigDecimal circulatingPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio;

    @PerformanceValue(serialNumber = "550")
    @ApiModelProperty(value = "流通合伙人-管理岗人均潜在客户数")
    private BigDecimal circulatingPartnerManagementPositionAverLatentCustomerCount;

    @PerformanceValue(serialNumber = "550")
    @ApiModelProperty(value = "流通合伙人-管理岗人均潜在客户数-环比")
    private BigDecimal circulatingPartnerManagementPositionAverLatentCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "550")
    @ApiModelProperty(value = "流通合伙人-管理岗人均潜在客户数-同比")
    private BigDecimal circulatingPartnerManagementPositionAverLatentCustomerCountYearRatio;

    @PerformanceValue(serialNumber = "551")
    @ApiModelProperty(value = "流通合伙人-管理岗人均开发客户数")
    private BigDecimal circulatingPartnerManagementPositionAverDevelopCustomerCount;
    @PerformanceValue(serialNumber = "552")
    @ApiModelProperty(value = "流通合伙人-管理岗人均关闭客户数")
    private BigDecimal circulatingPartnerManagementPositionAverCloseCustomerCount;

    @ApiModelProperty("流通合伙人-交易率")
    @PerformanceValue(serialNumber = "553")
    private BigDecimal circulatingPartnerTransactionRate;

    @ApiModelProperty("流通合伙人-盘点率")
    @PerformanceValue(serialNumber = "554")
    private BigDecimal circulatingPartnerTakeStockRate;

    @ApiModelProperty("流通合伙人-拜访率")
    @PerformanceValue(serialNumber = "555")
    private BigDecimal circulatingPartnerVisitCustomerRate;

    @ApiModelProperty("流通合伙人-拜访客户数")
    @PerformanceValue(serialNumber = "556")
    private Integer circulatingPartnerVisitCustomerCount;

    @PerformanceValue(serialNumber = "557")
    @ApiModelProperty(value = "流通合伙人-管理岗人均拜访客户数")
    private BigDecimal circulatingPartnerManagementPositionAverVisitCustomerCount;


    /**
     * 直营合伙人
     */
//    @ApiModelProperty(value = "流通合伙人数")
//    @PerformanceValue(serialNumber = "519")
//    private BigDecimal directPartnerCount;
    @ApiModelProperty(value = "开发流通合伙人数")
    @PerformanceValue(serialNumber = "520")
    private Integer openDirectPartnerCount;
    @ApiModelProperty(value = "关闭流通合伙人数")
    @PerformanceValue(serialNumber = "521")
    private Integer closeDirectPartnerCount;
    @ApiModelProperty(value = "流通合伙人关闭率")
    @PerformanceValue(serialNumber = "522")
    private BigDecimal closeDirectPartnerRate;


    @ApiModelProperty(value = "直营合伙人-意向客户数")
    @PerformanceValue(serialNumber = "558")
    private Integer directPartnerIntentionCustomerCount;

    @ApiModelProperty(value = "直营合伙人-意向客户数-环比")
    @PerformanceValue(serialNumber = "558")
    private BigDecimal directPartnerIntentionCustomerCountChainRatio;

    @ApiModelProperty(value = "直营合伙人-意向客户数-同比")
    @PerformanceValue(serialNumber = "558")
    private BigDecimal directPartnerIntentionCustomerCountYearRatio;

    @ApiModelProperty(value = "直营合伙人-意向未下单客户数")
    @PerformanceValue(serialNumber = "559")
    private Integer directPartnerIntentionNotOrderCustomerCount;

    @ApiModelProperty(value = "直营合伙人-意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "559")
    private BigDecimal directPartnerIntentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "直营合伙人-意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "559")
    private BigDecimal directPartnerIntentionNotOrderCustomerCountYearRatio;

    @ApiModelProperty(value = "直营合伙人-潜在客户数")
    @PerformanceValue(serialNumber = "560")
    private Integer directPartnerLatentCustomerCount;

    @ApiModelProperty(value = "直营合伙人-潜在客户数-环比")
    @PerformanceValue(serialNumber = "560")
    private BigDecimal directPartnerLatentCustomerCountChainRatio;

    @ApiModelProperty(value = "直营合伙人-潜在客户数-同比")
    @PerformanceValue(serialNumber = "560")
    private BigDecimal directPartnerLatentCustomerCountYearRatio;


    @PerformanceValue(serialNumber = "561")
    @ApiModelProperty(value = "直营合伙人-管理岗人均意向客户数")
    private BigDecimal directPartnerManagementPositionAverIntentionCustomerCount;

    @PerformanceValue(serialNumber = "561")
    @ApiModelProperty(value = "直营合伙人-管理岗人均意向客户数-环比")
    private BigDecimal directPartnerManagementPositionAverIntentionCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "561")
    @ApiModelProperty(value = "直营合伙人-管理岗人均意向客户数-同比")
    private BigDecimal directPartnerManagementPositionAverIntentionCustomerCountYearRatio;

    @ApiModelProperty(value = "直营合伙人-管理岗人均意向未下单客户数")
    @PerformanceValue(serialNumber = "562")
    private BigDecimal directPartnerManagementPositionAverIntentionNotOrderCustomerCount;

    @ApiModelProperty(value = "直营合伙人-管理岗人均意向未下单客户数-环比")
    @PerformanceValue(serialNumber = "562")
    private BigDecimal directPartnerManagementPositionAverIntentionNotOrderCustomerCountChainRatio;

    @ApiModelProperty(value = "直营合伙人-管理岗人均意向未下单客户数-同比")
    @PerformanceValue(serialNumber = "562")
    private BigDecimal directPartnerManagementPositionAverIntentionNotOrderCustomerCountYearRatio;

    @PerformanceValue(serialNumber = "563")
    @ApiModelProperty(value = "直营合伙人-管理岗人均潜在客户数")
    private BigDecimal directPartnerManagementPositionAverLatentCustomerCount;

    @PerformanceValue(serialNumber = "563")
    @ApiModelProperty(value = "直营合伙人-管理岗人均潜在客户数-环比")
    private BigDecimal directPartnerManagementPositionAverLatentCustomerCountChainRatio;

    @PerformanceValue(serialNumber = "563")
    @ApiModelProperty(value = "直营合伙人-管理岗人均潜在客户数-同比")
    private BigDecimal directPartnerManagementPositionAverLatentCustomerCountYearRatio;

    @PerformanceValue(serialNumber = "564")
    @ApiModelProperty(value = "直营合伙人-管理岗人均开发客户数")
    private BigDecimal directPartnerManagementPositionAverDevelopCustomerCount;
    @PerformanceValue(serialNumber = "565")
    @ApiModelProperty(value = "直营合伙人-管理岗人均关闭客户数")
    private BigDecimal directPartnerManagementPositionAverCloseCustomerCount;

    @ApiModelProperty("直营合伙人-交易率")
    @PerformanceValue(serialNumber = "566")
    private BigDecimal directPartnerTransactionRate;

    @ApiModelProperty("直营合伙人-盘点率")
    @PerformanceValue(serialNumber = "567")
    private BigDecimal directPartnerTakeStockRate;

    @ApiModelProperty("直营合伙人-拜访率")
    @PerformanceValue(serialNumber = "568")
    private BigDecimal directPartnerVisitCustomerRate;

    @ApiModelProperty("直营合伙人-拜访客户数")
    @PerformanceValue(serialNumber = "569")
    private Integer directPartnerVisitCustomerCount;

    @PerformanceValue(serialNumber = "570")
    @ApiModelProperty(value = "直营合伙人-管理岗人均拜访客户数")
    private BigDecimal directPartnerManagementPositionAverVisitCustomerCount;


    /**
     * 导购人员
     */

    @ApiModelProperty("导购模块展示标识")
    private Boolean shoppingGuideShowFlag;

    @ApiModelProperty(value = "导购在职人数")
    @PerformanceValue(serialNumber = "523")
    private BigDecimal onJobShoppingGuideCount;

//    @ApiModelProperty(value = "导购在职人数-环比")
//    @PerformanceValue(serialNumber = "523")
//    private BigDecimal onJobShoppingGuideCountChainRatio;
//
//    @ApiModelProperty(value = "导购在职人数-同比")
//    @PerformanceValue(serialNumber = "523")
//    private BigDecimal onJobShoppingGuideCountYearRatio;

    @ApiModelProperty(value = "导购关闭人数")
    @PerformanceValue(serialNumber = "524")
    private BigDecimal closeShoppingGuideCount;

//    @ApiModelProperty(value = "导购关闭人数-同比")
//    @PerformanceValue(serialNumber = "524")
//    private BigDecimal closeShoppingGuideCountChainRatio;
//
//    @ApiModelProperty(value = "导购关闭人数-环比")
//    @PerformanceValue(serialNumber = "524")
//    private BigDecimal closeShoppingGuideCountYearRatio;

    @ApiModelProperty(value = "导购入职人数")
    @PerformanceValue(serialNumber = "526")
    private BigDecimal entryShoppingGuideCount;
//
//    @ApiModelProperty(value = "导购入职人数-环比")
//    @PerformanceValue(serialNumber = "526")
//    private BigDecimal entryShoppingGuideCountChainRatio;
//
//
//    @ApiModelProperty(value = "导购入职人数-同比")
//    @PerformanceValue(serialNumber = "526")
//    private BigDecimal entryShoppingGuideCountYearRatio;

    /**
     * 合伙人详情页使用
     */
    @ApiModelProperty("系统类型")
    private String systemType;
    @ApiModelProperty("系统名称")
    private String systemName;
    @ApiModelProperty("客户编号")
    private Long memberKey;


    /**
     * 专案政策
     */
    @ApiModelProperty("专案政策客户业绩")
    @PerformanceValue(serialNumber = "573")
    private BigDecimal projectPolicyCustomerPerformance;

    @ApiModelProperty("专案政策客户业绩-环比")
    @PerformanceValue(serialNumber = "573")
    private BigDecimal projectPolicyCustomerPerformanceChainRatio;

    @ApiModelProperty("专案政策客户业绩-同比")
    @PerformanceValue(serialNumber = "573")
    private BigDecimal projectPolicyCustomerPerformanceYearRatio;

    @ApiModelProperty("专案政策客户业绩-占比")
    @PerformanceValue(serialNumber = "660")
    private BigDecimal projectPolicyCustomerPerformanceRate;

    @ApiModelProperty("专案政策交易客户数")
    @PerformanceValue(serialNumber = "574")
    private Integer projectPolicyTradingCustomerCount;

    @ApiModelProperty("专案政策客户客单价")
    @PerformanceValue(serialNumber = "575")
    private BigDecimal projectPolicyPerCustomer;

    @ApiModelProperty("专案政策客户旺金币折扣率")
    @PerformanceValue(serialNumber = "576")
    private BigDecimal projectPolicyWantGoldDiscountRatio;

    /**
     * 合伙人达成头部数据
     */

//    @ApiModelProperty(value = "意向客户数")
//    @PerformanceValue(serialNumber = "447")
//    private Integer intentionCustomerCount;

//    @ApiModelProperty("盘点率")
//    @PerformanceValue(serialNumber = "54")
//    private BigDecimal takeStockRate;

    @ApiModelProperty("县级市场数(区县数)")
    private Integer countryMarketCount;

    @ApiModelProperty("县级市场覆盖率")
    private BigDecimal countryMarketCoverage;

    /**
     * 颜色标识
     */
    @ApiModelProperty(value = "业绩达成率颜色标识:performanceAchievementRate")
    private String saleGoalAchievementColor;

    @ApiModelProperty("面试记录ID")
    private Integer interviewRecordId;

    @ApiModelProperty("数据更新时间")
    private String etlDate;

    @ApiModelProperty("是否被回访")
    private Boolean beCallBackedFlag;
    @ApiModelProperty("是否被拜访")
    private Boolean beVisitedFlag;
}
