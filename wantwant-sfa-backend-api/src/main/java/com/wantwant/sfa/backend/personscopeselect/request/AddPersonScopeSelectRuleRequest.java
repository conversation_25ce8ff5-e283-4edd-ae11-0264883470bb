package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.common.request
 * @Description:
 * @Date: 2025/2/18 16:25
 */
@Data
public class AddPersonScopeSelectRuleRequest {
    @ApiModelProperty("场景key")
    @NotNull(message = "PERSON_SELECTOR_SCENE_KEY_MUST_NOT_NULL")
    private String serviceKey;

    @ApiModelProperty("用户id")
    @NotNull(message = "COMMON_PERSON_MUST_NOT_NULL")
    private String employeeId;

    @ApiModelProperty("用户姓名：前端无可不传")
    private String employeeName;

    @ApiModelProperty("业务组id:前端可不传 使用请求头产品组")
    private Integer businessGroupId;

    @ApiModelProperty("人员选择id列表")
    private List<PersonScopeSelectRuleEmployeeInfoRequest> empInfos;

    @ApiModelProperty("组织选择列表")
    private List<PersonScopeSelectRuleOrganizationInfoRequest> organizationInfos;

    @ApiModelProperty("岗位类型列表")
    private List<PersonScopeSelectRulePositionTypeInfoRequest> positionTypeInfos;
}
