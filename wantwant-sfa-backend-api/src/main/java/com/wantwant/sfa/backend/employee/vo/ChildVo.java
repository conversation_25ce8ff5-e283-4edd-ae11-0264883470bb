package com.wantwant.sfa.backend.employee.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("下级列表返回")
public class ChildVo {

    @ApiModelProperty("id")
    private Integer employeeInfoId;

    @ApiModelProperty("工号")
    private String employeeId;

    @ApiModelProperty("姓名")
    private String employeeName;

    @ApiModelProperty("组织id")
    private String organizationId;

    @ApiModelProperty("组织名称")
    private String organizationName;

    @ApiModelProperty("岗位名称")
    private String positionName;

    @ApiModelProperty("n级")
    private Integer level;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty(value = "岗位类型id:结合 post type positionTypeId 三个字段")
    private Integer postTypeId;

}
