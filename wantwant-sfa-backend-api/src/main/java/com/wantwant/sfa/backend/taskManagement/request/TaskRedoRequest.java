package com.wantwant.sfa.backend.taskManagement.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2023/08/16/下午2:24
 */
@Data
@ApiModel("任务重办request")
@ToString
public class TaskRedoRequest extends TaskOperatorRequest {


    @ApiModelProperty("办理截止时间,格式yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "缺少办理截止时间")
    private LocalDate deadline;

    @ApiModelProperty("附件")
    private List<TaskAnnexRequest> appendix;

    @ApiModelProperty("是否需要发送消息:1.需要发送消息")
    private Integer sendMessage;
}
