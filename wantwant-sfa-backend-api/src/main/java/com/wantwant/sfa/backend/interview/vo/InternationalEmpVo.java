package com.wantwant.sfa.backend.interview.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("海外项目查人员查询返回值")
public class InternationalEmpVo {

    @ApiModelProperty("员工姓名")
    @ExcelProperty(value = "Name")
    private String employeeName;

    @ApiModelProperty("性别")
    @ExcelProperty(value = "Gender")
    private String gender;

    @ApiModelProperty("手机号")
    @ExcelProperty(value = "Mobile Number")
    private String mobile;

    @ApiModelProperty("身份证号")
    @ExcelProperty(value = "ID Number")
    private String idCard;

    @ApiModelProperty("家庭住址")
    @ExcelProperty(value = "Home Address")
    private String homeAddress;

    @ApiModelProperty("战区")
    @ExcelProperty(value = "Campaign Zone")
    private String areaName;

    @ApiModelProperty("大区")
    @ExcelProperty(value = "Area")
    private String vareaName;

    @ApiModelProperty("分公司")
    @ExcelProperty(value = "Branch Office")
    private String companyName;

    @ApiModelProperty("岗位")
    @ExcelProperty(value = "Position")
    private String position;

    @ApiModelProperty("渠道")
    @ExcelProperty("channel")
    private String channel;

    @ApiModelProperty("经销地区")
    @ExcelProperty("Distribution Area")
    private String salesAddress;

    @ApiModelProperty("办公地点")
    @ExcelProperty("Office Location")
    private String officeLocation;

    @ApiModelProperty("入职时间")
    @ExcelProperty("Onboarding Date")
    private String hireDate;

    @ApiModelProperty("在职日期")
    @ExcelProperty("Days Since Onboarding")
    private Long onTheJobDate;
    
    @ApiModelProperty("状态")
    @ExcelProperty("Employee Status")
    private String employeeStatus;
}
