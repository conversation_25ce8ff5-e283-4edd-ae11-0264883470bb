package com.wantwant.sfa.backend.order.api;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wantwant.commons.web.response.Response;
import com.wantwant.sfa.backend.order.request.*;
import com.wantwant.sfa.backend.order.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;


@Api(value = "OrderApi",tags = "订单相关接口")
public interface OrderApi {

    @ApiOperation(value = "订单列表", notes = "订单列表", httpMethod = "GET")
    @GetMapping("/new/order/list")
    Response<IPage<OrderListNewVo>> orderList(OrderListNewRequest request);

    @ApiOperation(value = "订单列表导出", notes = "订单列表导出", httpMethod = "GET")
    @GetMapping("/new/order/list/export")
    void orderListExport(OrderListNewRequest request);

    @ApiOperation(value = "订单详情", notes = "订单详情", httpMethod = "GET")
    @GetMapping("/new/order/detail")
    Response<OrderDetailNewVo> orderDetail(OrderDetailNewRequest request);

    @ApiOperation(value = "查询订单编号", notes = "查询订单编号", httpMethod = "POST")
    @PostMapping("/new/order/selectOrderNo")
    Response<List<String>> selectOrderNo(@RequestBody SearchOrderNoRequest searchOrderNoRequest);

    @ApiOperation(value = "订单商品列表", notes = "订单商品列表", httpMethod = "GET")
    @GetMapping("/new/order/sku/list")
    Response<IPage<OrderSkuListNewVo>> orderSkuList(OrderSkuListNewRequest request);


    @ApiOperation(value = "订单列表导出", notes = "订单列表导出")
    @RequestMapping(value = "/order/list/export",method = {RequestMethod.POST,RequestMethod.GET})
    void export(OrderListRequest request);


    @ApiOperation(value = "订单列表导出数量", notes = "订单列表导出数量")
    @RequestMapping(value = "/order/list/export/count",method = {RequestMethod.POST,RequestMethod.GET})
    Response count(@RequestBody OrderListRequest request);

    @ApiOperation(value = "订单列表离线导出")
    @RequestMapping(value = "/order/list/export/offline",method = {RequestMethod.POST,RequestMethod.GET})
    Response<JSONObject> orderListExportAsync(OrderListRequest request);

    @ApiOperation(value="下单通知")
    @RequestMapping(value = "/order/notice",method={RequestMethod.POST})
    Response orderNotice(@RequestBody OrderNoticeRequest request);

    @ApiOperation(value="品相利润")
    @GetMapping(value = "/order/queryGrantByPage")
    Response<IPage<OrderGrantVO>> queryGrantByPage(OrderGrantRequest request);

    @ApiOperation(value="品相利润导出")
    @GetMapping(value = "/order/exportGrant")
    void exportGrant(OrderGrantRequest request);

    @ApiOperation(value="品相利润监控")
    @GetMapping(value = "/order/queryMonitorByPage")
    Response<IPage<OrderMonitorVO>> queryMonitorByPage(OrderMonitorRequest request);

    @ApiOperation(value="品相利润监控导出")
    @GetMapping(value = "/order/exportMonitor")
    void exportMonitor(OrderMonitorRequest request, HttpServletResponse response);

    @ApiOperation(value = "品相利润监控批量处理", notes = "品相利润监控批量处理")
    @PostMapping("/order/monitorBatch")
    Response monitorBatch(@Valid @RequestBody MonitorBatchRequest batchRequest);


    @ApiOperation(value="利润趋势分析")
    @GetMapping(value = "/order/listProfit")
    Response<List<OrderMonitorVO>> listProfit(@ApiParam(name = "employeeInfoId", value = "员工ID", required = true) @RequestParam(value = "employeeInfoId") Integer employeeInfoId);

    @ApiOperation(value = "品相利润监控订单批量处理", notes = "品相利润监控订单批量处理")
    @PostMapping("/order/monitorOrderBatch")
    Response monitorOrderBatch(@Valid @RequestBody MonitorOrderBatchRequest batchRequest);


    @ApiOperation(value = "批量导入品相利润发放", notes = "批量导入数据", httpMethod = "POST")
    @PostMapping("/order/batchGrantUpload")
    Response<OrderBatchUploadVo> orderBatchGrantUpload(MultipartHttpServletRequest request);

    @ApiOperation(value = "批量导入品相利润发放失败数据导出", notes = "失败数据导出", httpMethod = "POST")
    @PostMapping("/order/batchGrantResultExport")
    void orderBatchResultExport(@RequestBody List<OrderBatchUploadListVO> request);

    @ApiOperation(value="品相利润")
    @GetMapping(value = "/order/queryOrderType")
    Response<List<OrderTypeVO>> queryOrderType();
}
