package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.personscopeselect.request
 * @Description:
 * @Date: 2025/2/20 11:09
 */
@Data
public class QueryPersonScopeSelectRuleInfoRequest {
    @ApiModelProperty("id")
    @NotNull(message = "PERSON_SELECTOR_RULE_ID_MUST_NOT_NULL")
    private Long id;
}
