package com.wantwant.sfa.backend.realData.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("新商品数据参数")
@Data
public class CommodityDataNewLineEnVo {

    @ApiModelProperty(value = "产品线")
    @Excel(name = "Product Line", replace = {"-_null"})
    private String lineName;

    @ApiModelProperty(value = "当期业绩")
    @Excel(name = "Performance-Achieved", numFormat = "#.#")
    private BigDecimal monthlyPerformance;

    @ApiModelProperty(value = "当期业绩环比")
    @Excel(name = "Performance-Year-on-year comparison", suffix = "%", numFormat = "#.#")
    private BigDecimal monthlyPerformanceRatio;

    @ApiModelProperty(value = "同比业绩")
    @Excel(name = "Performance-Year-on-Year Performance", numFormat = "#.#")
    private BigDecimal performanceYoy;

    @ApiModelProperty(value = "当期业绩同比")
    @Excel(name = "Performance-Year-on-year", suffix = "%", numFormat = "#.#")
    private BigDecimal monthlyPerformanceYearRatio;

    @ApiModelProperty(value = "当期业绩占比")
    @Excel(name = "Performance-Proportion", suffix = "%", numFormat = "#.#")
    private BigDecimal monthlyPerformanceRate;

    @ApiModelProperty(value = "预订单业绩")
    @Excel(name = "Performance-Pre-order performance", numFormat = "#.#")
    private BigDecimal bookingPerformance;

    @ApiModelProperty(value = "预订单未发货业绩")
    @Excel(name = "Performance-Pre-order sales not shipped", numFormat = "#.#")
    private BigDecimal bookingUnshippedPerformance;

    @ApiModelProperty(value = "现金业绩")
    @Excel(name = "Performance-Cash performance", numFormat = "#.#")
    private BigDecimal cashPerformance;

    @Excel(name = "Performance-Wang coin discount rate", suffix = "%", numFormat = "#.#")
    @ApiModelProperty("旺金币折扣率")
    private BigDecimal wantGoldDiscountRatio;

    @ApiModelProperty(value = "交易客户数")
    @Excel(name = "Trading clients", replace = {"-_null"})
    private Integer tradingClients;

    @ApiModelProperty(value = "交易客户数环比")
    @Excel(name = "Trading clients-Chain", suffix = "%", numFormat = "#.#")
    private BigDecimal tradingClientsRatio;

    @ApiModelProperty(value = "交易客户数同比")
    @Excel(name = "Trading clients-Year", suffix = "%", numFormat = "#.#")
    private BigDecimal tradingClientsYearRatio;

    @ApiModelProperty(value = "客单价")
    @Excel(name = "Average Order Value", numFormat = "#.#")
    private BigDecimal perCustomer;

    @ApiModelProperty(value = "客单价环比")
    @Excel(name = "Average Order Value-Chain", suffix = "%", numFormat = "#.#")
    private BigDecimal perCustomerRatio;

    @ApiModelProperty(value = "客单价同比")
    @Excel(name = "Average Order Value-Year", suffix = "%", numFormat = "#.#")
    private BigDecimal perCustomerYearRatio;

    @ApiModelProperty(value = "复购率")
    @Excel(name = "Repurchase Rate", suffix = "%", numFormat = "#.#")
    private BigDecimal customerRepurchaseRatio;

}
