package com.wantwant.sfa.backend.map.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.sfa.common.architecture.utils.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@ApiModel("地图-汇总-考勤")
public class MapDetail1Vo {

    @ApiModelProperty("打卡日期")
    private LocalDate attendanceDate;

    @ApiModelProperty(hidden = true)
    private String longitude;

    @ApiModelProperty(hidden = true)
    private String latitude;

    @ApiModelProperty("距昨日上班点距离")
    private double prePointDistance;

    @ApiModelProperty("当日距上班点最远距离")
    private double farthestDistance;

    @ApiModelProperty("当日距上班点最远距离点")
    private String farthestDistanceName;

    @ApiModelProperty(value = "序号-用来计算标记最远距离", hidden = true)
    private Integer indexTag;

    @ApiModelProperty("当日总距离")
    private double totalDistance;

    @ApiModelProperty("同组同岗-当日总距离-最大值")
    private double totalDistanceMax;

    @ApiModelProperty("同组同岗-当日总距离-平均值")
    private double totalDistanceAvg;

    @ApiModelProperty("上班打卡时间(HH:mm)")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private LocalTime timeOnWork;

    @ApiModelProperty("上班打卡名称")
    private String attendanceNameOnWork;

    @ApiModelProperty("上班打卡时间")
    private LocalDateTime attendanceTimeOnWork;

    @ApiModelProperty("下班打卡时间(HH:mm)")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private LocalTime timeOffWork;

    @ApiModelProperty("下班打卡名称")
    private String attendanceNameOffWork;

    @ApiModelProperty("下班打卡时间")
    private LocalDateTime attendanceTimeOffWork;

    @ApiModelProperty("考勤打卡状态名称")
    private String attendanceStatusName;
    @ApiModelProperty("考勤打卡异常类型名称")
    private String attendanceExecptionTypeName;
}
