package com.wantwant.sfa.backend.order.request;

import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(value = "订单列表")
public class OrderListNewRequest extends PageParam implements Serializable {
    private static final long serialVersionUID = 5156343128468133797L;

    @ApiModelProperty(value = "操作人工号", required = true)
    @NotBlank(message = "操作人工号不能为空")
    private String person;
    @ApiModelProperty(value = "操作人的组织", hidden = true)
    private List<String> personOrganizationIds;
    @ApiModelProperty(value = "组织类型", hidden = true)
    private String personOrganizationType;
    @ApiModelProperty(value = "业务组", hidden = true)
    private Integer personBusinessGroup;

    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @ApiModelProperty("订单开始日期")
    private LocalDate orderStartDate;

    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @ApiModelProperty("订单结束日期")
    private LocalDate orderEndDate;

    @ApiModelProperty(value = "订单状态")
    private String status;

    @ApiModelProperty(value = "订单信息")
    private String orderKey;

    @ApiModelProperty(value = "客户信息")
    private String customer;

    @ApiModelProperty(value = "业务姓名")
    private String employeeName;

    @ApiModelProperty(value = "组织id")
    private String organizationId;
    @ApiModelProperty(value = "组织类型", hidden = true)
    private String organizationType;

    @ApiModelProperty("订单类型")
    private Integer type;

    @ApiModelProperty("支付方式 1:组合支付 2:现金支付 3:旺金币支付")
    private Integer method;

    @ApiModelProperty(value = "金额区间:1.0～1000 2.1000~3000 3.3000~5000 4.5000~ 0.不限")
    private Integer priceRange;

    @ApiModelProperty("菜单来源类型 1:日报")
    private Integer menuType;

    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @ApiModelProperty("订单业绩统计开始日期")
    private LocalDate performanceStatisticsStartDate;

    @DateTimeFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd)
    @ApiModelProperty("订单业绩统计结束日期")
    private LocalDate performanceStatisticsEndDate;

    @ApiModelProperty("sku名称")
    private String skuName;

    @ApiModelProperty("sku")
    private String sku;

    @ApiModelProperty(value = "排序名称")
    private String sortName;

    @ApiModelProperty(value = "排序类型(asc正序 desc倒序)")
    private String sortType;

    @ApiModelProperty(value = "造旺订单渠道标记", hidden = true)
    private String orderChannel;

    @ApiModelProperty(value = "时区", hidden = true)
    private String timeZone;

}
