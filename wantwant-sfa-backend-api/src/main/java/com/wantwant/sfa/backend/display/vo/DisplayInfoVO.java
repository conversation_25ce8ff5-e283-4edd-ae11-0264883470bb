package com.wantwant.sfa.backend.display.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.backend.display.enums.ProcessStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 特陈审批列表
 *
 * @date 5/19/22 5:24 PM
 * @version 1.0
 */
@Data
public class DisplayInfoVO implements Serializable {

    private static final long serialVersionUID = -4626063472647253605L;

    @ApiModelProperty("id")
    private Integer id;

    @Excel(name = "申请编号",orderNum = "1")
    @ApiModelProperty("申请编号")
    private String applicationNo;

    @ApiModelProperty("审批Id")
    private Integer processId;

    @ApiModelProperty("流程Id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processDetailId;

    @ApiModelProperty(value = "战区")
    private String area;

    private String areaCode;

    @Excel(name = "大区",orderNum = "2")
    @ApiModelProperty(value = "大区")
    private String varea;

    @Excel(name = "分公司",orderNum = "4")
    @ApiModelProperty("分公司")
    private String company;

    @ApiModelProperty("分公司CODE")
    private String companyCode;

    private String departmentId;
    @Excel(name = "营业所",orderNum = "5")
    @ApiModelProperty("营业所")
    private String departmentName;

    @ApiModelProperty("标准市场")
    private String marketName;

    @Excel(name = "合伙人",orderNum = "6")
    @ApiModelProperty("合伙人")
    private String employeeName;

    @Excel(name = "代申请人",orderNum = "6")
    @ApiModelProperty("代申请人")
    private String agentEmpName;

    @Excel(name = "代申请人岗位",orderNum = "6")
    @ApiModelProperty("代申请人岗位")
    private String agentPosition;


    @ApiModelProperty("合伙人id")
    private String empId;

    @Excel(name = "建档客户编号",orderNum = "7")
    @ApiModelProperty("建档客户编号")
    private String customerId;

    @Excel(name = "建档客户名称",orderNum = "8")
    @ApiModelProperty("建档客户名称")
    private String customerName;

    @ApiModelProperty("手机号")
    private String customerPhone;

    /* 陈列客户信息start */

    @ApiModelProperty(value = "陈列客户编号")
    private String displayCustomerNo;

    @Excel(name = "客户姓名",orderNum = "9")
    @ApiModelProperty(value = "客户姓名")
    private String displayCustomerName;

    @Excel(name = "营业执照名称",orderNum = "10")
    @ApiModelProperty("营业执照名称")
    private String listedPersonName;

    /**
     * 客户类型:0-默认、1-量贩店、2-便利店、3连锁卖场、4-学校超市、5-干杂店、6-自贩机
     */
    private Integer displayCustomerType;
    @Excel(name = "陈列客户类型",orderNum = "11")
    @ApiModelProperty(value = "陈列客户类型")
    private String displayCustomerTypeStr;

    @Excel(name = "联系电话",orderNum = "12")
    @ApiModelProperty("联系电话")
    private String listedPersonPhone;

    @Excel(name = "门店名称",orderNum = "13")
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @Excel(name = "门店地址",orderNum = "14")
    @ApiModelProperty("门店地址")
    private String storeAddress;

    @Excel(name = "门店面积",orderNum = "15")
    @ApiModelProperty("门店面积")
    private String storeArea;

    /* 陈列客户信息end */

    @Excel(name = "申请时间",exportFormat = "yyyy-MM-dd HH:mm:ss",orderNum = "16")
    @ApiModelProperty(value = "申请时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss,timezone = "GMT+8")
    private Date createTime;

    @Excel(name = "核准费用",orderNum = "17")
    @ApiModelProperty(value = "核准费用")
    private BigDecimal quota;

    @ApiModelProperty(value = "区域经理建议额度")
    private BigDecimal deptQuota;

    @Excel(name = "驳回次数",orderNum = "25")
    @ApiModelProperty(value = "驳回次数")
    private Integer rejectNumber;

    @Excel(name = "驳回人员",orderNum = "26")
    @ApiModelProperty(value = "驳回人员")
    private String rejectName;

    @Excel(name = "驳回原因",orderNum = "27")
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    private Integer status;
    @Excel(name = "状态",orderNum = "28")
    @ApiModelProperty(value = "状态")
    private String statusStr;

    @Excel(name = "操作天数",orderNum = "29")
    @ApiModelProperty(value = "操作天数")
    private Integer days;

    /**
     * 是否发放
     */
    private Integer isGrant;
    @Excel(name = "发放状态",orderNum = "30")
    @ApiModelProperty(value = "发放状态")
    private String isGrantStr;

    @ApiModelProperty(value = "发放失败原因")
    private String grantFailureReason;

    private Integer auditStatus;
//    @Excel(name = "营运稽核状态",orderNum = "31")
    @ApiModelProperty(value = "营运稽核状态")
    private String auditStatusStr;

    @Excel(name = "稽核时间",exportFormat = "yyyy-MM-dd HH:mm:ss",orderNum = "32")
    @ApiModelProperty(value = "稽核时间(yyyy-MM-dd HH:mm:ss)")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss,timezone = "GMT+8")
    private LocalDateTime auditTime;

    @Excel(name = "合伙人异常",replace = {"否_0", "是_1","终端不存在_2"},orderNum = "37")
    @ApiModelProperty(value = "合伙人异常(0:正常,1:异常)")
    private Integer partnerAnomaly;

    @Excel(name = "区域经理异常",replace = {"否_0", "是_1"},orderNum = "38")
    @ApiModelProperty(value = "区域经理异常(0:正常,1:异常)")
    private Integer departmentAnomaly;

    @Excel(name = "异常原因",orderNum = "35")
    @ApiModelProperty(value = "异常原因")
    private String anomalyReason;

    @ApiModelProperty(value = "经度")
    private String longitude;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty("业务手机号")
    private String employeePhone;

    @ApiModelProperty(value = "本月发放额度")
    private BigDecimal monthDisplayQuota;

    @ApiModelProperty(value = "示例图片")
    private String samplePictureUrl;

    @ApiModelProperty(value = "陈列近景图片")
    private String nearPictureUrl;

    @ApiModelProperty(value = "陈列远景图片")
    private String farPictureUrl;

    @ApiModelProperty(value = "营业执照照片")
    private String licenseImageUrl;

    @ApiModelProperty(value = "门店照片")
    private String storeImageUrl;

    @ApiModelProperty(value = "客户门头照")
    private String storeHeadImageUrl;

    @ApiModelProperty(value = "到店门头照")
    private String arrivalStoreHeadImageUrl;

    @ApiModelProperty(value = "区域经理-上传合照")
    private String departmentImageUrl;

    @ApiModelProperty(value = "通关密码")
    private String passCode;

    @ApiModelProperty(value = "是否可审批(0:是,1:否)")
    private Integer isAudit;

    /**
     * 陈列标准中型现场审批
     */
    @ApiModelProperty(value = "是否现场审批(0:是,1:否)")
    private Integer isOnSite;

    @ApiModelProperty(value = "合伙人类型")
    private String positionType;

    @ApiModelProperty(value = "现场稽核-上传合照")
    private String checkImageUrl;

    private Integer checkStatus;
    @Excel(name = "现场稽核状态",orderNum = "36")
    @ApiModelProperty(value = "现场稽核状态")
    private String checkStatusStr;

    @Excel(name = "现场稽核人员",orderNum = "37")
    @ApiModelProperty(value = "稽核人员")
    private String checkReviewer;

    @Excel(name = "现场稽核时间",orderNum = "38")
    @ApiModelProperty(value = "现场稽核时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss,timezone = "GMT+8")
    private LocalDateTime checkTime;

    @Excel(name = "现场稽核原因",orderNum = "39")
    @ApiModelProperty(value = "现场稽核原因")
    private String checkFailureReason;

    /**
     * 是否需要区域经理线下审核:当月总订单 * N%（可配置），向下取整
     */
    @ApiModelProperty(value = "是否区域经理线下审核(0:否,1:是)")
    private Integer isNeedSite;

    @ApiModelProperty(value = "城市等级")
    private String cityLevel;


    private Integer partnerMemberKey;

    /**
     * 发放时间
     */
    private String releaseTime;

    @ApiModelProperty(value = "申请类型(0:合伙人申请,1:区域经理申请,2:区域总监申请)")
    private Integer applyType;

    @ApiModelProperty(value = "代申请人memberKey")
    private Long applyMemberKey;

    @ApiModelProperty(value = "代申请人姓名")
    private String applyName;

    @ApiModelProperty(value = "发放类型(1:特陈额度,2:分公司旺金币,3:战区旺金币)")
    private Integer quotaType;

    @ApiModelProperty("发放类型")
    @Excel(name = "发放类型",orderNum = "30")
    private String quotaTypeStr;

    @Excel(name = "陈列费用币种",orderNum = "33")
    private String feeType;

    @Excel(name = "币种子类",orderNum = "34")
    private String feeSubType;

    @ApiModelProperty("实际发放费用币种(0:通用币,1:产品组币,2:spu币,3:年节币)")
    @Excel(name = "实际发放陈列费用币种",replace={"-_null","通用币_0","产品组币_1","spu币_2","年节币_3"},orderNum = "35")
    private Integer useFeeTypeCode;

    @ApiModelProperty("实际发放币种子类")
    @Excel(name = "实际发放币种子类",orderNum = "36")
    private String useFeeSubType;

    @ApiModelProperty("实际发放币种子类code")
    private String useFeeSubTypeCode;

    @ApiModelProperty(value = "活动规则(1:有规则,2:无规则)")
    private Integer ruleType;

    @ApiModelProperty(value = "协议文件")
    private List<DisplayProtocolFileVO> displayProtocolFileVOList;

    @ApiModelProperty(value = "陈列客户渠道类型  1-直营渠道，2-传统渠道")
    private Integer channelType;
    @ApiModelProperty(value = "陈列客户渠道类型  1-直营渠道，2-传统渠道")
    private String channelTypeStr;

    @ApiModelProperty(value = "陈列客户门店类型  1-社区、2-校园、3-餐饮、4-写字楼/商圈、5-交通站点、6-休闲场所、7-标超、8-量贩、9-零食系统")
    private Integer salesroomType;

    @ApiModelProperty(value = "陈列客户门店类型  1-社区、2-校园、3-餐饮、4-写字楼/商圈、5-交通站点、6-休闲场所、7-标超、8-量贩、9-零食系统")
    private String salesroomTypeStr;

    private String evidenceUrl;

    @ApiModelProperty(value = "审核人员照片")
    private String auditPersonUrl;

    @ApiModelProperty(value = "举证照片")
    private List<String> evidenceUrls;

    private String organizationId;
    /**
     * 当前节点
     */
    private Integer processType;


    private String expenseReceiptImagesStr;

    @ApiModelProperty(value = "费用签收单图片")
    private List<String> expenseReceiptImages;

    @ApiModelProperty(value = "协议开始时间")
    private LocalDate displayAgreementStartDate;

    @ApiModelProperty(value = "协议结束时间")
    private LocalDate displayAgreementEndDate;


    public String getStatusStr() {
        return ProcessStatus.findProcessStatusByCode(Optional.ofNullable(status).orElse(0)).getStatusName();
    }

    public String getDisplayCustomerTypeStr() {
        if (Objects.nonNull(displayCustomerType)){
            switch (displayCustomerType){
                case 0 :
                    return "默认";
                case 1 :
                    return "综合卖场";
                case 2 :
                    return "超市/便利";
                case 3 :
                    return "小卖部";
                case 4 :
                    return "烟酒杂货";
                case 5 :
                    return "果摊奶站";
                case 6 :
                    return "自贩机";
                case 7 :
                    return "餐饮";
                default:
                    return "";
            }
        }else {
            return "";
        }
    }

    public String getAuditStatusStr() {
        if (Objects.nonNull(auditStatus)){
            switch (auditStatus){
                case 0 :
                    return "未稽核";
                case 1 :
                    return "正常";
                case 2 :
                    return "异常";
                default:
                    return "";
            }
        }else{
            return "";
        }
    }


    public String getCheckStatusStr() {
        if (Objects.nonNull(checkStatus)){
            switch (checkStatus){
                case 0 :
                    return "未稽核";
                case 1 :
                    return "正常";
                case 2 :
                    return "异常";
                default:
                    return "";
            }
        }else{
            return "";
        }
    }

    public String getIsGrantStr() {
        if (Objects.nonNull(isGrant)){
            switch (isGrant){
                case 0 :
                    return "未发放";
                case 1 :
                    return "审批通过待发放";
                case 2 :
                    return "发放成功";
                case 3 :
                    return "发放失败";
                default:
                    return "";
            }
        }else{
            return "";
        }
    }
}
