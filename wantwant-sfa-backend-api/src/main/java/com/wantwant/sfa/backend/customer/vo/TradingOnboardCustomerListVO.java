package com.wantwant.sfa.backend.customer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.wantwant.sfa.backend.coverter.LocalDateConverter;
import com.wantwant.sfa.backend.realData.anno.PerformanceValue;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @Author: Navy
 * @Package: com.wantwant.sfa.backend.customer.vo
 * @Description:
 * @Date: 2024/8/5 8:50
 */
@ApiModel(value = "本组客户查询列表返回", description = "本组客户查询列表返回")
@Data
public class TradingOnboardCustomerListVO {

    @ColumnWidth(10)
    @ExcelProperty(value = "时间",index = 0)
    @ApiModelProperty(value = "时间")
    private String theYearMonth;

    @ApiModelProperty("组织名称-全称")
    @ExcelIgnore
    private String fullOrganizationName;

    @ColumnWidth(10)
    @ExcelProperty(value = "战区",index = 1)
    @ApiModelProperty(value = "战区")
    private String areaName;

    @ColumnWidth(10)
    @ExcelProperty(value = "大区",index = 2)
    @ApiModelProperty(value = "大区")
    private String vareaName;

    @ColumnWidth(10)
    @ExcelProperty(value = "省区",index = 3)
    @ApiModelProperty(value = "省区")
    private String provinceName;

    @ColumnWidth(10)
    @ExcelProperty(value = "分公司",index = 4)
    @ApiModelProperty(value = "分公司")
    private String companyName;

    @ColumnWidth(10)
    @ExcelProperty(value = "营业所",index = 5)
    @ApiModelProperty(value = "营业所")
    private String departmentName;
    @ExcelIgnore
    private String departmentId;

    @ApiModelProperty("memberKey")
    @ExcelIgnore
    private Long memberKey;

    @ColumnWidth(20)
    @ExcelProperty(value = "管理人员岗位",index = 6)
    @ApiModelProperty(value = "管理人员岗位")
    private String managerPostName;

    @ColumnWidth(20)
    @ExcelProperty(value = "管理人员头像",index = 7)
    @ApiModelProperty(value = "管理人员头像")
    private String managerPicUrl;

    @ExcelIgnore
    @ApiModelProperty(value = "管理人员组织id")
    private String managerOrganizationId;

    @ColumnWidth(20)
    @ExcelProperty(value = "管理人员姓名",index = 8)
    @ApiModelProperty(value = "管理人员姓名")
    private String managerName;

    @ColumnWidth(20)
    @ExcelProperty(value = "交易人岗位",index = 9)
    @ApiModelProperty(value = "交易人岗位")
    private String postName;

    @ColumnWidth(20)
    @ExcelProperty(value = "交易人头像",index = 10)
    @ApiModelProperty(value = "交易人头像")
    private String picUrl;

    @ColumnWidth(20)
    @ExcelProperty(value = "交易人姓名",index = 11)
    @ApiModelProperty(value = "交易人姓名")
    private String name;

    @ExcelIgnore
    @ApiModelProperty(value = "交易人组织id")
    private String organizationId;

    @ColumnWidth(20)
    @ExcelProperty(value = "入职日期",index = 12,converter = LocalDateConverter.class)
    @ApiModelProperty(value = "入职日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd")
    private LocalDate entryDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "离职日期",index = 13,converter = LocalDateConverter.class)
    @ApiModelProperty(value = "离职日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @com.alibaba.excel.annotation.format.DateTimeFormat("yyyy-MM-dd")
    private LocalDate dischargeDate;

    @ApiModelProperty("客户等级")
    @ColumnWidth(15)
    @ExcelProperty(value = "客户等级",index = 14)
    private String partnerGrande;

    @ColumnWidth(15)
    @ExcelProperty(value = "生命周期",index = 15)
    @ApiModelProperty("客户生命周期")
    private String partnerLifeCycle;

    @ColumnWidth(15)
    @ApiModelProperty("最近一次下单时间")
    @ExcelProperty(value = "最近一次下单时间",index = 16)
    @GlobalTimezone
    private String recentOrderTime;

    @ColumnWidth(15)
    @ApiModelProperty("距离上次交易天数")
    @ExcelProperty(value = "距离上次交易天数",index = 17)
    private Integer gapDate;

    @ColumnWidth(15)
    @ApiModelProperty("最近一次拜访")
    @ExcelProperty(value = "最近一次拜访",index = 18)
    @GlobalTimezone
    private String recentVisitTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "累计业绩",index = 19)
    @ApiModelProperty(value = "累计业绩")
    private BigDecimal accumulateItemsSupplyTotal;

    @ColumnWidth(15)
    @ApiModelProperty("单单价")
    @ExcelProperty(value = "单单价",index = 20)
    private BigDecimal orderUnitPrice;

    @ColumnWidth(15)
    @ExcelProperty(value = "累计订单数",index = 21)
    @ApiModelProperty(value = "累计订单数")
    private Integer accumulateOrderNumber;

    @ColumnWidth(15)
    @ExcelProperty(value = "月均业绩",index = 22)
    @ApiModelProperty("月均业绩")
    private BigDecimal itemsSupplyTotalMonthlyAverage;

    @ColumnWidth(15)
    @ExcelProperty(value = "月均订单数",index = 23)
    @ApiModelProperty("月均订单数")
    private BigDecimal orderNumberMonthlyAverage;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单业绩(预定本月)"},index = 24)
    @ApiModelProperty(value = "预订单业绩(预定本月)")
    @PerformanceValue(serialNumber = "379")
    private BigDecimal bookingPerformance;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单业绩占比(预定本月)"},index = 25)
    @ApiModelProperty(value = "预订单业绩占比(预定本月)")
    @PerformanceValue(serialNumber = "377")
    private BigDecimal bookingPerformanceRate;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单业绩(预定次月)"},index = 26)
    @ApiModelProperty(value = "预订单业绩(预定次月)")
    private BigDecimal nextBookingPerformance;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单业绩占比(预定次月)"},index = 27)
    @ApiModelProperty(value = "预订单业绩占比(预定次月)")
    private BigDecimal nextBookingPerformanceRate;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单未发货业绩(本月)"},index = 28)
    @ApiModelProperty(value = "预订单未发货业绩(本月)")
    @PerformanceValue(serialNumber = "318")
    private BigDecimal advanceOrderUnshippedPerformance;

    @ColumnWidth(20)
    @ExcelProperty(value = {"预订单","预订单未发货业绩(次月)"},index = 29)
    @ApiModelProperty(value = "预订单未发货业绩(次月)")
    @PerformanceValue(serialNumber = "318")
    private BigDecimal nextAdvanceOrderUnshippedPerformance;

    @ColumnWidth(20)
    @ExcelProperty(value = "旺金币业绩占比",index = 30)
    @ApiModelProperty(value = "旺金币业绩占比")
    @PerformanceValue(serialNumber = "469")
    private BigDecimal goldenCoinPerformanceRate;

    @ColumnWidth(20)
    @ExcelProperty(value = "预储值余额",index = 31)
    @ApiModelProperty("储值余额")
    private BigDecimal storedValueBalance;

    @ColumnWidth(20)
    @ExcelProperty(value = "旺金币余额",index = 32)
    @ApiModelProperty("旺金币余额")
    private BigDecimal goldBalance;



    @ColumnWidth(20)
    @ExcelIgnore
    @ApiModelProperty("手机号")
    private String mobile;

    @ExcelIgnore
    @ApiModelProperty(value = "开户类型：0 - 意向客户、1 - 潜在客户、2-陈列客户")
    private Integer openTypeCode;

    @ExcelIgnore
    @ApiModelProperty(value = "客户类型：3流通 5直营")
    private Integer customerTypeCode;

}
