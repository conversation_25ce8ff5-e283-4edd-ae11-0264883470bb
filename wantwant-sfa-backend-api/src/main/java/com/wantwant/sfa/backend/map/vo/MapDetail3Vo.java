package com.wantwant.sfa.backend.map.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("地图-汇总-拜访")
public class MapDetail3Vo {

    @ApiModelProperty("拜访数量")
    private Integer visitNum;
    @ApiModelProperty("拜访时长")
    private String visitTimeDurationAvg;
    @ApiModelProperty("潜在占比")
    private String potentialRate;

    @ApiModelProperty(value = "客户类型：null-总的、 0-批发、1-终端、2-合伙人、3 - 建档")
    private Integer customerType;
    @ApiModelProperty("客户类型")
    private String customerTypeName;

    public void init(Integer customerType, String customerTypeName, String secondUnit) {
        this.setCustomerType(customerType);
        this.setCustomerTypeName(customerTypeName);
        this.setVisitNum(0);
        this.setVisitTimeDurationAvg("0" + secondUnit);
        this.setPotentialRate("0");
    }
}
