package com.wantwant.sfa.backend.attendance.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("业务管理人员考勤Vo")
@ToString
public class AttendanceListV2Vo {

    @ApiModelProperty("0-出勤日，1-节假日，2-休息日")
    private int workDayType;

    @ApiModelProperty("主键")
    private Integer id;

    @ApiModelProperty("日期")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd",timezone = "GMT+8")
    @Excel(name = "日期", replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd)
    private LocalDate date;

    @ApiModelProperty("入职日期")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDateTime onboardTime;

    @ApiModelProperty("战区")
    @Excel(name = "战区",replace = {"-_null"})
    private String area;

    @ApiModelProperty("大区")
    @Excel(name = "大区",replace = {"-_null"})
    private String virtualAreaName;

    @ApiModelProperty("省区")
    @Excel(name = "省区",replace = {"-_null"})
    private String provinceName;

    @ApiModelProperty("分公司")
    @Excel(name = "分公司",replace = {"-_null"})
    private String company;

    @ApiModelProperty("营业所")
    @Excel(name = "营业所",replace = {"-_null"})
    private String departmentName;

    @ApiModelProperty("工号")
    @Excel(name = "工号",replace = {"-_null"})
    private String memberId;

    @ApiModelProperty("姓名")
    @Excel(name = "姓名",replace = {"-_null"})
    private String employeeName;

    @ApiModelProperty("职位")
    @Excel(name = "职位",replace = {"-_null"})
    private String positionName;

    private Integer positionTypeId;

    @ApiModelProperty("上班打卡时间")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @Excel(name = "上班打卡时间", replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    @GlobalTimezone
    private LocalDateTime attendanceTime;

    @ApiModelProperty("打卡地址")
    @Excel(name = "打卡地址",replace = {"-_null"})
    private String attendanceAddress;

    @ApiModelProperty("考勤状态0：正常\\n1：异常")
    @Excel(name = "考勤状态", replace = {"正常_0", "异常_1"})
    private int attendanceStatus;

    @ApiModelProperty("1:未打卡 2迟到 3 无效打卡")
    @Excel(name = "异常原因", replace = {"-_null","未打卡_1", "迟到_2"})
    private Integer attendanceExecptionType;

    @ApiModelProperty("打卡点状态(1.正常 2.异常)")
    @Excel(name = "打卡点状态", replace = {"-_null","正常_1", "异常_2"})
    private Integer attendancePointStatus;

    @ApiModelProperty("0:未稽核 1:正常 2:异常")
    @Excel(name = "稽核结果", replace = {"未稽核_0", "正常_1", "异常_2"})
    private int auditStatus;

    @ApiModelProperty("稽核原因 1(居家打卡) 2(代打卡)  3(电子设备打卡) 4(未穿工装) 5(打卡地不符) 6(打卡背景不符)")
    @Excel(name = "原因", replace = {"-_null", "居家打卡_1", "代打卡_2", "电子设备打卡_3", "未穿工装_4","打卡地不符_5","打卡背景不符_6"})
    private Integer auditReason;

    @ApiModelProperty("操作人")
    @Excel(name = "操作人",replace = {"-_null"})
    private String auditName;

    @ApiModelProperty("打卡图片")
    private String picUrl;

    @ApiModelProperty("人脸识别图片")
    private String faceUrl;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("人脸相似度")
    @Excel(name = "人脸相似度(%)",replace = {"-_null"})
    private BigDecimal faceSimilarScore;

    @ApiModelProperty("入职照片")
    private String  signUpPicUrl;

    @ApiModelProperty(value = "是否是业务BD")
    private boolean businessBD;

    @ApiModelProperty(value = "是否超出范围：1.超出范围 0.未超出范围")
    private Integer exceedsRange;

    @ApiModelProperty(value = "超出米数")
    private BigDecimal distanceExceeded;

    @ApiModelProperty(value = "最近点位合伙人信息")
    private NearestPartnerVo nearestPartnerVo;

    @ApiModelProperty(hidden = true)
    private Integer employeeInfoId;
}

