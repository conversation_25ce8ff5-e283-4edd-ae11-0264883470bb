package com.wantwant.sfa.backend.personscopeselect.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CopyPersonScopeSelectRuleRequest {
    @ApiModelProperty("id")
    @NotNull(message = "PERSON_SELECTOR_RULE_ID_MUST_NOT_NULL")
    private Long id;

    @ApiModelProperty("场景key")
    @NotNull(message = "PERSON_SELECTOR_SCENE_KEY_MUST_NOT_NULL")
    private String serviceKey;

    @ApiModelProperty("用户id")
    @NotNull(message = "COMMON_PERSON_MUST_NOT_NULL")
    private String employeeId;

    @ApiModelProperty("用户姓名：前端无可不传")
    private String employeeName;

    @ApiModelProperty("业务组id:前端可不传 使用请求头产品组")
    private Integer businessGroupId;
}
