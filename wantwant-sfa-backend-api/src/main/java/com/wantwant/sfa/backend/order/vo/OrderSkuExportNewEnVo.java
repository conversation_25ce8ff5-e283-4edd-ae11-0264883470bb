package com.wantwant.sfa.backend.order.vo;


import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel("订单商品导出")
public class OrderSkuExportNewEnVo {

    @ApiModelProperty("产品组")
    @Excel(name = "Business group", replace = {"-_null"})
    private String businessGroupName;

    @ApiModelProperty("战区名称")
    private String areaName;

    @ApiModelProperty("大区名称")
    @Excel(name = "Area", replace = {"-_null"})
    private String vareaName;

    @ApiModelProperty("省区名称")
    private String provinceName;

    @ApiModelProperty("分公司名称")
    @Excel(name = "Branch Company", replace = {"-_null"})
    private String companyName;

    @ApiModelProperty("区域经理层名称")
    @Excel(name = "Business Office", replace = {"-_null"})
    private String departmentName;

    @ApiModelProperty("专员姓名")
    @Excel(name = "Partner's name", replace = {"-_null"})
    private String employeeName;

    @ApiModelProperty("客户姓名")
    @Excel(name = "Customer Name", replace = {"-_null"})
    private String userName;

    @ApiModelProperty("下单人手机号")
    @Excel(name = "Mobile Number", replace = {"-_null"})
    private String mobileNumber;

    @ApiModelProperty("sku")
    @Excel(name = "SKU", replace = {"-_null"})
    private String sku;

    @ApiModelProperty("sku名称")
    @Excel(name = "SKU Name", replace = {"-_null"})
    private String skuName;

    @ApiModelProperty("口味")
    @Excel(name = "Flavour", replace = {"-_null"})
    private String flavour;

    @ApiModelProperty("商品标签")
    private String commodityType;

    @ApiModelProperty("sku数量")
    @Excel(name = "Sku Quantity", replace = {"-_null"})
    private BigDecimal skuQuantity;

    @ApiModelProperty("经销价/标准盘价/经销单价/商品经销价")
    @Excel(name = "Distribution Price", replace = {"-_null"})
    private BigDecimal itemSupplyPrice;

    @ApiModelProperty("经销合计=标准盘价*箱规转换后数量")
    @Excel(name = "Distribution Price Total", replace = {"-_null"})
    private BigDecimal itemSupplyPriceSum;

    @ApiModelProperty("实付单价=实付小计/sku数量")
    @Excel(name = "Actual payment unit price", replace = {"-_null"})
    private BigDecimal skuRetailSub;

    @ApiModelProperty("sku现金支付金额/销售业绩/实付金额/现金小计/实付小计")
    @Excel(name = "Actual payment subtotal", replace = {"-_null"})
    private BigDecimal skuRetailSubTotal;

    @ApiModelProperty("旺金币合计/旺金币小计")
//    @Excel(name = "旺金币小计", replace = {"-_null"})
    private BigDecimal freeTotalAmount;

    @ApiModelProperty("订单标签")
    private Integer type;

    @ApiModelProperty("订单标签")
    @Excel(name = "Order labels", replace = {"-_null"})
    private String typeDesc;

    @ApiModelProperty("订单号")
    @Excel(name = "Order number", replace = {"-_null"})
    private String code;

    @ApiModelProperty("订单状态")
    @Excel(name = "Order Status", replace = {"-_null"})
    private String status;

    @ApiModelProperty("支付方式")
    @Excel(name = "Payment Methods", replace = {"-_null"})
    private String method;

    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @ApiModelProperty("下单时间")
    @Excel(name = "Time when the order was placed", replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime placedAt;

    @ApiModelProperty("收货省")
    @Excel(name = "Receiving Province/Central City", replace = {"-_null"})
    private String receiverProvince;

    @ApiModelProperty("收货市")
    @Excel(name = "Receiving City", replace = {"-_null"})
    private String receiverCity;

    @ApiModelProperty("收货区")
    @Excel(name = "Receiving District/County", replace = {"-_null"})
    private String receiverDistrict;

    @ApiModelProperty("收货地址")
    @Excel(name = "Receiver address ", replace = {"-_null"})
    private String receiverStreet;

    @ApiModelProperty("收货人手机号")
    @Excel(name = "Receiver mobile number", replace = {"-_null"})
    private String receiverMobileNumber;

    @ApiModelProperty("运输费用")
    @Excel(name = "Freight", replace = {"-_null"})
    private BigDecimal factoryShipingFee;

    @ApiModelProperty("满减活动/满减抵扣金额/预订单满减小计/满减小计/折扣金额")
    @Excel(name = "Discount Amount", replace = {"-_null"})
    private BigDecimal discountFullAmount;

    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @ApiModelProperty("收货时间")
    @Excel(name = "Receipt time", replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime receivedAt;

    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @ApiModelProperty("完成时间")
    @Excel(name = "Completion time", replace = {"-_null"}, exportFormat = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss)
    private LocalDateTime completeAt;

    @ApiModelProperty("退货金额")
    @Excel(name = "Return amount", replace = {"-_null"})
    private BigDecimal returnAmount;

    @ApiModelProperty("售后-物流破损部分")
    @Excel(name = "After-sales-logistics damage part", replace = {"-_null"})
    private String abnormalSecondaryCauseCps;

    @ApiModelProperty(value = "箱数")
    @ExcelIgnore
    private BigDecimal boxNum;

    @ApiModelProperty("售后破损箱数")
    @Excel(name = "Number of damaged boxes after sale", replace = {"-_null"})
    private BigDecimal goodsLossDifferenceBox;

    @ApiModelProperty("物流破损率")
    @Excel(name = "Logistics breakage rate", replace = {"-_null"})
    private BigDecimal goodsLossDifferenceRate;

}
