package com.wantwant.sfa.backend.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.google.common.collect.Lists;
import com.wantwant.sfa.common.base.CommonConstant;
import org.apache.commons.lang3.StringUtils;

import java.time.*;
import java.util.*;

public class LocalDateUtils {

    public static Date LDToDate(LocalDate localDate){
       return  Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }


    //季度日期转对应的月份
    // 季度(2024-01)转月份(2024-01，2024-02，2024-03)
    public static List<String> quarterTransformMonth(String quarterDate){
        //截取最后一个
        int year = Integer.valueOf(quarterDate.substring(0,4));
        int quarter = Integer.valueOf(quarterDate.substring(6));
        if(quarter >= 0 && quarter <=4) {
            String[] strings = new String[] {year + String.format("-%02d", quarter * 3 - 2),
                                            year + String.format("-%02d", quarter * 3 - 1),
                                            year + String.format("-%02d", quarter * 3)};
            return Arrays.asList(strings);
        }else {
            return null;
        }
    }

    /**
     * 通过月获取月的每天
     * @param yearMonthStr  yyyy-MM
     * @return
     */
    public static List<LocalDate> getDaysInMonth(String yearMonthStr) {
        if(StringUtils.isBlank(yearMonthStr)){
            return Lists.newArrayList();
        }
        // 将年月字符串转换为 YearMonth 对象
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);

        // 获取该月的第一天和最后一天
        LocalDate firstDay = yearMonth.atDay(1);
        LocalDate lastDay = yearMonth.atEndOfMonth();

        // 存储该月的每一天
        List<LocalDate> days = new ArrayList<>();
        LocalDate currentDay = firstDay;
        while (!currentDay.isAfter(lastDay)) {
            days.add(currentDay);
            // 逐日递增
            currentDay = currentDay.plusDays(1);
        }
        return days;
    }

    /**
     * 获取季度的每一天
     * @param yearQuarterStr 2024-Q1这种格式
     * @return
     */
    public static List<LocalDate> getDaysInQuarter(String yearQuarterStr) {
        if(StringUtils.isBlank(yearQuarterStr)){
            return Lists.newArrayList();
        }
        // 拆分年份和季度
        String[] parts = yearQuarterStr.split("-Q");
        int year = Integer.parseInt(parts[0]);
        int quarter = Integer.parseInt(parts[1]);

        // 确定季度的起始月份和结束月份
        int startMonth = (quarter - 1) * 3 + 1;
        int endMonth = startMonth + 2;

        // 获取该季度的第一天和最后一天
        LocalDate firstDay = LocalDate.of(year, startMonth, 1);
        LocalDate lastDay = Year.of(year).atMonth(endMonth).atEndOfMonth();

        // 存储该季度的每一天
        List<LocalDate> days = new ArrayList<>();
        LocalDate currentDay = firstDay;
        while (!currentDay.isAfter(lastDay)) {
            days.add(currentDay);
            // 逐日递增
            currentDay = currentDay.plusDays(1);
        }
        return days;
    }

    /**
     * 将02:30:00转换为分钟
     * @param timeCost
     * @return
     */
    public static long timeCostToMinutes(String timeCost){
        if(StringUtils.isBlank(timeCost)){
            return 0L;
        }
        LocalTime time = LocalTime.parse(timeCost);
        Duration duration = Duration.between(LocalTime.MIDNIGHT, time);
        return duration.toMinutes();
    }

    /**
     * 获取星期几
     */
    public static String getWeekOfDate(LocalDate date, String flag) {
        if (date == null) {
            return "";
        }
        String[] weekDays;
        if (CommonConstant.REGION_INDONESIA.equals(flag) || CommonConstant.LANGUAGE_ENGLISH.equals(flag) || CommonConstant.TIMEZONE_ASIA_JAKARTA.equals(flag)) {
            weekDays = new String[]{"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
        } else {
            weekDays = new String[]{"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        }
        return weekDays[LocalDateTimeUtil.dayOfWeek(date).getValue() - 1];
    }
}
