package com.wantwant.sfa.backend.complete.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.wantwant.commons.core.util.LocalDateTimeUtils;
import com.wantwant.sfa.common.base.annotation.GlobalTimezone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@ApiModel("通关今日返回")
public class CompleteTodayLatestListVo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("通关日期")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd, timezone = "GMT+8")
    private LocalDate completeDate;

    @ApiModelProperty("通关编号")
    private String completeNum;

    @ApiModelProperty("通关开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @GlobalTimezone
    private LocalDateTime completeStartTime;

    @ApiModelProperty("通关结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @GlobalTimezone
    private LocalDateTime completeEndTime;

    @ApiModelProperty("通关状态")
    private Integer completeStatus;

    @ApiModelProperty("通关打卡时间")
    @JsonFormat(pattern = LocalDateTimeUtils.yyyy_MM_dd_HH_mm_ss, timezone = "GMT+8")
    @GlobalTimezone
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "打卡地址")
    private String fullAddress;

    @ApiModelProperty(value = "打卡照片")
    private String image;

    @ApiModelProperty(value = "打卡照片名称")
    private String imageName;

    @ApiModelProperty("稽核状态")
    private Integer auditStatus;

    @ApiModelProperty("稽核异常原因")
    private String reason;

}
