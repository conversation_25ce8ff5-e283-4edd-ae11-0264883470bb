package com.wantwant.sfa.backend.activityQuota.request;

import com.wantwant.sfa.backend.util.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Description:
 * @Auther: zhengxu
 * @Date: 2022/10/17/上午10:24
 */
@Data
@ApiModel("扣罚监控查询request")
@ToString
public class PenaltyQueryRequest extends PageParam {
    @ApiModelProperty("查询开始时间:格式yyyy-MM-dd")
    private String queryStartTime;
    @ApiModelProperty("查询结束时间:格式yyyy-MM-dd")
    private String queryEndTime;
    @ApiModelProperty("扣罚规则ID")
    private Integer regularId;

    @ApiModelProperty("实际扣罚人员查询")
    private String actualPenaltyEmployeeKey;
    @ApiModelProperty("实际扣罚组织CODE")
    private String actualPenaltyOrgCode;
    @ApiModelProperty("应扣罚组织CODE")
    private String penaltyOrgCode;
    @ApiModelProperty("应扣人员查询")
    private String penaltyEmployeeKey;
    @ApiModelProperty("大类ID")
    private Integer categoryId;
    @ApiModelProperty("子类ID")
    private Integer secondaryCategoryId;

    @ApiModelProperty("金币类型:1.造旺币")
    private Integer coinsType;
    @ApiModelProperty("操作人")
    private String processUserKey;

    @ApiModelProperty("状态")
    private List<Integer> status;
    @ApiModelProperty("单据ID")
    private Long id;

    @ApiModelProperty("是否有实际扣罚:1")
    private Integer hasActualPenalty;

    private boolean needPage = true;

    private Integer businessGroup;
}
